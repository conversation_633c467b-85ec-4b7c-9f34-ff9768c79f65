import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { ProfilesComponent } from './components/interventions/profiles.component';
import { InventoryComponent } from './components/inventory/inventory.component';
import { DataEntryMainComponent } from './components/main/main.component';
import { PendingChangesGuard } from './guards/pending-changes.guard';

const routes: Routes = [
    {
        path: 'inventory',
        component: InventoryComponent
    },
    {
        path: 'intervention-profiles',
        component: ProfilesComponent
    },
    {
        path: 'targets-progress',
        component: DataEntryMainComponent,
        canDeactivate: [PendingChangesGuard],
        data: {
            slimHeader: true,
            hidePageTitle: true
        }
    }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class DataEntryRoutingModule {}
