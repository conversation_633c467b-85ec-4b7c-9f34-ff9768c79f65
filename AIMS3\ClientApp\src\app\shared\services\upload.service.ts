import { HttpClient, HttpEventType, HttpRequest, HttpResponse } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Doc } from '../../modules/library/models/document.model';

const API_URL = `${environment.apiUrl}`;

@Injectable({ providedIn: 'root' })
export class UploadService {
    constructor(private http: HttpClient) { }

    public uploadFile(urlPart: string, file: File, docModel?: Doc): { progress: Observable<number>, uploadStatus: Observable<number> } {
        // this will be the resulting map
        const status: { progress: Observable<number>, uploadStatus: Observable<number> } = { progress: null, uploadStatus: null};

        let url = `${API_URL}/${urlPart}`;

        // create a new multipart-form
        const formData: FormData = new FormData();
        formData.append('file', file, file.name);
        
        // if model is also to be sent
        if (docModel) {
            formData.append('data', JSON.stringify(docModel));
        }
        
        // create a http-post request and pass the form
        // tell it to report the upload progress
        let req = new HttpRequest('POST', url, formData, {
            reportProgress: true
        });

        // set content type
        req = req.clone({ headers: req.headers.set('Content-Type', '*') });

        // create a new progress-subject for every file
        const progress = new Subject<number>();
        let docId = new Subject<number>();

        // send the http-request and subscribe for progress-updates
        //const startTime = new Date().getTime();
        this.http.request(req).subscribe({
            next: (event) => {
                if (event.type === HttpEventType.UploadProgress) {
                    // calculate the progress percentage
                    const percentDone = Math.round((100 * event.loaded) / event.total);

                    // pass the percentage into the progress-stream
                    progress.next(percentDone);
                } else if (event instanceof HttpResponse) {
                    // capture return id and close the progress-stream if we get an answer from the API
                    docId.next(+event.body);
                }
            }, error: (err) => {
                docId.next(-1);
                progress.complete();
            }, complete: () => {
                // The upload is complete
                progress.complete();
                docId.complete();
            }
        });

        // Save progress-observable
        status.progress = progress.asObservable();
        status.uploadStatus = docId.asObservable();

        // return the map of progress.observables
        return status;
    }

    public uploadFiles(urlPart: string, files: Set<File>): { [key: string]: { progress: Observable<number>, docId: number } } {
        // this will be the resulting map
        const status: { [key: string]: { progress: Observable<number>, docId: number } } = {};

        let url = `${API_URL}/${urlPart}`;

        files.forEach(file => {
            // create a new multipart-form for every file
            const formData: FormData = new FormData();
            formData.append('file', file, file.name);

            // create a http-post request and pass the form
            // tell it to report the upload progress
            const req = new HttpRequest('POST', url, formData, {
                reportProgress: true
            });

            // create a new progress-subject for every file
            const progress = new Subject<number>();
            let docId: number;

            // send the http-request and subscribe for progress-updates
            //const startTime = new Date().getTime();
            this.http.request(req).subscribe(event => {
                if (event.type === HttpEventType.UploadProgress) {
                    // calculate the progress percentage
                    const percentDone = Math.round((100 * event.loaded) / event.total);

                    // pass the percentage into the progress-stream
                    progress.next(percentDone);
                } else if (event instanceof HttpResponse) {
                    // capture return id and close the progress-stream if we get an answer from the API
                    docId = +event.body;

                    // The upload is complete
                    progress.complete();
                }
            });

            // Save every progress-observable in a map of all observables
            status[file.name] = {
                progress: progress.asObservable(),
                docId: docId
            };
        });

        // return the map of progress.observables
        return status;
    }
}