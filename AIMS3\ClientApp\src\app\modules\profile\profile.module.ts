import { HttpClientModule } from '@angular/common/http';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { SharedModule } from '../../shared/shared.module';
import { ChangePasswordComponent } from './change-password/change-password.component';
import { ProfileRoutingModule } from './profile-routing.module';
import { ProfileComponent } from './profile.component';
import { UserInfoComponent } from './user-info/user-info.component';

@NgModule({
    declarations: [
        ProfileComponent,
        UserInfoComponent,
        ChangePasswordComponent,
    ],
    imports: [
        FormsModule,
        ReactiveFormsModule,
        HttpClientModule,
        ProfileRoutingModule,
        SharedModule,
        //InlineSVGModule,
        //DropdownMenusModule,
        //WidgetsModule,
        //CardsModule,
    ]
})
export class ProfileModule { }