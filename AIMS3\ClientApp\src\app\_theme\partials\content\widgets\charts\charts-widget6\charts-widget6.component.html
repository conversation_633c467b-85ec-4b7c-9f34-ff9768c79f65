<div class="card-header border-0 pt-5">
  <h3 class="card-title align-items-start flex-column">
    <span class="card-label fw-bolder fs-3 mb-1">Recent Orders</span>

    <span class="text-muted fw-bold fs-7">More than 500+ new orders</span>
  </h3>

  <!-- begin::Toolbar -->
  <div class="card-toolbar" data-qs-buttons="true">
    <a
      class="
        btn btn-sm btn-color-muted btn-active btn-active-primary
        active
        px-4
        me-1
      "
      id="qs_charts_widget_6_sales_btn"
    >
      Sales
    </a>

    <a
      class="btn btn-sm btn-color-muted btn-active btn-active-primary px-4 me-1"
      id="qs_charts_widget_6_expenses_btn"
    >
      Expenses
    </a>
  </div>
  <!-- end::Toolbar -->
</div>
<!-- end::Header -->

<!-- begin::Body -->
<div class="card-body">
  <!-- begin::Chart -->
  <div #chartRef id="qs_charts_widget_6_chart" [style.height]="'350px'">
    <apx-chart
      [series]="chartOptions.series"
      [chart]="chartOptions.chart"
      [xaxis]="chartOptions.xaxis"
      [yaxis]="chartOptions.yaxis"
      [dataLabels]="chartOptions.dataLabels"
      [stroke]="chartOptions.stroke"
      [legend]="chartOptions.legend"
      [fill]="chartOptions.fill"
      [states]="chartOptions.states"
      [tooltip]="chartOptions.tooltip"
      [colors]="chartOptions.colors"
      [markers]="chartOptions.markers"
      [plotOptions]="chartOptions.plotOptions"
    ></apx-chart>
  </div>
  <!-- end::Chart -->
</div>
