import { ChangeDetectorRef, Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { MessageService } from '../../../shared/services/message.service';
import { AppUtilities } from '../../../shared/utilities';
import { AuthService, User } from '../../auth';
import { AuthHTTPService } from '../../auth/services/auth-http';

@Component({
    selector: 'profile-user-info',
    templateUrl: './user-info.component.html',
    styleUrls: ['../profile.component.scss']
})
export class UserInfoComponent implements OnInit, OnDestroy {
    working: boolean = false;
    saving: boolean = false;

    user: User;
    form: FormGroup;
    
    private subscriptions: Subscription[] = [];
    constructor(
        private authService: AuthService,
        private userService: AuthHTTPService,
        private messageService: MessageService,
        private cdr: ChangeDetectorRef
    ) { }

    ngOnInit(): void {
        this.initForm();
        this.getLoggedInUser();
    }

    // convenient getter for easy access to form fields
    get f() {
        return this.form.controls;
    }

    initForm() {
        this.form = new FormGroup({
            //id: new FormControl({ value: this.user.id, disabled: true }),
            username: new FormControl({ value: '', disabled: true }),
            firstName: new FormControl('', Validators.required),
            //lastName: new FormControl(''),
            orgName: new FormControl({ value: '', disabled: true }),
            //phone: new FormControl(null,
            //    [Validators.pattern('^([0|\+[0-9]{1,5})?([1-9][0-9]{8})$'), Validators.maxLength(20)]),
            email: new FormControl('', [Validators.required, Validators.email]),
            emailAlt: new FormControl([])
        });
    }

    getLoggedInUser(): void {
        this.working = true;

        this.subscriptions.push(
            this.userService.getLoggedInUser().subscribe({
                next: (user: User) => {
                    this.user = user;
                },
                error: (e) => {
                    console.log(e);
                    this.working = false;
                },
                complete: () => {
                    this.form.setValue({
                        username: this.user.userName,
                        firstName: this.user.firstName,
                        //lastName: this.user.lastName,
                        orgName: this.user.organization?.fullName,
                        //phone: this.user.phoneNumber || '',
                        email: this.user.email,
                        emailAlt: this.user.emailAlt ? this.user.emailAlt.split(',') : []
                    });
                    
                    AppUtilities().initSelect2();
                    $('#emails').on('change', (e: any) => {
                        let selVal = [];
                        selVal = selVal.concat($(e.target).val());
                        this.f.emailAlt.setValue(new Set(selVal));
                        this.f.emailAlt.setErrors(null);

                        // test if any of the email is invalid
                        const regex = new RegExp(/^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/);
                        for (let i = 0; i < selVal.length; i++) {
                            if (!regex.test(selVal[i])) {
                                this.f.emailAlt.markAsDirty();
                                this.f.emailAlt.setErrors({ invalid: true });
                                break;
                            }
                        }
                    });
                    
                    this.cdr.detectChanges();
                    for (let i = 0; i < this.f.emailAlt.value.length; i++) {
                        const email = this.f.emailAlt.value[i];
                        $('#emails').append(new Option(email, email, true, true));
                    }
                    $('#emails').trigger('change');
                    
                    this.working = false;
                }
            })
        );
    }

    save() {
        this.user = new User(this.user.id, this.user.organizationId, this.user.userName,
            this.f.firstName.value, [], false, this.f.email.value,
            [...this.f.emailAlt.value].join(','));

        this.saving = true;

        // update user info
        this.subscriptions.push(
            this.userService.updateProfile(this.user).subscribe({
                error: (err) => {
                    this.saving = false;
                    console.log(err);
                },
                complete: () => {
                    // update the stored values
                    let currentUser = this.authService.currentUserValue;
                    if (currentUser) {
                        currentUser.firstName = this.user.firstName;
                        //currentUser.lastName = this.user.lastName;
                        currentUser.email = this.user.email;
                        
                        this.authService.currentUserSubject.next(currentUser);
                    }

                    this.saving = false;
                    this.messageService.success('Your information has been saved.');
                }
            })
        );
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}