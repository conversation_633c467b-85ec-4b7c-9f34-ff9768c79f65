using AIMS3.Business.Models;
using AIMS3.Business.Services;
using AIMS3.Data;
using AIMS3.Misc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AIMS3.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class TargetsController : ControllerBase
{
    private readonly ITargetService _targetService;

    public TargetsController(ITargetService targetService)
    {
        _targetService = targetService;
    }

    [HttpGet("{profId:int}/{projId:int}")]
    [HttpGet("{profId:int}/{projId:int}/{orgId:int}")]
    public async Task<IActionResult> GetOrgTargets(int profId, int projId, int? orgId)
    {
        if (User.IsInRole("Admin") || User.IsInRole("Approver") || User.IsInRole("Viewer"))
            orgId = orgId > 0 ? orgId : User.GetUserOrgId();
        else
            orgId = User.GetUserOrgId();

        return new ObjectResult(
            await _targetService.GetTargets(profId, projId, (int)orgId));
    }

    [HttpPost("{profId:int}/{projId:int}")]
    [HttpPost("{profId:int}/{projId:int}/{orgId:int}")]
    [Authorize(Policy = "RequireDataEntryRole")]
    public async Task<IActionResult> SaveTargets([FromBody] TargetDataModel model, int profId, int projId, int? orgId)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        if (User.IsInRole("Admin") || User.IsInRole("Approver") || User.IsInRole("LocalApprover"))
            orgId = orgId > 0 ? orgId : User.GetUserOrgId();
        else
            orgId = User.GetUserOrgId();

        var result = await _targetService.SaveTargets(model, profId, projId, (int)orgId, User.GetUserName());
        if (result.Result != OperationResult.Succeeded)
        {
            ModelState.TryAddModelError("serverError", "Error on the server.");
            return new BadRequestObjectResult(ModelState);
        }

        // AUTO-SUBMIT: For DataEntry users, automatically submit targets after saving
        if (User.IsInRole("DataEntry"))
        {
            // Get all unique periods and years from the saved targets
            var periods = model.Targets
                .Where(t => !string.IsNullOrEmpty(t.UniqueId) && t.Qtr.HasValue && t.Year.HasValue)
                .Select(t => new { Period = (int)t.Qtr.Value, Year = t.Year.Value })
                .Distinct()
                .ToList();

            // Submit data for each period/year combination
            foreach (var periodYear in periods)
            {
                var submitModel = new Business.Models.SubmitDataModel
                {
                    ProjId = projId,
                    Period = periodYear.Period,
                    Year = periodYear.Year,
                    OrgId = (int)orgId
                };

                // Call SubmitData to actually set DateSubmitted
                await _targetService.SubmitData(submitModel);
            }
        }

        return Ok(result);
    }

    [HttpPatch("submit")]
    [HttpPatch("submit/{voidStatus}")]
    [Authorize(Policy = "RequireDataEntryRole")]
    public async Task<IActionResult> ToggleSubmissionStatus([FromBody] int[] targetIds, string voidStatus)
    {
        return new OkObjectResult(
            await _targetService.MarkTargetsForSubmission(targetIds, User.GetUserOrgId(),
            User.GetUserName(), !string.IsNullOrEmpty(voidStatus))
        );
    }

    [HttpPatch("submit/{orgId:int}")]
    [HttpPatch("submit/{orgId:int}/{voidStatus}")]
    [Authorize(Policy = "RequireDataEntryRole")]
    public async Task<IActionResult> ToggleSubmissionStatus([FromBody] int[] targetIds, int orgId, string voidStatus)
    {
        if (!User.IsInRole("Admin") && !User.IsInRole("Approver"))
            orgId = User.GetUserOrgId();

        return new OkObjectResult(
            await _targetService.MarkTargetsForSubmission(targetIds, orgId,
            User.GetUserName(), !string.IsNullOrEmpty(voidStatus))
        );
    }

    #region Submit Data -----------------------------------------------------

    // Get submission log 
    [HttpGet("submission-log")]
    [Authorize(Policy = "RequireDataEntryRole")]
    public async Task<IActionResult> GetSubmissionLog()
    {
        if(User.IsInRole("DataEntry") || User.IsInRole("LocalApprover"))
        {
            return new ObjectResult(
            await _targetService.GetSubmissionLog(User.GetUserOrgId()));
        }

        return new ObjectResult(await _targetService.GetSubmissionLog());
    }

    [HttpPost("submit/check-data")]
    [Authorize(Policy = "RequireDataEntryRole")]
    public async Task<IActionResult> CheckMarkedData([FromBody] SubmitDataModel model)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        if (User.IsInRole("DataEntry") || User.IsInRole("LocalApprover"))
            model.OrgId = User.GetUserOrgId();

        return new ObjectResult(await _targetService.CheckMarkedForSubmissionData(model));
    }

    [HttpPatch("submit/data")]
    [Authorize(Policy = "RequireDataEntryRole")]
    public async Task<IActionResult> SubmitData([FromBody] SubmitDataModel model)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        if (User.IsInRole("DataEntry") || User.IsInRole("LocalApprover"))
            model.OrgId = User.GetUserOrgId();

        var result = await _targetService.SubmitData(model);
        if (result != OperationResult.Succeeded)
        {
            if (result == OperationResult.NoData)
                return new ObjectResult("NoData");
            else
                ModelState.TryAddModelError("serverError", "Error on the server.");

            return new BadRequestObjectResult(ModelState);
        }

        return Ok();
    }

    #endregion
}