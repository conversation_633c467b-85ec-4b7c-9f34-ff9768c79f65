import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DocumentsComponent } from './components/documents/documents.component';
import { DownloadComponent } from './components/download/download.component';

const routes: Routes = [
    {
        path: 'documents',
        component: DocumentsComponent
    },
    {
        path: 'download/:docId',
        component: DownloadComponent
    },
    {
        path: '',
        redirectTo: '/library/documents',
        pathMatch: 'full',
    }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class LibraryRoutingModule {}
