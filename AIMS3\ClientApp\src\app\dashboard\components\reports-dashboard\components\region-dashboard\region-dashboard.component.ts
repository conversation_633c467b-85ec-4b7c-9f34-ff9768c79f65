import { Component, Input, OnChanges, OnInit, SimpleChanges, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgApexchartsModule } from 'ng-apexcharts';
import { HttpClient } from '@angular/common/http';
import { 
  ReportData, 
  ReportFilter,
  Province,
  District
} from '../../models/reports.model';

declare var L: any; // For Leaflet map

@Component({
  selector: 'app-region-dashboard',
  standalone: true,
  imports: [CommonModule, NgApexchartsModule],
  templateUrl: './region-dashboard.component.html',
  styleUrls: ['./region-dashboard.component.scss']
})
export class RegionDashboardComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() reportData: ReportData;
  @Input() filters: ReportFilter;
  
  // For template use
  Math = Math;
  
  // Chart options
  regionChartOptions: any;
  provinceChartOptions: any;
  
  // Map properties
  map: any;
  mapInitialized = false;
  mapMarkers: any[] = [];
  defaultLatitude = 34.5553; // Afghanistan center latitude
  defaultLongitude = 69.2075; // Afghanistan center longitude
  defaultZoom = 6;
  
  // Add loading state
  mapLoading = true;
  mapError = false;

  constructor(private http: HttpClient) { }

  ngOnInit(): void {
    this.initializeCharts();
  }
  
  ngAfterViewInit(): void {
    this.initializeMap();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.reportData?.currentValue) {
      this.initializeCharts();
      
      if (this.mapInitialized) {
        this.updateMapMarkers();
      }
    }
  }

  private initializeCharts(): void {
    this.initRegionChart();
    this.initProvinceChart();
  }
  
  private initRegionChart(): void {
    if (!this.reportData) return;
    
    const regions = [
      { region: 'Central Highlands', value: 68 },
      { region: 'North Eastern', value: 42 },
      { region: 'Western', value: 56 },
      { region: 'Southern', value: 35 },
      { region: 'Eastern', value: 29 },
      { region: 'Central', value: 45 },
      { region: 'South Eastern', value: 38 },
      { region: 'Northern', value: 52 }
    ];
    
    this.regionChartOptions = {
      series: [{
        name: 'Activities',
        data: regions.map(r => r.value)
      }],
      chart: {
        type: 'bar',
        height: 350,
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          borderRadius: 4,
          horizontal: false,
          columnWidth: '60%'
        }
      },
      title: {
        text: 'Activities by Region',
        align: 'left'
      },
      colors: this.getRegionColors(),
      dataLabels: {
        enabled: false
      },
      xaxis: {
        categories: regions.map(r => r.region)
      },
      yaxis: {
        title: {
          text: 'Number of Activities'
        }
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val + ' activities';
          }
        }
      }
    };
  }
  
  private initProvinceChart(): void {
    if (!this.reportData) return;
    
    const provinces = [
      { province: 'Kabul', region: 'Central', value: 43 },
      { province: 'Herat', region: 'Western', value: 32 },
      { province: 'Balkh', region: 'Northern', value: 27 },
      { province: 'Kandahar', region: 'Southern', value: 21 },
      { province: 'Nangarhar', region: 'Eastern', value: 19 },
      { province: 'Paktia', region: 'South Eastern', value: 14 },
      { province: 'Bamyan', region: 'Central Highlands', value: 12 }
    ];
    
    this.provinceChartOptions = {
      series: [{
        name: 'Activities',
        data: provinces.map(p => p.value)
      }],
      chart: {
        type: 'bar',
        height: 350,
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          borderRadius: 4,
          horizontal: true
        }
      },
      title: {
        text: 'Top Provinces by Activities',
        align: 'left'
      },
      colors: provinces.map(p => this.getRegionColor(p.region)),
      dataLabels: {
        enabled: false
      },
      xaxis: {
        categories: provinces.map(p => p.province)
      },
      yaxis: {
        title: {
          text: 'Number of Activities'
        }
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val + ' activities';
          }
        }
      }
    };
  }

  private async initializeMap(): Promise<void> {
    if (typeof L !== 'undefined') {
      try {
        // Create map instance
        this.map = L.map('region-map').setView([this.defaultLatitude, this.defaultLongitude], this.defaultZoom);
        
        // Add tile layer (OpenStreetMap)
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
          attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        }).addTo(this.map);

        try {
          // Load province boundaries from local GeoJSON file using fetch
          const response = await fetch('/assets/data/afghanistan-provinces.geojson');
          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }
          const data = await response.json();
          
          if (!data || !data.features) {
            throw new Error('Invalid GeoJSON data');
          }

          const provinceLayer = L.geoJSON(data, {
            style: {
              color: '#666',
              weight: 2,
              fillColor: '#f8f9fa',
              fillOpacity: 0.3
            },
            onEachFeature: (feature, layer) => {
              layer.on({
                mouseover: (e) => {
                  const layer = e.target;
                  layer.setStyle({
                    fillOpacity: 0.5,
                    weight: 3
                  });
                },
                mouseout: (e) => {
                  const layer = e.target;
                  layer.setStyle({
                    fillOpacity: 0.3,
                    weight: 2
                  });
                },
                click: (e) => {
                  const layer = e.target;
                  const provinceName = feature.properties.PROV_34_NA;
                  const popupContent = `
                    <div class="map-popup">
                      <h5>${provinceName}</h5>
                      <p>Click to view detailed statistics</p>
                    </div>
                  `;
                  layer.bindPopup(popupContent).openPopup();
                }
              });
            }
          }).addTo(this.map);
          
          // Add markers for activities
          this.updateMapMarkers();
          
          this.mapInitialized = true;
          this.mapLoading = false;
        } catch (error) {
          console.error('Error loading or processing GeoJSON:', error);
          this.mapError = true;
          this.mapLoading = false;
        }
      } catch (error) {
        console.error('Error initializing map:', error);
        this.mapError = true;
        this.mapLoading = false;
      }
    } else {
      console.error('Leaflet library not loaded.');
      this.mapError = true;
      this.mapLoading = false;
    }
  }

  private updateMapMarkers(): void {
    if (!this.map) return;
    
    // Clear existing markers
    this.mapMarkers.forEach(marker => this.map.removeLayer(marker));
    this.mapMarkers = [];
    
    // Mock activity locations - replace with actual data
    const activityLocations = [
      { lat: 34.5553, lng: 69.2075, name: 'Kabul Activities', status: 'ongoing', count: 15 },
      { lat: 36.7069, lng: 67.1122, name: 'Mazar-e-Sharif Activities', status: 'completed', count: 8 },
      { lat: 34.3380, lng: 62.2041, name: 'Herat Activities', status: 'planned', count: 5 },
      { lat: 31.6201, lng: 65.7160, name: 'Kandahar Activities', status: 'ongoing', count: 12 },
      { lat: 34.4300, lng: 70.4460, name: 'Jalalabad Activities', status: 'completed', count: 7 }
    ];
    
    // Add new markers
    activityLocations.forEach(location => {
      const markerColor = this.getMarkerColorByStatus(location.status);
      const markerSize = this.getMarkerSizeByCount(location.count);
      
      const marker = L.circleMarker([location.lat, location.lng], {
        radius: markerSize,
        fillColor: markerColor,
        color: '#fff',
        weight: 1,
        opacity: 1,
        fillOpacity: 0.8
      }).addTo(this.map);
      
      marker.bindPopup(`
        <div class="map-popup">
          <h5>${location.name}</h5>
          <p><strong>Status:</strong> ${this.capitalizeFirstLetter(location.status)}</p>
          <p><strong>Activities:</strong> ${location.count}</p>
        </div>
      `);
      
      this.mapMarkers.push(marker);
    });
  }
  
  private getMarkerColorByStatus(status: string): string {
    switch (status) {
      case 'completed': return '#4CAF50';
      case 'ongoing': return '#FFC107';
      case 'planned': return '#2196F3';
      default: return '#9E9E9E';
    }
  }
  
  private getMarkerSizeByCount(count: number): number {
    // Scale marker size based on activity count (min 7, max 15)
    return Math.max(7, Math.min(15, 7 + (count / 5)));
  }
  
  private capitalizeFirstLetter(text: string): string {
    return text.charAt(0).toUpperCase() + text.slice(1);
  }
  
  formatNumber(num: number): string {
    return num ? num.toLocaleString() : '0';
  }

  private getRegionColors(): string[] {
    return [
      '#999ed8', // Central Highlands
      '#a7bccf', // North Eastern
      '#82b4b1', // Western
      '#cfe0bc', // Southern
      '#dde2a0', // Eastern
      '#9cd6c0', // Central
      '#99d597', // South Eastern
      '#afb593'  // Northern
    ];
  }

  private getRegionColor(region: string): string {
    const regionColors: { [key: string]: string } = {
      'central highlands': '#999ed8',
      'north eastern': '#a7bccf',
      'western': '#82b4b1',
      'southern': '#cfe0bc',
      'eastern': '#dde2a0',
      'central': '#9cd6c0',
      'south eastern': '#99d597',
      'northern': '#afb593'
    };
    
    return regionColors[region.toLowerCase()] || '#9E9E9E';
  }
}
