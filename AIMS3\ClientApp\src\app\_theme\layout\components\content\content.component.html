<ng-container *ngIf="appContentContiner">
    <!--begin::Content container-->
    <!-- d-flex align-items-stretch justify-content-between -->
    <div id="qs_app_content_container" class="app-container {{contentContainerCSSClass}}" *ngIf="loading"
         [ngClass]="{ 'container-fluid': appContentContiner === 'fluid', 'container-xxl': appContentContiner === 'fixed' }">
        <aims-loading></aims-loading>
    </div>
    <div id="qs_app_content_container" class="app-container {{contentContainerCSSClass}}"
         [ngClass]="{ 'container-fluid': appContentContiner === 'fluid', 'container-xxl': appContentContiner === 'fixed', 'd-none': loading }">
        <router-outlet></router-outlet>
    </div>
    <!--end::Content container-->
</ng-container>
<ng-container *ngIf="!appContentContiner">
    <router-outlet></router-outlet>
</ng-container>