import { Routes } from '@angular/router';
import { AuthGuard } from './auth/services/auth.guard';

const Routing: Routes = [
    {
        path: 'dashboard',
        loadChildren: () =>
            import('../dashboard/dashboard.module').then((m) => m.DashboardModule)
    },
    {
        path: 'profile',
        canActivate: [AuthGuard],
        loadChildren: () =>
            import('./profile/profile.module').then((m) => m.ProfileModule)
    },
    {
        path: 'admin',
        canActivate: [AuthGuard],
        loadChildren: () =>
            import('./admin/admin.module').then((m) => m.AdminModule),
        //data: { roles: [Role.Admin] }
    },
    {
        path: 'locations',
        loadChildren: () =>
            import('./locations/location.module').then((m) => m.LocationModule)
    },
    {
        path: 'data-entry',
        canActivate: [AuthGuard],
        loadChildren: () =>
            import('./data-entry/data-entry.module').then((m) => m.DataEntryModule)
    },
    {
        path: 'data',
        canActivate: [AuthGuard],
        loadChildren: () =>
            import('./data/data.module').then((m) => m.DataModule)
    },
    {
        path: 'library',
        canActivate: [AuthGuard],
        loadChildren: () =>
            import('./library/library.module').then((m) => m.LibraryModule)
    },
    {
        path: '',
        redirectTo: '/dashboard',
        pathMatch: 'full',
    },
    {
        path: '**',
        redirectTo: 'error/404',
    }
];

export { Routing };