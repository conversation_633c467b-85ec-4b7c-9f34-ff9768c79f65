import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { OperationResult } from '../../../shared/enums';
import { OperationDataResult } from '../../../shared/models/operation-result.model';
import { Profile, ProfileInfo } from '../models/profile.model';

const API_URL = `${environment.apiUrl}/interventions`;

@Injectable({ providedIn: 'root' })
export class ProfileService {
    constructor(private http: HttpClient) { }

    getProfiles(): Observable<ProfileInfo[]> {
        return this.http.get<ProfileInfo[]>(API_URL + '/filtered');
    }

    addProfile(prof: Profile): Observable<OperationDataResult> {
        return this.http.post<OperationDataResult>(API_URL, prof);
    }

    updateProfile(prof: Profile): Observable<OperationResult> {
        return this.http.put<OperationResult>(API_URL, prof);
    }

    duplicateProfile(prof: Profile): Observable<OperationDataResult> {
        return this.http.put<OperationDataResult>(`${API_URL}/duplicate`, prof);
    }

    deleteProfile(profId: number): Observable<void> {
        return this.http.delete<void>(`${API_URL}/${profId}`);
    }
}