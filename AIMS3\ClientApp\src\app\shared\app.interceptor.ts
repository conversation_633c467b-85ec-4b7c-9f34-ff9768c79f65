import { HttpErrorResponse, HttpEvent, HttpHandler, HttpInterceptor, HttpRequest } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { AuthService } from '../modules/auth';
import { ErrorService } from './services/error.service';

@Injectable()
export class AppInterceptor implements HttpInterceptor {
    constructor(
        private authService: AuthService,
        private errorService: ErrorService
    ) { }

    intercept(request: HttpRequest<any>, next: <PERSON>ttpHand<PERSON>): Observable<HttpEvent<any>> | Observable<never> {
        const token = this.authService.getUserAuthToken();

        // add authentication token to api request if user is logged in
        if (token) {
            request = request.clone({
                headers: request.headers.set('Authorization', 'Bearer ' + token)
            });
        }

        if (!request.headers.has('Content-Type')) {
            request = request.clone({
                headers: request.headers.set('Content-Type', 'application/json')
            });
        } else if (request.headers.get('Content-Type') === '*') {
            request = request.clone({
                headers: request.headers.delete('Content-Type')
            });
        }

        if (!request.headers.has('Accept')) {
            request = request.clone({
                headers: request.headers.set('Accept', 'application/json')
            });
        }

        return next.handle(request).pipe(
            map((event: HttpEvent<any>) => {
                return event;
            }),
            catchError((err: HttpErrorResponse) => {
                console.error(err);
                this.errorService.showMessage(err);

                if (err.status === 401) {
                    // if any modal is open, close it
                    const modalBackdrop = document.querySelector('ngb-modal-backdrop');
                    modalBackdrop?.remove();
                    const modal = document.querySelector('ngb-modal-window');
                    modal?.remove();

                    this.authService.logout();
                    return new Observable<never>;
                }

                return throwError(() => err);
            }));
    }
}