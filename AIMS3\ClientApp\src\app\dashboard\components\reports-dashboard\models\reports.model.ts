// Define the hierarchical structure for UNDP outputs, categories, and interventions

// Time period types for reports
export enum TimePeriodType {
  Monthly = 'monthly',
  Quarterly = 'quarterly',
  Yearly = 'yearly'
}

// Quarter definition
export enum Quarter {
  Q1 = 'Q1', // Jan-Mar
  Q2 = 'Q2', // Apr-Jun
  Q3 = 'Q3', // Jul-Sep
  Q4 = 'Q4'  // Oct-Dec
}

// Interface for dynamic column data
export interface DynamicColumnData {
  id: number;
  columnId: number;
  columnName: string;
  displayName?: string;
  columnType: string; // TargetInfo, Target, Progress, etc.
  dataType: string; // Text, Number, Currency, etc.
  value: string | number | boolean;
  unit?: string;
  description?: string;
  order?: number;
}

// Interface for dynamic indicator configuration
export interface IndicatorConfig {
  id: string;
  name: string;
  categoryId?: string; // Optional: specific to category
  interventionId?: string; // Optional: specific to intervention
  columnPattern: string; // Regex pattern to match column names
  displayType: 'table' | 'chart' | 'both';
  chartType?: 'bar' | 'line' | 'pie' | 'donut';
  groupBy?: string; // Field to group data by (e.g., 'sector', 'type', 'location')
  aggregationType?: 'sum' | 'average' | 'count' | 'max' | 'min';
  unit?: string;
  description?: string;
  order?: number;
}

// Interface for processed indicator data ready for visualization
export interface ProcessedIndicator {
  config: IndicatorConfig;
  data: IndicatorDataPoint[];
  chartData?: ChartData;
  apexChartData?: ApexChartData; // ApexCharts specific data
  tableData?: TableData;
  summary?: {
    total: number;
    average: number;
    count: number;
  };
}

// Data point for indicators
export interface IndicatorDataPoint {
  label: string;
  value: number;
  unit?: string;
  category?: string;
  subcategory?: string;
  metadata?: { [key: string]: any };
}

// Table data structure
export interface TableData {
  headers: string[];
  rows: TableRow[];
  totals?: { [key: string]: number };
}

export interface TableRow {
  [key: string]: any;
}

// Enhanced interface for dynamic column processing
export interface ProcessedDynamicData {
  info: { [key: string]: any }; // General information columns
  targets: { [key: string]: any }; // Target-related columns
  progress: { [key: string]: any }; // Progress-related columns
  raw: DynamicColumnData[]; // Raw dynamic column data
  indicators?: ProcessedIndicator[]; // Processed indicators for visualization
}

// ApexCharts specific chart data interface
export interface ApexChartData {
  id: string;
  title: string;
  options: any; // ApexOptions from ng-apexcharts
  series: any[];
  height?: number;
}

// Activity represents a specific implementation instance
export interface Activity {
  id: string;
  name: string;
  interventionId: string;
  location: {
    provinceId: number;
    provinceName: string;
    districtId?: number;
    districtName?: string;
    coordinates?: { lat: number; lng: number };
  };
  status: 'ongoing' | 'completed' | 'planned';
  startDate: string;
  endDate?: string;
  beneficiaries: number;
  budget: number;
  cashDistributed: number;
  progress: number; // 0-100 percent
  dynamicColumns?: DynamicColumnData[]; // Raw dynamic column data
  processedDynamicData?: ProcessedDynamicData; // Processed and organized dynamic data
  
  // Legacy fields for backward compatibility
  info?: ActivityInfo; // General information from dynamic columns
  cumulativeProgress?: ActivityProgress; // Progress metrics from dynamic columns
}

// Interface for additional activity information from dynamic columns
export interface ActivityInfo {
  [key: string]: any; // Generic key-value pairs for flexible data storage
  description?: string;
  beneficiaryDetails?: {
    men?: number;
    women?: number;
    children?: number;
    households?: number;
  };
  implementingPartner?: string;
  contactPerson?: string;
  notes?: string;
}

// Interface for activity progress metrics from dynamic columns
export interface ActivityProgress {
  [key: string]: any; // Generic key-value pairs for flexible data storage
  target?: number;
  actual?: number;
  percentComplete?: number;
  milestones?: {
    name: string;
    date: string;
    completed: boolean;
  }[];
  indicators?: {
    name: string;
    value: number;
    target: number;
    unit?: string;
  }[];
}

// Intervention represents a specific type of program under a category
export interface Intervention {
  id: string;
  name: string;
  categoryId: string;
  description?: string;
  activities?: Activity[];
  totalBudget?: number;
  totalCashDistributed?: number;
  totalBeneficiaries?: number;
  progress?: number; // Calculated from activities
  startDate?: string;
  endDate?: string;
  totalActivities?: number;
}

// Category groups related interventions under an output
export interface Category {
  id: string;
  code: string; // e.g. "1.1", "1.2"
  name: string;
  outputId: string;
  description?: string;
  interventions: Intervention[];
  totalBudget?: number;
  totalCashDistributed?: number;
  totalBeneficiaries?: number;
  progress?: number; // Calculated from interventions
  structures?: number; // Number of structures completed for Infrastructure category
  households?: number; // Number of households reached for Infrastructure category
  indicators?: CategoryIndicator[]; // Specific indicators for the category
  totalActivities?: number;
  projectIds?: string[]; // <-- Added for dashboard filtering
}

// Define category-specific indicators
export interface CategoryIndicator {
  id: string;
  name: string;
  value: number;
  description?: string;
  icon?: string; // Icon class for UI display
}

// Infrastructure specific indicators
export interface InfrastructureIndicators {
  structures: number; // Number of structures built/rehabilitated
  households: number; // Number of households benefiting
}

// Health specific indicators
export interface HealthIndicators {
  facilities: number; // Number of health facilities supported
  patients: number; // Number of patients served
}

// Livelihoods specific indicators
export interface LivelihoodsIndicators {
  businesses: number; // Number of businesses supported
  jobs: number; // Number of jobs created
}

// Economic specific indicators
export interface EconomicIndicators {
  cashTransfers: number; // Amount of cash transfers
  recipients: number; // Number of recipients 
}

// Output represents a major strategic objective
export interface Output {
  id: string;
  code: string; // e.g. "1", "2"
  name: string;
  description?: string;
  categories: Category[];
  totalBudget?: number;
  totalCashDistributed?: number;
  totalBeneficiaries?: number;
  progress?: number; // Calculated from categories
}

// Filter options for the reports
export interface ReportFilter {
  projectGroups?: string[];
  projectGroup?: string;
  projects?: string[];
  projectStatus?: ('active' | 'completed' | 'planned' | 'suspended')[];
  outputs?: string[];
  categories?: string[];
  catIds?: string[] | number[]; // Added for backend API compatibility - same as categories
  interventions?: string[];
  profIds?: string[] | number[]; // Added for backend API compatibility - same as interventions
  organizations?: string[];
  regions?: string[];
  provinces?: string[];
  districts?: string[];
  communities?: string[];
  activityStatus?: ('ongoing' | 'completed' | 'archived' | 'cancelled')[]; // Activity status filter
  timePeriod?: {
    type: TimePeriodType;
    year: number;
    quarter?: Quarter;
    month?: number;
  };
  startDate?: Date | null;
  endDate?: Date | null;
  gender?: string;
  ageGroup?: string;
  vulnerabilityGroup?: string;
  searchTerm?: string;
  
  // Time period filtering
  timePeriodType?: TimePeriodType;
  year?: number;
  quarter?: Quarter;
  month?: number;
  
  // Additional filtering options used in the UI
  status?: string[];
  minBudget?: number;
  maxBudget?: number;
  minProgress?: number;
  maxProgress?: number;
}

// Geographic location types
export interface Province {
  id: number;
  name: string;
  districts: District[];
  coordinates?: { lat: number; lng: number };
}

export interface District {
  id: number;
  name: string;
  provinceId: number;
  provinceName: string;
  coordinates?: { lat: number; lng: number };
}

// Report types
export interface ReportData {
  id?: string;
  title?: string;
  data?: any;
  outputs: Output[];
  totalBudget: number;
  totalCashDistributed: number;
  totalBeneficiaries: number;
  overallProgress: number;
  activitiesByStatus: {
    ongoing: number;
    completed: number;
    cancelled: number;
  };
  geographicCoverage: {
    provinces: number;
    districts: number;
  };
  kpis?: KPI[];
  timePeriod?: {
    type: TimePeriodType;
    year: number;
    quarter?: Quarter;
    month?: number;
  };
}

export interface KPI {
  id: string;
  title: string;
  value: number;
  change?: number;
  changeDirection?: 'up' | 'down' | 'none';
  unit?: string;
  icon?: string;
}

export interface TimeSeriesDataPoint {
  date: string;
  value: number;
  category?: string;
}

export interface TimeSeriesData {
  id?: string;
  title?: string;
  period?: string;
  periodType?: TimePeriodType;
  year?: number;
  quarter?: Quarter;
  month?: number;
  activitiesStarted?: number;
  activitiesCompleted?: number;
  beneficiariesReached?: number;
  budgetAllocated?: number;
  expenditure?: number;
  seriesData?: TimeSeriesDataPoint[];
  
  // New fields for Cash Distributed implementation
  date?: string;
  budget?: number;
  cashDistributed?: number;
  beneficiaries?: number;
  progress?: number;
  regions?: {
    [key: string]: RegionData;
  };
}

export interface ChartData {
  id: string;
  title: string;
  data: any;
  type: ChartType;
  options?: any;
}

export enum ChartType {
  Bar = 'bar',
  Line = 'line',
  Pie = 'pie',
  Donut = 'donut',
  Area = 'area',
  Scatter = 'scatter',
  Map = 'map'
}

// Summary statistic types for dashboards
export interface SummaryStats {
  totalProjects: number;
  totalBudget: number;
  totalCashDistributed: number;
  totalBeneficiaries: number;
  avgProgress: number;
  countByStatus: any;
  countByLocation: any;
  topCategories: any[];
}

export interface RegionalData {
  region: string;
  value: number;
}

export interface ProjectStatus {
  status: string;
  count: number;
}

// Project Group represents a collection of related projects
export interface ProjectGroup {
  id: string;
  name: string;
  description?: string;
  projects?: Project[]; // Make projects optional
  totalBudget?: number;
  totalCashDistributed?: number;
  totalBeneficiaries?: number;
  progress?: number;
}

// Project represents individual initiatives
export interface Project {
  id: string;
  code: string;
  name: string;
  groupId: string;
  grouping?: string; // Group name (same as group name)
  description?: string;
  status: 'active' | 'completed' | 'planned' | 'suspended';
  startDate: string;
  endDate?: string;
  budget: number;
  cashDistributed: number;
  beneficiaries?: number;
  progress: number;
  outputs?: string[]; // References to output IDs
  activities?: Activity[]; // Project activities
  location?: {
    provinceId: number;
    provinceName: string;
    districtId?: number;
    districtName?: string;
    coordinates?: { lat: number; lng: number };
  };
}

export interface RegionData {
  name: string;
  cashDistributed: number;
  beneficiaries: number;
  activities: number;
}

// Category modal configuration
export interface CategoryModalConfig {
  categoryId: string;
  categoryName: string;
  defaultIndicators: IndicatorConfig[];
  customIndicators?: IndicatorConfig[];
  allowUserCustomization: boolean;
} 