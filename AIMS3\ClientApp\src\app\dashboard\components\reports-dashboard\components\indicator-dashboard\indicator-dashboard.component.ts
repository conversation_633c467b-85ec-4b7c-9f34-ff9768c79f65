﻿import { Component, OnInit, Input, Output, EventEmitter, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReportFilter, IndicatorReportData, AvailableIndicator } from '../../models/reports.model';
import { ReportsService } from '../../services/reports.service';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'app-indicator-dashboard',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="indicator-dashboard">
      <!-- Summary Cards -->
      <div class="row mb-4" *ngIf="summaryStats?.length > 0">
        <div class="col-md-3" *ngFor="let stat of summaryStats">
          <div class="card">
            <div class="card-body text-center">
              <h3 class="text-primary">{{ stat.value }}</h3>
              <p class="text-muted mb-0">{{ stat.label }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- Loading State -->
      <div *ngIf="loading" class="d-flex justify-content-center my-5">
        <div class="spinner-border text-primary" role="status">
          <span class="visually-hidden">Loading categories and indicators...</span>
        </div>
      </div>

      <!-- Error State -->
      <div *ngIf="error" class="alert alert-danger">
        <i class="bi bi-exclamation-triangle me-2"></i>{{ error }}
        <button class="btn btn-sm btn-outline-danger ms-3" (click)="refreshIndicators()">
          <i class="bi bi-arrow-clockwise me-1"></i>Retry
        </button>
      </div>

      <!-- Categories Grid -->
      <div class="categories-grid" *ngIf="!loading && !error && categories?.length > 0">
        <h4 class="mb-3">
          <i class="bi bi-bar-chart-line me-2"></i>Categories with Indicators
          <span class="badge badge-light-primary ms-2">{{ categories.length }}</span>
        </h4>
        
        <div class="row">
          <div class="col-md-4 mb-4" *ngFor="let category of categories">
            <div class="card category-card h-100" (click)="selectCategory(category)" 
                 [class.selected]="selectedCategory?.id === category.id">
              <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-3">
                  <h5 class="card-title">{{ category.name }}</h5>
                  <span class="badge badge-light-info">{{ category.indicatorCount }} indicators</span>
                </div>
                
                <p class="card-text text-muted">{{ category.description || 'Category from ' + category.output }}</p>
                
                <!-- Quick Stats -->
                <div class="category-stats mt-3">
                  <div class="row text-center">
                    <div class="col-4">
                      <div class="stat-item">
                        <div class="stat-value">{{ category.totalActivities || 0 }}</div>
                        <div class="stat-label">Activities</div>
                      </div>
                    </div>
                    <div class="col-4">
                      <div class="stat-item">
                        <div class="stat-value">{{ category.totalBudget || 0 | number:'1.0-0' }}</div>
                        <div class="stat-label">Budget</div>
                      </div>
                    </div>
                    <div class="col-4">
                      <div class="stat-item">
                        <div class="stat-value">{{ category.progress || 0 }}%</div>
                        <div class="stat-label">Progress</div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Indicator Preview -->
                <div class="indicator-preview mt-3" *ngIf="category.sampleIndicators?.length > 0">
                  <small class="text-muted fw-bold">Sample Indicators:</small>
                  <ul class="list-unstyled mt-1">
                    <li class="small text-muted" *ngFor="let indicator of category.sampleIndicators.slice(0, 2)">
                      • {{ indicator.name }}
                    </li>
                    <li class="small text-info" *ngIf="category.sampleIndicators.length > 2">
                      • +{{ category.sampleIndicators.length - 2 }} more...
                    </li>
                  </ul>
                </div>
              </div>
              
              <div class="card-footer">
                <small class="text-muted">
                  <i class="bi bi-diagram-3 me-1"></i>
                  {{ category.output }} • Code: {{ category.code }}
                </small>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- No Categories Message -->
      <div *ngIf="!loading && !error && (!categories || categories.length === 0)" class="text-center my-5">
        <i class="bi bi-bar-chart-line fs-1 text-muted"></i>
        <h4 class="mt-3">No Categories with Indicators Found</h4>
        <p class="text-muted">No categories with indicators are available for the selected filters.</p>
        <button class="btn btn-primary" (click)="refreshIndicators()">
          <i class="bi bi-arrow-clockwise me-1"></i>Refresh Data
        </button>
      </div>

      <!-- Selected Category Indicators Modal -->
      <div class="modal fade" id="categoryIndicatorsModal" tabindex="-1" *ngIf="selectedCategory">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">
                <i class="bi bi-bar-chart-line me-2"></i>
                Indicators for {{ selectedCategory.name }}
              </h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <!-- Category Info -->
              <div class="alert alert-light-info mb-3">
                <div class="row">
                  <div class="col-md-8">
                    <h6 class="mb-1">{{ selectedCategory.name }}</h6>
                    <p class="mb-0 small">{{ selectedCategory.description || selectedCategory.output }}</p>
                  </div>
                  <div class="col-md-4 text-end">
                    <span class="badge badge-light-primary">Code: {{ selectedCategory.code }}</span>
                  </div>
                </div>
              </div>
              
              <!-- Indicators List -->
              <div *ngIf="selectedCategoryIndicators?.length > 0">
                <h6 class="mb-3">Indicators ({{ selectedCategoryIndicators.length }})</h6>
                <div class="indicator-item mb-3" *ngFor="let indicator of selectedCategoryIndicators">
                  <div class="card">
                    <div class="card-body">
                      <div class="d-flex justify-content-between align-items-start">
                        <div class="flex-grow-1">
                          <h6 class="card-title">{{ indicator.name }}</h6>
                          <p class="card-text text-muted small">{{ indicator.description || indicator.f1Info || 'No description available' }}</p>
                          <div class="formula-info mt-2">
                            <small class="text-info">
                              <i class="bi bi-calculator me-1"></i>
                              <strong>Formula:</strong> {{ indicator.formula1 || 'No formula defined' }}
                            </small>
                            <br>
                            <small class="text-success" *ngIf="indicator.projectGroups">
                              <i class="bi bi-diagram-2 me-1"></i>
                              <strong>Project Groups:</strong> {{ indicator.projectGroups }}
                            </small>
                          </div>
                        </div>
                        <div class="text-end ms-3">
                          <div class="indicator-value">
                            <span class="fs-4 fw-bold text-primary">{{ indicator.currentValue || indicator.value1 || 0 | number:'1.0-0' }}</span>
                            <div class="text-muted small">{{ indicator.unit || extractUnit(indicator.f1Info) || 'units' }}</div>
                          </div>
                          <div class="mt-2" *ngIf="indicator.value2">
                            <small class="text-muted">Target: {{ indicator.value2 | number:'1.0-0' }}</small>
                          </div>
                        </div>
                      </div>
                      
                      <!-- Progress Bar -->
                      <div class="mt-3" *ngIf="indicator.value2">
                        <div class="d-flex justify-content-between align-items-center">
                          <span class="small">Progress to Target:</span>
                          <span class="badge" [class]="getProgressBadgeClass(calculateProgress(indicator))">
                            {{ calculateProgress(indicator) }}%
                          </span>
                        </div>
                        <div class="progress mt-2">
                          <div class="progress-bar" [style.width.%]="calculateProgress(indicator)"
                               [class]="getProgressBarClass(calculateProgress(indicator))"></div>
                        </div>
                      </div>
                      
                      <!-- Additional Formula Info -->
                      <div class="mt-3" *ngIf="indicator.formula2 || indicator.formula3 || indicator.formula4">
                        <small class="text-muted">
                          <strong>Additional Formulas:</strong>
                          <span *ngIf="indicator.formula2"> F2: {{ indicator.formula2 }}</span>
                          <span *ngIf="indicator.formula3"> F3: {{ indicator.formula3 }}</span>
                          <span *ngIf="indicator.formula4"> F4: {{ indicator.formula4 }}</span>
                        </small>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- No Indicators -->
              <div *ngIf="!selectedCategoryIndicators || selectedCategoryIndicators.length === 0" class="text-center py-4">
                <i class="bi bi-info-circle fs-1 text-muted"></i>
                <h5 class="mt-3">No Indicators Found</h5>
                <p class="text-muted">This category doesn't have any indicators defined in the Indicators table yet.</p>
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
              <button type="button" class="btn btn-primary" (click)="exportCategoryReport()">
                <i class="bi bi-download me-1"></i>Export Report
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .category-card {
      cursor: pointer;
      transition: all 0.3s ease;
      border: 2px solid transparent;
    }
    
    .category-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      border-color: #007bff;
    }
    
    .category-card.selected {
      border-color: #007bff;
      background-color: #f8f9ff;
    }
    
    .stat-item {
      text-align: center;
    }
    
    .stat-value {
      font-weight: bold;
      font-size: 1.1rem;
      color: #007bff;
    }
    
    .stat-label {
      font-size: 0.8rem;
      color: #6c757d;
    }
    
    .indicator-value {
      text-align: center;
    }
    
    .progress {
      height: 8px;
    }
    
    .indicator-item {
      border-left: 4px solid #007bff;
    }
    
    .indicator-preview {
      border-top: 1px solid #eee;
      padding-top: 0.5rem;
    }
    
    .formula-info {
      background: #f8f9fa;
      padding: 0.5rem;
      border-radius: 0.25rem;
      border-left: 3px solid #007bff;
    }
  `]
})
export class IndicatorDashboardComponent implements OnInit {
  @Input() filters: ReportFilter = {};
  @Output() filterChange = new EventEmitter<ReportFilter>();

  // Data properties
  categories: any[] = [];
  allIndicators: any[] = [];
  selectedCategory: any = null;
  selectedCategoryIndicators: any[] = [];
  summaryStats: any[] = [];
  
  // State properties
  loading = false;
  error: string | null = null;

  constructor(
    private reportsService: ReportsService,
    private cdr: ChangeDetectorRef
  ) {}

  ngOnInit(): void {
    this.loadCategoriesAndIndicators();
  }

  /**
   * Load real categories from hierarchical endpoint and indicators from database
   */
  private loadCategoriesAndIndicators(): void {
    this.loading = true;
    this.error = null;

    console.log('🔄 Loading categories from hierarchical endpoint and indicators from database...');

    // Load both categories and indicators in parallel
    forkJoin({
      hierarchicalData: this.reportsService.getHierarchicalReport(this.filters),
      availableIndicators: this.reportsService.getAvailableIndicators(this.filters)
    }).subscribe({
      next: (result) => {
        console.log('✅ Received hierarchical data:', result.hierarchicalData?.length || 0, 'groups');
        console.log('✅ Received indicators:', result.availableIndicators?.length || 0, 'indicators');

        // Extract categories from hierarchical data
        this.extractCategoriesFromHierarchical(result.hierarchicalData || []);
        
        // Store all indicators
        this.allIndicators = result.availableIndicators || [];
        
        // Map indicators to categories
        this.mapIndicatorsToCategories();
        
        // Calculate summary stats
        this.calculateSummaryStats();
        
        this.loading = false;
        this.cdr.detectChanges();
      },
      error: (err) => {
        console.error('❌ Error loading categories and indicators:', err);
        this.error = 'Failed to load categories and indicators. Please check if the backend is running.';
        this.loading = false;
        this.cdr.detectChanges();
      }
    });
  }

  /**
   * Extract categories from hierarchical data
   */
  private extractCategoriesFromHierarchical(hierarchicalData: any[]): void {
    const categoryMap = new Map<string, any>();

    hierarchicalData.forEach(group => {
      if (group.projects && Array.isArray(group.projects)) {
        group.projects.forEach(project => {
          if (project.interventions && Array.isArray(project.interventions)) {
            project.interventions.forEach(intervention => {
              const categoryId = intervention.categoryId || 'unknown';
              const categoryName = intervention.categoryName || 'Unknown Category';
              
              if (!categoryMap.has(categoryId)) {
                categoryMap.set(categoryId, {
                  id: categoryId,
                  name: categoryName,
                  code: this.extractCategoryCode(categoryName),
                  output: group.name || 'Unknown Output',
                  description: intervention.description || `Activities for ${categoryName}`,
                  indicatorCount: 0,
                  totalActivities: 0,
                  totalBudget: 0,
                  progress: 0,
                  sampleIndicators: [],
                  lastUpdated: new Date()
                });
              }

              const category = categoryMap.get(categoryId);
              
              // Aggregate activity data
              if (intervention.activities && Array.isArray(intervention.activities)) {
                category.totalActivities += intervention.activities.length;
                
                intervention.activities.forEach(activity => {
                  category.totalBudget += activity.budget || 0;
                  category.progress += activity.progress || 0;
                });
              }
            });
          }
        });
      }
    });

    // Calculate average progress
    categoryMap.forEach(category => {
      if (category.totalActivities > 0) {
        category.progress = Math.round(category.progress / category.totalActivities);
      }
    });

    this.categories = Array.from(categoryMap.values());
    console.log('✅ Extracted categories from hierarchical data:', this.categories.length);
  }

  /**
   * Map indicators to categories using TypeName
   */
  private mapIndicatorsToCategories(): void {
    console.log('🔄 Mapping indicators to categories...');
    
    // Group indicators by TypeName
    const indicatorsByType = new Map<string, any[]>();
    
    this.allIndicators.forEach(indicator => {
      const typeName = indicator.typeName || 'Uncategorized';
      
      if (!indicatorsByType.has(typeName)) {
        indicatorsByType.set(typeName, []);
      }
      
      indicatorsByType.get(typeName)!.push({
        ...indicator,
        currentValue: indicator.value1,
        unit: this.extractUnit(indicator.f1Info),
        description: indicator.f1Info
      });
    });

    console.log('✅ Indicators grouped by type:', indicatorsByType.size, 'types');

    // Update categories with indicator counts and samples
    this.categories.forEach(category => {
      // Try to match category name with indicator TypeName
      const matchingIndicators = this.findMatchingIndicators(category.name, indicatorsByType);
      
      if (matchingIndicators.length > 0) {
        category.indicatorCount = matchingIndicators.length;
        category.sampleIndicators = matchingIndicators.slice(0, 3);
      }
    });

    // Add categories for indicators that don't match existing categories
    indicatorsByType.forEach((indicators, typeName) => {
      const existingCategory = this.categories.find(cat => 
        this.getCategoryNameVariations(cat.name).some(variation => 
          variation.toLowerCase().includes(typeName.toLowerCase()) ||
          typeName.toLowerCase().includes(variation.toLowerCase())
        )
      );

      if (!existingCategory) {
        // Create new category for this indicator type
        this.categories.push({
          id: typeName.toLowerCase().replace(/\s+/g, '_'),
          name: typeName,
          code: this.extractCategoryCode(typeName),
          output: 'Output from Indicators',
          description: `Indicators of type: ${typeName}`,
          indicatorCount: indicators.length,
          totalActivities: 0,
          totalBudget: 0,
          progress: 0,
          sampleIndicators: indicators.slice(0, 3),
          lastUpdated: new Date()
        });
      }
    });

    console.log('✅ Final categories with indicators:', this.categories.length);
    this.categories.forEach(cat => {
      console.log(`  - ${cat.name}: ${cat.indicatorCount} indicators`);
    });
  }

  /**
   * Find matching indicators for a category
   */
  private findMatchingIndicators(categoryName: string, indicatorsByType: Map<string, any[]>): any[] {
    const categoryVariations = this.getCategoryNameVariations(categoryName);
    let matchingIndicators: any[] = [];

    indicatorsByType.forEach((indicators, typeName) => {
      const typeNameLower = typeName.toLowerCase();
      
      if (categoryVariations.some(variation => 
        variation.toLowerCase().includes(typeNameLower) ||
        typeNameLower.includes(variation.toLowerCase())
      )) {
        matchingIndicators.push(...indicators);
      }
    });

    return matchingIndicators;
  }

  /**
   * Get variations of category name for matching
   */
  private getCategoryNameVariations(categoryName: string): string[] {
    const variations = [categoryName];
    
    // Add shortened versions
    if (categoryName.includes(' ')) {
      variations.push(categoryName.split(' ')[0]); // First word
      variations.push(categoryName.split(' ').pop()!); // Last word
    }
    
    // Add common variations
    if (categoryName.toLowerCase().includes('infrastructure')) {
      variations.push('Infrastructure', 'Construction', 'Development');
    }
    if (categoryName.toLowerCase().includes('health')) {
      variations.push('Health', 'Medical', 'Healthcare');
    }
    if (categoryName.toLowerCase().includes('economic')) {
      variations.push('Economic', 'UCT', 'Cash', 'Financial');
    }
    
    return variations;
  }

  /**
   * Extract category code from name
   */
  private extractCategoryCode(categoryName: string): string {
    // Try to extract numeric code or create one
    const match = categoryName.match(/(\d+\.?\d*)/);
    if (match) {
      return match[1];
    }
    
    // Generate code from first letters
    return categoryName.split(' ')
      .filter(word => word.length > 0)
      .map(word => word[0].toUpperCase())
      .join('.');
  }

  /**
   * Calculate summary statistics
   */
  private calculateSummaryStats(): void {
    const totalIndicators = this.allIndicators.length;
    const categoriesWithIndicators = this.categories.filter(cat => cat.indicatorCount > 0).length;
    const totalActivities = this.categories.reduce((sum, cat) => sum + cat.totalActivities, 0);
    const avgProgress = this.categories.length > 0 
      ? Math.round(this.categories.reduce((sum, cat) => sum + cat.progress, 0) / this.categories.length)
      : 0;

    this.summaryStats = [
      { label: 'Total Indicators', value: totalIndicators },
      { label: 'Categories', value: categoriesWithIndicators },
      { label: 'Total Activities', value: totalActivities },
      { label: 'Average Progress', value: `${avgProgress}%` }
    ];
  }

  /**
   * Select a category and show its indicators
   */
  selectCategory(category: any): void {
    this.selectedCategory = category;
    
    // Find all indicators for this category
    this.selectedCategoryIndicators = this.allIndicators.filter(indicator => {
      const typeName = indicator.typeName || '';
      const categoryVariations = this.getCategoryNameVariations(category.name);
      
      return categoryVariations.some(variation => 
        variation.toLowerCase().includes(typeName.toLowerCase()) ||
        typeName.toLowerCase().includes(variation.toLowerCase())
      );
    });

    console.log(`✅ Selected category "${category.name}" with ${this.selectedCategoryIndicators.length} indicators`);
    
    // Show modal
    const modalElement = document.getElementById('categoryIndicatorsModal');
    if (modalElement) {
      const modal = new (window as any).bootstrap.Modal(modalElement);
      modal.show();
    }
  }

  /**
   * Calculate progress percentage for an indicator
   */
  calculateProgress(indicator: any): number {
    const current = parseFloat(indicator.value1 || indicator.currentValue || 0);
    const target = parseFloat(indicator.value2 || 0);
    
    if (target <= 0) return 0;
    return Math.min(Math.round((current / target) * 100), 100);
  }

  /**
   * Extract unit from formula info
   */
  extractUnit(formulaInfo: string): string {
    if (!formulaInfo) return '';
    
    const info = formulaInfo.toLowerCase();
    if (info.includes('usd') || info.includes('dollar')) return 'USD';
    if (info.includes('percent') || info.includes('%')) return '%';
    if (info.includes('household')) return 'households';
    if (info.includes('patient')) return 'patients';
    if (info.includes('facility')) return 'facilities';
    if (info.includes('business')) return 'businesses';
    if (info.includes('job')) return 'jobs';
    if (info.includes('recipient')) return 'recipients';
    if (info.includes('count') || info.includes('number')) return 'count';
    
    return 'units';
  }

  /**
   * Get progress badge CSS class
   */
  getProgressBadgeClass(progress: number): string {
    if (progress >= 80) return 'badge-success';
    if (progress >= 50) return 'badge-warning';
    return 'badge-danger';
  }

  /**
   * Get progress bar CSS class
   */
  getProgressBarClass(progress: number): string {
    if (progress >= 80) return 'bg-success';
    if (progress >= 50) return 'bg-warning';
    return 'bg-danger';
  }

  /**
   * Export category report
   */
  exportCategoryReport(): void {
    if (!this.selectedCategory) return;
    
    const reportData = {
      category: this.selectedCategory,
      indicators: this.selectedCategoryIndicators,
      filters: this.filters,
      exportDate: new Date(),
      summary: {
        categoryName: this.selectedCategory.name,
        totalIndicators: this.selectedCategoryIndicators.length,
        averageProgress: this.selectedCategoryIndicators.length > 0 
          ? Math.round(this.selectedCategoryIndicators.reduce((sum, ind) => sum + this.calculateProgress(ind), 0) / this.selectedCategoryIndicators.length)
          : 0
      }
    };
    
    const dataStr = JSON.stringify(reportData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `${this.selectedCategory.name.replace(/\s+/g, '_')}-indicators-report.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  }

  /**
   * Refresh indicators data
   */
  refreshIndicators(): void {
    this.loadCategoriesAndIndicators();
  }
}
