<app-modal #modal [modalConfig]="modalConfig">
    <form id="inventoryForm" class="form" [formGroup]="form">
        <div class="row form-group">
            <label class="col-3 col-form-label required">Intervention</label>
            <div class="col-9">
                <select id="profile" class="form-select" data-control="select2" data-placeholder="Select profile" formControlName="profId">
                    <optgroup *ngFor="let intp of interventions" [label]="intp.category">
                        <option *ngFor="let profile of intp.profiles" [value]="profile.id">{{ profile.name }} ({{ profile.abbreviation }})</option>
                    </optgroup>
                </select>
            </div>
        </div>
        <div class="row form-group">
            <label class="col-3 col-form-label required">Project</label>
            <div class="col-9">
                <span class="spinner-border text-secondary h-15px w-15px float-end" title="Getting data..." *ngIf="working"></span>
                <select id="proj" class="form-select" data-control="select2" data-placeholder="Select project" formControlName="projId">
                    <option *ngFor="let proj of projects" [value]="proj.id">{{ proj.name }} ({{ proj.abbreviation }})</option>
                </select>
            </div>
        </div>
        <div class="row form-group" *ngIf="(['ga'] | isAuth)">
            <label class="col-3 col-form-label required">Partner</label>
            <div class="col-9">
                <select id="org" class="form-select" data-control="select2" data-placeholder="Select partner" formControlName="orgId">
                    <option *ngFor="let org of orgs" [value]="org.id" [title]="org.tooltip">{{ org.name }}</option>
                </select>
            </div>
        </div>
        <!-- Details -->
        <div class="row form-group">
            <label class="col-3 col-form-label">
                Description
                <i class="la la-info-circle icon-md text-muted ms-2" ngbTooltip="Details based on BoQ and workplan."></i>
            </label>
            <div class="col-9">
                <textarea class="form-control" type="text" formControlName="details" rows="2"></textarea>
            </div>
        </div>
        <div class="row form-group" *ngIf="(['ga'] | isAuth)">
            <label class="col-3 col-form-label required">Status</label>
            <div class="col-9">
                <div class="form-check form-check-custom form-check-solid pt-2">
                    <input id="status" type="checkbox" class="form-check-input" checked="checked" disabled />
                    <label for="status" class="form-check-label ms-3">Activated</label>
                </div>
            </div>
        </div>
    </form>

    <ng-template #formError let-control="control" let-message="message" let-validation="validation">
        <ng-container *ngIf="control.hasError(validation) && control.dirty">
            <div class="fv-plugins-message-container">
                <div class="fv-help-block">
                    <span role="alert">{{ message }}</span>
                </div>
            </div>
        </ng-container>
    </ng-template>
</app-modal>