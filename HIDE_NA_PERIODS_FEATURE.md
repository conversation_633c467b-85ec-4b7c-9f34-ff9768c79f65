# Hide Empty Progress Feature

## Overview
This feature allows users to hide activities with empty or meaningless progress data from the data/view page when viewing progress data. This includes activities with "N/A" periods AND activities that don't have actual progress values for the selected time series. This helps clean up the interface by removing activities that don't contribute meaningful data to the cumulative progress view.

## How It Works

### Frontend Implementation
1. **Toggle Control**: A toggle switch "Hide empty progress" appears in the filter toolbar when viewing Progress data (not visible for Target data)
2. **Default Behavior**: The feature is enabled by default (`hideNAPeriods = true`)
3. **Enhanced Filtering**: Activities are filtered at two levels:
   - In `ViewDataComponent.getData()` - filters the data before passing to the grid
   - In `ReadOnlyGridComponent.refreshGridRows()` - ensures consistency at the grid level

### Filtering Logic
An activity is filtered out (hidden) if ANY of the following conditions are true:

1. **N/A Period Check**:
   - The `asOf` field is "N/A", null, undefined, or empty
   - The `progressId` is missing or invalid (≤ 0)

2. **Empty Progress Data Check** (NEW):
   - When time series filters are applied (period > 0 or periodEnd > 0)
   - The activity has no meaningful progress values in `colVals`
   - All dynamic column values are null, undefined, empty, or whitespace-only

3. **Backend Filtering** (NEW):
   - Activities without progress records in the filtered time period
   - Activities without dynamic column values containing actual data

### Backend Implementation
Enhanced `ViewDataService.cs` with additional filtering in the `GetProgressData` method:

```csharp
// Filter out activities without meaningful progress data
var activitiesWithProgress = activitiesWithCols.Where(activity => {
    // For activities with time series filtering, ensure they have relevant progress data
    if ((filters.Period > 0 || filters.PeriodEnd > 0) && !filters.ApprovalMode)
    {
        // Check if activity has valid progress data in the filtered time period
        bool hasValidProgress = false;
        
        // Check if there's an actual progress record (not just info-level data)
        if (activity.ProgressId != null && activity.ProgressId > 0)
        {
            // Check if the activity has dynamic column values (progress metrics)
            var progressColumns = activity.ColVals?.Where(cv => cv != null && 
                !string.IsNullOrEmpty(cv.Value?.ToString())).ToList();
            
            if (progressColumns?.Any() == true)
            {
                hasValidProgress = true;
            }
        }
        
        // Also check if AsOfDate is within the filtered period and not "N/A"
        if (hasValidProgress && activity.AsOfDate != null)
        {
            var asOfString = GetAsOfDateString(activity.Id, activity.SDate, activity.EDate, activity.AsOfDate);
            if (asOfString == "N/A" || string.IsNullOrEmpty(asOfString))
            {
                hasValidProgress = false;
            }
        }
        
        return hasValidProgress;
    }
    
    // For non-time-series filtering or approval mode, keep all activities
    return true;
});
```

## User Experience

### Before Enhancement
- Activities appeared in the list even if they had no progress data for the filtered time period
- Empty cumulative progress sections confused users
- Cluttered interface with non-informative rows

### After Enhancement  
- Only activities with meaningful progress data appear in the results
- Cleaner, more focused view showing actual progress
- Better user experience when filtering by time series
- Toggle allows users to see all activities if needed

## Benefits

1. **Cleaner Interface**: Removes clutter from activities without meaningful progress
2. **Better Focus**: Users see only activities with actual progress data for their filtered period
3. **Improved Performance**: Fewer rows to render and process
4. **User Control**: Toggle allows viewing all activities when needed
5. **Consistent Behavior**: Works the same way across all time series filtering scenarios

## Technical Notes

- Only affects Progress data view (not Target data)
- Filtering is applied both on backend and frontend for consistency
- Works with all time series filtering combinations (single period, period ranges)
- Preserves user choice through the toggle control
- Default behavior improves user experience out of the box 