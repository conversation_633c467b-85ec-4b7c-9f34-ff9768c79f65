<aims-working *ngIf="working"></aims-working>
<div class="row form-group">
    <label class="col-3 pt-2">Pivots for</label>
    <div class="col-9">
        <select id="profiles" class="form-select form-select-sm" data-control="select2" data-placeholder="Select intervention">
            <option id="0" *ngIf="(['ga','gv'] | isAuth)">All Interventions</option>
            <ng-container *ngFor="let pCat of interventions">
                <optgroup [label]="pCat.name" *ngIf="pCat.profiles.length">
                    <option *ngFor="let profile of pCat.profiles" [value]="profile.id" [selected]="selProfId === profile.id">
                    {{ profile.name }} ({{ profile.abbreviation }})
                    </option>
                </optgroup>
            </ng-container>
        </select>
    </div>
</div>

<div class="text-center flex-fill" tabindex="0" (click)="onAddEdit()" *ngIf="isAdmin && selProfId > 0">
    <label class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex align-items-center justify-content-center px-4 py-2">
        <span class="fw-semibold d-block fs-6">Add new pivot table</span>
    </label>
</div>
<div class="text-center flex-fill" tabindex="0" (click)="onView()" *ngIf="!isAdmin">
    <label class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex align-items-center justify-content-center px-4 py-2">
        <span class="fw-semibold d-block fs-6">New pivot playground</span>
    </label>
</div>

<div class="pivot-tables mt-5 fs-7">
    <p class="text-center text-muted" *ngIf="!pivots.length">No pivot table created.</p>
    <div class="pivot-card border rounded mb-5" *ngFor="let pivot of pivots" title="Pivot table: {{pivot.name}}"
         [draggable]="isAdmin && selProfId > 0 && pivots.length > 1 ? true : null"
         [attr.data-pivot-id]="pivot.id" [attr.data-order]="pivot.order">
        <div class="d-flex flex-stack p-2 bg-light-{{pivot.isTarget ? 'info' : 'primary'}} border-bottom border-gray-300"
             [ngClass]="{'cursor-move': isAdmin && selProfId > 0 && pivots.length > 1}">
            <span class="pivot-name fw-bold text-truncate" (click)="onAddEdit(pivot)">{{ pivot.name }}</span>
            <div class="flex-end">
                <span class="text-muted fs-8 me-1">{{ pivot.prof?.abbreviation }}</span>
                <span class="badge badge-badge-sm badge-light-info" ngbTooltip="Targets" *ngIf="pivot.isTarget">T</span>
                <span class="badge badge-badge-sm badge-light-primary" ngbTooltip="Progress" *ngIf="!pivot.isTarget">P</span>
                <span class="btn btn-sm btn-icon w-15px h-15px cursor-move btn-bg-light btn-active-color-primary"
                      ngbTooltip="Drag and drop to re-order pivot" *ngIf="isAdmin && selProfId > 0 && pivots.length > 1">
                    <i class="la la-arrows-alt-v"></i>
                </span>
                <!-- Actions -->
                <button class="btn btn-sm btn-icon w-15px
                        h-15px btn-bg-light btn-active-color-primary ms-2"
                        ngbTooltip="Expand table" *ngIf="!(['admin'] | isAuth) else settingMenu">
                    <i class="la la-expand-arrows-alt"></i>
                </button>
                <ng-template #settingMenu>
                    <button class="btn btn-sm btn-icon w-15px h-15px btn-bg-light btn-active-color-primary ms-2"
                            data-qs-menu-trigger="click" data-qs-menu-placement="bottom-end" data-qs-menu-flip="top-end">
                        <i class="bi bi-three-dots"></i>
                    </button>
                    <!-- Dropdown menu -->
                    <div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown py-lg-1" data-qs-menu="true">
                        <div class="menu-item menu-lg-down-accordion pb-0">
                            <a class="menu-link pt-2" (click)="onAddEdit(pivot)">
                                <span class="menu-icon">
                                    <span [inlineSVG]="'./assets/media/icons/duotune/general/gen019.svg'" class="svg-icon svg-icon-3"></span>
                                </span><span class="menu-title">Setting</span>
                            </a>
                        </div>
                        <div class="menu-item separator"></div>
                        <div class="menu-item menu-lg-down-accordion">
                            <a class="menu-link py-2" (click)="onDelete(pivot.id, pivot.name)">
                                <span class="menu-icon">
                                    <span [inlineSVG]="'./assets/media/icons/duotune/general/trash.svg'" class="svg-icon svg-icon-3 text-danger"></span>
                                </span><span class="menu-title text-danger">Delete</span>
                            </a>
                        </div>
                    </div>
                </ng-template>
            </div>
        </div>
        <aims-pivot-grid-thumb class="pivot-table bg-light" [pivot]="pivot" [rowData]="rowData" (click)="onAddEdit(pivot)"></aims-pivot-grid-thumb>
    </div>
</div>

<!-- Pivot Grid Modal -->
<pivot-modal (done)="getPivotTables()"></pivot-modal>