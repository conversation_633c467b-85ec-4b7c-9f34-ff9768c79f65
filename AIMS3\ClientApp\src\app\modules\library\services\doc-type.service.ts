import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { DocType } from '../models/doc-type.model';

const API_URL = `${environment.apiUrl}/documents/doc-types`;

@Injectable({ providedIn: 'root' })
export class DocumentTypeService {
    constructor(private http: HttpClient) { }

    getAllDocumentTypes(): Observable<DocType[]> {
        return this.http.get<DocType[]>(`${API_URL}/all`);
    }

    addDocumentType(docType: DocType): Observable<DocType> {
        return this.http.post<DocType>(API_URL, docType);
    }

    updateDocumentType(docType: DocType): Observable<DocType> {
        return this.http.put<DocType>(API_URL, docType);
    }

    deleteDocType(docTypeId: number): Observable<void> {
        return this.http.delete<void>(`${API_URL}/${docTypeId}`);
    }

}