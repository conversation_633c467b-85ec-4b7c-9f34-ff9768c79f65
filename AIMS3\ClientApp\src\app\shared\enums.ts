export enum OperationResult {
    Succeeded,
    Exists,
    NotAllowed,
    Failed
}

export enum Role {
    Admin           = 'Admin',
    Approver        = 'Approver',
    Viewer          = 'Viewer',
    DataEntry       = 'DataEntry',
    LocalApprover   = 'LocalApprover',
    LocalViewer     = 'LocalViewer'
}

export enum OrganizationType {
    UNDP,
    RPA,
    LVG,
    Other
}

export enum Region {
    National,
    Central,
    CentralHighland,
    Eastern,
    SouthEastern,
    NorthEastern,
    Western,
    Southern,
    Northern
}

export enum ColumnVarType {   // order maintains compatiblity with AIMS 2.0
    TargetInfo,
    Target,
    Progress,
    TargetStatic,
    ProgressInfo,
    ProgressStatic
}

export enum ColDataType {
    Text,
    Number,
    Currency,
    Percentage,
    GPS,
    Date,
    Checkbox,
    SelectSingle,
    SelectMultiple,
    Formula,
    Attachment
}

export enum TargetPeriod {
    Annual,
    Q1,
    Q2,
    Q3,
    Q4,
    All
}

export enum ActivityStatus {
    Ongoing,
    Completed,
    Archived,
    Cancelled
}

export enum SubmissionType {
    Monthly,
    Quarterly,
    Annual
}

export enum ApprovalStatus {
    UnderReview,
    Approved,
    Rejected
}

export enum UploadLocation {
    Any,
    Library,
    DataEntry
}