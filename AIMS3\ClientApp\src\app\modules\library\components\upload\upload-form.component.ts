import { ChangeDetector<PERSON><PERSON>, Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Subject, Subscription, lastValueFrom } from 'rxjs';
import { ModalComponent, ModalConfig } from '../../../../_theme/partials';
import { FilterDropdownList } from '../../../../shared/components/filter-dropdown/filter-ddl.control';
import { ColDataType } from '../../../../shared/enums';
import { MessageService } from '../../../../shared/services/message.service';
import { UploadService } from '../../../../shared/services/upload.service';
import { AppUtilities } from '../../../../shared/utilities';
import { DocType, DocTypeField } from '../../models/doc-type.model';
import { Doc, FieldValue } from '../../models/document.model';
import { DocumentService } from '../../services/document.service';

@Component({
    selector: 'doc-upload-form-modal',
    templateUrl: './upload-form.component.html',
    styleUrls: ['./upload-form.component.scss']
})
export class UploadFormComponent implements OnInit, OnDestroy {
    working: boolean = false;

    document: Doc;
    form: FormGroup;
    file: File;
    uploadFile: any;

    fileTypes: DocType[] = [];
    activities: FieldValue[] = [];

    @ViewChild('modal') private modalComponent: ModalComponent;
    modalConfig: ModalConfig = {
        modalTitle: 'Upload document',
        cancelButtonLabel: 'Close',
        disableDoneButton: true,
        options: { size: 'lg' },
        shouldDo: () => this.save(),
        shouldCancel: () => { this.ngOnDestroy(); return true; }
    };
    @Output() done = new EventEmitter<Doc>();

    subscriptions: Subscription[] = [];
    constructor(
        private docService: DocumentService,
        private uploadService: UploadService,
        //private sharedService: SharedService,
        private cdr: ChangeDetectorRef,
        private messageService: MessageService
    ) {
        this.document = new Doc(-1, '', '');
    }

    ngOnInit() {
        this.initForm();
        this.actIdRequired = false;

        if (this.document.id > -1) {
            this.modalComponent?.open().then();

            if (this.document.id > 0) {
                this.modalConfig.modalTitle = 'Edit document information';
                this.modalConfig.doneButtonLabel = 'Save';
            } else if (!this.document.isLink) {
                this.modalConfig.modalTitle = 'Upload document';
                this.modalConfig.doneButtonLabel = 'Upload';

                // click on file select
                setTimeout(() => {
                    const fileInput = document.querySelector('#fileInput') as HTMLInputElement;
                    fileInput.click();
                }, 50);
            } else {
                this.modalConfig.modalTitle = 'Add a linked document/file';
                this.modalConfig.doneButtonLabel = 'Add link';
            }

            this.fields = [];
            if (!this.activities.length)
                this.getActivities();

            if (this.document.docTypeId)
                this.onSelectFileType();
        }
    }

    getActivities(): void {
        this.working = true;
        this.subscriptions.push(
            this.docService.getActivities().subscribe({
                next: (acts) => {
                    this.activities = acts;
                },
                error: (e) => {
                    console.log(e);
                    this.working = false;
                },
                complete: () => {
                    this.bindActivities();
                    this.working = false;
                }
            }));
    }

    private bindActivities(): void {
        this.cdr.detectChanges();
        AppUtilities().initSelect2();

        $('#activities').on('change', (e) => {
            const selVal = $(e.target).val() as string[];
            let vals = [];
            selVal.forEach(v => vals.push(+v));
            this.f.actIds.setValue(vals);

            if (this.docType?.isActRequired)
                this.actIdRequired = vals.length ? false : true;
        });
        
        if (this.document.activityIds?.length) {
            let actIds = [];
            this.document.activityIds.forEach(a => {
                const actId = this.activities.find(act => act.value === String(a))?.fieldId;
                actIds.push(actId);
            });

            $('#activities').val(actIds).trigger('change');
        }
    }

    // convenient getter for easy access to form fields
    get f() {
        return this.form.controls;
    }

    fields: DocTypeField[] = [];
    private initForm() {
        this.form = new FormGroup({
            id: new FormControl({ value: this.document.id, disabled: true }),
            fName: new FormControl(this.document.fileName),
            name: new FormControl(this.document.docName, Validators.required),
            fileType: new FormControl(this.document.docTypeId, [Validators.required, Validators.min(-1)]),
            desc: new FormControl(this.document.docDesc),
            shared: new FormControl(!this.document.organizationId),
            actIds: new FormControl(this.document.activityIds || [])
        });

        if (this.document.isLink) {
            this.form.controls['fName'].addValidators(Validators.required);
            this.form.controls['fileType'].disable();
        } else
            this.form.controls['fName'].disable();

        this.form.valueChanges.subscribe(() => {
            this.modalConfig.disableDoneButton = true;

            if (this.form.valid) {
                if (this.document.id > 0)
                    this.modalConfig.disableDoneButton = false;
                else if (this.file || (!this.file && this.document.isLink))
                    this.modalConfig.disableDoneButton = false;
            }
        });

        this.form.controls['name'].valueChanges.subscribe((val) => {
            this.document.docName = val;
        });
    }

    onFileSelect(e): void {
        try {
            if (!e.target.files?.length) {
                this.file = null;
                this.modalConfig.disableDoneButton = true;
            } else {
                // check file type
                let isValid: boolean = false;

                const acceptedTypes: string[] = ["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/msword",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                    "application/vnd.openxmlformats-officedocument.presentationml.slideshow", "application/vnd.msword", "application/vnd.ms-excel",
                    "application/vnd.ms-powerpoint", "image/bmp", "image/jpeg", "image/pjpeg", "image/png", "image/gif", "application/zip",
                    "application/x-zip-compressed"];

                acceptedTypes.forEach(t => {
                    if (e.target.files[0].type === t) {
                        isValid = true;
                    }
                });

                if (isValid) {
                    this.file = e.target.files[0];
                    this.form.controls['name'].setValue(this.file.name.substring(0,
                        this.file.name.lastIndexOf('.')).replace(/\s/g, ' '));
                    this.document.docName = this.f.name.value;

                    if (this.form.valid && this.f.fileType.value)
                        this.modalConfig.disableDoneButton = false;
                } else {
                    this.file = null;
                    this.modalConfig.disableDoneButton = true;
                    this.messageService.error('Invalid file type.');
                }
            }
        } catch (e) {
            this.file = null;
            this.messageService.error('Something went wrong.');
            console.log(e);
        }
    }

    docType: DocType;
    onSelectFileType(): void {
        const fileTypeId = +this.f.fileType.value;
        this.fields = [];

        if (!fileTypeId || fileTypeId < 0) {
            this.document.activityIds = [];
            if (this.form.valid)
                this.modalConfig.disableDoneButton = false;
            return;
        } else
            this.bindActivities();

        this.docType = this.fileTypes.find(ft => ft.id === fileTypeId);
        if (this.docType.fields) {
            this.docType.fields.forEach(field => {
                let validators = [];
                if (field.isRequired && field.fieldType !== ColDataType.Checkbox)
                    validators.push(Validators.required);

                if (field.fieldType === ColDataType.Percentage) {
                    validators.push(Validators.min(-100));
                    validators.push(Validators.max(100));
                } else if (field.fieldType === ColDataType.GPS) {
                    validators.push(Validators.min(29.00000001));
                    validators.push(Validators.max(74.99999999));
                } else if (field.fieldType >= ColDataType.SelectSingle && field.fieldTypeValues) {
                    if (!Array.isArray(field.fieldTypeValues)) {
                        field.fieldTypeValues = field.fieldTypeValues.split(',');
                        let fieldVals = [];

                        // make array of dropdown items from csv string
                        for (let i = 0; i < field.fieldTypeValues.length; i++) {
                            fieldVals.push({
                                id: field.fieldTypeValues[i],
                                name: field.fieldTypeValues[i]
                            });
                        }

                        field.fieldTypeValues = fieldVals;
                    }
                }

                let fieldValue: any = this.getFieldValue(this.document.fieldsWithValues, field.id);
                if (fieldValue) {
                    if (field.fieldType === ColDataType.Checkbox)
                        fieldValue = fieldValue == 'true' || fieldValue == 'True';
                    else if (field.fieldType >= ColDataType.SelectSingle) {
                        fieldValue = fieldValue.split(',');
                    }
                }
                this.form.addControl('field' + field.id, new FormControl(fieldValue || null, validators));
            });

            this.fields = [...this.docType.fields];
        }
    }

    getFieldValue(docValues: FieldValue[], fId: number): string {
        if (!docValues?.length)
            return '';

        return docValues.find(fv => fv.fieldId === fId)?.value;
    }

    onMultiSelect(ctrl: FilterDropdownList): void {
        this.form.controls[ctrl.id].setValue(ctrl.selectedValues || ctrl['selVals']);
    }

    actIdRequired: boolean = false;
    async save(): Promise<boolean> {
        const result = new Subject<boolean>();
        this.actIdRequired = false;

        try {
            this.modalConfig.working = true;

            let doc = new Doc(this.document.id, this.f.name.value, this.file?.name,
                null, [], this.document.targetId, this.document.dynamicColumnId);

            doc.organizationId = this.document.organizationId;     // current user org id

            doc.docTypeId = +this.f.fileType.value;
            if (doc.docTypeId === -1) {
                doc.docDesc = this.f.desc.value;
                doc.docTypeId = null;
                doc.activityIds = [];
                doc.fieldsWithValues = [];

                if (this.f.shared.value)
                    doc.organizationId = null;
                else
                    doc.organizationId = 1;

                doc.fileName = this.f.fName.value;
                doc.isLink = this.document.isLink;
            } else {
                if (!doc.targetId && !doc.dynamicColumnId) {
                    doc.activityIds = this.f.actIds.value || [];

                    if (this.docType.isActRequired && !doc.activityIds.length) {
                        this.actIdRequired = true;
                        this.modalConfig.working = false;
                        return;
                    }
                }

                doc.fieldsWithValues = [];
                this.fields.forEach(field => {
                    let val = this.form.controls['field' + field.id].value;
                    
                    if (field.fieldType >= ColDataType.SelectSingle) {
                        if (!Array.isArray(val))
                            val = [val];
                        val = val.join(',');
                    } else val = '' + val;
                    doc.fieldsWithValues.push(new FieldValue(field.id, val));
                });
            }

            //console.log(doc);
            // upload and add new file, or create link
            if (this.document.id === 0) {
                if (doc.isLink) {
                    this.saveDocumentLink(doc);
                    return;
                }

                this.uploadFile = this.uploadService.uploadFile('documents', this.file, doc);
                this.uploadFile.uploadStatus.subscribe(returnDocId => { }, error => {
                    this.messageService.error('Error uploading the file.');
                    this.modalConfig.working = false;
                    this.file = null;

                    result.next(false);
                    result.complete();

                    console.log(error);
                }, () => { // on upload complete
                    this.done.emit(doc);
                    this.messageService.success('The file has been uploaded successfully.', 'Uploaded');
                    this.file = null;

                    this.modalConfig.working = false;
                    this.modalComponent.cancel();
                    result.next(true);
                    result.complete();
                });
            } else {
                this.subscriptions.push(this.docService.updateDocument(doc).subscribe({
                    error: (err) => {
                        this.modalConfig.working = false;
                        console.log(err);
                        result.next(false);
                        result.complete();
                    },
                    complete: () => {
                        if (doc.activityIds.length)
                            doc.activityIds = this.activities.filter(a => doc.activityIds.includes(a.fieldId))
                                .map(a => a.value);

                        this.done.emit(doc);
                        this.messageService.success('The file information have been updated successfully.', 'Saved');

                        this.modalConfig.working = false;
                        this.modalComponent.cancel();
                        result.next(true);
                        result.complete();
                    }
                }));
            }
        } catch (e) {
            this.modalConfig.working = false;
            this.messageService.error('Something went wrong.');
            console.log(e);
            result.next(false);
            result.complete();
        }

        return await lastValueFrom(result.asObservable());
    }

    private async saveDocumentLink(doc: Doc): Promise<boolean> {
        const result = new Subject<boolean>();

        this.subscriptions.push(this.docService.addLinkedDocument(doc).subscribe({
            error: (err) => {
                this.modalConfig.working = false;
                console.log(err);
                result.next(false);
                result.complete();
            },
            complete: () => {
                this.done.emit(doc);
                this.messageService.success('The document link has been created successfully.', 'Saved');

                this.modalConfig.working = false;
                this.modalComponent.cancel();
                result.next(true);
                result.complete();
            }
        }));

        return await lastValueFrom(result.asObservable());
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}