<!-- Root container with the reports-container class -->
<div class="reports-container">
  <!-- Dashboard Header -->
  <div class="card mb-5">
    <div class="card-body py-3">
      <div class="d-flex flex-column flex-md-row align-items-md-center justify-content-md-between">
        <div class="d-flex align-items-center mb-3 mb-md-0">
          <h1 class="fw-bold text-dark mb-0 me-3">UNDP Afghanistan Reports Dashboard</h1>
          <div class="d-flex align-items-center gap-2">
            <div class="badge badge-light-success fs-7">Live Data</div>
            <!-- Quarterly Period Display -->
            <div class="quarterly-display" *ngIf="selectedQuarter && selectedYear">
              <div class="badge badge-light-primary fs-7 px-3 py-2">
                <i class="bi bi-calendar-range me-1"></i>
                {{ selectedQuarter }} {{ selectedYear }}
                <span *ngIf="enableQuarterComparison" class="ms-1">
                  <i class="bi bi-bar-chart text-warning"></i>
                </span>
              </div>
            </div>
          </div>
        </div>
        <div class="d-flex align-items-center flex-wrap">
          <!-- Quick Quarter Navigation -->
          <div class="quarter-navigation me-3 mb-2 mb-md-0" *ngIf="selectedQuarter">
            <div class="btn-group btn-group-sm" role="group">
              <button class="btn btn-outline-primary" (click)="navigateQuarter('prev')" [disabled]="!canNavigateToPrevQuarter()">
                <i class="bi bi-chevron-left"></i>
              </button>
              <button class="btn btn-outline-primary quarter-display-btn" disabled>
                {{ selectedQuarter }} {{ selectedYear }}
              </button>
              <button class="btn btn-outline-primary" (click)="navigateQuarter('next')" [disabled]="!canNavigateToNextQuarter()">
                <i class="bi bi-chevron-right"></i>
              </button>
            </div>
          </div>
          
          <!-- Date Range Picker (for custom periods) -->
          <div class="me-3 mb-2 mb-md-0" *ngIf="!selectedQuarter">
            <input type="date" class="form-control form-control-sm" [(ngModel)]="startDate" (change)="onDateRangeChange()" placeholder="Start Date" style="width: 150px;">
          </div>
          <div class="me-3 mb-2 mb-md-0" *ngIf="!selectedQuarter">
            <input type="date" class="form-control form-control-sm" [(ngModel)]="endDate" (change)="onDateRangeChange()" placeholder="End Date" style="width: 150px;">
          </div>
          
          <button class="btn btn-sm btn-light-primary me-3 mb-2 mb-md-0" (click)="toggleFilters()">
            <i class="bi bi-funnel-fill me-1"></i>
            Quarterly Filters
            <span class="badge badge-circle badge-light-danger ms-1" *ngIf="getActiveFiltersCount() > 0">{{ getActiveFiltersCount() }}</span>
          </button>
          <button class="btn btn-sm btn-light-danger me-3 mb-2 mb-md-0" (click)="forceRefresh()">
            <i class="bi bi-arrow-clockwise me-1"></i>
            Refresh Data
          </button>
          <div class="dropdown mb-2 mb-md-0">
            <button class="btn btn-sm btn-light-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
              <i class="bi bi-download me-1"></i> Export
            </button>
            <ul class="dropdown-menu">
              <li><a class="dropdown-item" (click)="exportAsPdf()"><i class="bi bi-file-pdf me-2"></i>Export as PDF</a></li>
              <li><a class="dropdown-item" (click)="exportAsExcel()"><i class="bi bi-file-excel me-2"></i>Export as Excel</a></li>
              <li><a class="dropdown-item" (click)="exportAsJson()"><i class="bi bi-filetype-json me-2"></i>Export as JSON</a></li>
              <li><a class="dropdown-item" (click)="exportQuarterlyReport()"><i class="bi bi-calendar-range me-2"></i>Export Quarterly Report</a></li>
              <li><a class="dropdown-item" (click)="scheduleReport()"><i class="bi bi-calendar-event me-2"></i>Schedule Report</a></li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Project Group and Project Selection Dropdowns -->
  <div class="card mb-5">
    <div class="card-body py-3">
      <div class="row">
        <div class="col-md-6 mb-3 mb-md-0">
          <select class="form-select" (change)="selectProjectGroupById($any($event.target).value)">
            <option value="">All Project Groups</option>
            <option *ngFor="let group of projectGroups" [value]="group.id" [selected]="selectedProjectGroup?.id === group.id">
              {{ group.name }}
            </option>
          </select>
        </div>
        
        <div class="col-md-6">
          <select class="form-select" (change)="selectProjectById($any($event.target).value)" [disabled]="!hasFilteredProjects()">
            <option value="">All Projects</option>
            <option *ngFor="let project of getFilteredProjects()" [value]="project.id" [selected]="selectedProject?.id === project.id">
              {{ project.name }}
            </option>
          </select>
        </div>
      </div>
      
      <!-- Applied Filter Pills -->
      <div class="d-flex flex-wrap mt-4" *ngIf="selectedProjectGroup || selectedProject">
        <div class="badge badge-light-primary py-2 px-3 fs-7 d-flex align-items-center me-3 mb-2" *ngIf="selectedProjectGroup">
          <span>Project Group: {{ selectedProjectGroup.name }}</span>
          <i class="bi bi-x-circle ms-2 cursor-pointer" (click)="clearProjectGroupFilter()"></i>
        </div>
        <div class="badge badge-light-info py-2 px-3 fs-7 d-flex align-items-center me-3 mb-2" *ngIf="selectedProject">
          <span>Project: {{ selectedProject.name }}</span>
          <i class="bi bi-x-circle ms-2 cursor-pointer" (click)="clearProjectFilter()"></i>
        </div>
      </div>
    </div>
  </div>

  <!-- Filters Section (toggleable) -->
  <div class="card mb-5" *ngIf="filterVisible">
    <div class="card-header border-0">
      <h3 class="card-title align-items-start flex-column">
        <span class="card-label fw-bold fs-3 mb-1">Dashboard Filters</span>
        <span class="text-muted fw-semibold fs-7">Refine the dashboard data</span>
      </h3>
      <div class="card-toolbar">
        <button class="btn btn-sm btn-icon btn-active-color-primary" (click)="toggleFilters()">
          <i class="bi bi-x fs-2"></i>
        </button>
      </div>
    </div>
    <div class="card-body">
      <!-- Enhanced Quarterly Filter Section -->
      <div class="quarterly-filter-section mb-5">
        <h5 class="fw-bold text-dark mb-3">
          <i class="bi bi-calendar-range text-primary me-2"></i>Quarterly Reporting Period
        </h5>
        
        <!-- Year Selector -->
        <div class="row mb-4">
          <div class="col-md-3">
            <label class="form-label fw-semibold">Year</label>
            <select class="form-select" [(ngModel)]="selectedYear" (change)="onYearChange()">
              <option *ngFor="let year of availableYears" [value]="year" [selected]="year === selectedYear">
                {{ year }}
              </option>
            </select>
          </div>
          <div class="col-md-9">
            <label class="form-label fw-semibold">Quarter Selection</label>
            <!-- Quarter Cards -->
            <div class="quarter-selector d-flex gap-3">
              <div class="quarter-card" 
                   [class.active]="selectedQuarter === 'Q1'" 
                   (click)="selectQuarter('Q1')">
                <div class="quarter-icon">
                  <i class="bi bi-calendar-month text-success"></i>
                </div>
                <div class="quarter-label">Q1</div>
                <div class="quarter-months">Jan - Mar</div>
                <div class="quarter-stats" *ngIf="quarterlyStats.Q1">
                  <small class="text-muted">{{ quarterlyStats.Q1.activities || 0 }} activities</small>
                </div>
              </div>
              
              <div class="quarter-card" 
                   [class.active]="selectedQuarter === 'Q2'" 
                   (click)="selectQuarter('Q2')">
                <div class="quarter-icon">
                  <i class="bi bi-calendar-month text-info"></i>
                </div>
                <div class="quarter-label">Q2</div>
                <div class="quarter-months">Apr - Jun</div>
                <div class="quarter-stats" *ngIf="quarterlyStats.Q2">
                  <small class="text-muted">{{ quarterlyStats.Q2.activities || 0 }} activities</small>
                </div>
              </div>
              
              <div class="quarter-card" 
                   [class.active]="selectedQuarter === 'Q3'" 
                   (click)="selectQuarter('Q3')">
                <div class="quarter-icon">
                  <i class="bi bi-calendar-month text-warning"></i>
                </div>
                <div class="quarter-label">Q3</div>
                <div class="quarter-months">Jul - Sep</div>
                <div class="quarter-stats" *ngIf="quarterlyStats.Q3">
                  <small class="text-muted">{{ quarterlyStats.Q3.activities || 0 }} activities</small>
                </div>
              </div>
              
              <div class="quarter-card" 
                   [class.active]="selectedQuarter === 'Q4'" 
                   (click)="selectQuarter('Q4')">
                <div class="quarter-icon">
                  <i class="bi bi-calendar-month text-danger"></i>
                </div>
                <div class="quarter-label">Q4</div>
                <div class="quarter-months">Oct - Dec</div>
                <div class="quarter-stats" *ngIf="quarterlyStats.Q4">
                  <small class="text-muted">{{ quarterlyStats.Q4.activities || 0 }} activities</small>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Quarter Comparison Option -->
        <div class="quarter-comparison mb-4">
          <div class="d-flex align-items-center justify-content-between">
            <div>
              <label class="form-check-label fw-semibold">
                <input class="form-check-input me-2" type="checkbox" [(ngModel)]="enableQuarterComparison" (change)="onQuarterComparisonToggle()">
                Enable Quarter Comparison
              </label>
              <div class="form-text">Compare selected quarter with previous periods</div>
            </div>
            <div *ngIf="enableQuarterComparison" class="comparison-quarters">
              <label class="form-label fw-semibold me-3">Compare with:</label>
              <div class="btn-group" role="group">
                <input type="checkbox" class="btn-check" id="comparePrevQuarter" [(ngModel)]="compareWithPreviousQuarter">
                <label class="btn btn-outline-primary btn-sm" for="comparePrevQuarter">Previous Quarter</label>
                
                <input type="checkbox" class="btn-check" id="compareSameQuarterLastYear" [(ngModel)]="compareWithSameQuarterLastYear">
                <label class="btn btn-outline-primary btn-sm" for="compareSameQuarterLastYear">Same Quarter Last Year</label>
              </div>
            </div>
          </div>
        </div>

        <!-- Quick Period Shortcuts -->
        <div class="quick-periods">
          <h6 class="fw-semibold text-gray-700 mb-2">Quick Shortcuts:</h6>
          <div class="d-flex flex-wrap gap-2">
            <button class="btn btn-sm btn-light-primary" (click)="applyQuickPeriod('currentQuarter')">
              <i class="bi bi-calendar-check me-1"></i>Current Quarter
            </button>
            <button class="btn btn-sm btn-light-info" (click)="applyQuickPeriod('lastQuarter')">
              <i class="bi bi-calendar-minus me-1"></i>Last Quarter
            </button>
            <button class="btn btn-sm btn-light-success" (click)="applyQuickPeriod('ytd')">
              <i class="bi bi-calendar-year me-1"></i>Year to Date
            </button>
            <button class="btn btn-sm btn-light-warning" (click)="applyQuickPeriod('lastYear')">
              <i class="bi bi-calendar-x me-1"></i>Last Year
            </button>
            <button class="btn btn-sm btn-light-secondary" (click)="applyQuickPeriod('allTime')">
              <i class="bi bi-infinity me-1"></i>All Time
            </button>
          </div>
        </div>
      </div>

      <!-- Divider -->
      <hr class="my-4">

      <!-- Additional Filters -->
      <div class="additional-filters">
        <h6 class="fw-semibold text-gray-700 mb-3">Additional Filters:</h6>
      <app-report-filter (filterChange)="onFilterChange($event)" [showProjectFilters]="true"></app-report-filter>
      </div>
      
      <!-- Filter Buttons -->
      <div class="d-flex justify-content-between align-items-center gap-2 mt-5">
        <div class="filter-summary">
          <span class="badge badge-light-primary me-2" *ngIf="getActiveFiltersCount() > 0">
            {{ getActiveFiltersCount() }} filter(s) active
          </span>
          <span class="text-muted fs-7" *ngIf="selectedQuarter && selectedYear">
            Viewing {{ selectedQuarter }} {{ selectedYear }}
            <span *ngIf="enableQuarterComparison"> with comparison</span>
          </span>
        </div>
        <div class="d-flex gap-2">
          <button class="btn btn-light" (click)="resetAllFilters()">
            <i class="bi bi-arrow-counterclockwise me-1"></i>Reset All
        </button>
          <button class="btn btn-primary" (click)="applyQuarterlyFilters()">
            <i class="bi bi-funnel me-1"></i>Apply Quarterly Filters
        </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Loading Spinner - Only shown when *no* data is available yet -->
  <div *ngIf="loading && !filteredData" class="d-flex justify-content-center my-10">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
  </div>

  <!-- Error Message -->
  <div *ngIf="error" class="alert alert-danger">
    {{ error }}
  </div>

  <!-- Main Dashboard Content - Now shown even during loading if data is available -->
  <div *ngIf="filteredData">
    <!-- Project/Project Group Filter Banner -->
    <div class="alert alert-light-primary mb-5" *ngIf="selectedProjectGroup || selectedProject">
      <div class="d-flex align-items-center">
        <i class="bi bi-info-circle-fill text-primary fs-2 me-3"></i>
        <div>
          <h4 class="mb-1 text-dark">Filtered View</h4>
          <p class="mb-0 text-gray-700">
            <span *ngIf="selectedProjectGroup">Showing data for project group: <strong>{{ selectedProjectGroup.name }}</strong></span>
            <span *ngIf="selectedProject">{{ selectedProjectGroup ? ' and project: ' : 'Showing data for project: ' }}<strong>{{ selectedProject.name }}</strong></span>
          </p>
        </div>
      </div>
    </div>

    <!-- Tab Navigation using Angular Material -->
    <mat-tab-group [selectedIndex]="selectedTabIndex" (selectedIndexChange)="onTabChange($event)" animationDuration="0ms" class="reports-tab-group">
      <mat-tab label="Categories">
        <div class="mat-tab-content py-4">
          <!-- Quarterly Filter Status Banner -->
          <div class="alert alert-light-info mb-4" *ngIf="selectedQuarter && selectedYear">
            <div class="d-flex align-items-center">
              <i class="bi bi-calendar-range text-info fs-2 me-3"></i>
              <div>
                <h6 class="mb-1 text-dark">Quarterly Filter Active</h6>
                <p class="mb-0 text-muted">
                  Showing data for <strong>{{ selectedQuarter }} {{ selectedYear }}</strong>
                  <span *ngIf="filteredData?.categories">({{ filteredData.categories.length }} categories)</span>
                </p>
              </div>
              <button class="btn btn-sm btn-light-secondary ms-auto" (click)="resetAllFilters()">
                <i class="bi bi-x-circle me-1"></i>Clear Filter
              </button>
            </div>
          </div>
          
          <!-- Loading spinner inside tab -->
          <div *ngIf="loading" class="d-flex justify-content-center my-5">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">Loading categories data...</span>
            </div>
          </div>
          <app-category-dashboard 
            [fullReport]="fullReportData" 
            [filteredData]="filteredData"
            [filters]="currentFilters"
            (filterChange)="onCategoryFilterChange($event)">
          </app-category-dashboard>
        </div>
      </mat-tab>
    </mat-tab-group>
  </div>
</div>
