import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable, of, map, catchError, tap, forkJoin, switchMap, throwError } from 'rxjs';
import { retry, timeout, delay } from 'rxjs/operators';
import { 
  ReportData, 
  Output, 
  Category, 
  Intervention, 
  Activity, 
  ReportFilter, 
  SummaryStats, 
  TimeSeriesData,
  TimePeriodType,
  Quarter,
  ProjectGroup,
  Project,
  ProcessedDynamicData
} from '../models/reports.model';
import { environment } from '../../../../../environments/environment';
import { SharedService } from '../../../../shared/services/shared.service';

@Injectable({
  providedIn: 'root'
})
export class ReportsService {
  private apiUrl = environment.apiUrl;

  constructor(
    private http: HttpClient,
    public sharedService: SharedService // Make public so it can be accessed from components
  ) { 
    // Log the API URL to help with debugging
    console.log('ReportsService initialized with API URL:', this.apiUrl);
  }

  /**
   * Get the complete report data based on filters
   */
  getReportData(filters?: ReportFilter): Observable<ReportData> {
    const url = `${this.apiUrl}/dashboard/reports`;
    console.log('🔍 API call to:', url);
    return this.http.post<ReportData>(url, filters || {}).pipe(
      catchError((error) => {
        console.error('❌ API error for', url, error);
        return of(this.getMockReportData(filters));
      })
    );
  }

  /**
   * Get project groups from dedicated API endpoint
   */
  getProjectGroups(filters?: ReportFilter): Observable<string[]> {
    // Use the dedicated project groupings endpoint for better performance
    const groupingsEndpoint = `${this.apiUrl}/projects/groupings`;
    
    console.log('🔍 Making API call to project groupings endpoint:', groupingsEndpoint);
    
    return this.http.get<string[]>(groupingsEndpoint).pipe(
      tap(response => {
        console.log('✅ Project groupings API success! Received groups:', response);
      }),
      map(groups => {
        // Filter out null/empty groups and ensure uniqueness
        const validGroups = groups.filter(group => group && group.trim() !== '');
        const uniqueGroups = Array.from(new Set(validGroups));
        console.log('✅ Processed project groups:', uniqueGroups);
        return uniqueGroups;
      }),
      catchError(error => {
        console.error('❌ Project groupings API error:', error);
        console.log('⚠️ Falling back to extracting groups from hierarchical report');
        
        // Fallback to the hierarchical endpoint instead of full dashboard report
        return this.getHierarchicalReport().pipe(
          map(hierarchicalData => {
            const groups = (hierarchicalData || [])
              .map((group: any) => group.name)
              .filter((g: any) => !!g) as string[];
            const uniqueGroups = Array.from(new Set(groups));
            console.log('✅ Fallback: Extracted project groups from hierarchical report:', uniqueGroups);
            return uniqueGroups;
          }),
          catchError(fallbackError => {
            console.error('❌ Hierarchical fallback also failed:', fallbackError);
            // Return some default groups if everything fails
            return of(['Economic Development', 'Health', 'Education', 'Infrastructure'] as string[]);
          })
        );
      })
    );
  }

  /**
   * Get projects from API with optional filtering by project group
   */
  getProjects(filters?: ReportFilter): Observable<Project[]> {
    // Use the projects API endpoint for better performance
    const projectsEndpoint = `${this.apiUrl}/projects/filtered`;
    
    console.log('🔍 Making API call to projects endpoint:', projectsEndpoint);
    console.log('🔍 Projects request filters:', filters);
    
    return this.http.get<Project[]>(projectsEndpoint).pipe(
      tap(response => {
        console.log('✅ Projects API success! Received projects:', response?.length || 0);
        if (response && response.length > 0) {
          console.log('✅ Sample project structure:', response[0]);
          console.log('✅ Project groupings found:', [...new Set(response.map(p => p.grouping || p.groupId).filter(g => g))]);
        }
      }),
      map(projects => {
        let filteredProjects = projects || [];
        
        // Apply project group filter if specified
        if (filters?.projectGroup) {
          console.log('🔍 Filtering projects by project group:', filters.projectGroup);
          filteredProjects = filteredProjects.filter((p: any) => 
            p.grouping === filters.projectGroup || p.groupId === filters.projectGroup
          );
          console.log('✅ Filtered projects count:', filteredProjects.length);
        }
        
        // Ensure projects have the correct structure for the frontend
        const processedProjects = filteredProjects.map((project: any) => ({
          id: project.id,
          code: project.abbreviation || project.code,
          name: project.name,
          groupId: project.grouping || project.groupId, // Support both possible field names
          grouping: project.grouping || project.groupId,
          description: project.description || '',
          status: project.status || 'active',
          startDate: project.startDate,
          endDate: project.endDate,
          budget: project.budget || 0,
          cashDistributed: project.cashDistributed || 0,
          beneficiaries: project.beneficiaries || 0,
          progress: project.progress || 0,
          outputs: project.outputs || [],
          location: project.location
        }));
        
        console.log('✅ Processed projects for frontend:', processedProjects.length);
        return processedProjects;
      }),
      catchError(error => {
        console.error('❌ Projects API error:', error);
        console.log('⚠️ Falling back to extracting projects from hierarchical report');
        
        // Fallback to the hierarchical endpoint instead of full dashboard report
        return this.getHierarchicalReport().pipe(
          map(hierarchicalData => {
            let projects: any[] = [];
            
            // Extract projects from hierarchical structure
            (hierarchicalData || []).forEach((group: any) => {
              if (group.projects && Array.isArray(group.projects)) {
                group.projects.forEach((project: any) => {
                  projects.push({
                    id: project.id,
                    code: project.code,
                    name: project.name,
                    groupId: group.name,
                    grouping: group.name,
                    description: project.description || '',
                    status: project.status || 'active',
                    startDate: project.startDate,
                    endDate: project.endDate,
                    budget: project.budget || 0,
                    cashDistributed: project.cashDistributed || 0,
                    beneficiaries: project.beneficiaries || 0,
                    progress: project.progress || 0,
                    outputs: project.outputs || [],
                    location: project.location
                  });
                });
              }
            });
            
            // Apply project group filter if specified
        if (filters?.projectGroup) {
          projects = projects.filter((p: any) => p.grouping === filters.projectGroup);
        }
            
            console.log('✅ Fallback: Extracted projects from hierarchical report:', projects.length);
        return projects;
          }),
          catchError(fallbackError => {
            console.error('❌ Projects hierarchical fallback also failed:', fallbackError);
            // Return empty array if everything fails
            return of([]);
          })
        );
      })
    );
  }

  /**
   * Process the filter for API compatibility
   * @param filter The filter to process
   * @returns A processed filter object ready for the API
   */
  private processFilter(filter: ReportFilter): any {
    const processedFilter = { ...filter };
    
    // Helper function to filter out empty values from arrays
    const filterEmptyValues = (arr: any[]): any[] => {
      return arr?.filter(value => 
        value !== null && 
        value !== undefined && 
        value !== '' && 
        value.toString().trim() !== ''
      ) || [];
    };
    
    // Map interventions to profIds if only interventions is present
    if (processedFilter.interventions && !processedFilter.profIds) {
      processedFilter.profIds = filterEmptyValues(processedFilter.interventions);
    }
    
    // Map categories to catIds if needed for backend API
    if (processedFilter.categories && !processedFilter.catIds) {
      processedFilter.catIds = filterEmptyValues(processedFilter.categories);
    }
    
    // Filter out empty values from all array properties
    if (processedFilter.catIds) {
      processedFilter.catIds = filterEmptyValues(processedFilter.catIds);
      // Remove catIds entirely if it's empty after filtering
      if (processedFilter.catIds.length === 0) {
        delete processedFilter.catIds;
      }
    }
    
    if (processedFilter.profIds) {
      processedFilter.profIds = filterEmptyValues(processedFilter.profIds);
      if (processedFilter.profIds.length === 0) {
        delete processedFilter.profIds;
      }
    }
    
    if (processedFilter.projects) {
      processedFilter.projects = filterEmptyValues(processedFilter.projects);
      if (processedFilter.projects.length === 0) {
        delete processedFilter.projects;
      }
    }
    
    if (processedFilter.projectGroups) {
      processedFilter.projectGroups = filterEmptyValues(processedFilter.projectGroups);
      if (processedFilter.projectGroups.length === 0) {
        delete processedFilter.projectGroups;
      }
    }
    
    // Map activity status from string values to integer values for backend
    // Frontend: 'ongoing', 'completed', 'archived', 'cancelled'
    // Backend:  0,         1,           2,          3
    if (processedFilter.activityStatus && processedFilter.activityStatus.length > 0) {
      const statusMapping: { [key: string]: number } = {
        'ongoing': 0,
        'completed': 1,
        'archived': 2,
        'cancelled': 3
      };
      
      (processedFilter as any).ActivityStatus = filterEmptyValues(processedFilter.activityStatus)
        .map(status => statusMapping[status])
        .filter(status => status !== undefined); // Remove any invalid status values
      
      console.log('Mapped activity status from frontend to backend:', 
                  processedFilter.activityStatus, '->', (processedFilter as any).ActivityStatus);
                  
      // IMPORTANT: Remove the original string property to avoid duplicate parameters
      delete processedFilter.activityStatus;
    }
    
    console.log('🔍 ReportsService: Processed filters with empty value filtering:', processedFilter);
    
    return processedFilter;
  }
  
  /**
   * Get full dashboard report with all data
   */
  getFullDashboardReport(filters?: ReportFilter): Observable<any> {
    // Process the filters for API compatibility
    const apiFilters = this.processFilter(filters || {});
    
    console.log('Sending dashboard API request with processed filters:', apiFilters);
    
    // Use the API URL from the environment with the correct endpoint path
    const fullEndpoint = `${this.apiUrl}/dashboard/reports/full`;
    console.log('🔍 Making API call to:', fullEndpoint);
    
    // Convert filters to query parameters if using GET
    let params = new HttpParams();
    if (apiFilters) {
      // Add each filter as a query param
      Object.entries(apiFilters).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          if (Array.isArray(value)) {
            // Handle arrays by adding multiple entries with the same key
            value.forEach(item => {
              params = params.append(key, item);
            });
          } else if (typeof value === 'object') {
            // Handle objects by serializing them
            params = params.append(key, JSON.stringify(value));
          } else {
            // Handle primitive values
            params = params.append(key, value.toString());
          }
        }
      });
    }
    
    // Use GET with query parameters, with better error handling
    return this.http.get<any>(fullEndpoint, { params }).pipe(
      tap(response => {
        console.log('✅ API response received for full dashboard report, data:', response);
      }),
      catchError(error => {
        console.error('❌ API error for full dashboard report:', error);
        console.log('Using mock data with filters:', apiFilters);
        return of(this.getMockDashboardData(apiFilters));
      })
    );
  }
  
  /**
   * Get mock dashboard data with filtering
   */
  private getMockDashboardData(filters?: any): any {
    console.log('Generating mock dashboard data with filters:', filters);
    
    // Base mock data
    const mockData = {
      outputs: this.getMockOutputs(),
      categories: this.getMockCategories(),
      interventionProfiles: this.getMockInterventionProfiles(),
      activities: this.getMockActivities(),
      projects: this.getMockProjects(),
      totalBudget: 25000000,
      totalCashDistributed: 15000000,
      totalBeneficiaries: 125000,
      overallProgress: 60
    };
    
    // Apply filtering similar to the data/view component
    if (filters) {
      // Step 1: First filter by project groups and projects if applicable
      let filteredProjects = [...mockData.projects];
      
      if (filters.projectGroups && filters.projectGroups.length > 0) {
        filteredProjects = filteredProjects.filter(project => 
          filters.projectGroups.includes(project.groupId)
        );
        console.log('Filtered projects by groups:', filteredProjects.length);
      }
      
      if (filters.projects && filters.projects.length > 0) {
        filteredProjects = filteredProjects.filter(project => 
          filters.projects.includes(project.id)
        );
        console.log('Filtered projects by project IDs:', filteredProjects.length);
      }
      
      // Step 2: Filter categories based on filtered projects
      // This would require a mapping of categories to projects
      // For simplicity in this mock, we'll skip this unless there's a clear project-category mapping
      
      // Step 3: Filter by categories (catIds) if specified
      if (filters.catIds && filters.catIds.length > 0) {
        mockData.categories = mockData.categories.filter(
          category => filters.catIds.includes(category.id)
        );
        
        // Filter interventions to match filtered categories
        mockData.interventionProfiles = mockData.interventionProfiles.filter(
          profile => mockData.categories.some(c => c.id === profile.categoryId)
        );
        
        console.log('Filtered interventions by categories:', mockData.interventionProfiles.length);
      }
      
      // Step 4: Filter by profIds (interventions) if specified
      if (filters.profIds && filters.profIds.length > 0) {
        // For profIds, we directly filter the intervention profiles
        mockData.interventionProfiles = mockData.interventionProfiles.filter(
          profile => filters.profIds.includes(profile.id)
        );
        
        console.log('Filtered interventions by profIds:', mockData.interventionProfiles.length);
      }
      
      // Step 5: Filter activities based on filtered interventions
      const validProfileIds = mockData.interventionProfiles.map(p => p.id);
      mockData.activities = mockData.activities.filter(
        activity => validProfileIds.includes(activity.interventionProfileId)
      );
      
      console.log('Filtered activities:', mockData.activities.length);
    }
    
    return mockData;
  }

  // Helper method to get mock data using existing methods
  private getMockData(): Observable<any> {
    // Create a simple mock response using existing mock methods
    return of({
      outputs: this.getMockOutputs(),
      // Use existing mock data instead of calling non-existent methods
      projects: this.getMockProjects(),
      // Add any other required properties
      totalBudget: 25000000,
      totalCashDistributed: 15000000,
      totalBeneficiaries: 125000,
      overallProgress: 60
    });
  }

  // --- MOCK DATA METHODS BELOW ---
  private getMockReportData(filters?: ReportFilter): ReportData {
    const baseData = {
      outputs: this.getMockOutputs(),
      totalBudget: 37500000,
      totalCashDistributed: 18750000,
      totalBeneficiaries: 125780,
      overallProgress: 58,
      activitiesByStatus: {
        ongoing: 87,
        completed: 109,
        cancelled: 23
      },
      geographicCoverage: {
        provinces: 24,
        districts: 187
      }
    };
    const projects = this.getMockProjects();
    for (const output of baseData.outputs) {
      for (const category of output.categories) {
        const projectIds = projects
          .filter(p => p.outputs && p.outputs.includes(output.id))
          .map(p => p.id);
        category.projectIds = projectIds;
      }
    }
    if (filters?.timePeriodType) {
      baseData['timePeriod'] = {
        type: filters.timePeriodType,
        year: filters.year || new Date().getFullYear(),
        quarter: filters.quarter,
        month: filters.month
      };
      const adjustmentFactor = this.getTimePeriodAdjustmentFactor(filters);
      baseData.totalBudget = Math.round(baseData.totalBudget * adjustmentFactor);
      baseData.totalCashDistributed = Math.round(baseData.totalCashDistributed * adjustmentFactor);
      baseData.totalBeneficiaries = Math.round(baseData.totalBeneficiaries * adjustmentFactor);
      baseData.activitiesByStatus.ongoing = Math.round(baseData.activitiesByStatus.ongoing * adjustmentFactor);
      baseData.activitiesByStatus.completed = Math.round(baseData.activitiesByStatus.completed * adjustmentFactor);
      baseData.activitiesByStatus.cancelled = Math.round(baseData.activitiesByStatus.cancelled * adjustmentFactor);
    }
    return baseData as ReportData;
  }

  private getMockOutputs(): Output[] {
    // Return a minimal mock outputs array for demonstration
    return [
      {
        id: '1',
        code: '1',
        name: 'Output 1',
        totalBudget: 1000000,
        totalCashDistributed: 500000,
        totalBeneficiaries: 10000,
        progress: 50,
        categories: []
      }
    ];
  }

  private getMockProjects(filters?: ReportFilter): Project[] {
    const projects = [
      {
        id: 'p1',
        code: 'ECO-001',
        name: 'Market Access and Development',
        groupId: 'pg1',
        description: 'Improving market access for small producers and businesses',
        status: 'active' as const,
        startDate: '2023-01-15',
        endDate: '2024-12-31',
        budget: 3500000,
        cashDistributed: 2100000,
        beneficiaries: 15000,
        progress: 60,
        outputs: ['1', '2'],
        location: {
          provinceId: 1,
          provinceName: 'Kabul',
          districtId: 101,
          districtName: 'Kabul City'
        }
      },
      {
        id: 'p2',
        code: 'ECO-002',
        name: 'SME Support Program',
        groupId: 'pg1',
        description: 'Supporting small and medium enterprises through financing and capacity building',
        status: 'active' as const,
        startDate: '2023-03-01',
        endDate: '2025-02-28',
        budget: 4200000,
        cashDistributed: 2500000,
        beneficiaries: 12000,
        progress: 65,
        outputs: ['2'],
        location: {
          provinceId: 1,
          provinceName: 'Kabul',
          districtId: 101,
          districtName: 'Kabul City'
        }
      },
      {
        id: 'p3',
        code: 'ECO-003',
        name: 'Women Entrepreneurship',
        groupId: 'pg1',
        description: 'Supporting women entrepreneurs through grants and training',
        status: 'active' as const,
        startDate: '2023-05-15',
        endDate: '2024-11-30',
        budget: 2800000,
        cashDistributed: 1400000,
        beneficiaries: 8000,
        progress: 50,
        outputs: ['1', '4'],
        location: {
          provinceId: 3,
          provinceName: 'Herat',
          districtId: 301,
          districtName: 'Herat City'
        }
      },
      {
        id: 'p4',
        code: 'HEA-001',
        name: 'Primary Healthcare Strengthening',
        groupId: 'pg2',
        description: 'Improving primary healthcare services in rural areas',
        status: 'active' as const,
        startDate: '2023-02-01',
        endDate: '2025-01-31',
        budget: 4500000,
        cashDistributed: 2800000,
        beneficiaries: 25000,
        progress: 65,
        outputs: ['1.2'],
        location: {
          provinceId: 4,
          provinceName: 'Balkh',
          districtId: 401,
          districtName: 'Mazar-i-Sharif'
        }
      },
      {
        id: 'p5',
        code: 'HEA-002',
        name: 'Maternal and Child Health',
        groupId: 'pg2',
        description: 'Improving maternal and child health services and outcomes',
        status: 'active' as const,
        startDate: '2023-04-01',
        endDate: '2025-03-31',
        budget: 3800000,
        cashDistributed: 2100000,
        beneficiaries: 13000,
        progress: 55,
        outputs: ['1.2'],
        location: {
          provinceId: 5,
          provinceName: 'Kandahar',
          districtId: 501,
          districtName: 'Kandahar City'
        }
      },
      {
        id: 'p6',
        code: 'AGR-001',
        name: 'Sustainable Agriculture',
        groupId: 'pg3',
        description: 'Promoting sustainable agricultural practices and technologies',
        status: 'active' as const,
        startDate: '2023-03-15',
        endDate: '2025-03-14',
        budget: 3200000,
        cashDistributed: 1600000,
        beneficiaries: 12000,
        progress: 50,
        outputs: ['3'],
        location: {
          provinceId: 6,
          provinceName: 'Nangarhar',
          districtId: 601,
          districtName: 'Jalalabad'
        }
      }
    ];
    return projects;
  }

  private getTimePeriodAdjustmentFactor(filters?: ReportFilter): number {
    if (!filters?.timePeriodType) return 1;
    switch (filters.timePeriodType) {
      case TimePeriodType.Monthly:
        return 0.08;
      case TimePeriodType.Quarterly:
        return 0.25;
      case TimePeriodType.Yearly:
      default:
        return 1;
    }
  }

  private getMockCategories(): any[] {
    return [
      {
        id: 'cat1',
        code: '1.1',
        name: 'Infrastructure',
        description: 'Infrastructure development projects',
        totalBudget: 5000000,
        totalCashDistributed: 3000000,
        totalBeneficiaries: 50000,
        progress: 60
      },
      {
        id: 'cat2',
        code: '1.2',
        name: 'Health',
        description: 'Health services and facilities',
        totalBudget: 4000000,
        totalCashDistributed: 2500000,
        totalBeneficiaries: 40000,
        progress: 65
      }
    ];
  }

  private getMockInterventionProfiles(): any[] {
    return [
      {
        id: 'int1',
        name: 'Public Infrastructure',
        categoryId: 'cat1',
        description: 'Development of public infrastructure',
        totalBudget: 2500000,
        totalCashDistributed: 1500000,
        progress: 60,
        totalActivities: 3
      },
      {
        id: 'int2',
        name: 'Healthcare Facilities',
        categoryId: 'cat2',
        description: 'Development of healthcare facilities',
        totalBudget: 2000000,
        totalCashDistributed: 1200000,
        progress: 65,
        totalActivities: 2
      }
    ];
  }

  private getMockActivities(): any[] {
    return [
      {
        id: 'act1',
        name: 'Road Construction Project',
        interventionProfileId: 'int1',
        status: 'ongoing',
        budget: 800000,
        cashDistributed: 500000,
        progress: 60
      },
      {
        id: 'act2',
        name: 'Hospital Renovation',
        interventionProfileId: 'int2',
        status: 'ongoing',
        budget: 600000,
        cashDistributed: 400000,
        progress: 70
      }
    ];
  }

  /**
   * Get dynamic column data for specific activities
   * @param activityIds Array of activity IDs to fetch dynamic column data for
   * @returns Observable with dynamic column data
   */
  getDynamicColumnsForActivities(activityIds: string[] | number[]): Observable<any> {
    if (!activityIds || activityIds.length === 0) {
      console.warn('⚠️ No activity IDs provided for dynamic column data request');
      return of({});
    }
    
    console.log(`🔍 Fetching dynamic column data for ${activityIds.length} activities`);
    
    // If we have a large number of activities, chunk the requests to avoid URL length limits or server overload
    const CHUNK_SIZE = 50; // Adjust this value based on server capacity
    
    if (activityIds.length > CHUNK_SIZE) {
      console.log(`⚠️ Large activity set detected (${activityIds.length}), chunking into smaller requests`);
      
      // Split the activity IDs into chunks
      const chunks: (string | number)[][] = [];
      for (let i = 0; i < activityIds.length; i += CHUNK_SIZE) {
        chunks.push(activityIds.slice(i, i + CHUNK_SIZE));
      }
      
      console.log(`🔄 Split into ${chunks.length} chunks of approximately ${CHUNK_SIZE} activities each`);
      
      // Process each chunk and combine the results
      return forkJoin(
        chunks.map((chunk, index) => {
          console.log(`🔍 Processing chunk ${index + 1}/${chunks.length} with ${chunk.length} activities`);
          return this.fetchDynamicColumnsChunk(chunk).pipe(
            // Add a delay between chunks to avoid overwhelming the server
            delay(index * 300),
            catchError(error => {
              console.error(`❌ Error processing chunk ${index + 1}/${chunks.length}:`, error);
              // Return empty object for this chunk but don't fail the entire operation
              return of({});
            })
          );
        })
      ).pipe(
        map(chunkResults => {
          // Combine all chunk results into a single result object
          const combinedResult: Record<string, any> = {};
          
          chunkResults.forEach(chunkResult => {
            Object.entries(chunkResult).forEach(([key, value]) => {
              combinedResult[key] = value;
            });
          });
          
          console.log(`✅ Combined data from ${chunks.length} chunks`);
          return combinedResult;
        }),
        catchError(error => {
          console.error('❌ Error combining chunk results:', error);
          return of({});
        })
      );
    }
    
    // For smaller sets, make a single request
    return this.fetchDynamicColumnsChunk(activityIds);
  }
  
  /**
   * Helper method to fetch a chunk of dynamic column data
   * @param activityIds Chunk of activity IDs to fetch data for
   * @returns Observable with dynamic column data for the chunk
   */
  private fetchDynamicColumnsChunk(activityIds: (string | number)[]): Observable<any> {
    // Create the request params
    let params = new HttpParams();
    
    // Add activity IDs to request
    activityIds.forEach(id => {
      params = params.append('activityIds', id.toString());
    });
    
    // Try multiple endpoints in case one of them doesn't work
    // This is a fallback mechanism to handle different API routes
    const primaryEndpoint = `${this.apiUrl}/dashboard/activities/dynamic-columns`;
    const fallbackEndpoint = `${this.apiUrl}/dynamicdata/activities`;
    
    console.log(`🔍 Attempting to fetch dynamic columns data from primary endpoint: ${primaryEndpoint}`);
    
    // Try the primary endpoint first, then fall back to the secondary if needed
    return this.http.get<any>(primaryEndpoint, { 
      params,
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Accept': 'application/json'
      }
    }).pipe(
      retry(1), // Retry primary endpoint once
      timeout(15000), // 15 second timeout
      tap(response => {
        console.log(`✅ Primary endpoint success! Retrieved dynamic column data for ${activityIds.length} activities`);
        
        // Check response structure for debugging
        if (response) {
          const count = Array.isArray(response) ? response.length : 
            (typeof response === 'object' ? Object.keys(response).length : 0);
          console.log(`✅ Response contains data for ${count} activities or entries`);
        }
      }),
      catchError(error => {
        console.warn(`⚠️ Primary endpoint failed: ${error.message}. Trying fallback endpoint...`);
        
        // Try the fallback endpoint
        return this.http.get<any>(fallbackEndpoint, { 
          params,
          headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Accept': 'application/json'
          }
        }).pipe(
          retry(1), // Retry fallback endpoint once
          timeout(15000), // 15 second timeout
          tap(response => {
            console.log(`✅ Fallback endpoint success! Retrieved dynamic column data for ${activityIds.length} activities`);
          }),
          catchError(fallbackError => {
            console.error(`❌ Both endpoints failed for dynamic column data. Primary: ${error.message}, Fallback: ${fallbackError.message}`);
            console.log('⚠️ Returning empty result for this chunk');
            return of({});
          })
        );
      })
    );
  }
  
  /**
   * Enhance hierarchical data with dynamic column data
   * @param hierarchicalData The base hierarchical data without dynamic columns
   * @returns Observable with enhanced hierarchical data including dynamic columns
   */
  enhanceHierarchicalDataWithDynamicColumns(hierarchicalData: any[]): Observable<any[]> {
    if (!hierarchicalData || hierarchicalData.length === 0) {
      console.log('⚠️ No hierarchical data to enhance, returning as is');
      return of([]);
    }
    
    console.log('🔍 Enhancing hierarchical data with dynamic column data');
    
    // Extract all activity IDs from the hierarchical data
    const activityIds: number[] = [];
    
    // Traverse the hierarchical structure to collect all activity IDs
    try {
      hierarchicalData.forEach(group => {
        if (group.projects && Array.isArray(group.projects)) {
          group.projects.forEach(project => {
            if (project.interventions && Array.isArray(project.interventions)) {
              project.interventions.forEach(intervention => {
                if (intervention.activities && Array.isArray(intervention.activities)) {
                  intervention.activities.forEach(activity => {
                    if (activity.id) {
                      activityIds.push(activity.id);
                    }
                  });
                }
              });
            }
          });
        }
      });
      
      console.log(`🔍 Found ${activityIds.length} activities to enhance with dynamic column data`);
      
      // If we have too many activities, limit to a reasonable number to avoid performance issues
      const MAX_ACTIVITIES = 500; // Adjust based on performance testing
      
      if (activityIds.length > MAX_ACTIVITIES) {
        console.warn(`⚠️ Large number of activities (${activityIds.length}), limiting to ${MAX_ACTIVITIES} to avoid performance issues`);
        // Take a subset of activities, preferring those that are likely to have dynamic columns
        const limitedIds = activityIds.slice(0, MAX_ACTIVITIES);
        console.log(`🔍 Proceeding with ${limitedIds.length} activities`);
        
        // Fetch dynamic column data only for the limited set
        return this.getDynamicColumnsForActivities(limitedIds).pipe(
          timeout(60000), // 60 second timeout for large requests
          map(dynamicColumnsData => {
            // Apply the data we were able to get
            return this.applyDynamicColumnsToHierarchicalData(hierarchicalData, this.createDynamicColumnsMap(dynamicColumnsData));
          }),
          catchError(error => {
            console.error('❌ Error enhancing hierarchical data (limited set):', error);
            // Return the original data if we couldn't enhance it
            return of(hierarchicalData);
          })
        );
      }
      
      // If no activity IDs found, return the original data
      if (activityIds.length === 0) {
        console.log('⚠️ No activities found in hierarchical data to enhance');
        return of(hierarchicalData);
      }
    } catch (error) {
      console.error('❌ Error extracting activity IDs from hierarchical data:', error);
      return of(hierarchicalData);
    }
    
    // Fetch dynamic column data for all activities
    return this.getDynamicColumnsForActivities(activityIds).pipe(
      timeout(60000), // 60 second timeout
      map(dynamicColumnsData => {
        // Process the dynamic column data response
        if (!dynamicColumnsData) {
          console.warn('⚠️ No dynamic column data received from API');
          return hierarchicalData;
        }
        
        // Create a map of activity ID to dynamic column data for easier lookup
        const activityDynamicColumnsMap = this.createDynamicColumnsMap(dynamicColumnsData);
        
        console.log(`✅ Processed dynamic column data for ${activityDynamicColumnsMap.size} activities`);
        
        // Now enhance the hierarchical data with the dynamic column data
        const enhancedData = this.applyDynamicColumnsToHierarchicalData(
          hierarchicalData, 
          activityDynamicColumnsMap
        );
        
        return enhancedData;
      }),
      catchError(error => {
        console.error('❌ Error fetching dynamic column data:', error);
        // If there's an error, return the original data without enhancements
        return of(hierarchicalData);
      })
    );
  }
  
  /**
   * Helper method to create a map of activity IDs to dynamic column data
   * @param dynamicColumnsData Raw dynamic column data from API
   * @returns Map of activity ID to dynamic column data array
   */
  private createDynamicColumnsMap(dynamicColumnsData: any): Map<number, any[]> {
    const activityDynamicColumnsMap = new Map<number, any[]>();
    
    try {
      // Process the response based on its structure
      if (Array.isArray(dynamicColumnsData)) {
        // If response is an array of dynamic column entries
        dynamicColumnsData.forEach(entry => {
          const activityId = entry.ActivityId || entry.activityId;
          if (activityId) {
            if (!activityDynamicColumnsMap.has(activityId)) {
              activityDynamicColumnsMap.set(activityId, []);
            }
            activityDynamicColumnsMap.get(activityId).push(entry);
          }
        });
      } else if (typeof dynamicColumnsData === 'object') {
        // If response is an object with activity IDs as keys
        // This is the format from our new mock API endpoint
        Object.entries(dynamicColumnsData).forEach(([activityId, columns]) => {
          if (columns && Array.isArray(columns)) {
            // Use parseInt with radix 10 to ensure correct conversion
            const actId = parseInt(activityId, 10);
            if (!isNaN(actId)) {
              activityDynamicColumnsMap.set(actId, columns);
              console.log(`✅ Added dynamic column data for activity ${actId} (${columns.length} columns)`);
            }
          }
        });
      }
      
      // Log the results
      if (activityDynamicColumnsMap.size > 0) {
        console.log(`✅ Successfully created dynamic columns map with data for ${activityDynamicColumnsMap.size} activities`);
        // Log example data for debugging
        const firstActivityId = activityDynamicColumnsMap.keys().next().value;
        const firstActivityData = activityDynamicColumnsMap.get(firstActivityId);
        console.log(`✅ Sample data for activity ${firstActivityId}:`, firstActivityData?.[0]);
      } else {
        console.warn(`⚠️ Created empty dynamic columns map - no valid data found`);
        console.log(`⚠️ Raw response format:`, typeof dynamicColumnsData);
        if (typeof dynamicColumnsData === 'object') {
          console.log(`⚠️ Response keys:`, Object.keys(dynamicColumnsData));
        }
      }
    } catch (error) {
      console.error('❌ Error creating dynamic columns map:', error);
    }
    
    return activityDynamicColumnsMap;
  }
  
  /**
   * Apply dynamic column data to hierarchical data structure
   */
  private applyDynamicColumnsToHierarchicalData(
    hierarchicalData: any[], 
    activityDynamicColumnsMap: Map<number, any[]>
  ): any[] {
    if (!hierarchicalData || hierarchicalData.length === 0) {
      return hierarchicalData;
    }
    
    // Process each group
    return hierarchicalData.map(group => {
      if (group.projects && Array.isArray(group.projects)) {
        // Process each project
        group.projects = group.projects.map(project => {
          if (project.interventions && Array.isArray(project.interventions)) {
            // Process each intervention
            project.interventions = project.interventions.map(intervention => {
              if (intervention.activities && Array.isArray(intervention.activities)) {
                // Process each activity
                intervention.activities = intervention.activities.map(activity => {
                  // Check if we have dynamic column data for this activity
                  if (activity.id && activityDynamicColumnsMap.has(activity.id)) {
                    const dynamicColumns = activityDynamicColumnsMap.get(activity.id);
                    
                    // Initialize dynamic columns array and objects
                    if (!activity.dynamicColumns) {
                      activity.dynamicColumns = [];
                    }
                    
                    if (!activity.info) {
                      activity.info = {};
                    }
                    
                    if (!activity.cumulativeProgress) {
                      activity.cumulativeProgress = {};
                    }
                    
                    // Process dynamic column data
                    dynamicColumns.forEach(columnData => {
                      // Create standardized dynamic column object
                      const columnName = columnData.Name || columnData.name || 
                        (columnData.Column ? columnData.Column.Name : `Column ${columnData.Id || columnData.id}`);
                        
                      const columnType = columnData.DataType || columnData.dataType || 
                        (columnData.Column ? columnData.Column.DataType : 'string');
                        
                      const value = columnData.Value !== undefined ? columnData.Value : 
                                   (columnData.value !== undefined ? columnData.value : null);
                      
                      const unit = columnData.Unit || columnData.unit || 
                        (columnData.Column ? columnData.Column.Unit : '');
                        
                      const description = columnData.Description || columnData.description || 
                        (columnData.Column ? columnData.Column.Description : '');
                      
                      // Add to dynamic columns array
                      activity.dynamicColumns.push({
                        id: columnData.Id || columnData.id,
                        columnId: columnData.ColumnId || columnData.columnId || 
                          (columnData.DynamicColumnId || columnData.dynamicColumnId),
                        columnName: columnName,
                        columnType: columnType,
                        value: value,
                        unit: unit,
                        description: description
                      });
                      
                      // Determine if this is a progress indicator or general info
                      const columnNameLower = columnName.toLowerCase();
                      const isProgressIndicator = 
                        columnNameLower.includes('progress') || 
                        columnNameLower.includes('complete') || 
                        columnNameLower.includes('target') ||
                        columnNameLower.includes('achieve');
                      
                      if (isProgressIndicator) {
                        // Add to cumulative progress
                        activity.cumulativeProgress[this.sanitizePropertyName(columnName)] = {
                          value: value,
                          unit: unit,
                          description: description,
                          type: columnType
                        };
                      } else {
                        // Add to info object
                        activity.info[this.sanitizePropertyName(columnName)] = {
                          value: value,
                          unit: unit,
                          description: description,
                          type: columnType
                        };
                      }
                    });
                    
                    // Organize the structured data
                    this.organizeStructuredData(activity);
                  }
                  
                  return activity;
                });
              }
              
              return intervention;
            });
          }
          
          return project;
        });
      }
      
      return group;
    });
  }

  /**
   * Get hierarchical report data organized by project groups, projects, interventions, and activities
   * @param filters Optional filters to apply to the report data
   * @param options Additional options for the request
   * @returns Observable with hierarchical report data
   */
  getHierarchicalReport(
    filters?: ReportFilter, 
    options: { includeDynamicColumns?: boolean } = { includeDynamicColumns: false }
  ): Observable<any> {
    // Log the call details
    console.log('💡 getHierarchicalReport called with filters:', filters);
    console.log('💡 Options:', options);
    
    // Process filters the same way as getFullDashboardReport for consistency
    const apiFilters = this.processFilter(filters || {});
    
    console.log('💡 Processed filters for hierarchical endpoint:', apiFilters);

    // Prepare API parameters
    let params = new HttpParams();
    if (apiFilters) {
      // Add each filter parameter using the same approach as in getFullDashboardReport
      Object.entries(apiFilters).forEach(([key, value]) => {
        if (value !== null && value !== undefined) {
          if (Array.isArray(value)) {
            value.forEach(item => {
              params = params.append(key, item);
            });
          } else if (typeof value === 'object') {
            params = params.append(key, JSON.stringify(value));
          } else {
            params = params.append(key, value.toString());
          }
        }
      });
    }
    
    // Add parameters to request dynamic column data if specified in options
    if (options.includeDynamicColumns) {
      params = params.append('includeDynamicColumns', 'true');
    }
    
    // Use the API URL from the environment config
    const hierarchicalEndpoint = `${this.apiUrl}/dashboard/reports/hierarchical`;
    
    // Log the exact URL and parameters being used
    console.log('📡 Making API call to hierarchical endpoint:', hierarchicalEndpoint);
    console.log('📡 Query params:', params.toString());
    
    // Make the HTTP request with the endpoint from the environment
    return this.http.get<any>(hierarchicalEndpoint, { 
      params,
      // Add request headers for CORS debugging
      headers: {
        'X-Requested-With': 'XMLHttpRequest',
        'Accept': 'application/json'
      }
    }).pipe(
      // Add timeout to avoid hanging requests
      timeout(30000), // 30 seconds
      // Add retry for transient errors
      retry(1),
      tap(response => {
        console.log('✅ Hierarchical report API success!');
        // Limit log size for large responses
        if (response && Array.isArray(response)) {
          console.log(`✅ Received ${response.length} project groups`);
        } else {
          console.log('✅ Response data structure:', typeof response);
        }
      }),
      switchMap(response => {
        // First get the base hierarchical data
        const hierarchicalData = response || [];
        
        // If we don't want dynamic columns or the server included them already, return as is
        if (!options.includeDynamicColumns) {
          console.log('ℹ️ Not requesting dynamic columns as per options');
          return of(hierarchicalData);
        }
        
        // Check if dynamic columns are already included in the response
        const hasDynamicColumns = this.checkIfDynamicColumnsExist(hierarchicalData);
        if (hasDynamicColumns) {
          console.log('✅ Dynamic columns already included in response');
          return of(hierarchicalData);
        }
        
        // Limit the size of the data to enhance for performance
        console.log('🔄 Enhancing hierarchical data with dynamic columns...');
        
        // If too many activities, warn but still attempt to process
        const activityCount = this.countActivitiesInHierarchicalData(hierarchicalData);
        if (activityCount > 1000) {
          console.warn(`⚠️ Large dataset with ${activityCount} activities. Consider filtering data or turning off dynamic columns.`);
        }
        
        // Then enhance it with dynamic columns data
        return this.enhanceHierarchicalDataWithDynamicColumns(hierarchicalData).pipe(
          // Add timeout specific to the enhancement process
          timeout(60000), // 60 seconds
          catchError(error => {
            console.error('❌ Error enhancing with dynamic columns:', error);
            // Return the original hierarchical data without enhancements
            return of(hierarchicalData);
          })
        );
      }),
      catchError(error => {
        console.error('❌ Hierarchical report API error:', error);
        console.error('❌ Failed URL was:', hierarchicalEndpoint);
        console.error('❌ Status code:', error.status);
        console.error('❌ Error details:', error.message);
        
        // Fall back to the mock data since we know the real API exists but might have temporary issues
        console.log('⚠️ Falling back to mock data');
        return of(this.getMockHierarchicalReport(filters));
      })
    );
  }
  
  /**
   * Check if hierarchical data already contains dynamic columns
   */
  private checkIfDynamicColumnsExist(hierarchicalData: any[]): boolean {
    if (!hierarchicalData || !Array.isArray(hierarchicalData) || hierarchicalData.length === 0) {
      return false;
    }
    
    // Sample a few activities to check if they have dynamic columns
    let hasColumns = false;
    
    try {
      // Check first group's first project's first intervention's first activity
      if (hierarchicalData[0]?.projects?.[0]?.interventions?.[0]?.activities?.[0]) {
        const sampleActivity = hierarchicalData[0].projects[0].interventions[0].activities[0];
        hasColumns = !!sampleActivity.dynamicColumns || !!sampleActivity.info || !!sampleActivity.cumulativeProgress;
      }
    } catch (e) {
      console.error('Error checking for dynamic columns:', e);
    }
    
    return hasColumns;
  }
  
  /**
   * Count the total number of activities in hierarchical data
   */
  private countActivitiesInHierarchicalData(hierarchicalData: any[]): number {
    if (!hierarchicalData || !Array.isArray(hierarchicalData)) {
      return 0;
    }
    
    let count = 0;
    
    try {
      hierarchicalData.forEach(group => {
        if (group.projects && Array.isArray(group.projects)) {
          group.projects.forEach(project => {
            if (project.interventions && Array.isArray(project.interventions)) {
              project.interventions.forEach(intervention => {
                if (intervention.activities && Array.isArray(intervention.activities)) {
                  count += intervention.activities.length;
                }
              });
            }
          });
        }
      });
    } catch (e) {
      console.error('Error counting activities:', e);
    }
    
    return count;
  }

  /**
   * Sanitize property names for safe object property access
   */
  private sanitizePropertyName(name: string): string {
    // Add null check
    if (!name || typeof name !== 'string') {
      return 'unknown';
    }
    
    // Remove spaces and special characters, convert to camelCase
    return name
      .replace(/[^\w\s]/g, '')  // Remove special characters
      .replace(/\s+(.)/g, (_, c) => c.toUpperCase())  // Convert to camelCase
      .replace(/\s/g, '')  // Remove remaining spaces
      .replace(/^(.)/, (_, c) => c.toLowerCase());  // Ensure first character is lowercase
  }
  
  /**
   * Organize the structured data into meaningful sections
   */
  private organizeStructuredData(activity: any): void {
    // Define pattern matching for categorizing columns
    const beneficiaryPatterns = [/beneficiar/i, /household/i, /people/i, /men/i, /women/i, /children/i, /families/i];
    const progressPatterns = [/progress/i, /complete/i, /status/i, /milestone/i, /stage/i, /phase/i];
    const targetPatterns = [/target/i, /goal/i, /planned/i, /expected/i];
    const financialPatterns = [/budget/i, /cost/i, /expenditure/i, /spending/i, /fund/i, /financial/i];
    
    // Set up standardized structures
    if (!activity.info.beneficiaryDetails) {
      activity.info.beneficiaryDetails = {};
    }
    
    if (!activity.cumulativeProgress.indicators) {
      activity.cumulativeProgress.indicators = [];
    }
    
    // Process each dynamic column and organize into the appropriate structures
    if (activity.dynamicColumns && Array.isArray(activity.dynamicColumns)) {
      activity.dynamicColumns.forEach(column => {
        const name = column.columnName;
        const value = column.value;
        
        // Categorize columns based on patterns
        if (beneficiaryPatterns.some(pattern => pattern.test(name))) {
          // Add to beneficiary details
          const beneficiaryKey = this.sanitizePropertyName(name);
          activity.info.beneficiaryDetails[beneficiaryKey] = value;
        } 
        else if (progressPatterns.some(pattern => pattern.test(name))) {
          // Add to progress indicators
          activity.cumulativeProgress.percentComplete = activity.cumulativeProgress.percentComplete || value;
        }
        else if (targetPatterns.some(pattern => pattern.test(name))) {
          // Add as target
          activity.cumulativeProgress.target = activity.cumulativeProgress.target || value;
        }
        else if (financialPatterns.some(pattern => pattern.test(name))) {
          // Add as financial info
          activity.info[this.sanitizePropertyName(name)] = value;
        }
        
        // Add to indicators array in cumulativeProgress
        if (column.unit && typeof value === 'number') {
          activity.cumulativeProgress.indicators.push({
            name: name,
            value: value,
            unit: column.unit,
            description: column.description
          });
        }
      });
    }
    
    // Clean up empty objects
    if (Object.keys(activity.info.beneficiaryDetails).length === 0) {
      delete activity.info.beneficiaryDetails;
    }
    
    if (activity.cumulativeProgress.indicators.length === 0) {
      delete activity.cumulativeProgress.indicators;
    }
  }

  /**
   * Convert quarter enum to number for API
   */
  private getQuarterNumber(quarter: Quarter): number {
    switch (quarter) {
      case Quarter.Q1: return 1;
      case Quarter.Q2: return 2;
      case Quarter.Q3: return 3;
      case Quarter.Q4: return 4;
      default: return 0;
    }
  }
  
  /**
   * Generate mock hierarchical report data for development/testing
   */
  private getMockHierarchicalReport(filters?: ReportFilter): any[] {
    console.log('Generating mock hierarchical report with filters:', filters);
    
    // Create sample data structure
    return [
      {
        id: 'economic',
        name: 'Economic Development',
        projects: [
          {
            id: 'p1',
            code: 'ECO-001',
            name: 'Market Access Program',
            status: 'active',
            startDate: '2023-01-15',
            endDate: '2024-12-31',
            budget: 3500000,
            cashDistributed: 2100000,
            progress: 60,
            interventions: [
              {
                id: 'int1',
                name: 'Small Business Support',
                categoryId: 'cat1',
                categoryName: 'Economic Empowerment',
                description: 'Supporting small businesses through training and grants',
                totalActivities: 3,
                activities: [
                  {
                    id: 1,
                    uniqueId: 'ECO-001-SBS-001',
                    status: 'ongoing',
                    startDate: '2023-01-15',
                    endDate: '2023-09-30',
                    location: {
                      provinceId: 1,
                      provinceName: 'Kabul',
                      districtId: 101,
                      districtName: 'District 1'
                    },
                    progress: 75,
                    budget: 150000,
                    cashDistributed: 112500
                  },
                  {
                    id: 2,
                    uniqueId: 'ECO-001-SBS-002',
                    status: 'completed',
                    startDate: '2023-02-01',
                    endDate: '2023-08-15',
                    location: {
                      provinceId: 2,
                      provinceName: 'Herat',
                      districtId: 201,
                      districtName: 'District 5'
                    },
                    progress: 100,
                    budget: 120000,
                    cashDistributed: 120000
                  },
                  {
                    id: 3,
                    uniqueId: 'ECO-001-SBS-003',
                    status: 'archived',
                    startDate: '2022-06-01',
                    endDate: '2023-02-28',
                    location: {
                      provinceId: 1,
                      provinceName: 'Kabul',
                      districtId: 102,
                      districtName: 'District 2'
                    },
                    progress: 100,
                    budget: 80000,
                    cashDistributed: 80000
                  }
                ],
                summary: {
                  totalActivities: 3,
                  totalBudget: 350000,
                  totalCashDistributed: 312500,
                  progress: 91,
                  beneficiaries: 1200
                }
              }
            ],
            summary: {
              totalInterventions: 1,
              totalActivities: 3,
              totalBudget: 350000,
              totalCashDistributed: 312500,
              progress: 91,
              beneficiaries: 1200
            }
          }
        ],
        summary: {
          totalProjects: 1,
          totalInterventions: 1,
          totalActivities: 3,
          totalBudget: 350000,
          totalCashDistributed: 312500,
          progress: 91,
          beneficiaries: 1200
        }
      },
      {
        id: 'health',
        name: 'Health',
        projects: [
          {
            id: 'p2',
            code: 'HEA-001',
            name: 'Primary Healthcare Strengthening',
            status: 'active',
            startDate: '2023-03-01',
            endDate: '2025-02-28',
            budget: 4800000,
            cashDistributed: 2400000,
            progress: 50,
            interventions: [
              {
                id: 'int2',
                name: 'Healthcare Facilities Improvement',
                categoryId: 'cat2',
                categoryName: 'Health Infrastructure',
                description: 'Upgrading rural healthcare facilities',
                totalActivities: 2,
                activities: [
                  {
                    id: 4,
                    uniqueId: 'HEA-001-HFI-001',
                    status: 'ongoing',
                    startDate: '2023-03-15',
                    endDate: '2024-06-30',
                    location: {
                      provinceId: 3,
                      provinceName: 'Kandahar',
                      districtId: 301,
                      districtName: 'District 2'
                    },
                    progress: 45,
                    budget: 350000,
                    cashDistributed: 157500
                  },
                  {
                    id: 6,
                    uniqueId: 'HEA-001-HFI-002',
                    status: 'completed',
                    startDate: '2022-08-01',
                    endDate: '2023-07-31',
                    location: {
                      provinceId: 3,
                      provinceName: 'Kandahar',
                      districtId: 302,
                      districtName: 'District 3'
                    },
                    progress: 100,
                    budget: 280000,
                    cashDistributed: 280000
                  }
                ],
                summary: {
                  totalActivities: 2,
                  totalBudget: 630000,
                  totalCashDistributed: 437500,
                  progress: 72,
                  beneficiaries: 15000
                }
              },
              {
                id: 'int3',
                name: 'Maternal and Child Health',
                categoryId: 'cat2',
                categoryName: 'Health Infrastructure',
                description: 'Improving maternal and child health services',
                totalActivities: 2,
                activities: [
                  {
                    id: 5,
                    uniqueId: 'HEA-001-MCH-001',
                    status: 'ongoing',
                    startDate: '2023-04-01',
                    endDate: '2024-03-31',
                    location: {
                      provinceId: 4,
                      provinceName: 'Balkh',
                      districtId: 401,
                      districtName: 'District 3'
                    },
                    progress: 55,
                    budget: 280000,
                    cashDistributed: 154000
                  },
                  {
                    id: 7,
                    uniqueId: 'HEA-001-MCH-002',
                    status: 'cancelled',
                    startDate: '2023-01-01',
                    endDate: '2023-03-31',
                    location: {
                      provinceId: 4,
                      provinceName: 'Balkh',
                      districtId: 402,
                      districtName: 'District 4'
                    },
                    progress: 25,
                    budget: 150000,
                    cashDistributed: 37500
                  }
                ],
                summary: {
                  totalActivities: 2,
                  totalBudget: 430000,
                  totalCashDistributed: 191500,
                  progress: 40,
                  beneficiaries: 10000
                }
              }
            ],
            summary: {
              totalInterventions: 2,
              totalActivities: 2,
              totalBudget: 630000,
              totalCashDistributed: 311500,
              progress: 50,
              beneficiaries: 20500
            }
          }
        ],
        summary: {
          totalProjects: 1,
          totalInterventions: 2,
          totalActivities: 2,
          totalBudget: 630000,
          totalCashDistributed: 311500,
          progress: 50,
          beneficiaries: 20500
        }
      }
    ];
  }

  /**
   * Process raw dynamic column data into organized structure
   */
  processDynamicColumnData(rawData: any[]): ProcessedDynamicData {
    const processed: ProcessedDynamicData = {
      info: {},
      targets: {},
      progress: {},
      raw: rawData
    };

    rawData.forEach(column => {
      // Add null checks for column name
      const columnName = column.Name || column.columnName || column.name || 'unknown';
      const key = this.sanitizePropertyName(columnName);
      const value = this.parseColumnValue(column.Value || column.value, column.DataType || column.dataType);
      
      // Organize by column type
      switch (column.Type || column.columnType) {
        case 'TargetInfo':
        case 'TargetStatic':
          processed.targets[key] = {
            value: value,
            displayName: column.DisplayName || column.displayName || columnName,
            unit: column.Unit || column.unit,
            description: column.Description || column.description,
            dataType: column.DataType || column.dataType
          };
          break;
          
        case 'Progress':
        case 'ProgressStatic':
          processed.progress[key] = {
            value: value,
            displayName: column.DisplayName || column.displayName || columnName,
            unit: column.Unit || column.unit,
            description: column.Description || column.description,
            dataType: column.DataType || column.dataType
          };
          break;
          
        case 'ProgressInfo':
        default:
          processed.info[key] = {
            value: value,
            displayName: column.DisplayName || column.displayName || columnName,
            unit: column.Unit || column.unit,
            description: column.Description || column.description,
            dataType: column.DataType || column.dataType
          };
          break;
      }
    });

    return processed;
  }

  /**
   * Parse column value based on data type
   */
  private parseColumnValue(value: any, dataType: string): any {
    if (value === null || value === undefined || value === '') {
      return null;
    }

    switch (dataType?.toLowerCase()) {
      case 'number':
      case 'currency':
      case 'percentage':
        const numValue = parseFloat(value);
        return isNaN(numValue) ? null : numValue;
        
      case 'checkbox':
        if (typeof value === 'boolean') return value;
        return value === 'true' || value === '1' || value === 1;
        
      case 'date':
        const dateValue = new Date(value);
        return isNaN(dateValue.getTime()) ? null : dateValue.toISOString();
        
      default:
        return value.toString();
    }
  }
} 

