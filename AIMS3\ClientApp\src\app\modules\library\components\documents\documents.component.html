<aims-loading *ngIf="working"></aims-loading>
<div class="card card-custom card-stretch" [ngClass]="{'d-none':working}">
    <!-- begin::Header -->
    <div class="card-header">
        <div class="card-title" style="max-width:80%;">
            <!-- Filters -->
            <div class="d-flex align-items-center">
                <span class="fs-7 fw-semibold text-gray-700 pe-4 text-nowrap d-none d-md-block">File type</span>
                <select id="fileType" class="form-select form-select-sm w-200px mr-3" data-control="select2"
                    data-placeholder="File type">
                    <option selected value="0">All</option>
                    <option value="-1">Templates / General</option>
                    <option *ngFor="let fType of fileTypes" [value]="fType.id">{{ fType.typeName }}</option>
                </select>
            </div>
            <!-- Filters -->
            <ng-container *ngIf="(['ga'] | isAuth)">
                <div class="bullet bg-secondary h-35px w-1px mx-4"></div>
                <!-- Partner -->
                <filter-ddl [id]="'partner'" class="filter-container w-130px" [placeholders]="['Partner']"
                    [minWidth]="130" [options]="orgs" (change)="onFilterChange($event)" [multiple]="true" #partner>
                </filter-ddl>
                <!-- Status -->
                <filter-ddl [id]="'status'" class="filter-container w-100px ms-2" [placeholders]="['Status']"
                    [minWidth]="130" [options]="statuses" [showSearch]="false" #status (change)="onFilterChange($event)"
                    [ngClass]="{'d-none': +fileTypeId === -1 }">
                </filter-ddl>
                <button role="button" class="btn btn-sm btn-light-primary border border-primary py-2 px-3 ms-2"
                    (click)="getDocuments()">Apply filter</button>
                <div class="fs-7 ms-3" *ngIf="lblFiltered" style="max-width:20%;">
                    <span class="fs-9 text-muted text-uppercase">Filtered: </span>
                    <a class="fs-9 cursor-pointer" (click)="resetFilters()">Reset</a><br />
                    <span class="text-primary">{{ lblFiltered }}</span>
                </div>
            </ng-container>
            <div class="d-flex fs-7 fw-semibold ms-4"
                *ngIf="!fileTypeId && documents.length === 500 && (['ga'] | isAuth)">
                <span class="text-gray-700 text-nowrap">TOP 500</span>
                <a class="cursor-pointer" (click)="getDocuments(true)">Show all</a>
            </div>
        </div>
        <div class="card-toolbar">
            <!--begin::Search-->
            <div class="d-flex align-items-center position-relative">
                <span [inlineSVG]="'./assets/media/icons/duotune/general/gen021.svg'"
                    class="svg-icon svg-icon-2 position-absolute ms-3"></span>
                <input type="search" class="form-control form-control-sm form-control-solid w-200px ps-12"
                    placeholder="Search" (search)="onSearch($event)" (keyup)="onSearch($event)" />
            </div>
            <div class="btn-group" role="group" aria-label="Upload or create a link"
                *ngIf="(['ga'] | isAuth) else btnUploadOnly">
                <button type="button" class="btn btn-sm btn-icon btn-light-primary px-3 w-auto ms-3"
                    (click)="onUpload()">
                    <i class="fas fa-upload me-2"></i> Upload
                </button>
                <button id="createLink" type="button" class="btn btn-sm btn-icon btn-light-primary dropdown-toggle"
                    data-qs-menu-trigger="click" data-qs-menu-placement="bottom-end">
                    <i class="fas fa-chevron-down"></i>
                </button>
                <ul class="dropdown-menu" aria-labelledby="createLink" data-qs-menu="true">
                    <li class="py-1"><a class="dropdown-item py-2" (click)="onCreateLink()">Add a linked document</a>
                    </li>
                </ul>
            </div>
            <ng-template #btnUploadOnly>
                <button type="button" class="btn btn-sm btn-icon btn-light-primary px-3 w-auto ms-3"
                    (click)="onUpload()" [disabled]="+fileTypeId === -1 && !(['ga'] | isAuth)"
                    *ngIf="(['ga','la','lde'] | isAuth)">
                    <i class="fas fa-upload me-2"></i> Upload
                </button>
            </ng-template>
            <button type="button" class="btn btn-sm btn-icon btn-light-primary px-3 w-auto ms-2" (click)="onSettings()"
                ngbTooltip="Document upload settings" *ngIf="(['admin'] | isAuth)">
                <span [inlineSVG]="'./assets/media/icons/duotune/general/gen019.svg'"
                    class="svg-icon svg-icon-2 me-2"></span>
                Settings
            </button>
        </div>
    </div>
    <!-- end::Header -->
    <!-- begin::Body -->
    <div class="card-body">
        <div class="table-responsive" *ngIf="documents.length else noDoc">
            <table id="table_documents" class="table table-sm table-hover fs-7 table-row-dashed align-middle">
                <thead>
                    <tr class="text-start text-gray-900 fw-bold gs-1" *ngIf="+fileTypeId === -1">
                        <th data-orderable="false"></th>
                        <th class="d-none"></th>
                        <th>File name</th>
                        <th>Description</th>
                        <th width="5%">Size</th>
                        <th class="text-end w-100px" data-orderable="false"></th>
                    </tr>
                    <tr class="text-start text-gray-900 fw-bold gs-1" *ngIf="+fileTypeId === 0">
                        <th data-orderable="false"></th>
                        <th class="d-none"></th>
                        <th>File name</th>
                        <th width="10%" *ngIf="isGlobalUser">IP</th>
                        <th width="15%"
                            title="This column shows Activity ID(s) or Target, intervention and column name under which a file is uploaded.">
                            Activity ID <span class="la la-info-circle ms-2"></span></th>
                        <th width="10%">File type</th>
                        <th>File information</th>
                        <th width="6%">Size</th>
                        <th width="5%">Status</th>
                        <th class="text-end" data-orderable="false"></th>
                    </tr>
                    <tr class="text-start text-gray-900 fw-bold gs-1" *ngIf="+fileTypeId > 0">
                        <th data-orderable="false"></th>
                        <th class="d-none"></th>
                        <th>File name</th>
                        <th width="10%" *ngIf="isGlobalUser">IP</th>
                        <th width="12%"
                            title="This column shows Activity ID(s) or Target, intervention and column name under which a file is uploaded.">
                            Activity ID <span class="la la-info-circle ms-2"></span></th>
                        <th *ngFor="let f of fields['t' + fileTypeId]">{{ f.fieldName }}</th>
                        <th width="6%">Size</th>
                        <th width="5%">Status</th>
                        <th class="text-end" data-orderable="false"></th>
                    </tr>
                </thead>
                <tbody>
                    <tr *ngFor="let doc of documents; let ind = index;">
                        <td class="text-center"></td>
                        <td class="d-none">{{ ind+1 }}</td>
                        <td class="fs-6 file-name">
                            <a class="text-primary file-link" *ngIf="!doc.isLink">
                                {{ doc.docName }}
                            </a>
                            <button class="btn btn-sm btn-icon h-25px w-25px btn-bg-light float-end"
                                ngbTooltip="Copy file link" (click)="onCopyFileLink(doc.id, doc.fileName, doc.isLink)">
                                <i class="la la-copy"></i>
                            </button>
                            <a [href]="doc.fileName" target="_blank" *ngIf="doc.isLink"
                                title="External document or file">
                                {{ doc.docName }} <i class="fas fa-external-link text-muted ms-2 fs-9"></i>
                            </a>
                        </td>
                        <td class="fs-8" *ngIf="+fileTypeId === -1" [innerHtml]="doc.docDesc | nlHtml"></td>
                        <ng-container *ngIf="+fileTypeId !== -1">
                            <td *ngIf="isGlobalUser">{{ doc.orgName || 'Shared' }}</td>
                            <td class="text-gray-800 fs-8" *ngIf="+fileTypeId !== -1">
                                <ul class="list-unstyled p-0 m-0" *ngIf="doc.activityIds.length">
                                    <li *ngFor="let actId of doc.activityIds">{{ actId }}</li>
                                </ul>
                                <ng-container *ngIf="!doc.activityIds.length && doc.columnName">
                                    <span class="me-1" title="Intervention: {{ doc.profile?.name }}">{{
                                        doc.profile?.abbrv }}:</span>
                                    <span class="text-primary" title="Column name: {{ doc.columnName }}">{{
                                        doc.columnName }}:</span>
                                    <span class="ms-1" *ngIf="doc.targetPeriod">({{ doc.targetPeriod }})</span>
                                </ng-container>
                            </td>
                            <td *ngIf="+fileTypeId === 0">{{ doc.docTypeName }}</td>
                            <td class="fs-8" *ngIf="+fileTypeId === 0">
                                <ul class="list-unstyled p-0 m-0">
                                    <li *ngFor="let f of fields['t' + doc.docTypeId]">
                                        <span class="fw-semibold">{{ f.fieldName }}:</span> {{
                                        getFieldValue(doc.fieldsWithValues, f.id) }}
                                    </li>
                                </ul>
                            </td>
                            <ng-container *ngIf="+fileTypeId > 0">
                                <td *ngFor="let f of fields['t' + doc.docTypeId]">{{ getFieldValue(doc.fieldsWithValues,
                                    f.id) }}</td>
                            </ng-container>
                        </ng-container>
                        <td width="5%">{{ doc.docSize | fileSize }}</td>
                        <td width="5%" *ngIf="+fileTypeId > -1">
                            <span class="badge badge-light-primary"
                                title="Approved on: {{doc.dateApproved | date:'medium'}}"
                                *ngIf="doc.dateApproved">Approved</span>
                            <span class="badge badge-light fw-normal" title="Not approved"
                                *ngIf="doc.orgId > 0 && !doc.dateApproved">Unapproved</span>
                        </td>
                        <td width="10%" class="text-end fs-7">
                                                          
                            <!-- File Preview -->
                              <button
                              class="btn btn-sm btn-icon h-30px btn-bg-light col-{{ doc.fileName | fileExtColor }}"
                              (click)="openPreview(doc.fileName)" ngbTooltip="Preview" *ngIf="!doc.isLink">
                              <i class="far fa-eye"></i>  
                          </button>
                            <!-- Download -->
                              <!-- <button
                                class="btn btn-sm btn-icon h-30px btn-bg-light col-{{ doc.fileName | fileExtColor }}"
                                (click)="downloadFile(doc.fileName)" ngbTooltip="Download" *ngIf="!doc.isLink">
                                <i class="far {{ doc.fileName | fileExt }}"></i>
                            </button> -->
                            <!-- Link -->
                            <a class="btn btn-sm btn-icon h-30px btn-bg-light btn-active-color-primary"
                                [href]="doc.fileName" target="_blank" ngbTooltip="Visit external link"
                                *ngIf="doc.isLink">
                                <i class="fas fa-external-link"></i>
                            </a>
                            <!-- Actions: Edit, Delete -->
                            <ng-container *ngIf="+fileTypeId > 0 && (['ga','la','lde'] | isAuth:doc.orgId ?? -1)">
                                <button
                                    class="btn btn-sm btn-icon w-30px h-30px btn-bg-light btn-active-color-primary ms-2"
                                    ngbTooltip="More Actions" data-qs-menu-trigger="click"
                                    data-qs-menu-placement="bottom-end" data-qs-menu-flip="top-end">
                                    <i class="bi bi-three-dots fs-5"></i>
                                </button>
                                <!-- Dropdown menu -->
                                <div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown py-lg-1 w-lg-225px"
                                    data-qs-menu="true">
                                    <div class="menu-item menu-lg-down-accordion" *ngIf="!doc.dateApproved">
                                        <a class="menu-link py-3" (click)="onEdit(doc.id)">
                                            <span class="menu-icon">
                                                <span [inlineSVG]="'./assets/media/icons/duotune/general/gen055.svg'"
                                                    class="svg-icon svg-icon-3"></span>
                                            </span><span class="menu-title">Edit</span>
                                        </a>
                                    </div>
                                    <ng-container *ngIf="doc.orgId > 0 && (['ga'] | isAuth)">
                                        <div class="menu-item menu-lg-down-accordion"
                                            *ngIf="!doc.dateApproved else voidApproval">
                                            <a class="menu-link py-3" (click)="toggleStatus(doc.id, doc.dateApproved)">
                                                <span class="menu-icon">
                                                    <span
                                                        [inlineSVG]="'./assets/media/icons/duotune/general/gen026.svg'"
                                                        class="svg-icon svg-icon-3"></span>
                                                </span><span class="menu-title">Approve</span>
                                            </a>
                                        </div>
                                        <ng-template #voidApproval>
                                            <div class="menu-item menu-lg-down-accordion">
                                                <a class="menu-link py-3"
                                                    (click)="toggleStatus(doc.id, doc.dateApproved)">
                                                    <span class="menu-icon">
                                                        <span
                                                            [inlineSVG]="'./assets/media/icons/duotune/general/gen026.svg'"
                                                            class="svg-icon svg-icon-3 text-muted"></span>
                                                    </span><span class="menu-title">Retreat Approval</span>
                                                </a>
                                            </div>
                                        </ng-template>
                                    </ng-container>
                                    <div class="menu-item separator"></div>
                                    <div class="menu-item menu-lg-down-accordion">
                                        <a class="menu-link py-3" (click)="onDelete(doc.id, doc.fileName)">
                                            <span class="menu-icon">
                                                <span [inlineSVG]="'./assets/media/icons/duotune/general/trash.svg'"
                                                    class="svg-icon svg-icon-3 text-danger"></span>
                                            </span><span class="menu-title text-danger">Delete</span>
                                        </a>
                                    </div>
                                </div>
                            </ng-container>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <ng-template #noDoc>
            <p class="text-muted">No document found.</p>
        </ng-template>
    </div>
</div>
<div class="modal fade"
     [ngClass]="{'show d-block': showPreviewModal}" tabindex="-1" role="dialog" *ngIf="showPreviewModal">
    <div class="modal-dialog modal-xl" role="document" style="max-width: 90%;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">File Preview: {{previewFileName}}</h5>
                <button type="button" class="close" (click)="closePreview()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="min-height: 80vh;">
                <div *ngIf="previewFileBlobUrl" class="h-100">
                    <ng-container *ngIf="previewFileName?.endsWith('.pdf')">
                        <iframe [src]="previewFileBlobUrl" style="width: 100%; height: 500px; border: none;"></iframe>
                    </ng-container>
                    <ng-container *ngIf="isImageFile(previewFileName)">
                        <img [src]="previewFileBlobUrl" alt="Preview" class="img-fluid" style="max-height: 70vh; display: block; margin: 0 auto;">
                    </ng-container>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-info px-4 align-self-center text-nowrap" (click)="downloadFile(previewFileName)" ngbTooltip="Download">
                    <span [inlineSVG]="'./assets/media/icons/duotune/arrows/arr091.svg'" class="svg-icon svg-icon-2 me-1"></span> Download
            </button>
            </div>
        </div>
    </div>
</div>
<div class="modal-backdrop fade" [ngClass]="{'show': showPreviewModal}" *ngIf="showPreviewModal"></div>
<!-- Upload modal -->
<doc-upload-form-modal id="docModal" (done)="onSaved($event)"></doc-upload-form-modal>

<!-- Document upload settings -->
<doc-types-modal id="docTypesModal" (done)="onSettingSaved($event)"></doc-types-modal>