<div class="overview-dashboard">
  <!-- KPI Metrics Cards -->
  <div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="card stats-card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="card-title mb-0">Total Projects</h6>
            <i class="fas fa-briefcase text-success icon-bg"></i>
          </div>
          <h2 class="mb-2">{{ formatNumber(summaryStats?.totalProjects) }}</h2>
          <div class="progress progress-sm">
            <div class="progress-bar bg-success" role="progressbar" 
                 [style.width]="'100%'" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-3 col-sm-6 mb-3">
      <div class="card stats-card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="card-title mb-0">Total Activities</h6>
            <i class="fas fa-tasks text-warning icon-bg"></i>
          </div>
          <h2 class="mb-2">{{ reportData?.overallProgress }}%</h2>
          <div class="progress progress-sm">
            <div class="progress-bar bg-warning" role="progressbar" 
                 [style.width]="reportData?.overallProgress + '%'" 
                 [attr.aria-valuenow]="reportData?.overallProgress" 
                 aria-valuemin="0" aria-valuemax="100"></div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="card stats-card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="card-title mb-0">Beneficiaries</h6>
            <i class="fas fa-users text-info icon-bg"></i>
          </div>
          <h2 class="mb-2">{{ formatNumber(summaryStats?.totalBeneficiaries) }}</h2>
          <div class="progress progress-sm">
            <div class="progress-bar bg-info" role="progressbar" 
                 [style.width]="'100%'" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-md-3 col-sm-6 mb-3">
      <div class="card stats-card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="card-title mb-0">Total Cash Distributed</h6>
            <i class="fas fa-chart-line text-primary icon-bg"></i>
          </div>
          <h2 class="mb-2">{{ formatCurrency(summaryStats?.totalCashDistributed) }}</h2>
          <div class="progress progress-sm">
            <div class="progress-bar bg-info" role="progressbar" 
                 [style.width]="calculatePercentage(summaryStats?.totalCashDistributed, summaryStats?.totalBudget) + '%'" 
                 [attr.aria-valuenow]="calculatePercentage(summaryStats?.totalCashDistributed, summaryStats?.totalBudget)" 
                 aria-valuemin="0" aria-valuemax="100"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Secondary Metrics Row -->
  <div class="row mb-4">
    <div class="col-md-4 col-sm-6 mb-3">
      <div class="card stats-card-sm">
        <div class="card-body d-flex justify-content-between align-items-center">
          <div>
            <h6 class="card-title text-muted mb-1">Activities</h6>
            <h4 class="mb-0">{{ formatNumber((reportData?.activitiesByStatus?.ongoing || 0) + (reportData?.activitiesByStatus?.completed || 0) + (reportData?.activitiesByStatus?.cancelled || 0)) }}</h4>
          </div>
          <div class="icon-bg-sm">
            <i class="fas fa-clipboard-list text-primary"></i>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-4 col-sm-6 mb-3">
      <div class="card stats-card-sm">
        <div class="card-body d-flex justify-content-between align-items-center">
          <div>
            <h6 class="card-title text-muted mb-1">Provinces</h6>
            <h4 class="mb-0">{{ formatNumber(reportData?.geographicCoverage?.provinces) }}</h4>
          </div>
          <div class="icon-bg-sm">
            <i class="fas fa-map-marker-alt text-danger"></i>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-4 col-sm-6 mb-3">
      <div class="card stats-card-sm">
        <div class="card-body d-flex justify-content-between align-items-center">
          <div>
            <h6 class="card-title text-muted mb-1">Districts</h6>
            <h4 class="mb-0">{{ formatNumber(reportData?.geographicCoverage?.districts) }}</h4>
          </div>
          <div class="icon-bg-sm">
            <i class="fas fa-map text-success"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Charts Row 1 -->
  <div class="row mb-4">
    <div class="col-md-8 mb-3">
      <div class="card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 class="card-title font-weight-bold mb-0">Cash Distributed By Regions</h4>
            <div class="year-filter">
              <select class="form-control" [(ngModel)]="selectedYear" (change)="onYearChange($event)">
                <option *ngFor="let year of availableYears" [value]="year">{{ year }}</option>
              </select>
            </div>
          </div>
          <!-- AmCharts 5 container -->
          <div #budgetChartDiv style="width: 100%; height: 350px;"></div>
        </div>
      </div>
    </div>
    
    <div class="col-md-4 mb-3">
      <div class="card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-3">
            <h4 class="card-title font-weight-bold mb-0">Activities Status</h4>
            <div class="region-filter">
              <select class="form-control form-control-sm" [(ngModel)]="selectedRegion" (change)="onRegionChange($event)">
                <option value="all">All Regions</option>
                <option *ngFor="let region of availableRegions.slice(1)" [value]="region">
                  {{ region }}
                </option>
              </select>
            </div>
          </div>
          <!-- AmCharts 5 container -->
          <div #activityStatusChartDiv style="width: 100%; height: 300px;"></div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Charts Row 2 -->
  <div class="row mb-4">
    <div class="col-md-8 mb-3">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title font-weight-bold mb-3">Progress Trend</h4>
          <!-- AmCharts 5 container -->
          <div #progressTrendChartDiv style="width: 100%; height: 350px;"></div>
        </div>
      </div>
    </div>
    
    <div class="col-md-4 mb-3">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title font-weight-bold mb-3">Geographic Coverage</h4>
          <!-- AmCharts 5 container -->
          <div #geographicCoverageChartDiv style="width: 100%; height: 350px;"></div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Charts Row 3 -->
  <div class="row">
    <div class="col-md-12 mb-3">
      <div class="card">
        <div class="card-body">
          <h4 class="card-title font-weight-bold mb-3">Beneficiaries</h4>
          <!-- AmCharts 5 container -->
          <div #beneficiariesChartDiv style="width: 100%; height: 350px;"></div>
        </div>
      </div>
    </div>
  </div>
</div> 