.hierarchical-report-container {
  margin-bottom: 2rem;
}

.cursor-pointer {
  cursor: pointer;
}

// Group Card Styling
.card-header {
  .bi {
    font-size: 1.25rem;
    transition: transform 0.2s ease;
  }
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.03);
  }
}

// Project Item Styling
.project-item {
  transition: background-color 0.2s ease;
  
  .bi {
    font-size: 1.1rem;
    transition: transform 0.2s ease;
  }
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.01);
  }
}

// Intervention Item Styling
.intervention-item {
  transition: background-color 0.2s ease;
  
  .bi {
    font-size: 1rem;
    transition: transform 0.2s ease;
  }
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.01);
  }
}

// Background colors for expanded items
.bg-light-primary {
  background-color: rgba(13, 110, 253, 0.05);
}

.bg-light-success {
  background-color: rgba(25, 135, 84, 0.05);
}

// Progress bars
.progress {
  background-color: rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border-radius: 0.25rem;
}

// Tables in activities
.activities-table {
  table {
    font-size: 0.9rem;
    
    th {
      background-color: #f8f9fa;
      font-weight: 600;
    }
  }
}

// Animation for toggle icons
.bi-plus-square, .bi-dash-square,
.bi-plus-circle, .bi-dash-circle,
.bi-plus-circle-fill, .bi-dash-circle-fill {
  transition: transform 0.2s ease;
}

[class*="bi-plus"] {
  &:hover {
    transform: scale(1.1);
  }
}

[class*="bi-dash"] {
  &:hover {
    transform: scale(1.1);
  }
}

// Custom progress bar colors
.progress-bar {
  &.bg-success {
    background-color: #198754;
  }
  
  &.bg-warning {
    background-color: #ffc107;
  }
  
  &.bg-danger {
    background-color: #dc3545;
  }
}

// Export buttons
.export-buttons {
  .btn {
    i {
      font-size: 1rem;
    }
  }
}

// Add styles for dynamic indicators section

.report-content {
  h6 {
    margin-bottom: 1rem;
    font-weight: 600;
  }
}

.dynamic-indicators-section {
  background-color: rgba(0, 123, 255, 0.04);
  border-radius: 8px;
  padding: 20px;
  margin-top: 20px;
  border-left: 4px solid #007bff;
  
  h6 {
    color: #007bff;
    font-weight: 600;
    margin-bottom: 15px;
  }
  
  .table {
    background-color: white;
    
    th {
      background-color: #f8f9fa;
      color: #495057;
      font-weight: 600;
    }
  }
}

.debug-info {
  border-left: 4px solid #dc3545;
  background-color: rgba(220, 53, 69, 0.03);
  border-radius: 8px;
  padding: 15px;
  
  h6 {
    font-weight: 600;
    margin-bottom: 12px;
  }
  
  .badge {
    font-size: 0.75rem;
  }
  
  strong {
    font-weight: 600;
  }
  
  .small {
    font-size: 0.85rem;
    line-height: 1.5;
  }
}

// Structured Data Section Styles
.structured-data {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding-top: 20px;
  
  h6.text-primary {
    color: #007bff;
    margin-bottom: 15px;
    font-weight: 600;
  }
  
  .accordion {
    .accordion-item {
      border-radius: 4px;
      margin-bottom: 8px;
      border: 1px solid rgba(0, 0, 0, 0.125);
      overflow: hidden;
      
      .accordion-header {
        .accordion-button {
          font-weight: 500;
          color: #495057;
          background-color: #f8f9fa;
          padding: 10px 15px;
          
          &:not(.collapsed) {
            background-color: rgba(0, 123, 255, 0.1);
            color: #007bff;
          }
          
          &:focus {
            box-shadow: none;
            border-color: rgba(0, 123, 255, 0.25);
          }
        }
      }
      
      .accordion-body {
        padding: 15px;
      }
    }
  }
  
  // Info and Progress Cards
  .card {
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    
    .card-header {
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      padding: 12px 15px;
      
      h6 {
        font-weight: 600;
        margin: 0;
      }
    }
    
    .card-body {
      padding: 15px;
    }
    
    .table-sm {
      margin-bottom: 0;
      
      th {
        font-weight: 500;
        color: #495057;
        width: 40%;
      }
      
      td {
        color: #212529;
        
        small.text-muted {
          font-size: 0.8rem;
        }
      }
    }
  }
}

// Custom card header background colors
.bg-light-info {
  background-color: rgba(13, 202, 240, 0.1);
  color: #0dcaf0;
}

.bg-light-success {
  background-color: rgba(25, 135, 84, 0.1);
  color: #198754;
} 