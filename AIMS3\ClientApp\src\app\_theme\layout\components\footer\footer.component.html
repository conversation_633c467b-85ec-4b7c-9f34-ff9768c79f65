<ng-container *ngIf="appFooterContainerCSSClass">
    <!--begin::Footer container-->
    <div class="app-container" [ngClass]="appFooterContainerCSSClass">
        <ng-container *ngTemplateOutlet="footerView"></ng-container>
    </div>
    <!--end::Footer container-->
</ng-container>

<ng-container *ngIf="!appFooterContainerCSSClass">
    <ng-container *ngTemplateOutlet="footerView"></ng-container>
</ng-container>

<ng-template #footerView>
    <!--begin::Copyright-->
    <div class="text-dark order-2 order-md-1">
        <span class="text-muted fw-semibold me-1">&copy; {{currentDateStr}}</span>
        <a href="https://undpima.org/" target="_blank" class="text-gray-800 text-hover-primary">AIMS 3.0</a>
    </div>
    <!--end::Copyright-->

    <!--begin::Menu-->
    <ul class="menu menu-gray-600 menu-hover-primary fw-semibold order-1">
        <li class="menu-item">
            <a href="https://www.undp.org/afghanistan" target="_blank" class="menu-link px-2" title="UNDP Afghanistan">UNDP</a>
        </li>
    </ul>
    <!--end::Menu-->
</ng-template>
