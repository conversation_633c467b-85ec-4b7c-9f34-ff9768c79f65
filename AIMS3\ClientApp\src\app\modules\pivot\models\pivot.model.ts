import { ProfileInfo } from "../../data-entry/models/profile.model";

export interface IPivot {
    id: number;
    name: string;
    isTarget: boolean;
    profId: number;
    order: number;
    prof?: ProfileInfo;
    pivotColumns?: string;          // column states
    filtersApplied?: string;
}

export class Pivot implements IPivot {
    constructor(
        public id: number,
        public name: string,
        public profId: number,
        public isTarget: boolean,
        public order: number,
        public pivotColumns?: string,
        public filtersApplied?: string
    ) { }
}