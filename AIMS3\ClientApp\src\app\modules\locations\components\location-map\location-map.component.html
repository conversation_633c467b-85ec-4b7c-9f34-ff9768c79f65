<aims-working *ngIf="loading" [label]="'Loading map...'"></aims-working>
<div id="map" style="width:100%; height:100%;">
    <div class="notice bg-light-primary rounded border-primary border border-dashed fs-8 fw-semibold" *ngIf="editMode">
        Right click to add a point from the map directly.
    </div>
</div>

<div id="mapContextMenu" class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown py-lg-1 w-lg-225px">
    <div class="menu-item menu-lg-down-accordion fs-8 text-gray-800 py-1 px-3">
        Lat: {{ centerPosition.lat }}<br/>Lon: {{ centerPosition.lon }}
    </div>
    <div class="separator mb-3 opacity-75"></div>
    <div class="menu-item menu-lg-down-accordion">
        <a class="menu-link py-3" (click)="onAddMovePoint()">
            <span class="menu-icon">
                <span [inlineSVG]="'./assets/media/icons/duotune/general/marker.svg'" class="svg-icon svg-icon-3"></span>
            </span><span class="menu-title">{{ mainMarker ? 'Move to this' : 'Add this' }} point</span>
        </a>
    </div>
</div>