<!-- Loading spinner -->
<div *ngIf="loading" class="d-flex justify-content-center my-5">
  <div class="spinner-border text-primary" role="status">
    <span class="visually-hidden">Loading statistics...</span>
  </div>
</div>

<!-- Error message -->
<div *ngIf="error" class="alert alert-danger mb-4">
  {{ error }}
  <button class="btn btn-sm btn-light ms-2" (click)="loadStatistics()">Retry</button>
</div>

<!-- Database test info -->
<div *ngIf="dbTestResult && !dbTestResult.tablesExist" class="alert alert-warning mb-4">
  <strong>Note:</strong> Could not connect to the database. Using sample data instead.
</div>

<div *ngIf="dbTestResult && dbTestResult.tablesExist && dbTestResult.outputsCount === 0 && dbTestResult.categoriesCount === 0" class="alert alert-info mb-4">
  <strong>Note:</strong> Connected to database but no real data found. Using sample data for display purposes.
</div>

<!-- Statistics cards -->
<div class="row mb-4" *ngIf="!loading && !error">
  <div class="col-md-3 mb-3">
    <div class="card stats-card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <h6 class="card-title mb-0">Total Outputs</h6>
          <i class="fas fa-th-large text-primary icon-bg"></i>
        </div>
        <h2 class="mb-2">{{ statistics?.totalOutputs || 0 }}</h2>
        <div class="progress progress-sm">
          <div class="progress-bar bg-primary" role="progressbar" 
               [style.width]="'100%'" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="col-md-3 mb-3">
    <div class="card stats-card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <h6 class="card-title mb-0">Total Categories</h6>
          <i class="fas fa-list-alt text-success icon-bg"></i>
        </div>
        <h2 class="mb-2">{{ statistics?.totalCategories || 0 }}</h2>
        <div class="progress progress-sm">
          <div class="progress-bar bg-success" role="progressbar" 
               [style.width]="'100%'" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="col-md-3 mb-3">
    <div class="card stats-card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <h6 class="card-title mb-0">Total Beneficiaries</h6>
          <i class="fas fa-users text-info icon-bg"></i>
        </div>
        <h2 class="mb-2">{{ statistics?.totalBeneficiaries || 0 }}</h2>
        <div class="progress progress-sm">
          <div class="progress-bar bg-info" role="progressbar" 
               [style.width]="'100%'" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
    </div>
  </div>
  
  <div class="col-md-3 mb-3">
    <div class="card stats-card">
      <div class="card-body">
        <div class="d-flex justify-content-between align-items-center mb-2">
          <h6 class="card-title mb-0">Progress Rate</h6>
          <i class="fas fa-chart-line text-warning icon-bg"></i>
        </div>
        <h2 class="mb-2">{{ statistics?.progressRate || 0 }}%</h2>
        <div class="progress progress-sm">
          <div class="progress-bar bg-warning" role="progressbar" 
               [style.width]="(statistics?.progressRate || 0) + '%'" [attr.aria-valuenow]="statistics?.progressRate || 0" aria-valuemin="0" aria-valuemax="100"></div>
        </div>
      </div>
    </div>
  </div>
</div> 