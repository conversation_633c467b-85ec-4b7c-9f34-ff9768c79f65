import { AfterViewInit, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewEncapsulation } from '@angular/core';
import Point from '@arcgis/core/geometry/Point';
import Graphic from '@arcgis/core/Graphic';
import Map from '@arcgis/core/Map';
import PopupTemplate from '@arcgis/core/PopupTemplate';
import PictureMarkerSymbol from '@arcgis/core/symbols/PictureMarkerSymbol';
import SimpleMarkerSymbol from '@arcgis/core/symbols/SimpleMarkerSymbol';
import MapView from '@arcgis/core/views/MapView';
import { loadModules } from 'esri-loader';
import { Subscription } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import { MessageService } from '../../../../shared/services/message.service';
import { formatNum } from '../../../../shared/utilities';
import { ThemeModeService } from '../../../../_theme/partials/layout/theme-mode-switcher/theme-mode.service';
import { CommunityList } from '../../models/community.model';

@Component({
    selector: 'location-map',
    templateUrl: './location-map.component.html',
    styleUrls: ['./location-map.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class LocationMapComponent implements OnInit, AfterViewInit, OnDestroy {
    loading: boolean = false;

    private mapConfig = environment.arcgisConfig;
    map: Map;
    mapView: MapView;
    mainMarker: Graphic;

    @Output() initialized: EventEmitter<LocationMapComponent> = new EventEmitter<LocationMapComponent>();
    @Input() point: number[];
    @Input() locName: string = '';
    @Input() loc: string[];
    @Input() editMode: boolean = false;
    @Output() onGpsUpdate = new EventEmitter<number[]>();

    centerPosition = { lat: 33.885, lon: 67.434 };

    private popupTemplate = new PopupTemplate({
        title: '{name}',
        content: '<p class="fs-8 text-muted">{loc}</p>{lat}, {lon}'
    });

    private markerIcon = new PictureMarkerSymbol({
        url: "/assets/media/icons/duotune/maps/marker.svg",
        width: '35px',
        height: '35px',
        yoffset: '-10px' // move the marker little bit up, so its tip is on the coords
    });

    contextMenu = document.querySelector('#mapContextMenu');
    private subscriptions: Subscription[] = [];
    constructor(
        private themeService: ThemeModeService,
        //private locationsService: LocationService,
        //private sharedService: SharedService,
        private messageService: MessageService
    ) {

    }

    async ngOnInit() {
        this.loading = true;

        let baseMap: string = 'streets-navigation-vector';

        if (this.themeService.mode.value === 'dark') {
            baseMap = 'streets-night-vector';
            this.mapConfig.css = this.mapConfig.cssDark;
        }

        // const [Map, MapView, popupTemp] = 
        await loadModules(['esri/Map', 'esri/views/MapView', 'esri/Graphic',
            'esri/PopupTemplate', 'esri/widgets/Popup'], this.mapConfig);

        this.map = new Map({ basemap: baseMap });

        const point = new Point({
            latitude: this.point[0],
            longitude: this.point[1]
        });

        this.mapView = new MapView({
            container: 'map',
            map: this.map,
            center: [this.centerPosition.lon, this.centerPosition.lat],
            zoom: 5
        });

        // bind context menu
        if (this.editMode)
            this.addContextMenu();

        if (!this.point[0]) {
            this.loading = false;
            return;
        }

        this.mainMarker = new Graphic({
            geometry: point,
            symbol: this.markerIcon,
            attributes: {
                name: this.locName || '-',
                loc: this.loc.join(', '),
                lat: this.point[0],
                lon: this.point[1]
            },

            popupTemplate: this.popupTemplate
        });

        this.mapView.when(() => {
            setTimeout(() => {
                this.mapView.center = point;
                this.flyTo(point, 10);
                this.mapView.graphics.add(this.mainMarker);
            }, 2000);
        });

        this.loading = false;
    }

    ngAfterViewInit(): void {
        this.initialized.emit(this);
        this.contextMenu = document.querySelector('#mapContextMenu');
    }

    flyTo(point: Point, zoom: number = 10): void {
        // Fly to the point with animation and increase the zoom level
        this.mapView.goTo({
            target: point,
            zoom: zoom
        }, {
            animate: true,
            duration: 2000,
            easing: "ease-in-out" // linear, ease, ease-in, ease-out, ease-in-out
        });
    }

    markers = [];
    removeMarkers(): void {
        this.markers.forEach((m) => {
            if (this.mapView.graphics.includes(m))
                this.mapView.graphics.remove(m);
        });
    }

    refreshMainMarker(locName: string, distProv: string[], gps: number[]): void {
        if (this.mainMarker && this.mapView.graphics.includes(this.mainMarker)) {
            this.mapView.graphics.remove(this.mainMarker);
        }

        const newPoint = new Point({
            latitude: gps[0],
            longitude: gps[1]
        });

        this.mapView.center = newPoint;
        this.flyTo(newPoint, 10);

        this.mainMarker = new Graphic({
            geometry: newPoint,
            symbol: this.markerIcon,
            attributes: {
                name: locName || '-',
                loc: distProv.join(', '),
                lat: gps[0],
                lon: gps[1]
            },
            popupTemplate: this.popupTemplate
        });
        this.mapView.graphics.add(this.mainMarker);

        // remove other markers
        //this.removeMarkers();
    }

    addMarkers(similarLocs: CommunityList[], radius: number): void {
        // if any previous markers are there, remove them
        this.removeMarkers();

        similarLocs.forEach((loc: CommunityList) => {
            let symbolColor = [94, 98, 120];
            if (loc.distance <= radius)
                symbolColor = [217, 33, 78];

            const _symbol = new SimpleMarkerSymbol({
                size: 6,
                outline: {
                    width: '1px',
                    color: symbolColor
                },
                color: symbolColor.concat(0.5)
            });

            const point = new Point({
                latitude: loc.gpsLat,
                longitude: loc.gpsLon
            });

            const popup = new PopupTemplate({
                title: '{name}',
                content: `<p class="fs-8 text-muted">{loc}</p>{lat}, {lon}
                <br/>Distance from location: <span class="text-danger">{distance}m</span>`
            });

            const marker = new Graphic({
                geometry: point,
                symbol: _symbol,
                attributes: {
                    name: loc.name,
                    loc: loc.distName + ', ' + loc.provName,
                    lat: loc.gpsLat,
                    lon: loc.gpsLon,
                    distance: formatNum(loc.distance, true)
                },
                popupTemplate: popup
            });

            this.markers.push(marker);
            this.mapView.graphics.add(marker);
        });
    }

    addContextMenu(): void {
        // Add a pointer-down event handler to the LayerView
        this.mapView.on("pointer-down", (event: any) => {
            if (event.button === 2) {
                // Prevent the default context menu from appearing
                event.stopPropagation();

                // Convert the screen coordinates to map coordinates
                const screenPoint = {
                    x: event.x,
                    y: event.y
                };
                const mapPoint = this.mapView.toMap(screenPoint);

                // store point in the center position variable
                this.centerPosition.lat = mapPoint.latitude;
                this.centerPosition.lon = mapPoint.longitude;

                // show context meu
                this.contextMenu.setAttribute('style', `top: ${event.y + 1}px; left: ${event.x + 1}px`);
                this.contextMenu.classList.add('show');

                // Open the popup at the map point
                //this.mapView.popup.open({
                //    location: mapPoint,
                //    content: coordinates,
                //    featureMenuOpen: true,
                //});
            } else {
                this.contextMenu.classList.remove('show');
                this.contextMenu.setAttribute('style', null);
            }
        });
    }

    onAddMovePoint(): void {
        // emit to the form, the marker will be refreshed by parent
        // validation will be done after emit on the form itself
        this.onGpsUpdate.emit([+this.centerPosition.lat.toFixed(8),
        +this.centerPosition.lon.toFixed(8)]);

        // hide the menu
        this.contextMenu.classList.remove('show');
        this.contextMenu.setAttribute('style', null);
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}