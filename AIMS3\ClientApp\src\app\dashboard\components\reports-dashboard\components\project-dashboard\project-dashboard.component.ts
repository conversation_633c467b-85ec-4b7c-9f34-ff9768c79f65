import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgApexchartsModule } from 'ng-apexcharts';
import { 
  ReportData, 
  ReportFilter,
  Project,
  ProjectGroup
} from '../../models/reports.model';

@Component({
  selector: 'app-project-dashboard',
  standalone: true,
  imports: [CommonModule, NgApexchartsModule],
  templateUrl: './project-dashboard.component.html',
  styleUrls: ['./project-dashboard.component.scss']
})
export class ProjectDashboardComponent implements OnInit, OnChanges {
  @Input() reportData: ReportData;
  @Input() filters: ReportFilter;
  @Input() projects: Project[] = [];
  @Input() projectGroups: ProjectGroup[] = [];
  
  // For template use
  Math = Math;
  
  // Chart options
  budgetChartOptions: any;
  expenditureChartOptions: any;
  statusChartOptions: any;
  implementationChartOptions: any;
  
  constructor() { }

  ngOnInit(): void {
    this.initializeCharts();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.reportData?.currentValue || 
        changes.projects?.currentValue || 
        changes.projectGroups?.currentValue) {
      this.initializeCharts();
    }
  }

  private initializeCharts(): void {
    this.initBudgetChart();
    this.initExpenditureChart();
    this.initStatusChart();
    this.initImplementationChart();
  }

  private initBudgetChart(): void {
    if (!this.projects || this.projects.length === 0) return;
    
    // Get top 5 projects by budget
    const topProjects = [...this.projects]
      .sort((a, b) => b.budget - a.budget)
      .slice(0, 5);
    
    this.budgetChartOptions = {
      series: [{
        name: 'Budget',
        data: topProjects.map(p => p.budget)
      }],
      chart: {
        type: 'bar',
        height: 350,
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          borderRadius: 4,
          horizontal: true,
          barHeight: '70%'
        }
      },
      title: {
        text: 'Top Projects by Budget',
        align: 'left'
      },
      colors: ['#4CAF50'],
      dataLabels: {
        enabled: true,
        formatter: function (val) {
          return '$' + Number(val).toLocaleString();
        },
        offsetX: 15
      },
      xaxis: {
        categories: topProjects.map(p => p.name),
        labels: {
          formatter: function (val) {
            return '$' + Number(val).toLocaleString();
          }
        }
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return '$' + val.toLocaleString();
          }
        }
      }
    };
  }

  private initExpenditureChart(): void {
    if (!this.projectGroups || this.projectGroups.length === 0) return;
    
    this.expenditureChartOptions = {
      series: this.projectGroups.map(g => ({
        name: g.name,
        data: [g.totalCashDistributed]
      })),
      chart: {
        type: 'bar',
        height: 350,
        stacked: true,
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          horizontal: true
        }
      },
      title: {
        text: 'Cash Distributed by Project Group',
        align: 'left'
      },
      xaxis: {
        categories: ['Cash Distributed'],
        labels: {
          formatter: function (val) {
            return '$' + Number(val).toLocaleString();
          }
        }
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return '$' + val.toLocaleString();
          }
        }
      },
      fill: {
        opacity: 1
      },
      legend: {
        position: 'top',
        horizontalAlign: 'left'
      }
    };
  }

  private initStatusChart(): void {
    if (!this.projects || this.projects.length === 0) return;
    
    // Count projects by status
    const statusCounts = {
      active: 0,
      completed: 0,
      planned: 0,
      suspended: 0
    };
    
    this.projects.forEach(project => {
      statusCounts[project.status]++;
    });
    
    this.statusChartOptions = {
      series: [
        statusCounts.active,
        statusCounts.completed,
        statusCounts.planned,
        statusCounts.suspended
      ],
      chart: {
        type: 'donut',
        height: 350
      },
      labels: ['Active', 'Completed', 'Planned', 'Suspended'],
      title: {
        text: 'Project Status Distribution',
        align: 'left'
      },
      colors: ['#4CAF50', '#2196F3', '#FF9800', '#F44336'],
      legend: {
        position: 'bottom'
      },
      dataLabels: {
        enabled: true,
        formatter: function (val, opts) {
          return opts.w.globals.series[opts.seriesIndex];
        }
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val + ' projects';
          }
        }
      }
    };
  }

  private initImplementationChart(): void {
    if (!this.projects || this.projects.length === 0) return;
    
    // Get top projects by implementation progress
    const projectsProgress = [...this.projects]
      .sort((a, b) => b.progress - a.progress)
      .slice(0, 8);
    
    this.implementationChartOptions = {
      series: [{
        name: 'Progress',
        data: projectsProgress.map(p => p.progress)
      }],
      chart: {
        height: 350,
        type: 'radar',
        toolbar: {
          show: false
        }
      },
      title: {
        text: 'Project Implementation Progress',
        align: 'left'
      },
      xaxis: {
        categories: projectsProgress.map(p => p.name),
        labels: {
          show: true,
          style: {
            fontSize: '12px'
          }
        }
      },
      yaxis: {
        show: false,
        max: 100
      },
      fill: {
        opacity: 0.7
      },
      markers: {
        size: 5,
        hover: {
          size: 7
        }
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val + '%';
          }
        }
      }
    };
  }

  formatNumber(num: number): string {
    return num ? num.toLocaleString() : '0';
  }

  formatCurrency(num: number): string {
    return num ? '$' + num.toLocaleString() : '$0';
  }

  calculateAverage(property: string): number {
    if (!this.projects || this.projects.length === 0) return 0;
    
    const sum = this.projects.reduce((acc, project) => acc + project[property], 0);
    return Math.round(sum / this.projects.length);
  }
  
  getTotalBudget(): number {
    if (!this.projects || this.projects.length === 0) return 0;
    return this.projects.reduce((acc, project) => acc + project.budget, 0);
  }
  
  getTotalCashDistributed(): number {
    if (!this.projects) return 0;
    return this.projects.reduce((acc, project) => acc + project.cashDistributed, 0);
  }
  
  getUtilizationRate(): number {
    const totalBudget = this.getTotalBudget();
    const totalCashDistributed = this.getTotalCashDistributed();
    return totalBudget ? Math.round((totalCashDistributed / totalBudget) * 100) : 0;
  }
} 