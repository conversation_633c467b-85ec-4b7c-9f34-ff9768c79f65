<div class="card card-custom card-stretch blockui">
    <aims-working *ngIf="working"></aims-working>
    <!--begin::Form-->
    <form id="user_personal_info" class="form" [formGroup]="form" (ngSubmit)="form.valid && save()">
        <!--begin::Header-->
        <div class="card-header">
            <div class="card-title align-items-start flex-column">
                <h3 class="card-label fw-bolder fs-3 mb-1">Personal information</h3>
                <span class="text-muted fw-bold fs-7">Update your personal information</span>
            </div>
            <div class="card-toolbar">
                <button type="submit" class="btn btn-primary" [disabled]="!form.valid || saving">
                    <span class="indicator-label" *ngIf="!saving; else btnSpinner">Save changes</span>
                    <ng-template #btnSpinner>
                        <span class="indicator-progress" style="display: block">
                            Please wait...
                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                        </span>
                    </ng-template>
                </button>
            </div>
        </div>
        <!--end::Header-->
        <!--begin::Body-->
        <div class="card-body">
            <div class="form-group row">
                <label class="col-xl-3 col-lg-3 col-form-label required">Username</label>
                <div class="col-lg-4 col-xl-3">
                    <input name="username" class="form-control" type="text" formControlName="username">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-xl-3 col-lg-3 col-form-label required">Designation</label>
                <div class="col-lg-4 col-xl-3">
                    <input name="firstName" class="form-control" type="text" formControlName="firstName"
                            [ngClass]="{ 'is-invalid': form.controls['firstName'].dirty && form.controls['firstName'].invalid }" />
                    <ng-container [ngTemplateOutlet]="formError"
                                    [ngTemplateOutletContext]="{
                                validation: 'required',
                                message: 'Designation is required.',
                                control: form.controls['firstName']
                                }"></ng-container>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-xl-3 col-lg-3 col-form-label required">Implementation partner</label>
                <div class="col-lg-9 col-xl-6">
                    <input name="orgName" class="form-control" type="text" formControlName="orgName">
                </div>
            </div>
            <div class="form-group row">
                <label class="col-xl-3 col-lg-3 col-form-label required">Primary email</label>
                <div class="col-lg-9 col-xl-6">
                    <div class="input-group">
                        <div class="input-group-prepend"><span class="input-group-text"><i class="la la-at"></i></span></div>
                        <input name="email" type="email" class="form-control" placeholder="Email" formControlName="email"
                                [ngClass]="{ 'is-invalid': form.controls['email'].dirty && form.controls['email'].invalid }" />
                    </div>
                    <ng-container [ngTemplateOutlet]="formError"
                                    [ngTemplateOutletContext]="{
                                    validation: 'required',
                                    message: 'Email address is required.',
                                    control: form.controls['email']
                                    }"></ng-container>
                    <ng-container [ngTemplateOutlet]="formError"
                                    [ngTemplateOutletContext]="{
                                    validation: 'email',
                                    message: 'Invalid email address.',
                                    control: form.controls['email']
                                    }"></ng-container>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-xl-3 col-lg-3 col-form-label">
                    Emails (secondary)
                    <i class="la la-info-circle icon-md text-muted ms-2" ngbTooltip="To enter multiple emails, press Enter between each one."></i>
                </label>
                <div class="col-lg-9 col-xl-6">
                    <select id="emails" class="form-select" data-control="select2" data-tags="true"
                            multiple data-placeholder="Enter email(s)"></select>
                    <div class="fv-plugins-message-container" *ngIf="f.emailAlt.dirty && f.emailAlt.invalid">
                        <div class="fv-help-block">
                            <span role="alert">At least one email address is not valid.</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--end::Body-->
    </form>
    <!--end::Form-->
</div>


<ng-template #formError let-control="control" let-message="message" let-validation="validation">
    <ng-container *ngIf="control.hasError(validation) && control.dirty">
        <div class="fv-plugins-message-container">
            <div class="fv-help-block">
                <span role="alert">{{ message }}</span>
            </div>
        </div>
    </ng-container>
</ng-template>