import { Component, EventEmitter, HostBinding, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { lastValueFrom, Subject, Subscription } from 'rxjs';
import { OperationResult } from '../../../../../shared/enums';
import { MessageService } from '../../../../../shared/services/message.service';
import { AppUtilities } from '../../../../../shared/utilities';
import { ModalConfig } from '../../../../../_theme/partials';
import { Category } from '../../../../admin/models/category.model';
import { Profile, ProfileInfo } from '../../../models/profile.model';
import { ProfileService } from '../../../services/profile.service';

@Component({
    selector: 'profile-form-modal',
    templateUrl: './profile-form.component.html',
    styleUrls: ['./profile-form.component.scss']
})
export class InterventionProfileFormComponent implements OnInit, OnDestroy {
    @HostBinding('class') classList = 'menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown blockui menu-modal';
    private modalWidth: number = 400;

    working: boolean = false;

    profile: Profile;
    @Input() outputs = [];

    form: FormGroup;

    modalConfig: ModalConfig = {
        modalTitle: 'Add new intervention profile',
        cancelButtonLabel: 'Cancel',
        doneButtonLabel: 'Add',
        disableDoneButton: true,
        options: { size: 'md' }
    };
    @Output() done = new EventEmitter<ProfileInfo>();

    modalElRef: any;
    dismissEvent: any;

    private subscriptions: Subscription[] = [];
    constructor(
        private profService: ProfileService,
        private messageService: MessageService
    ) {
        this.profile = new Profile(0, 0, '', '');
    }

    ngOnInit() {
        if (this.modalConfig.options.size === 'sm') {
            this.classList += ' w-200px';
            this.modalWidth = 200;
        } else if (this.modalConfig.options.size === 'lg') {
            this.classList += ' w-600px';
            this.modalWidth = 600;
        } else
            this.classList += ' w-400px';

        this.modalElRef = document.querySelector('#profileForm');
    }

    open(pEvent: any): void {
        if (this.dismissEvent) {
            this.close();
            return;
        }

        this.initForm();

        const target = pEvent.target.closest('.nav-item');
        const targetRect = target.getBoundingClientRect();

        let y = targetRect.bottom;
        if (target.clientHeight > 35) y = y - target.clientHeight+35;
        
        let x = window.innerWidth - targetRect.right-5;
        if (targetRect.right < this.modalWidth)
            x = window.innerWidth - (this.modalWidth + targetRect.left);
        this.modalElRef.setAttribute('style', `
            z-index: 105;
            position: fixed;
            inset: 0px 0px auto auto;
            margin: 0px;
            transform: translate3d(-${x}px, ${y}px, 0px);
        `);
        
        this.modalElRef.classList.add('show');
        
        // add event to dismiss outside the modal
        this.dismissEvent = (e) => {
            if (target.contains(e.target) || this.modalElRef.contains(e.target) ||
                e.target.className.indexOf('swal2') > -1)
                return;
            this.close();
        }
        document.addEventListener('click', this.dismissEvent, true);
    }
    
    // convenient getter for easy access to form fields
    get f() {
        return this.form.controls;
    }

    initForm() {
        this.form = new FormGroup({
            id: new FormControl({ value: this.profile.id, disabled: true }),
            catId: new FormControl(this.profile.categoryId, [Validators.required, Validators.min(1)]),
            name: new FormControl(this.profile.name, Validators.required),
            abbrv: new FormControl(this.profile.abbreviation,
                [Validators.required, Validators.minLength(3), Validators.maxLength(10)]),
            desc: new FormControl(this.profile.description)
        });

        if (this.profile.id > 0) {
            this.modalConfig.modalTitle = 'Edit intervention profile';
            this.modalConfig.doneButtonLabel = 'Save';
        } else {
            this.modalConfig.modalTitle = 'Add new intervention profile';
            this.modalConfig.doneButtonLabel = 'Add';

            // bind name to generate abbreviation
            this.f.name.valueChanges.subscribe((val) => {
                this.f.abbrv.setValue(AppUtilities().generateAbbreviation(val));
            });
        }

        this.exists = false;
        this.subscriptions.push(
            this.form.valueChanges.subscribe((res) => {
                this.modalConfig.disableDoneButton = !this.form.valid;
                this.exists = false;
            })
        );
    }

    exists: boolean = false;
    async save(): Promise<boolean> {
        const result = new Subject<boolean>();
        this.modalConfig.working = true;

        const _profile = new Profile(this.profile.id, +this.f.catId.value,
            this.f.name.value, this.f.abbrv.value, this.f.desc.value);

        let cat: Category;
        for (let i = 0; i < this.outputs.length; i++) {
            cat = this.outputs[i].categories.find(c => c.id == _profile.categoryId);
            if (cat)
                break;
        }
        
        let updatedProfile = new ProfileInfo(this.profile.id, +this.f.catId.value,
            cat, this.f.name.value, this.f.abbrv.value, this.f.desc.value);

        this.exists = false;

        // add or update profile
        if (this.profile.id > 0) {
            this.subscriptions.push(this.profService.updateProfile(_profile).subscribe({
                //next: (res) => {
                //    //if (res && res.result === OperationResult.Succeeded) {
                //    //    updatedProfile = res.data;
                //    //}
                //},
                error: (err) => {
                    this.modalConfig.working = false;
                    console.log(err);
                    this.exists = err.error.srProfExists;
                    result.next(false);
                    result.complete();
                },
                complete: () => {
                    this.messageService.success('The intervention profile has been updated successfully.');
                    this.done.emit(updatedProfile);

                    this.modalConfig.working = false;
                    this.close();
                    result.next(true);
                    result.complete();
                }
            }));
        } else {
            this.subscriptions.push(this.profService.addProfile(_profile).subscribe({
                next: (res) => {
                    if (res && res.result === OperationResult.Succeeded) {
                        updatedProfile = res.data;
                        updatedProfile.category = cat;
                    }
                },
                error: (err) => {
                    this.modalConfig.working = false;
                    console.log(err);
                    this.exists = err.error.srProfExists;
                    result.next(false);
                    result.complete();
                },
                complete: () => {
                    this.messageService.success('The intervention profile has been added successfully.');
                    this.done.emit(updatedProfile);

                    this.modalConfig.working = false;
                    this.close();
                    result.next(true);
                    result.complete();
                }
            }));
        }
        
        return await lastValueFrom(result.asObservable());
    }

    duplicateProfile(): void {
        if (this.form.invalid) {
            this.messageService.error('Please enter the required fields.');
            return;
        }

        let profile = new Profile(this.profile.id, +this.f.catId.value,
            this.f.name.value, this.f.abbrv.value, this.f.desc.value);

        this.messageService.confirmMessage('Duplicate Intervention Profile', `A copy of the Intervention Profile will be created.`,
            () => {
                this.working = true;
                let _profile: ProfileInfo;

                this.subscriptions.push(this.profService.duplicateProfile(profile).subscribe({
                    next: (res) => {
                        // for emitting to parent
                        if (res.result === OperationResult.Succeeded)
                            _profile = res.data;
                    },
                    error: (e) => {
                        console.log(e);
                        this.working = false;
                    },
                    complete: () => {
                        let cat: Category;
                        for (let i = 0; i < this.outputs.length; i++) {
                            cat = this.outputs[i].categories.find(c => c.id == _profile.categoryId);
                            if (cat)
                                break;
                        }

                        // emit to parent
                        _profile.category = cat;
                        this.done.emit(_profile);
                        this.close();
                        this.messageService.success('The profile has been duplicated successfully.');
                        this.working = false;
                    }
                }));
            }, false, 'Duplicate');
    }

    deleteProfile(): void {
        this.messageService.confirmMessage('Confirm Delete', `Are you sure you want to delete this profile?<p class="text-danger mt-3">All your data collected under this profile will be deleted.</p>`,
            () => {
                this.working = true;

                this.subscriptions.push(this.profService.deleteProfile(this.profile.id).subscribe({
                    error: (err) => {
                        this.working = false;
                        console.error(err);
                    },
                    complete: () => {
                        this.profile.id = this.profile.id * -1; // mark deleted
                        this.done.emit(this.profile as ProfileInfo);
                        this.messageService.success('The profile has been deleted successfully.');
                        this.close();
                        this.working = false;
                    }
                })
                );
            }, true, 'Delete');
    }

    close(): void {
        // do not interrupt if saving
        if (!this.working && !this.modalConfig.working)
            this.ngOnDestroy();

        // dismiss the menu modal
        this.modalElRef.classList.remove('show');
        this.modalElRef.setAttribute('style', '');

        // remove any dismiss click event
        if (this.dismissEvent) {
            document.removeEventListener('click', this.dismissEvent, true);
            this.dismissEvent = null;
        }
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
        document.removeEventListener('click', this.dismissEvent, true);
    }
}