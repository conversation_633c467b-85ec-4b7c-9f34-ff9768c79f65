:host {
  height: calc(100vh - 85px);
  width: calc(100% - var(--qs-app-sidebar-width));
  position: absolute;
  display: block;
  top: 50px;
  left: var(--qs-app-sidebar-width);
}

.top-toolbar {
  display: inline-block;
  position: absolute;
  top: -3.15rem;
  left: calc(var(--qs-app-sidebar-width) + 5rem);
  z-index: 999;
}

.app-toolbar {
  top: 50px !important;
  padding: .5rem 2rem;
  opacity: .4;
  transition: opacity ease .5s;
  overflow-x: auto;
  z-index: 99;

  &:hover {
    opacity: 1;
  }
}

@media (min-width: 992px) {
  .app-toolbar {
    height: auto;
  }
}

.filter-toolbar {
  width: 100%;
}

filter-ddl {
  max-width: 130px;
}

input[type="month"] {
  max-width: 100px;
}
