import { environment } from "../../environments/environment";

declare const $: any;

export const getEnumKeyByEnumValue = (myEnum: { [x: string]: any; }, enumValue: any) => {
    let keys = Object.keys(myEnum).filter(x => myEnum[x] == enumValue);
    return keys.length > 0 ? keys[0] : null;
}

export const getDistanceTo = (lon1: number, lat1: number, lon2: number, lat2: number) => {
    let R = 6371; // Radius of the earth in km
    let dLat = (lat2 - lat1) * Math.PI / 180;  // to radians
    let dLon = (lon2 - lon1) * Math.PI / 180;

    let a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
        Math.sin(dLon / 2) * Math.sin(dLon / 2);

    let c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    let d = R * c * 1000; // Distance in meters

    return d;
}


export const formatNum = (num: any, round?: boolean, decPlaces?: number) => {
    if (!num || num === undefined || num.length === 0) return '';

    if (!isNaN(num)) {
        if (round)
            num = Math.round(+num);

        num = `${Number.parseFloat((+num).toFixed(decPlaces || 6))}`.replace(/[.,]0+$/, '');
    }

    return num.toString().replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,");
}

export const colors = (index: number): string => {
    const c = ['#fff', '#007bff', '#6610f2', '#6f42c1', '#dc3545', '#e83e8c', '#fd7e14', '#ffc107', '#28a745',
        '#20c997', '#e6beff', '#46f0f0', '#a70000', '#3cb44b', '#ffe119', '#f032e6', '#bcf60c', '#4363d8',
        '#f58231', '#fabebe', '#911eb4', '#6c757d', '#008080', '#17a2b8', '#9a6324', '#fffac8', '#800000',
        '#2fc45b', '#808000', '#ffd8b1', '#000075', '#eac7ff', '#b27fb2'];
    return c[index];
}

// compress image on the client side
export const compressImage = (base64: string) => {
    const canvas = document.createElement('canvas');
    const img = document.createElement('img');

    return new Promise((resolve, reject) => {
        img.onload = () => {
            let width = img.width;
            let height = img.height;
            const maxHeight = 200;
            const maxWidth = 200;

            if (width > height) {
                if (width > maxWidth) {
                    height = Math.round((height *= maxWidth / width));
                    width = maxWidth;
                }
            } else {
                if (height > maxHeight) {
                    width = Math.round((width *= maxHeight / height));
                    height = maxHeight;
                }
            }
            canvas.width = width;
            canvas.height = height;

            const ctx = canvas.getContext('2d');
            if (ctx)
                ctx.drawImage(img, 0, 0, width, height);

            resolve(canvas.toDataURL('image/jpeg', 0.7));           // image quality 70%
        }

        img.onerror = function (err) {
            reject(err);
        }

        img.src = base64;
    });
}


export const getSize = (fileSize: number): string => {
    let size: string = fileSize + ' B';
    if (fileSize >= 1023 && fileSize <= 1048576)
        size = Math.round(fileSize / 1024) + ' KB';
    else if (fileSize > 1048576 && fileSize <= 1073741824)
        size = Math.round(fileSize / 1048576) + ' MB';
    return size;
}

export const getExt = (fn: string): string => {
    let c: any = /(?:\.([^.]+))?$/.exec(fn);
    if (!c) return '';

    c = c[1];
    c = c.toLowerCase();
    if (c.indexOf('pdf') > -1)
        return 'fa-file-pdf-o';
    else if (c.indexOf('xls') > -1)
        return 'fa-file-excel-o';
    else if (c.indexOf('doc') > -1)
        return 'fa-file-word-o';
    else if (c.indexOf('ppt') > -1 || c.indexOf('pps') > -1)
        return 'fa-file-powerpoint-o';
    else if (c.indexOf('jpg') > -1 || c.indexOf('jpeg') > -1 || c.indexOf('png') > -1)
        return 'fa-file-photo-o'
    else if (c.indexOf('zip') > -1)
        return 'fa-file-archive-o'
    else
        return 'fa-file-o';
}

export const getExtColor = (fn: string): string => {
    let c: any = /(?:\.([^.]+))?$/.exec(fn);
    if (!c) return '';

    c = c[1];
    c = c.toLowerCase();
    if (c.indexOf('pdf') > -1)
        return 'red';
    else if (c.indexOf('xls') > -1)
        return 'green-haze';
    else if (c.indexOf('doc') > -1)
        return 'blue';
    else if (c.indexOf('ppt') > -1 || c.indexOf('pps') > -1)
        return 'red-haze';
    else if (c.indexOf('jpg') > -1 || c.indexOf('jpeg') > -1 || c.indexOf('png') > -1)
        return 'purple-sharp'
    else if (c.indexOf('zip') > -1)
        return 'yellow'
    else
        return 'btn-active-color-primary';
}

export const getExtAndColor = (fn: string): string => {
    return getExt(fn) + ' font-' + getExtColor(fn.replace('?', '')).replace('btn-default', '');
}


// get column text size
export const getColWidth = (text: string, icons: boolean[] = []): number => {
    const widths = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0,
        0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0.2796875,
        0.2765625, 0.3546875, 0.5546875, 0.5546875, 0.8890625,
        0.665625, 0.190625, 0.3328125, 0.3328125, 0.3890625,
        0.5828125, 0.2765625, 0.3328125, 0.2765625, 0.3015625,
        0.5546875, 0.5546875, 0.5546875, 0.5546875, 0.5546875,
        0.5546875, 0.5546875, 0.5546875, 0.5546875, 0.5546875,
        0.2765625, 0.2765625, 0.584375, 0.5828125, 0.584375,
        0.5546875, 1.0140625, 0.665625, 0.665625, 0.721875,
        0.721875, 0.665625, 0.609375, 0.7765625, 0.721875,
        0.2765625, 0.5, 0.665625, 0.5546875, 0.8328125,
        0.721875, 0.7765625, 0.665625, 0.7765625, 0.721875,
        0.665625, 0.609375, 0.721875, 0.665625, 0.94375,
        0.665625, 0.665625, 0.609375, 0.2765625, 0.3546875,
        0.2765625, 0.4765625, 0.5546875, 0.3328125, 0.5546875,
        0.5546875, 0.5, 0.5546875, 0.5546875, 0.2765625, 0.5546875,
        0.5546875, 0.221875, 0.240625, 0.5, 0.221875, 0.8328125,
        0.5546875, 0.5546875, 0.5546875, 0.5546875, 0.3328125, 0.5,
        0.2765625, 0.5546875, 0.5, 0.721875, 0.5, 0.5, 0.5, 0.3546875,
        0.259375, 0.353125, 0.5890625];

    const avg = 0.5279276315789471;
    const fontSize = 14;
    const iconsSize = icons.filter(i => i === true).length * 17;
    const width = Array.from(text).reduce(
        (acc, cur) => acc + (widths[cur.charCodeAt(0)] ?? avg), 0
    ) * fontSize + (65 + iconsSize);
    
    return Math.ceil(width);
}

// get random number between x and y
export const getRndNum = (min: number, max: number, f = 0) => {
    if (f > 0) {
        f = Math.random() * (max - min) + min
        return f.toFixed();
    }

    return Math.floor(Math.random() * (max - min)) + min;
}

/** This function converts and validate all possible date cases from an input string including all of the following cases:
     *  0   '01 2021'       => '01/01/2021'
        1	'10/22'         => '01/10/2022'
        2	'13 Sep 2023'   => '13/09/2023'
        3	'13-sep-2023'   => '13/09/2023'
        4	'Jan 19, 2023'  => '19/01/2023'
        5	'Aug 08 2023'   => '08/08/2023'
        6	'19/08-2021'    => '19/08/2021'
        7	'17 12 21'      => '17/12/2021'
        8	'11/12/2023'    => '11/12/2023'
        9	'31/12/01'      => '31/12/2001'
        10	'21 March 20'   => '21/03/2020'
        11	'21 March 2020' => '21/03/2020'
        12	'21 Mar 2020'   => '21/03/2020'
        13	'Mar 2023'      => '01/03/2023'
        14	'May/2023'      => '01/05/2023'
    */
export const validateDate = (strDate: string, lastDay?: boolean): string => {
    const dateRegex = /(?:(\d{1,2})[-,\/\s])?(\d{1,2})[-,\/\s](\d{2,4})|(\d{1,2})[-,\/\s]([a-z]{3,}|[a-z]{2})[-,\/\s](\d{2,4})|(\d{4})[-,\/\s](\d{1,2})[-,\/\s](\d{1,2})|([a-z]{3,}),?[-,\/\s](\d{1,2}),?[-,\/\s](\d{2,4})|([a-z]{3,}),?[-,\/\s](\d{1,2}),?[-,\/\s](\d{2,4})|([a-z]{3,}),?[-,\/\s](\d{2,4})/i;
    const match = strDate.match(dateRegex);

    if (!match)
        return null;

    match[1] = match[1] || '01';
    match[4] = match[4] || '01';
    match[9] = match[9] || '01';
    match[11] = match[11] || '01';
    match[14] = match[14] || '01';
    match[18] = match[18] || '01';
    match[21] = match[21] || '01';

    let day, month, year;
    if (match[1] && match[2] && match[3]) {
        day = match[1];
        month = match[2];
        year = match[3];
    } else if (match[4] && match[5] && match[6]) {
        day = match[4];
        month = getMonthNumber(match[5]);
        year = match[6];
    } else if (match[7] && match[8] && match[9]) {
        year = match[7];
        month = match[8];
        day = match[9];
    } else if (match[10] && match[11] && match[12]) {
        day = match[11];
        month = getMonthNumber(match[10]);
        year = match[12];
    } else if (match[13] && match[14] && match[15]) {
        day = match[14];
        month = getMonthNumber(match[13]);
        year = match[15];
    } else if (match[16] && match[17] && match[18]) {
        day = match[18];
        month = getMonthNumber(match[16]);
        year = match[17];
    } else if (match[19] && match[20] && match[21]) {
        day = match[21];
        month = getMonthNumber(match[20]);
        year = match[19];
    } else if (match[22] && match[23]) {
        day = '01';
        month = getMonthNumber(match[22]);
        year = match[23];
    } else
        return null;

    year = year.length > 2 ? parseInt(year, 10) : (parseInt(year, 10) + 2000);
    if (year < 2020 || year > 2050)
        return null;

    // if still not a valid date
    const date = day === '01' && lastDay
        ? new Date(year, +month, 0)
        : new Date(year, +month - 1, +day);

    if (date.toString() === 'Invalid Date')
        return null;

    return date.toLocaleDateString('en-GB');
}

export const getMonthNumber = (month): string => {
    const monthMap = {
        jan: '01', feb: '02', mar: '03', apr: '04', may: '05', jun: '06',
        jul: '07', aug: '08', sep: '09', oct: '10', nov: '11', dec: '12'
    };

    const lowercaseMonth = month.toLowerCase();
    return monthMap[lowercaseMonth.slice(0, 3)] || monthMap[lowercaseMonth];
}

export const getMonthName = (monthNum: number, full?: boolean) => {
    const monthNames = ['', 'January', 'February', 'March', 'April',
        'May', 'June', 'July', 'August', 'September', 'October',
        'November', 'December'];

    return monthNames[monthNum].substring(0, 3);
}

export const compareSort = (x, y, desc?: boolean) => {
    if (x > y) return desc ? -1 : +1;
    if (x < y) return desc ? +1 : -1;
    return desc ? 1 : 0;
}

/** Formats dates from en-AF locale -- format: dd/MM/yyyy */
export const formatDate = (strDate: string, format?: string): string => {
    const date = strDate.split('/');
    format = format || environment.dateFormat;

    const options: Intl.DateTimeFormatOptions = {
        timeZone: 'Asia/Kabul',
        year: format.indexOf('yyyy') > -1 ? 'numeric' : (format.indexOf('yy') > -1 ? '2-digit' : 'numeric'),
        month: format.indexOf('MMM') > -1 ? 'short' : '2-digit',
        day: '2-digit'
        //hour: 'numeric', minute: 'numeric', second: 'numeric'
    };

    let res = new Date(+date[2], +date[1] - 1, +date[0])
        .toLocaleString('en-GB', options);

    if (res.startsWith('Invalid'))
        return strDate;

    return res;
}

/** Formats dates from en-AF locale -- format: dd/MM/yyyy
 *  Always return last day of the month.
 */
export const formatDateLastDay = (strDate: string, format?: string): string => {
    let date = strDate.split('/');
    format = format || environment.dateFormat;

    const options: Intl.DateTimeFormatOptions = {
        timeZone: 'Asia/Kabul',
        year: format.indexOf('yyyy') > -1 ? 'numeric' : (format.indexOf('yy') > -1 ? '2-digit' : 'numeric'),
        month: format.indexOf('MMM') > -1 ? 'short' : '2-digit',
        day: '2-digit'
        //hour: 'numeric', minute: 'numeric', second: 'numeric'
    };

    return new Date(+date[2], +date[1], 0)
        .toLocaleString('en-GB', options);
}

export const AppUtilities = () => {
    return {
        initSelect2: (selectors?: string, reinit?: boolean) => {
            // Check if jQuery included
            if (typeof jQuery == 'undefined') {
                return;
            }

            // Check if select2 included
            if (typeof $.fn.select2 === 'undefined') {
                return;
            }

            if (!selectors)
                selectors = '[data-control="select2"]';

            let elements = [].slice.call(document.querySelectorAll(selectors));

            elements.map((element: Element) => {
                if (!reinit && element.getAttribute("data-initialized") === "1") {   
                    return;
                }
                
                let options: any = {
                    width: element.getAttribute('width') || '100%',
                    containerCssClass: element.className,
                    selectionCssClass: element.className,
                    dir: document.body.getAttribute('direction')
                };

                if (element.getAttribute('data-hide-search') == 'true') {
                    options.minimumResultsForSearch = Infinity;
                }

                if (element.hasAttribute('multiple'))
                    options.closeOnSelect = false;
                    
                if (element.getAttribute('data-tags') == 'true')
                    options.tags = true;

                const maxSelection = element.getAttribute('data-max-selection');
                if (maxSelection) {
                    options.maximumSelectionLength = +maxSelection;
                }

                if (reinit) {
                    $(element).select2('destroy');
                    options.containerCssClass = options.containerCssClass.replace('select2-hidden-accessible', '');
                    options.selectionCssClass = options.selectionCssClass.replace('select2-hidden-accessible', '');
                    setTimeout(() => $(element).select2(options), 100);
                } else {
                    $(element).select2(options);

                    // init to unselected
                    $(element).val('').trigger('change');
                    let isSelected;
                    for (let i = 0; i < element.children.length; i++) {
                        if (element.children[i].hasAttribute('selected')) {
                            isSelected = $(element.children[i]).val();
                            break;
                        }
                    }

                    if (isSelected)
                        $(element).val(isSelected).trigger('change');

                    element.setAttribute("data-initialized", "1");

                    if (element.hasAttribute('multiple')) {
                        $(element).on('change', () => {
                            const searchBoxes = document.querySelectorAll('textarea[type="search"]');
                            searchBoxes.forEach(txtSearch => {
                                const searchBox = txtSearch as HTMLInputElement;
                                searchBox.value = '';
                            });
                        });
                    }
                }
            });
            
            /* fix for a bug in select2 with jQuery 3.6.0's new nested-focus */
            $(document).off('select2:open').on('select2:open', (e: Event) => {
                const elements = document.querySelectorAll('.select2-container--open .select2-search__field');
                if (elements.length > 0) {
                    const searchField = elements[elements.length - 1] as HTMLInputElement;
                    searchField.focus();
                }
            });
        },

        //reinitSelect2: (selectors: string, classList?: string) => {
        //    //$(selectors).select2('destroy');
        //    //let options: any = {
        //    //    dir: document.body.getAttribute('direction')
        //    //};

        //    //if (classList) {
        //    //    options.containerCssClass = classList;
        //    //    options.selectionCssClass = classList;
        //    //}

        //    //setTimeout(() => {
        //        //$(selectors).select2()//, 100
        //    //});
        //},

        generateAbbreviation: (str: string,) => {
            let words = str.trim().split(' ');
            let abbreviation;

            if (words.length === 1) {
                abbreviation = str.slice(0, 3);
            } else if (words.length === 2) {
                abbreviation = words[0][0] + words[1][0] + str.slice(2, 3);
            } else {
                abbreviation = words.map(word => word[0]).join('');
            }

            return abbreviation.substr(0, 10).padEnd(3, words[0][0]).toUpperCase();
        }
    }
}