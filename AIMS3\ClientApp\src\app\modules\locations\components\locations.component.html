<aims-loading *ngIf="working"></aims-loading>
<div class="card card-custom card-stretch" [ngClass]="{'d-none':working}">
    <!-- begin::Header -->
    <div class="card-header">
        <div class="card-title">
            <!-- Filters -->
            <div class="w-150px">
                <select id="filterProvinces" class="form-select form-select-sm form-select-solid" data-control="select2"
                        data-placeholder="Filter by province">
                    <option value="*">Province: All</option>
                    <option *ngFor="let prov of allProvinces" [value]="prov.id">{{ prov.name }}</option>
                </select>
            </div>
            <div class="w-200px ms-2">
                <select id="filterDistricts" class="form-select form-select-sm form-select-solid" data-control="select2"
                        data-placeholder="Filter by district">
                    <option value="*">District: All</option>
                    <optgroup *ngFor="let prov of provinces" [label]="prov.name">
                        <option *ngFor="let dist of prov.districts" [value]="dist.id">{{ dist.name }}</option>
                    </optgroup>
                </select>
            </div>
            <div class="w-150px ms-2">
                <select id="filterStatus" class="form-select form-select-sm form-select-solid" data-control="select2"
                        data-hide-search="true" data-placeholder="Filter by status">
                    <option value="*">Status: All</option>
                    <option value="0" selected>Pending</option>
                    <option value="1">Approved</option>
                </select>
            </div>
            <button role="button" class="btn btn-sm btn-light-primary border border-primary py-2 px-3 ms-2" (click)="onFilter()">Apply filter</button>

            <div class="fs-7 ms-3" *ngIf="lblFiltered">
                <span class="fs-9 text-muted text-uppercase">Filtered: </span><br />
                <span class="text-primary">{{ lblFiltered }}</span>
            </div>
        </div>
        <div class="card-toolbar">
            <!--begin::Search-->
            <div class="d-flex align-items-center position-relative">
                <span [inlineSVG]="'./assets/media/icons/duotune/general/gen021.svg'" class="svg-icon svg-icon-2 position-absolute ms-3"></span>
                <input type="search" class="form-control form-control-sm form-control-solid w-200px ps-12"
                       placeholder="Search" (search)="onSearch($event)" (keyup)="onSearch($event)" />
            </div>
            <button type="button" class="btn btn-sm btn-primary px-4 ms-3" (click)="manageLocation()" *ngIf="(['ga','la','lde'] | isAuth)">
                <span [inlineSVG]="'./assets/media/icons/duotune/arrows/arr075.svg'" class="svg-icon svg-icon-2"></span>
                Add new
            </button>
        </div>
    </div>
    <!-- end::Header -->
    <!-- begin::Body -->
    <div class="card-body">
        <div class="table-responsive">
            <table id="table_locations" class="table table-sm table-hover table-row-dashed align-middle">
                <thead>
                    <tr class="text-start text-gray-900 fw-bold gs-0">
                        <th data-ordertable="false">
                            <div class="form-check form-check-sm d-block form-check-solid m-0" *ngIf="chkAll && (['ga','la'] | isAuth)">
                                <input class="form-check-input" type="checkbox" (change)="onSelLoc(-1)">
                            </div>
                        </th>
                        <th data-orderable="false"></th>
                        <th class="d-none"></th>
                        <th class="w-120px">Province</th>
                        <th width="15%">District</th>
                        <th width="25%">Community</th>
                        <th width="10%">GPS Lat</th>
                        <th width="10%">GPS Lon</th>
                        <th>Request status</th>
                        <th class="text-end min-w-100px" data-orderable="false">
                            <button class="btn btn-sm px-4 btn-primary" *ngIf="chkAll && selLocs.length" (click)="approveLocs()">
                                Approve selected
                            </button>
                        </th>
                    </tr>
                </thead>
                <tbody class="text-gray-800">
                    <tr *ngFor="let loc of locations; let ind = index;">
                        <td>
                            <div class="form-check form-check-sm d-block form-check-solid m-0" *ngIf="loc.locationId && (['ga','la'] | isAuth)">
                                <input class="form-check-input" type="checkbox" [checked]="selLocs.includes(loc.locationId)"
                                       (change)="onSelLoc(loc.locationId)">
                            </div>
                        </td>
                        <td class="text-center"></td>
                        <td class="d-none">{{ ind+1 }}</td>
                        <td>{{ loc.provName }}</td>
                        <td [innerHtml]="loc.distName"></td>
                        <td class="text-gray-900">{{ loc.name }}</td>
                        <td class="fs-8">{{ loc.gpsLat }}</td>
                        <td class="fs-8">{{ loc.gpsLon }}</td>
                        <td class="fs-8" [attr.data-order]="loc.status">
                            <span class="badge badge-light-{{ loc.color }} my-2">{{ loc.status }}</span>
                            <p class="text-primary m-0" *ngIf="loc.orgId" [title]="loc.dateCreated | date:'medium'">
                                Request by: {{ loc.orgName }} on {{ loc.dateCreated | date:'mediumDate' }}
                            </p>
                        </td>
                        <td class="text-end">
                            <button class="btn btn-sm px-4 btn-bg-light btn-active-color-primary"
                                    (click)="manageLocation(loc, 'view')" ngbTooltip="Show location on map">
                                <span [inlineSVG]="'./assets/media/icons/duotune/maps/map002.svg'" class="svg-icon svg-icon-3"></span> Map
                            </button>
                            <ng-container *ngIf="(['ga','la','lde'] | isAuth:loc.orgId)">
                                <button class="btn btn-sm btn-icon w-30px h-30px btn-bg-light btn-active-color-primary ms-2" ngbTooltip="More Actions"
                                        data-qs-menu-trigger="click" data-qs-menu-placement="bottom-end" data-qs-menu-flip="top-end">
                                    <i class="bi bi-three-dots fs-5"></i>
                                </button>
                                <!-- Dropdown menu -->
                                <div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown py-lg-1 w-lg-225px" data-qs-menu="true">
                                    <div class="menu-item menu-lg-down-accordion">
                                        <a class="menu-link py-3" (click)="manageLocation(loc, 'edit')">
                                            <span class="menu-icon">
                                                <span [inlineSVG]="'./assets/media/icons/duotune/general/gen055.svg'" class="svg-icon svg-icon-3"></span>
                                            </span><span class="menu-title">Edit</span>
                                        </a>
                                    </div>
                                    <ng-container *ngIf="(['ga','la'] | isAuth)">
                                        <div class="menu-item menu-lg-down-accordion" *ngIf="loc.locationId && !loc.isApproved">
                                            <a class="menu-link py-3" (click)="manageLocation(loc, 'approve')">
                                                <span class="menu-icon">
                                                    <span [inlineSVG]="'./assets/media/icons/duotune/general/gen026.svg'" class="svg-icon svg-icon-3"></span>
                                                </span><span class="menu-title">Approve</span>
                                            </a>
                                        </div>
                                    </ng-container>
                                    <div class="menu-item separator"></div>
                                    <div class="menu-item menu-lg-down-accordion">
                                        <a class="menu-link py-3" (click)="deleteLocation([loc.name, loc.distName || '', loc.provName], loc.id, loc.locationId)">
                                            <span class="menu-icon">
                                                <span [inlineSVG]="'./assets/media/icons/duotune/general/trash.svg'" class="svg-icon svg-icon-3 text-danger"></span>
                                            </span><span class="menu-title text-danger">Delete</span>
                                        </a>
                                    </div>
                                </div>
                            </ng-container>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<location-form-modal #locForm (done)="getLocations()"></location-form-modal>