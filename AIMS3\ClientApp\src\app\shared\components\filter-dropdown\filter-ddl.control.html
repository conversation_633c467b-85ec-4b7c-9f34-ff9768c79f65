<div role="button" class="form-select form-select-sm text-truncate" [title]="selItem || ''"
     (click)="onInit($event)" tabindex="0" [ngClass]="{ 'bg-light-primary': highlightSelected && selItem }">
    <span class="text-muted" *ngIf="!selItem else selVal">{{ placeholders[0] }}</span>
    <ng-template #selVal>{{ selItem }}</ng-template>
</div>
<div class="menu menu-sub options menu-sub-lg-down-accordion menu-sub-lg-dropdown py-2 fs-7 {{minWidth ? ('min-w-' + minWidth + 'px') : ''}}"
     [ngClass]="{ 'show': show }">
    <ng-container *ngIf="showSearch">
        <div class="menu-item menu-item-search p-3">
            <input type="search" class="form-control form-control-sm" placeholder="Search" (search)="onSearch($event)" (keyup)="onSearch($event)" />
        </div>
        <div class="menu-item separator"></div>
    </ng-container>
    <ul class="filter-options" role="listbox">
        <li class="menu-item p-0" role="option" tabindex="0" (click)="selectAll()" *ngIf="showAll">
            <a class="menu-link flex-stack bg-light-secondary text-primary px-4 py-3" *ngIf="!selectedValues.length else notAll">
                <span class="menu-title me-4">All</span>
                <i class="fas fa-check text-primary"></i>
            </a>
            <ng-template #notAll>
                <a class="menu-link px-4 py-3">
                    <span class="menu-title me-4">All</span>
                </a>
            </ng-template>
        </li>
        <li class="menu-item p-2 mt-2" *ngIf="!items.length">
            <p class="text-center fw-normal text-muted">Nothing found.</p>
        </li>
        <li class="menu-item p-0" role="option" tabindex="0" *ngFor="let opt of items" (click)="onSelectOption(opt.id)">
            <a class="menu-link flex-stack bg-light-secondary text-primary px-4 py-3" [title]="opt.tooltip || ''"
               *ngIf="selectedValues.includes(opt.id) else notSelected">
                <span class="menu-title me-4">{{ opt.name }}</span>
                <i class="fas fa-check text-primary"></i>
            </a>
            <ng-template #notSelected>
                <a class="menu-link px-4 py-3" [title]="opt.tooltip || ''">
                    <span class="menu-title me-4">{{ opt.name }}</span>
                </a>
            </ng-template>
        </li>
    </ul>
</div>