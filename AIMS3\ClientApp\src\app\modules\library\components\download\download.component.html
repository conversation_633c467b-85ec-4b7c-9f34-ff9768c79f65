<aims-loading [showTable]="false" *ngIf="working"></aims-loading>
<div class="notice d-flex justify-content-center bg-light-primary rounded border border-dashed h-lg-100 p-6"
     [ngClass]="{'d-none':working, 'border-primary': document }">
    <div class="d-flex flex-stack flex-grow-1 flex-wrap flex-md-nowrap" *ngIf="document else noFile">
        <div class="d-flex align-items-center mb-3 mb-md-0 fw-semibold">
            <div class="symbol symbol-circle me-5">
                <div class="symbol-label bg-transparent text-primary border border-dashed col-{{ document.fileName | fileExtColor }}">
                    <i class="fs-1 far {{ document.fileName | fileExt }}"></i>
                </div>
            </div>
            <div class="d-flex flex-column">
                <h2 class="mb-1">{{ document.docName }}</h2>
                <div class="text-muted">
                    <span class="fw-bold">{{ document.docSize | fileSize }}</span> <span class="mx-2"> | </span>
                    <a class="btn-link cursor-pointer" (click)="downloadFile(document.fileName)">{{ document.fileName }}</a>
                </div>
            </div>
        </div>
        <div>
            <button type="button" class="btn btn-primary px-4 align-self-center text-nowrap" (click)="downloadFile(document.fileName)">
                <span [inlineSVG]="'./assets/media/icons/duotune/files/fil021.svg'" class="svg-icon svg-icon-2 me-1"></span>
                Download
            </button>

            <button type="button" class="btn btn-success px-4 align-self-center text-nowrap ms-3" (click)="openPreview(document.fileName)">
                <span [inlineSVG]="'./assets/media/icons/duotune/files/fil024.svg'" class="svg-icon svg-icon-2 me-1"></span>
                Preview
            </button>
        </div>
    </div>
    <ng-template #noFile>
        <div class="d-flex flex-stack flex-grow-1 flex-wrap flex-md-nowrap">
            <p class="text-mute">No file found or the link is expired.</p>
        </div>
    </ng-template>
</div>
<div class="modal fade"
     [ngClass]="{'show d-block': showModal}" tabindex="-1" role="dialog" *ngIf="showModal">

    <div class="modal-dialog modal-xl" role="document" style="max-width: 90%;">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">File Preview: {{document?.fileName}}</h5>
                <button type="button" class="close" (click)="closePreview()" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" style="min-height: 80vh;">
                <div *ngIf="fileBlobUrl" class="h-100">
                    <ng-container *ngIf="document?.fileName?.endsWith('.pdf')">
                        <iframe [src]="fileBlobUrl" style="width: 100%; height: 500px; border: none;"></iframe>
                    </ng-container>
                    <ng-container *ngIf="isImageFile(document?.fileName)">
                        <img [src]="fileBlobUrl" alt="Preview" class="img-fluid" style="max-height: 70vh; display: block; margin: 0 auto;">
                    </ng-container>
                </div>
            </div>
            <div class="modal-footer">
              
            </div>
        </div>
    </div>
</div>
    <div class="modal-backdrop fade" [ngClass]="{'show': showModal}" *ngIf="showModal"></div>


