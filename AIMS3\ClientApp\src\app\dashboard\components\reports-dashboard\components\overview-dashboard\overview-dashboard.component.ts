import { Component, Input, OnChanges, OnInit, SimpleChanges, ViewChild, ElementRef, AfterViewInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { 
  SummaryStats, 
  ReportData, 
  TimeSeriesData,
  ReportFilter
} from '../../models/reports.model';

// Import AmCharts 5
import * as am5 from '@amcharts/amcharts5';
import * as am5xy from '@amcharts/amcharts5/xy';
import * as am5percent from '@amcharts/amcharts5/percent';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';

@Component({
  selector: 'app-overview-dashboard',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './overview-dashboard.component.html',
  styleUrls: ['./overview-dashboard.component.scss']
})
export class OverviewDashboardComponent implements OnInit, OnChanges, AfterViewInit, OnDestroy {
  @Input() reportData: ReportData;
  @Input() summaryStats: SummaryStats;
  @Input() timeSeriesData: TimeSeriesData[];
  @Input() filters: ReportFilter;

  // AmCharts elements
  @ViewChild('budgetChartDiv') budgetChartDiv: ElementRef;
  @ViewChild('activityStatusChartDiv') activityStatusChartDiv: ElementRef;
  @ViewChild('progressTrendChartDiv') progressTrendChartDiv: ElementRef;
  @ViewChild('geographicCoverageChartDiv') geographicCoverageChartDiv: ElementRef;
  @ViewChild('beneficiariesChartDiv') beneficiariesChartDiv: ElementRef;
  
  // AmCharts root objects
  private budgetRoot: am5.Root;
  private activityStatusRoot: am5.Root;
  private progressTrendRoot: am5.Root;
  private geographicCoverageRoot: am5.Root;
  private beneficiariesRoot: am5.Root;

  // Year filter
  selectedYear: number = new Date().getFullYear();
  availableYears: number[] = [2023, 2024];
  selectedRegion: string = 'all';
  availableRegions: string[] = ['all', 'Northern', 'Eastern', 'Western', 'Central', 'Central Highland', 'South Eastern', 'North Eastern', 'Southern'];

  constructor() { }

  ngOnInit(): void {
    // Initial setup - charts will be created in afterViewInit
  }
  
  ngAfterViewInit(): void {
    this.initializeCharts();
  }

  ngOnChanges(changes: SimpleChanges): void {
    // Re-initialize charts when input data changes
    if ((changes.summaryStats?.currentValue || 
        changes.reportData?.currentValue || 
        changes.timeSeriesData?.currentValue) &&
        this.budgetChartDiv) {
      this.initializeCharts();
    }
  }
  
  ngOnDestroy(): void {
    // Dispose of chart resources when component is destroyed
    if (this.budgetRoot) {
      this.budgetRoot.dispose();
    }
    if (this.activityStatusRoot) {
      this.activityStatusRoot.dispose();
    }
    if (this.progressTrendRoot) {
      this.progressTrendRoot.dispose();
    }
    if (this.geographicCoverageRoot) {
      this.geographicCoverageRoot.dispose();
    }
    if (this.beneficiariesRoot) {
      this.beneficiariesRoot.dispose();
    }
  }

  private initializeCharts(): void {
    if (this.budgetChartDiv?.nativeElement) {
      this.createBudgetChart();
    }
    if (this.activityStatusChartDiv?.nativeElement) {
      this.createActivityStatusChart();
    }
    if (this.progressTrendChartDiv?.nativeElement) {
      this.createProgressTrendChart();
    }
    if (this.geographicCoverageChartDiv?.nativeElement) {
      this.createGeographicCoverageChart();
    }
    if (this.beneficiariesChartDiv?.nativeElement) {
      this.createBeneficiariesChart();
    }
  }

  onYearChange(event: any): void {
    this.selectedYear = event.target.value;
    this.initializeCharts();
  }

  onRegionChange(event: any): void {
    this.selectedRegion = event.target.value;
    this.initializeCharts();
  }

  private createBudgetChart(): void {
    // Check if data is available
    if (!this.timeSeriesData || this.timeSeriesData.length === 0) return;

    // Define regions in order
    const regions = [
      'Northern', 'Eastern', 'Western', 'Central', 
      'Central Highland', 'South Eastern', 'North Eastern', 'Southern'
    ];

    // Get quarters from time series data
    const quarters = this.timeSeriesData.map(data => data.period);
    
    // Dispose previous chart if exists
    if (this.budgetRoot) {
      this.budgetRoot.dispose();
    }

    // Create root element
    this.budgetRoot = am5.Root.new(this.budgetChartDiv.nativeElement);
    
    // Set themes
    this.budgetRoot.setThemes([am5themes_Animated.new(this.budgetRoot)]);
    
    // Create chart
    const chart = this.budgetRoot.container.children.push(
      am5xy.XYChart.new(this.budgetRoot, {
        panX: false,
        panY: false,
        wheelX: "panX",
        wheelY: "zoomX",
        layout: this.budgetRoot.verticalLayout
      })
    );
    
    // Create axes
    const xRenderer = am5xy.AxisRendererX.new(this.budgetRoot, {
      minGridDistance: 30
    });
    
    const xAxis = chart.xAxes.push(
      am5xy.CategoryAxis.new(this.budgetRoot, {
        maxDeviation: 0,
        categoryField: "quarter",
        renderer: xRenderer,
        tooltip: am5.Tooltip.new(this.budgetRoot, {})
      })
    );

    xAxis.data.setAll(quarters.map(quarter => ({ quarter })));

    const yAxis = chart.yAxes.push(
      am5xy.ValueAxis.new(this.budgetRoot, {
        maxDeviation: 0.3,
        numberFormat: "$'#,###.##",
        renderer: am5xy.AxisRendererY.new(this.budgetRoot, {})
      })
    );
    
    // Create series for each region
    const colors = [
      am5.color("#afb593"), // Northern
      am5.color("#dde2a0"), // Eastern
      am5.color("#82b4b1"), // Western
      am5.color("#9cd6c0"), // Central
      am5.color("#999ed8"), // Central Highland
      am5.color("#99d597"), // South Eastern
      am5.color("#a7bccf"), // North Eastern
      am5.color("#cfe0bc")  // Southern
    ];
    
    regions.forEach((region, index) => {
      const series = chart.series.push(
        am5xy.ColumnSeries.new(this.budgetRoot, {
      name: region,
          xAxis: xAxis,
          yAxis: yAxis,
          valueYField: "value",
          categoryXField: "quarter",
          tooltip: am5.Tooltip.new(this.budgetRoot, {
            labelText: "{name}: ${valueY}"
          })
        })
      );
      
      // Set column color
      series.columns.template.setAll({
        fill: colors[index],
        stroke: colors[index]
      });

      // Set data for this region
      const data = quarters.map((quarter, qIndex) => {
        const timeData = this.timeSeriesData[qIndex];
        const regionData = timeData.regions?.[region]?.cashDistributed || 0;
        return {
          quarter: quarter,
          value: regionData
        };
      });
      
      series.data.setAll(data);
    });
    
    // Add legend
    const legend = chart.children.push(am5.Legend.new(this.budgetRoot, {
      centerX: am5.percent(50),
      x: am5.percent(50),
      layout: this.budgetRoot.horizontalLayout,
      marginTop: 15
    }));
    
    legend.data.setAll(chart.series.values);
    
    // Add title
    chart.children.unshift(
      am5.Label.new(this.budgetRoot, {
        text: "Budget by Region and Quarter",
        fontSize: 16,
        fontWeight: "500",
        textAlign: "center",
        x: am5.percent(50),
        centerX: am5.percent(50),
        paddingTop: 0,
        paddingBottom: 0
      })
    );
    
    // Add cursor
    chart.set("cursor", am5xy.XYCursor.new(this.budgetRoot, {
      behavior: "zoomX"
    }));
  }

  private createActivityStatusChart(): void {
    if (!this.reportData || !this.reportData.activitiesByStatus) return;
    
    let activitiesData = this.reportData.activitiesByStatus;
    
    // Filter activities by region if a specific region is selected
    if (this.selectedRegion !== 'all') {
      activitiesData = this.filterActivitiesByRegion(this.selectedRegion);
    }

    // Dispose previous chart if exists
    if (this.activityStatusRoot) {
      this.activityStatusRoot.dispose();
    }
    
    // Create root element
    this.activityStatusRoot = am5.Root.new(this.activityStatusChartDiv.nativeElement);
    
    // Set themes
    this.activityStatusRoot.setThemes([am5themes_Animated.new(this.activityStatusRoot)]);
    
    // Create chart
    const chart = this.activityStatusRoot.container.children.push(
      am5percent.PieChart.new(this.activityStatusRoot, {
        layout: this.activityStatusRoot.verticalLayout,
        innerRadius: am5.percent(40)
      })
    );
    
    // Create series
    const series = chart.series.push(
      am5percent.PieSeries.new(this.activityStatusRoot, {
        valueField: "value",
        categoryField: "category",
        legendValueText: "{value} activities",
        legendLabelText: "{category}",
      })
    );
    
    // Set slice colors
    series.slices.template.adapters.add("fill", (fill, target) => {
      const dataItem = target.dataItem;
      if (dataItem) {
        const category = (dataItem.dataContext as { categoryCode: string }).categoryCode;
        if (category === "ongoing") return am5.color("#FFC107");
        if (category === "completed") return am5.color("#4CAF50");
        if (category === "cancelled") return am5.color("#F44336");
      }
      return fill;
    });
    
    series.slices.template.adapters.add("stroke", (stroke, target) => {
      const dataItem = target.dataItem;
      if (dataItem) {
        const category = (dataItem.dataContext as { categoryCode: string }).categoryCode;
        if (category === "ongoing") return am5.color("#FFC107");
        if (category === "completed") return am5.color("#4CAF50");
        if (category === "cancelled") return am5.color("#F44336");
      }
      return stroke;
    });
    
    // Set data
    const data = [
      { category: "Ongoing", categoryCode: "ongoing", value: activitiesData.ongoing },
      { category: "Completed", categoryCode: "completed", value: activitiesData.completed },
      { category: "Cancelled", categoryCode: "cancelled", value: activitiesData.cancelled }
    ];
    
    series.data.setAll(data);
    
    // Add legend
    const legend = chart.children.push(am5.Legend.new(this.activityStatusRoot, {
      centerX: am5.percent(50),
      x: am5.percent(50),
      layout: this.activityStatusRoot.horizontalLayout
    }));
    
    legend.data.setAll(series.dataItems);
    
    // Add title
    chart.children.unshift(
      am5.Label.new(this.activityStatusRoot, {
        text: "Activities by Status",
        fontSize: 16,
        fontWeight: "500",
        textAlign: "center",
        x: am5.percent(50),
        centerX: am5.percent(50),
        paddingTop: 0,
        paddingBottom: 5
      })
    );
  }

  private createProgressTrendChart(): void {
    // For this example, let's assume we have some time series data for progress
    if (!this.timeSeriesData || this.timeSeriesData.length === 0) return;
    
    // Dispose previous chart if exists
    if (this.progressTrendRoot) {
      this.progressTrendRoot.dispose();
    }
    
    // Create root element
    this.progressTrendRoot = am5.Root.new(this.progressTrendChartDiv.nativeElement);
    
    // Set themes
    this.progressTrendRoot.setThemes([am5themes_Animated.new(this.progressTrendRoot)]);
    
    // Create chart
    const chart = this.progressTrendRoot.container.children.push(
      am5xy.XYChart.new(this.progressTrendRoot, {
        panX: false,
        panY: false,
        wheelX: "panX",
        wheelY: "zoomX",
        layout: this.progressTrendRoot.verticalLayout
      })
    );
    
    // Create axes
    const xAxis = chart.xAxes.push(
      am5xy.CategoryAxis.new(this.progressTrendRoot, {
        maxDeviation: 0,
        categoryField: "period",
        renderer: am5xy.AxisRendererX.new(this.progressTrendRoot, {}),
        tooltip: am5.Tooltip.new(this.progressTrendRoot, {})
      })
    );

    const yAxis = chart.yAxes.push(
      am5xy.ValueAxis.new(this.progressTrendRoot, {
        maxDeviation: 0.3,
        min: 0,
        max: 100,
        renderer: am5xy.AxisRendererY.new(this.progressTrendRoot, {})
      })
    );
    
    // Create series
    const series = chart.series.push(
      am5xy.LineSeries.new(this.progressTrendRoot, {
        name: "Progress",
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: "progress",
        categoryXField: "period",
        tooltip: am5.Tooltip.new(this.progressTrendRoot, {
          labelText: "{valueY}%"
        })
      })
    );
    
    // Set colors
    series.strokes.template.setAll({
      strokeWidth: 3,
      stroke: am5.color("#4e73df")
    });
    
    series.fills.template.setAll({
      fillOpacity: 0.3,
      fill: am5.color("#4e73df")
    });
    
    // Add bullet points
    series.bullets.push(function() {
      const circle = am5.Circle.new(this.progressTrendRoot, {
        radius: 4,
        stroke: am5.color("#4e73df"),
        fill: am5.color("#ffffff"),
        strokeWidth: 2
      });
      return am5.Bullet.new(this.progressTrendRoot, {
        sprite: circle
      });
    });
    
    // Set data
    const data = this.timeSeriesData.map(item => ({
      period: item.period,
      progress: item.progress || 0
    }));
    
    xAxis.data.setAll(data);
    series.data.setAll(data);
    
    // Add title
    chart.children.unshift(
      am5.Label.new(this.progressTrendRoot, {
        text: "Progress Trend",
        fontSize: 16,
        fontWeight: "500",
        textAlign: "center",
        x: am5.percent(50),
        centerX: am5.percent(50),
        paddingTop: 0,
        paddingBottom: 5
      })
    );
    
    // Add cursor
    chart.set("cursor", am5xy.XYCursor.new(this.progressTrendRoot, {
      behavior: "zoomX"
    }));
  }

  private createGeographicCoverageChart(): void {
    // For this example, we'll create a simple column chart showing activity counts by region
    if (!this.reportData) return;
    
    // Dispose previous chart if exists
    if (this.geographicCoverageRoot) {
      this.geographicCoverageRoot.dispose();
    }
    
    // Create root element
    this.geographicCoverageRoot = am5.Root.new(this.geographicCoverageChartDiv.nativeElement);
    
    // Set themes
    this.geographicCoverageRoot.setThemes([am5themes_Animated.new(this.geographicCoverageRoot)]);
    
    // Create chart
    const chart = this.geographicCoverageRoot.container.children.push(
      am5xy.XYChart.new(this.geographicCoverageRoot, {
        panX: false,
        panY: false,
        wheelX: "panX",
        wheelY: "zoomX",
        layout: this.geographicCoverageRoot.verticalLayout
      })
    );
    
    // Create axes
    const yRenderer = am5xy.AxisRendererY.new(this.geographicCoverageRoot, {
      minGridDistance: 30
    });
    
    const yAxis = chart.yAxes.push(
      am5xy.CategoryAxis.new(this.geographicCoverageRoot, {
        maxDeviation: 0,
        categoryField: "region",
        renderer: yRenderer
      })
    );
    
    const xAxis = chart.xAxes.push(
      am5xy.ValueAxis.new(this.geographicCoverageRoot, {
        maxDeviation: 0.3,
        renderer: am5xy.AxisRendererX.new(this.geographicCoverageRoot, {})
      })
    );
    
    // Create series
    const series = chart.series.push(
      am5xy.ColumnSeries.new(this.geographicCoverageRoot, {
        name: "Activities",
        xAxis: xAxis,
        yAxis: yAxis,
        valueXField: "activities",
        categoryYField: "region",
        tooltip: am5.Tooltip.new(this.geographicCoverageRoot, {
          labelText: "{valueX} activities"
        })
      })
    );
    
    // Set columns
    series.columns.template.setAll({
      cornerRadiusBR: 5,
      cornerRadiusTR: 5,
      fill: am5.color("#3F51B5"),
      stroke: am5.color("#3F51B5")
    });
    
    // Set data - here we would use real region data
    const regionData = [
      { region: "Northern", activities: 84 },
      { region: "Eastern", activities: 67 },
      { region: "Western", activities: 92 },
      { region: "Central", activities: 105 },
      { region: "Central Highland", activities: 56 },
      { region: "South Eastern", activities: 72 },
      { region: "North Eastern", activities: 63 },
      { region: "Southern", activities: 89 }
    ].sort((a, b) => b.activities - a.activities); // Sort by count descending
    
    yAxis.data.setAll(regionData);
    series.data.setAll(regionData);
    
    // Add title
    chart.children.unshift(
      am5.Label.new(this.geographicCoverageRoot, {
        text: "Geographic Coverage",
        fontSize: 16,
        fontWeight: "500",
        textAlign: "center",
        x: am5.percent(50),
        centerX: am5.percent(50),
        paddingTop: 0,
        paddingBottom: 5
      })
    );
  }

  private createBeneficiariesChart(): void {
    if (!this.timeSeriesData || this.timeSeriesData.length === 0) return;
    
    // Dispose previous chart if exists
    if (this.beneficiariesRoot) {
      this.beneficiariesRoot.dispose();
    }
    
    // Create root element
    this.beneficiariesRoot = am5.Root.new(this.beneficiariesChartDiv.nativeElement);
    
    // Set themes
    this.beneficiariesRoot.setThemes([am5themes_Animated.new(this.beneficiariesRoot)]);
    
    // Create chart
    const chart = this.beneficiariesRoot.container.children.push(
      am5xy.XYChart.new(this.beneficiariesRoot, {
        panX: false,
        panY: false,
        wheelX: "panX",
        wheelY: "zoomX",
        layout: this.beneficiariesRoot.verticalLayout
      })
    );
    
    // Create axes
    const xAxis = chart.xAxes.push(
      am5xy.CategoryAxis.new(this.beneficiariesRoot, {
        maxDeviation: 0,
        categoryField: "period",
        renderer: am5xy.AxisRendererX.new(this.beneficiariesRoot, {}),
        tooltip: am5.Tooltip.new(this.beneficiariesRoot, {})
      })
    );
    
    const yAxis = chart.yAxes.push(
      am5xy.ValueAxis.new(this.beneficiariesRoot, {
        maxDeviation: 0.3,
        renderer: am5xy.AxisRendererY.new(this.beneficiariesRoot, {})
      })
    );
    
    // Create series (bar chart with stacked columns for male/female)
    const maleSeries = chart.series.push(
      am5xy.ColumnSeries.new(this.beneficiariesRoot, {
        name: "Male",
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: "male",
        categoryXField: "period",
        stacked: true,
        tooltip: am5.Tooltip.new(this.beneficiariesRoot, {
          labelText: "{name}: {valueY}"
        })
      })
    );
    
    const femaleSeries = chart.series.push(
      am5xy.ColumnSeries.new(this.beneficiariesRoot, {
        name: "Female",
        xAxis: xAxis,
        yAxis: yAxis,
        valueYField: "female",
        categoryXField: "period",
        stacked: true,
        tooltip: am5.Tooltip.new(this.beneficiariesRoot, {
          labelText: "{name}: {valueY}"
        })
      })
    );
    
    // Set colors
    maleSeries.columns.template.setAll({
      fill: am5.color("#2196F3"),
      stroke: am5.color("#2196F3")
    });
    
    femaleSeries.columns.template.setAll({
      fill: am5.color("#E91E63"),
      stroke: am5.color("#E91E63")
    });
    
    // Set data
    const data = this.timeSeriesData.map(item => ({
      period: item.period,
      male: Math.round((item.beneficiaries || 0) / 2),
      female: Math.floor((item.beneficiaries || 0) / 2)
    }));
    
    xAxis.data.setAll(data);
    maleSeries.data.setAll(data);
    femaleSeries.data.setAll(data);
    
    // Add legend
    const legend = chart.children.push(am5.Legend.new(this.beneficiariesRoot, {
      centerX: am5.percent(50),
      x: am5.percent(50),
      layout: this.beneficiariesRoot.horizontalLayout
    }));
    
    legend.data.setAll(chart.series.values);
    
    // Add title
    chart.children.unshift(
      am5.Label.new(this.beneficiariesRoot, {
        text: "Beneficiaries by Period",
        fontSize: 16,
        fontWeight: "500",
        textAlign: "center",
        x: am5.percent(50),
        centerX: am5.percent(50),
        paddingTop: 0,
        paddingBottom: 5
      })
    );
    
    // Add cursor
    chart.set("cursor", am5xy.XYCursor.new(this.beneficiariesRoot, {
      behavior: "zoomX"
    }));
  }

  private filterActivitiesByRegion(region: string): { ongoing: number; completed: number; cancelled: number } {
    // Get the time series data for the selected region
    const regionData = this.timeSeriesData
      .filter(data => data.regions && data.regions[region])
      .reduce((acc, curr) => {
        const regionInfo = curr.regions[region];
        if (regionInfo) {
          acc.ongoing += regionInfo.activities || 0;
          acc.completed += regionInfo.activities || 0;
          acc.cancelled += regionInfo.activities || 0;
        }
        return acc;
      }, { ongoing: 0, completed: 0, cancelled: 0 });

    return regionData;
  }

  formatNumber(num: number): string {
    return num ? num.toLocaleString() : '0';
  }

  formatCurrency(num: number): string {
    return num ? '$' + num.toLocaleString() : '$0';
  }

  calculatePercentage(value: number, total: number): number {
    return total ? Math.round((value / total) * 100) : 0;
  }
} 