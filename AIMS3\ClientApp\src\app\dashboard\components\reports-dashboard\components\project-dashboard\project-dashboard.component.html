<div class="project-dashboard">
  <!-- Key Metrics Row -->
  <div class="row mb-4">
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="card stats-card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="card-title mb-0">Total Projects</h6>
            <i class="fas fa-project-diagram text-primary icon-bg"></i>
          </div>
          <h2 class="mb-2">{{ formatNumber(projects?.length) }}</h2>
          <div class="progress progress-sm">
            <div class="progress-bar bg-primary" role="progressbar" 
                 [style.width]="'100%'" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="card stats-card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="card-title mb-0">Total Budget</h6>
            <i class="fas fa-dollar-sign text-success icon-bg"></i>
          </div>
          <h2 class="mb-2">{{ formatCurrency(getTotalBudget()) }}</h2>
          <div class="progress progress-sm">
            <div class="progress-bar bg-success" role="progressbar" 
                 [style.width]="'100%'" aria-valuenow="100" aria-valuemin="0" aria-valuemax="100"></div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="card stats-card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="card-title mb-0">Cash Distributed</h6>
            <i class="fas fa-chart-line text-info icon-bg"></i>
          </div>
          <h2 class="mb-2">{{ formatCurrency(getTotalCashDistributed()) }}</h2>
          <div class="progress progress-sm">
            <div class="progress-bar bg-info" role="progressbar" 
                 [style.width]="getUtilizationRate() + '%'" 
                 [attr.aria-valuenow]="getUtilizationRate()" 
                 aria-valuemin="0" aria-valuemax="100"></div>
          </div>
          <small class="text-muted">{{ getUtilizationRate() }}% of budget utilized</small>
        </div>
      </div>
    </div>
    
    <div class="col-md-3 col-sm-6 mb-3">
      <div class="card stats-card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="card-title mb-0">Avg. Progress</h6>
            <i class="fas fa-tasks text-warning icon-bg"></i>
          </div>
          <h2 class="mb-2">{{ calculateAverage('progress') }}%</h2>
          <div class="progress progress-sm">
            <div class="progress-bar bg-warning" role="progressbar" 
                 [style.width]="calculateAverage('progress') + '%'" 
                 [attr.aria-valuenow]="calculateAverage('progress')" 
                 aria-valuemin="0" aria-valuemax="100"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Charts Row 1 -->
  <div class="row mb-4">
    <div class="col-md-8 mb-3">
      <div class="card">
        <div class="card-body">
          <div *ngIf="budgetChartOptions">
            <apx-chart
              [series]="budgetChartOptions.series"
              [chart]="budgetChartOptions.chart"
              [title]="budgetChartOptions.title"
              [colors]="budgetChartOptions.colors"
              [xaxis]="budgetChartOptions.xaxis"
              [plotOptions]="budgetChartOptions.plotOptions"
              [dataLabels]="budgetChartOptions.dataLabels"
              [tooltip]="budgetChartOptions.tooltip"
            ></apx-chart>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-4 mb-3">
      <div class="card">
        <div class="card-body">
          <div *ngIf="statusChartOptions">
            <apx-chart
              [series]="statusChartOptions.series"
              [chart]="statusChartOptions.chart"
              [labels]="statusChartOptions.labels"
              [title]="statusChartOptions.title"
              [colors]="statusChartOptions.colors"
              [dataLabels]="statusChartOptions.dataLabels"
              [legend]="statusChartOptions.legend"
              [tooltip]="statusChartOptions.tooltip"
            ></apx-chart>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Charts Row 2 -->
  <div class="row">
    <div class="col-md-6 mb-3">
      <div class="card">
        <div class="card-body">
          <div *ngIf="expenditureChartOptions">
            <apx-chart
              [series]="expenditureChartOptions.series"
              [chart]="expenditureChartOptions.chart"
              [title]="expenditureChartOptions.title"
              [colors]="expenditureChartOptions.colors"
              [xaxis]="expenditureChartOptions.xaxis"
              [plotOptions]="expenditureChartOptions.plotOptions"
              [dataLabels]="expenditureChartOptions.dataLabels"
              [legend]="expenditureChartOptions.legend"
              [tooltip]="expenditureChartOptions.tooltip"
              [fill]="expenditureChartOptions.fill"
            ></apx-chart>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-6 mb-3">
      <div class="card">
        <div class="card-body">
          <div *ngIf="implementationChartOptions">
            <apx-chart
              [series]="implementationChartOptions.series"
              [chart]="implementationChartOptions.chart"
              [title]="implementationChartOptions.title"
              [xaxis]="implementationChartOptions.xaxis"
              [yaxis]="implementationChartOptions.yaxis"
              [markers]="implementationChartOptions.markers"
              [fill]="implementationChartOptions.fill"
              [tooltip]="implementationChartOptions.tooltip"
            ></apx-chart>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Project Groups Info -->
  <div class="row mt-4" *ngIf="projectGroups && projectGroups.length > 0">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Project Groups</h5>
          <div class="table-responsive">
            <table class="table table-hover">
              <thead>
                <tr>
                  <th>Group Name</th>
                  <th>Projects</th>
                  <th>Budget</th>
                  <th>Cash Distributed</th>
                  <th>Utilization</th>
                  <th>Progress</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let group of projectGroups">
                  <td><strong>{{ group.name }}</strong></td>
                  <td>{{ group.projects?.length || 0 }}</td>
                  <td>{{ formatCurrency(group.totalBudget) }}</td>
                  <td>{{ formatCurrency(group.totalCashDistributed) }}</td>
                  <td>
                    <div class="progress" style="height: 6px">
                      <div class="progress-bar" role="progressbar" 
                           [style.width]="(group.totalCashDistributed / group.totalBudget * 100) + '%'" 
                           [style.background-color]="'#2196F3'"></div>
                    </div>
                    <span class="small">{{ Math.round(group.totalCashDistributed / group.totalBudget * 100) }}%</span>
                  </td>
                  <td>
                    <div class="progress" style="height: 6px">
                      <div class="progress-bar" role="progressbar" 
                           [style.width]="group.progress + '%'" 
                           [style.background-color]="'#4CAF50'"></div>
                    </div>
                    <span class="small">{{ group.progress }}%</span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div> 