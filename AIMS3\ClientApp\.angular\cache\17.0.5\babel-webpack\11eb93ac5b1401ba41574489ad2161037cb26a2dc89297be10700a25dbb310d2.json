{"ast": null, "code": "import { fork<PERSON><PERSON>n } from 'rxjs';\nimport { ActivityStatus, Region } from '../../../../shared/enums';\nimport { compareSort, getMonthName } from '../../../../shared/utilities';\nimport { ReadOnlyGridComponent } from './grid/readonly-grid.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../auth\";\nimport * as i2 from \"../../../data-entry/services/profile.service\";\nimport * as i3 from \"../../services/view-data.service\";\nimport * as i4 from \"../../../../shared/services/shared.service\";\nimport * as i5 from \"../../../../shared/services/message.service\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"../../../../shared/components/working/working.component\";\nimport * as i8 from \"../../../../shared/components/loading/loading.component\";\nimport * as i9 from \"../../../../shared/components/filter-dropdown/filter-ddl.control\";\nimport * as i10 from \"@ng-bootstrap/ng-bootstrap\";\nimport * as i11 from \"./grid/readonly-grid.component\";\nconst _c0 = [\"output\"];\nconst _c1 = [\"category\"];\nconst _c2 = [\"profile\"];\nconst _c3 = [\"pgroups\"];\nconst _c4 = [\"project\"];\nconst _c5 = [\"partner\"];\nconst _c6 = [\"status\"];\nconst _c7 = [\"region\"];\nconst _c8 = (a0, a1) => ({\n  \"text-primary\": a0,\n  \"text-muted\": a1\n});\nfunction ViewDataComponent_label_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 21)(1, \"input\", 22);\n    i0.ɵɵlistener(\"change\", function ViewDataComponent_label_12_Template_input_change_1_listener() {\n      i0.ɵɵrestoreView(_r10);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.onApprovalMode());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 23);\n    i0.ɵɵtext(3, \"Approval mode\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(1, _c8, ctx_r0.filters.approvalMode, !ctx_r0.filters.approvalMode));\n  }\n}\nfunction ViewDataComponent_label_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r12 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"label\", 21)(1, \"input\", 24);\n    i0.ɵɵlistener(\"change\", function ViewDataComponent_label_13_Template_input_change_1_listener() {\n      i0.ɵɵrestoreView(_r12);\n      const ctx_r11 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r11.onToggleNAPeriods());\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(2, \"span\", 23);\n    i0.ɵɵtext(3, \"Hide empty progress (ongoing activities)\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"checked\", ctx_r1.hideNAPeriods);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(2, _c8, ctx_r1.hideNAPeriods, !ctx_r1.hideNAPeriods));\n  }\n}\nfunction ViewDataComponent_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25)(1, \"span\", 26);\n    i0.ɵɵtext(2, \"Filtered by:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.filtered[0], \"\", ctx_r2.filtered.length > 1 && ctx_r2.gridFilteredBy.length > 1 ? \", \" + ctx_r2.gridFilteredBy[0] : ctx_r2.gridFilteredBy[0], \" \");\n  }\n}\nfunction ViewDataComponent_aims_loading_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"aims-loading\", 27);\n  }\n  if (rf & 2) {\n    i0.ɵɵproperty(\"showTable\", false);\n  }\n}\nconst _c9 = () => [\"Proj. group\"];\nfunction ViewDataComponent_div_17_filter_ddl_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r29 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"filter-ddl\", 51, 52);\n    i0.ɵɵlistener(\"change\", function ViewDataComponent_div_17_filter_ddl_2_Template_filter_ddl_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r29);\n      const ctx_r28 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r28.onFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r13 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"id\", \"projGroups\")(\"placeholders\", i0.ɵɵpureFunction0(6, _c9))(\"minWidth\", 130)(\"options\", ctx_r13.projGroups)(\"multiple\", true)(\"showAll\", false);\n  }\n}\nconst _c10 = () => [\"Partner\"];\nfunction ViewDataComponent_div_17_filter_ddl_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r32 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"filter-ddl\", 34, 53);\n    i0.ɵɵlistener(\"change\", function ViewDataComponent_div_17_filter_ddl_13_Template_filter_ddl_change_0_listener($event) {\n      i0.ɵɵrestoreView(_r32);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.onFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r18 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"id\", \"orgIds\")(\"placeholders\", i0.ɵɵpureFunction0(5, _c10))(\"minWidth\", 130)(\"options\", ctx_r18.orgs)(\"multiple\", true);\n  }\n}\nfunction ViewDataComponent_div_17_div_19_ng_container_1_ng_template_4_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 61);\n    i0.ɵɵelement(1, \"br\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r38 = i0.ɵɵnextContext(5);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r38.strPeriod[0]);\n  }\n}\nfunction ViewDataComponent_div_17_div_19_ng_container_1_ng_template_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Period: From date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, ViewDataComponent_div_17_div_19_ng_container_1_ng_template_4_span_2_Template, 3, 1, \"span\", 60);\n  }\n  if (rf & 2) {\n    const ctx_r36 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r36.strPeriod[0]);\n  }\n}\nfunction ViewDataComponent_div_17_div_19_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r40 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 55);\n    i0.ɵɵtext(2, \"From\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"input\", 58);\n    i0.ɵɵlistener(\"change\", function ViewDataComponent_div_17_div_19_ng_container_1_Template_input_change_3_listener($event) {\n      i0.ɵɵrestoreView(_r40);\n      const ctx_r39 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r39.onPeriodChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(4, ViewDataComponent_div_17_div_19_ng_container_1_ng_template_4_Template, 3, 1, \"ng-template\", null, 59, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const _r37 = i0.ɵɵreference(5);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngbTooltip\", _r37);\n  }\n}\nfunction ViewDataComponent_div_17_div_19_ng_template_5_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 61);\n    i0.ɵɵelement(1, \"br\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r41 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r41.strPeriod[1]);\n  }\n}\nfunction ViewDataComponent_div_17_div_19_ng_template_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Period: To date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(2, ViewDataComponent_div_17_div_19_ng_template_5_span_2_Template, 3, 1, \"span\", 60);\n  }\n  if (rf & 2) {\n    const ctx_r34 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r34.filters.approvalMode && ctx_r34.strPeriod[1]);\n  }\n}\nfunction ViewDataComponent_div_17_div_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r43 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 54);\n    i0.ɵɵtemplate(1, ViewDataComponent_div_17_div_19_ng_container_1_Template, 6, 1, \"ng-container\", 18);\n    i0.ɵɵelementStart(2, \"span\", 55);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"input\", 56);\n    i0.ɵɵlistener(\"change\", function ViewDataComponent_div_17_div_19_Template_input_change_4_listener($event) {\n      i0.ɵɵrestoreView(_r43);\n      const ctx_r42 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r42.onPeriodChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, ViewDataComponent_div_17_div_19_ng_template_5_Template, 3, 1, \"ng-template\", null, 57, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const _r35 = i0.ɵɵreference(6);\n    const ctx_r21 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r21.filters.approvalMode);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r21.filters.approvalMode ? \"As of\" : \"To\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngbTooltip\", _r35);\n  }\n}\nconst _c11 = () => [\"Qtr\"];\nconst _c12 = () => [\"Year\"];\nfunction ViewDataComponent_div_17_ng_container_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r47 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"filter-ddl\", 62, 63);\n    i0.ɵɵlistener(\"change\", function ViewDataComponent_div_17_ng_container_20_Template_filter_ddl_change_1_listener($event) {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r46 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r46.onFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"filter-ddl\", 64, 65);\n    i0.ɵɵlistener(\"change\", function ViewDataComponent_div_17_ng_container_20_Template_filter_ddl_change_3_listener($event) {\n      i0.ɵɵrestoreView(_r47);\n      const ctx_r48 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r48.onFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"id\", \"period\")(\"placeholders\", i0.ɵɵpureFunction0(9, _c11))(\"minWidth\", 80)(\"options\", ctx_r22.qtrs)(\"showSearch\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"year\")(\"placeholders\", i0.ɵɵpureFunction0(10, _c12))(\"minWidth\", 80)(\"options\", ctx_r22.years);\n  }\n}\nfunction ViewDataComponent_div_17_a_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r50 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"a\", 66);\n    i0.ɵɵlistener(\"click\", function ViewDataComponent_div_17_a_23_Template_a_click_0_listener() {\n      i0.ɵɵrestoreView(_r50);\n      const ctx_r49 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r49.resetFilters());\n    });\n    i0.ɵɵtext(1, \"Reset\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewDataComponent_div_17_ng_container_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelement(1, \"i\", 67);\n    i0.ɵɵtext(2, \" Download \");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ViewDataComponent_div_17_ng_template_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 68);\n    i0.ɵɵelement(1, \"span\", 69);\n    i0.ɵɵtext(2, \" Downloading... \");\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c13 = a0 => ({\n  \"ms-2\": a0\n});\nconst _c14 = () => [\"Project\"];\nconst _c15 = () => [\"Output\"];\nconst _c16 = () => [\"Category\", \"Categories\"];\nconst _c17 = () => [\"Intervention\"];\nconst _c18 = () => [\"Data type\"];\nconst _c19 = () => [1, 2];\nconst _c20 = () => [\"Region\"];\nfunction ViewDataComponent_div_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r52 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29);\n    i0.ɵɵtemplate(2, ViewDataComponent_div_17_filter_ddl_2_Template, 2, 7, \"filter-ddl\", 30);\n    i0.ɵɵelementStart(3, \"filter-ddl\", 31, 32);\n    i0.ɵɵlistener(\"change\", function ViewDataComponent_div_17_Template_filter_ddl_change_3_listener($event) {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r51 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r51.onFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(5, \"div\", 33);\n    i0.ɵɵelementStart(6, \"filter-ddl\", 34, 35);\n    i0.ɵɵlistener(\"change\", function ViewDataComponent_div_17_Template_filter_ddl_change_6_listener($event) {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r53 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r53.onFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"filter-ddl\", 36, 37);\n    i0.ɵɵlistener(\"change\", function ViewDataComponent_div_17_Template_filter_ddl_change_8_listener($event) {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r54 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r54.onFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"filter-ddl\", 36, 38);\n    i0.ɵɵlistener(\"change\", function ViewDataComponent_div_17_Template_filter_ddl_change_10_listener($event) {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r55 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r55.onFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(12, \"div\", 33);\n    i0.ɵɵtemplate(13, ViewDataComponent_div_17_filter_ddl_13_Template, 2, 6, \"filter-ddl\", 39);\n    i0.ɵɵelementStart(14, \"filter-ddl\", 40, 41);\n    i0.ɵɵlistener(\"change\", function ViewDataComponent_div_17_Template_filter_ddl_change_14_listener($event) {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r56 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r56.onFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"filter-ddl\", 36, 42);\n    i0.ɵɵlistener(\"change\", function ViewDataComponent_div_17_Template_filter_ddl_change_16_listener($event) {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r57 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r57.onFilterChange($event));\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(18, \"div\", 33);\n    i0.ɵɵtemplate(19, ViewDataComponent_div_17_div_19_Template, 7, 3, \"div\", 43)(20, ViewDataComponent_div_17_ng_container_20_Template, 5, 11, \"ng-container\", 18);\n    i0.ɵɵelementStart(21, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ViewDataComponent_div_17_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r58 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r58.onFilterData());\n    });\n    i0.ɵɵtext(22, \" Apply filter \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(23, ViewDataComponent_div_17_a_23_Template, 2, 0, \"a\", 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"div\", 46);\n    i0.ɵɵelement(25, \"div\", 47);\n    i0.ɵɵelementStart(26, \"button\", 48);\n    i0.ɵɵlistener(\"click\", function ViewDataComponent_div_17_Template_button_click_26_listener() {\n      i0.ɵɵrestoreView(_r52);\n      const ctx_r59 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r59.onDownload());\n    });\n    i0.ɵɵtemplate(27, ViewDataComponent_div_17_ng_container_27_Template, 3, 0, \"ng-container\", 49)(28, ViewDataComponent_div_17_ng_template_28_Template, 3, 0, \"ng-template\", null, 50, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r26 = i0.ɵɵreference(29);\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isGlobalUser);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"id\", \"projIds\")(\"ngClass\", i0.ɵɵpureFunction1(43, _c13, ctx_r4.isGlobalUser))(\"placeholders\", i0.ɵɵpureFunction0(45, _c14))(\"minWidth\", 150)(\"options\", ctx_r4.projects)(\"multiple\", true)(\"showSearch\", true)(\"showAll\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"id\", \"outputs\")(\"placeholders\", i0.ɵɵpureFunction0(46, _c15))(\"minWidth\", 100)(\"options\", ctx_r4.outputs)(\"multiple\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"catIds\")(\"placeholders\", i0.ɵɵpureFunction0(47, _c16))(\"minWidth\", 140)(\"options\", ctx_r4.categories)(\"multiple\", true);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"profIds\")(\"placeholders\", i0.ɵɵpureFunction0(48, _c17))(\"minWidth\", 120)(\"options\", ctx_r4.profiles)(\"multiple\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.isGlobalUser);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"id\", \"dataStatus\")(\"placeholders\", i0.ɵɵpureFunction0(49, _c18))(\"minWidth\", 130)(\"options\", ctx_r4.submitStatus)(\"showSearch\", false)(\"multiple\", true)(\"selectedValues\", i0.ɵɵpureFunction0(50, _c19));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"id\", \"regions\")(\"placeholders\", i0.ɵɵpureFunction0(51, _c20))(\"minWidth\", 130)(\"options\", ctx_r4.regions)(\"multiple\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.filters.isTarget);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.filters.isTarget);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.filtered.length > 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"disabled\", ctx_r4.downloading || ctx_r4.working || ctx_r4.gridWorking || !(ctx_r4.interventions == null ? null : ctx_r4.interventions.length));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.downloading)(\"ngIfElse\", _r26);\n  }\n}\nfunction ViewDataComponent_aims_loading_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"aims-loading\", 70);\n  }\n}\nfunction ViewDataComponent_div_20_li_4_ng_template_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 85)(1, \"p\", 86);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\", 87);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 88);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const prof_r68 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", prof_r68.category.code, \" \", prof_r68.category.name, \"\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", prof_r68.name, \" (\", prof_r68.abbreviation, \")\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(prof_r68.description);\n  }\n}\nconst _c21 = a0 => ({\n  \"active\": a0\n});\nfunction ViewDataComponent_div_20_li_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r74 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"li\", 82)(1, \"a\", 83);\n    i0.ɵɵlistener(\"click\", function ViewDataComponent_div_20_li_4_Template_a_click_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r74);\n      const prof_r68 = restoredCtx.$implicit;\n      const ctx_r73 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r73.getInterventionData(prof_r68.id));\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, ViewDataComponent_div_20_li_4_ng_template_3_Template, 7, 5, \"ng-template\", null, 84, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const prof_r68 = ctx.$implicit;\n    const ind_r69 = ctx.index;\n    const _r71 = i0.ɵɵreference(4);\n    const ctx_r60 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngbTooltip\", _r71);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(4, _c21, ctx_r60.selTabId === prof_r68.id))(\"tabindex\", ind_r69 === 0 ? -1 : null);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate(prof_r68.name);\n  }\n}\nconst _c22 = (a0, a1) => ({\n  \"btn-light\": a0,\n  \"btn-light-primary border border-primary\": a1\n});\nfunction ViewDataComponent_div_20_button_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r76 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 89);\n    i0.ɵɵlistener(\"click\", function ViewDataComponent_div_20_button_6_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r76);\n      const ctx_r75 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r75.enableGridGroupAndPivot());\n    });\n    i0.ɵɵelement(1, \"i\", 90);\n    i0.ɵɵtext(2, \"Pivot \");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r63 = i0.ɵɵreference(8);\n    const ctx_r61 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngbTooltip\", _r63)(\"ngClass\", i0.ɵɵpureFunction2(2, _c22, !ctx_r61.isPivotEnabled, ctx_r61.isPivotEnabled));\n  }\n}\nfunction ViewDataComponent_div_20_ng_template_7_span_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 26);\n    i0.ɵɵtext(1, \"Toggle Grouping and Pivot\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ViewDataComponent_div_20_ng_template_7_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"p\", 94);\n    i0.ɵɵtext(2, \"Pivot and Grouping enabled\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewDataComponent_div_20_ng_template_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ViewDataComponent_div_20_ng_template_7_span_0_Template, 2, 0, \"span\", 91)(1, ViewDataComponent_div_20_ng_template_7_div_1_Template, 3, 0, \"div\", 92);\n  }\n  if (rf & 2) {\n    const ctx_r62 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r62.isPivotEnabled);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r62.isPivotEnabled);\n  }\n}\nfunction ViewDataComponent_div_20_ng_container_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1, \"Sort\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ViewDataComponent_div_20_ng_container_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const ctx_r65 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"Sorted by \", ctx_r65.gridSortedBy, \" field\", ctx_r65.gridSortedBy > 1 ? \"s\" : \"\", \"\");\n  }\n}\nfunction ViewDataComponent_div_20_ng_template_13_ng_container_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"span\", 26);\n    i0.ɵɵtext(2, \"Enable sorting\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" on each column.\");\n    i0.ɵɵelementContainerEnd();\n  }\n}\nfunction ViewDataComponent_div_20_ng_template_13_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"p\", 94);\n    i0.ɵɵtext(2, \"Sorting enabled\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Click on a column header to sort by that column. Use \");\n    i0.ɵɵelementStart(5, \"code\");\n    i0.ɵɵtext(6, \"Shift\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(7, \" key to sort by multiple columns.\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ViewDataComponent_div_20_ng_template_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, ViewDataComponent_div_20_ng_template_13_ng_container_0_Template, 4, 0, \"ng-container\", 18)(1, ViewDataComponent_div_20_ng_template_13_div_1_Template, 8, 0, \"div\", 92);\n  }\n  if (rf & 2) {\n    const ctx_r66 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r66.gridSortedBy < 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r66.gridSortedBy >= 0);\n  }\n}\nfunction ViewDataComponent_div_20_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r82 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 71)(1, \"div\", 72)(2, \"div\", 73)(3, \"ul\", 74);\n    i0.ɵɵtemplate(4, ViewDataComponent_div_20_li_4_Template, 5, 6, \"li\", 75);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 76);\n    i0.ɵɵtemplate(6, ViewDataComponent_div_20_button_6_Template, 3, 5, \"button\", 77)(7, ViewDataComponent_div_20_ng_template_7_Template, 2, 2, \"ng-template\", null, 78, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementStart(9, \"button\", 79);\n    i0.ɵɵlistener(\"click\", function ViewDataComponent_div_20_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r82);\n      const ctx_r81 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r81.enableGridSort());\n    });\n    i0.ɵɵelement(10, \"i\", 80);\n    i0.ɵɵtemplate(11, ViewDataComponent_div_20_ng_container_11_Template, 2, 0, \"ng-container\", 18)(12, ViewDataComponent_div_20_ng_container_12_Template, 2, 2, \"ng-container\", 18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(13, ViewDataComponent_div_20_ng_template_13_Template, 2, 2, \"ng-template\", null, 81, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const _r67 = i0.ɵɵreference(14);\n    const ctx_r6 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r6.interventions);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r6.filters.approvalMode);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngbTooltip\", _r67)(\"ngClass\", i0.ɵɵpureFunction2(6, _c22, ctx_r6.gridSortedBy < 0, ctx_r6.gridSortedBy >= 0));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.gridSortedBy <= 0);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r6.gridSortedBy > 0);\n  }\n}\nfunction ViewDataComponent_aims_working_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"aims-working\");\n  }\n}\nfunction ViewDataComponent_div_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 95)(1, \"div\", 96);\n    i0.ɵɵtext(2, \" No intervention is selected or no intervention found. \");\n    i0.ɵɵelementEnd()();\n  }\n}\nconst _c23 = a0 => ({\n  \"border-info\": a0\n});\nconst _c24 = a0 => ({\n  \"d-none\": a0\n});\nexport class ViewDataComponent {\n  constructor(authService, profService, dataService, sharedService, messageService, cdr) {\n    this.authService = authService;\n    this.profService = profService;\n    this.dataService = dataService;\n    this.sharedService = sharedService;\n    this.messageService = messageService;\n    this.cdr = cdr;\n    this.pgToolbar = 'true';\n    this.pgHeader = 'true';\n    this.pushToolbar = 'true';\n    //appHeader: HTMLElement;\n    // **** Profiles ----------------------------------------------------------\n    this.working = false;\n    this.gridWorking = false;\n    this.isApprover = false;\n    this.isGlobalUser = false;\n    this.downloading = false;\n    this.userOrgId = 0;\n    this.projGroups = [];\n    this.projects = [];\n    this.outputs = [];\n    this.categories = [];\n    this.profiles = [];\n    this.orgs = [];\n    this.submitStatus = [{\n      id: 0,\n      name: 'Draft'\n    }, {\n      id: 1,\n      name: 'Submitted'\n    }, {\n      id: 2,\n      name: 'Approved'\n    }];\n    this.regions = [];\n    this.qtrs = [{\n      id: 1,\n      name: 'Q-1'\n    }, {\n      id: 2,\n      name: 'Q-2'\n    }, {\n      id: 3,\n      name: 'Q-3'\n    }, {\n      id: 4,\n      name: 'Q-4'\n    }];\n    this.years = [];\n    this.interventions = [];\n    this.allInterventions = [];\n    this.selTabId = 0;\n    this.filters = {\n      isTarget: false,\n      approvalMode: false,\n      dataStatus: [1, 2]\n    };\n    this.filtered = [''];\n    this.strPeriod = ['', ''];\n    // Add property to control filtering of activities without meaningful progress data\n    this.hideNAPeriods = true; // Default to hiding activities with N/A periods or empty progress\n    this.subscriptions = [];\n    this.gridFilteredBy = ['-'];\n    this.gridSortedBy = -1;\n    /*\n    gridGroupedBy: number = -1;\n    enableGridGroup(): void {\n        let enableRowGroup = false;\n        if (this.gridGroupedBy === -1) {\n            enableRowGroup = true;\n            this.gridComponent.gridApi.setRowGroupPanelShow('always');\n            this.gridGroupedBy = 0;\n        } else {\n            enableRowGroup = false;\n            this.gridComponent.gridApi.setRowGroupPanelShow('never');\n            this.gridComponent.gridColumnApi.getRowGroupColumns().forEach(col => {\n                this.gridComponent.gridColumnApi.removeRowGroupColumn(col.getColId());\n            });\n            this.gridGroupedBy = -1;\n        }\n        this.gridComponent.gridColumnApi.getColumns().forEach(col => {\n            const colId = col.getColId();\n            if (!colId.startsWith('cols-hidden')) {\n                let colDef = col.getColDef();\n                colDef.enableRowGroup = enableRowGroup;\n                col.setColDef(colDef, col.getColDef());\n            }\n        });\n    }\n    */\n    this.isPivotEnabled = false;\n    const roles = this.authService.currentUserValue.roles;\n    this.isGlobalUser = roles.includes('Admin') || roles.includes('Approver') || roles.includes('LocalApprover') || roles.includes('Viewer');\n    this.isApprover = roles.includes('Admin') || roles.includes('Approver') || roles.includes('LocalApprover');\n    const isLocalUser = roles.includes('DataEntry') || roles.includes('LocalViewer') || roles.includes('LocalApprover');\n    this.filters.dataStatus = [1, 2];\n    //this.appHeader = document.querySelector('app-header');\n    Object.values(Region).forEach((v, i) => {\n      if (typeof v === 'string' && v !== 'National') this.regions.push({\n        id: i,\n        name: v\n      });\n    });\n    this.regions.sort((x, y) => x.name < y.name ? -1 : 1);\n    const currYear = new Date().getFullYear();\n    for (let i = 2020; i <= currYear + 5; i++) this.years.push({\n      id: i,\n      name: String(i)\n    });\n    // Initialize userOrgId\n    if (this.authService.currentUserValue.org) {\n      this.userOrgId = this.authService.currentUserValue.org.id;\n    }\n  }\n  ngOnInit() {\n    this.gridComponent.isGlobalUser = this.isGlobalUser;\n    this.gridComponent.isApprover = this.isApprover;\n    // this.gridComponent.selectedVals.org = this.authService.currentUserValue.org.shortName;\n    this.getDropdownItems();\n  }\n  // Initialize the project filter dropdown after view init\n  ngAfterViewInit() {\n    // Give the DOM time to initialize\n    setTimeout(() => {\n      this.initializeProjectFilter();\n    }, 500);\n  }\n  // Add this new method to handle project filter initialization\n  initializeProjectFilter() {\n    // Make sure project filter is properly initialized\n    if (this.projFilterCtrl) {\n      // Ensure we have projects\n      if (!this.projects || this.projects.length === 0) {\n        return;\n      }\n      // Force a refresh of the options and items\n      // Create fresh copies to avoid reference issues\n      this.projFilterCtrl.options = [...this.projects];\n      this.projFilterCtrl.items = [...this.projects];\n      // Set the default filter options\n      this.projFilterCtrl.showAll = true;\n      this.projFilterCtrl.showSearch = true;\n      // For global users only, also initialize project group filter\n      if (this.isGlobalUser && this.projGroupsFilterCtrl) {\n        console.log('Initializing project group filter with', this.projGroups.length, 'groups');\n        // Create fresh copies to avoid reference issues\n        this.projGroupsFilterCtrl.options = [...this.projGroups];\n        this.projGroupsFilterCtrl.items = [...this.projGroups];\n        this.projGroupsFilterCtrl.showAll = false;\n        this.projGroupsFilterCtrl.showSearch = true;\n      }\n      // Force change detection\n      this.cdr.detectChanges();\n    }\n  }\n  // Add this new method to handle partner filter initialization\n  initializePartnerFilter() {\n    // Only initialize for global users (since partner filter is hidden for local users)\n    if (this.isGlobalUser && this.partnerFilterCtrl) {\n      // Ensure we have organizations\n      if (!this.orgs || this.orgs.length === 0) {\n        // console.log('No organizations available to display in partner filter');\n        return;\n      }\n      // console.log('Initializing partner filter with', this.orgs.length, 'organizations');\n      // Force a refresh of the options and items\n      // Create fresh copies to avoid reference issues\n      this.partnerFilterCtrl.options = [...this.orgs];\n      this.partnerFilterCtrl.items = [...this.orgs];\n      // Set the default filter options\n      this.partnerFilterCtrl.showAll = true;\n      this.partnerFilterCtrl.showSearch = true;\n      // Force change detection\n      this.cdr.detectChanges();\n    }\n  }\n  // get categories and profiles\n  getDropdownItems() {\n    this.working = true;\n    // For LDE users, use the filtered API endpoint\n    const roles = this.authService.currentUserValue.roles;\n    const isLDEUser = roles.includes('DataEntry') || roles.includes('LocalViewer') || roles.includes('LocalApprover');\n    // Choose the appropriate API call based on user role\n    let projectsApi = this.sharedService.getProjectsWithGrouping();\n    if (!this.isGlobalUser && isLDEUser) {\n      // Use filtered API which automatically filters by user org ID on server side\n      console.log('Using filtered API for LDE user');\n      projectsApi = this.sharedService.getProjectsFiltered();\n    }\n    let endPoints = {\n      projs: projectsApi,\n      cats: this.sharedService.getCategories(),\n      profs: this.profService.getProfiles(),\n      orgs: this.sharedService.getOrgsList()\n    };\n    this.subscriptions.push(forkJoin(endPoints).subscribe({\n      next: ({\n        projs,\n        cats,\n        profs,\n        orgs\n      }) => {\n        // Debug log to check the data received\n        // console.log('Projects from API:', projs);\n        // console.log('Current user org ID:', this.userOrgId);\n        // For GLOBAL users, show all projects and project groups\n        if (this.isGlobalUser) {\n          this.projects = projs;\n          // Extract all project groups\n          this.projGroups = [];\n          projs.forEach(proj => {\n            if (proj.grouping && proj.grouping.trim() !== '') {\n              const existingGroup = this.projGroups.find(pg => pg.id === proj.grouping);\n              if (!existingGroup) {\n                this.projGroups.push({\n                  id: proj.grouping,\n                  name: proj.grouping\n                });\n              }\n            }\n          });\n          // Sort project groups alphabetically\n          this.projGroups.sort((x, y) => compareSort(x.name, y.name));\n        } else {\n          // For LDE users, use the filtered projects - project group filter is hidden in UI\n          // Check if API returned IProjectIntervention[] or Project[]\n          if (Array.isArray(projs) && projs.length > 0 && 'organizations' in projs[0]) {\n            // Convert IProjectIntervention[] to Project[]\n            console.log('Converting filtered projects to standard format');\n            this.projects = projs.map(p => {\n              return {\n                id: p.id,\n                name: p.name,\n                abbreviation: p.abbreviation,\n                grouping: p.grouping || '',\n                partners: p.organizations?.map(orgId => ({\n                  organizationId: orgId,\n                  projectId: p.id\n                }))\n              };\n            });\n          } else {\n            // Use projects as-is from API\n            this.projects = projs;\n          }\n          console.log(`LDE user projects: ${this.projects.length}`);\n          // Project groups are not needed for LDE users (hidden in UI)\n          this.projGroups = [];\n        }\n        // Transform organizations to match the expected structure for filter dropdown\n        this.orgs = [];\n        orgs.forEach(org => {\n          this.orgs.push({\n            id: org.id,\n            name: org.shortName\n          });\n        });\n        this.orgs.sort((x, y) => compareSort(x.name, y.name));\n        console.log('Transformed organizations for dropdown:', this.orgs);\n        this.allInterventions = [...profs];\n        this.categories = cats;\n        this.outputs = [];\n        this.categories.forEach(cat => {\n          cat.name = cat.code + ' ' + cat.name;\n          const outputExists = this.outputs.findIndex(o => o.name === cat.output) > -1;\n          if (!outputExists) {\n            this.outputs.push({\n              id: cat.output,\n              name: cat.output,\n              categories: this.categories.filter(c => c.output === cat.output)\n            });\n          }\n        });\n        // Initialize for both global and local users immediately\n        setTimeout(() => {\n          this.initializeProjectFilter();\n          this.initializePartnerFilter();\n        }, 100);\n      },\n      error: err => {\n        this.working = false;\n      },\n      complete: () => {\n        this.createProfileFilters();\n        this.cdr.detectChanges();\n        this.working = false;\n        this.getData();\n      }\n    }));\n  }\n  createProfileFilters() {\n    this.profiles = [];\n    this.allInterventions.forEach(prof => {\n      this.profiles.push({\n        id: prof.id,\n        catId: prof.categoryId,\n        name: prof.name\n      });\n    });\n    // sort it alphabetically\n    this.profiles.sort((x, y) => x.name > y.name ? 1 : -1);\n  }\n  getData() {\n    this.gridWorking = true;\n    // Create a deep copy of the filters to ensure we're sending a fresh object\n    const filtersCopy = JSON.parse(JSON.stringify(this.filters));\n    this.subscriptions.push(this.dataService.getData(filtersCopy).subscribe({\n      next: result => {\n        this.dataResult = result;\n      },\n      error: err => {\n        this.gridWorking = false;\n        this.messageService.error('Failed to fetch data. Please try again.', 'Data Fetch Error');\n      },\n      complete: () => {\n        if (!this.dataResult || !this.dataResult.interventions || this.dataResult.interventions.length === 0) {\n          this.interventions = [];\n          this.gridWorking = false;\n          return;\n        }\n        // Get interventions that match IDs in the data result\n        let filteredInterventions = this.allInterventions.filter(i => this.dataResult.interventions.includes(i.id));\n        // For local users (non-global), filter interventions to only show those\n        // associated with the user's organization\n        if (!this.isGlobalUser) {\n          const currentUser = this.authService.currentUserValue;\n          if (currentUser.org) {\n            // Filter interventions based on the data associated with the user's organization\n            filteredInterventions = filteredInterventions.filter(intervention => {\n              // Check if there's any data entry associated with this intervention \n              // and the user's organization\n              const orgId = currentUser.org.id;\n              const data = this.dataResult.data;\n              // Check if there's any data entry that matches both the intervention ID and org ID\n              return data.some(item => {\n                // For progress data, we can check directly\n                if ('orgId' in item) {\n                  return item.profId === intervention.id && item.orgId === orgId;\n                }\n                // For target data, check the partner field\n                return item.partner && (item.partner.includes(currentUser.org.shortName) || item.partner.includes(currentUser.org.fullName));\n              });\n            });\n          }\n        }\n        this.interventions = filteredInterventions;\n        this.interventions.sort((x, y) => compareSort(x.category.code, y.category.code) || compareSort(x.name, y.name));\n        // Filter out data with N/A periods (applies to progress data only)\n        if (this.dataResult.data && !this.filters.isTarget) {\n          this.dataResult.data = this.filterOutNAPeriods(this.dataResult.data);\n        }\n        this.gridSortedBy = -1;\n        //this.gridGroupedBy = -1;\n        this.gridComponent.gridApi.setFilterModel(null);\n        //this.gridComponent.gridApi.setRowGroupPanelShow('never');\n        if (this.isPivotEnabled) this.enableGridGroupAndPivot();\n        this.cdr.detectChanges();\n        this.createProfileTabs();\n        this.gridWorking = false;\n      }\n    }));\n  }\n  /**\n   * Filter out data rows that have N/A periods (for progress data only) and\n   * activities without meaningful cumulative progress data in the filtered time series.\n   * This method prioritizes activities with meaningful data but ensures each intervention\n   * has at least one activity to prevent empty interventions.\n   *\n   * @param data The data array to filter\n   * @returns Filtered data array with balanced filtering approach\n   */\n  filterOutNAPeriods(data) {\n    // If hideNAPeriods is disabled, return all data\n    if (!this.hideNAPeriods) {\n      return data;\n    }\n    // Group data by intervention (profId) to ensure each intervention has at least one activity\n    const dataByIntervention = data.reduce((groups, item) => {\n      const profId = item.profId || 'unknown';\n      if (!groups[profId]) {\n        groups[profId] = [];\n      }\n      groups[profId].push(item);\n      return groups;\n    }, {});\n    const filteredData = [];\n    // Process each intervention separately\n    Object.keys(dataByIntervention).forEach(profId => {\n      const interventionData = dataByIntervention[profId];\n      const validActivities = [];\n      // For progress data, apply validation for time series filtering\n      if (interventionData.length > 0 && 'asOf' in interventionData[0]) {\n        // First pass: find activities that meet strict criteria\n        for (const item of interventionData) {\n          // Check if this is an ongoing activity - only apply strict filtering to ongoing activities\n          const isOngoingActivity = item.status === 0; // ActivityStatus.Ongoing = 0\n          // STEP 1: Basic validation - must have valid asOf and progressId\n          const hasValidAsOf = item.asOf && item.asOf !== 'N/A' && item.asOf.trim() !== '';\n          const hasValidProgressId = item.progressId && item.progressId > 0;\n          if (hasValidAsOf && hasValidProgressId) {\n            if (isOngoingActivity) {\n              // STEP 2: For ongoing activities with time series filtering, validate meaningful progress data\n              if (this.filters.period > 0 || this.filters.periodEnd > 0) {\n                const hasProgressValues = item.colVals && Object.keys(item.colVals).some(key => {\n                  const value = item.colVals[key];\n                  if (value === null || value === undefined || value === '') {\n                    return false;\n                  }\n                  const stringValue = value.toString().trim();\n                  // Filter out only truly empty/invalid values - allow zeros as valid cumulative progress\n                  if (stringValue === '' || stringValue === 'N/A' || stringValue === '-' || stringValue === 'null' || stringValue === 'undefined') {\n                    return false;\n                  }\n                  // For numeric values, allow zero and positive values as valid cumulative progress\n                  const numValue = parseFloat(stringValue.replace(/,/g, ''));\n                  if (!isNaN(numValue)) {\n                    return numValue >= 0; // Allow zero as valid cumulative progress\n                  }\n                  // For non-numeric values, they should be meaningful strings\n                  return stringValue.length > 0;\n                });\n                if (hasProgressValues) {\n                  validActivities.push(item);\n                }\n              } else {\n                // No time series filtering for ongoing activities, include if basic criteria are met\n                validActivities.push(item);\n              }\n            } else {\n              // For non-ongoing activities (Completed, Archived, Cancelled), apply more lenient filtering\n              // Just check that it's not completely empty\n              validActivities.push(item);\n            }\n          }\n        }\n        // If no activities passed strict validation, include the most recent activity\n        // to ensure the intervention doesn't disappear completely\n        if (validActivities.length === 0 && interventionData.length > 0) {\n          // Find the most recent activity (prefer those with valid asOf dates)\n          const activitiesWithDates = interventionData.filter(item => item.asOf && item.asOf !== 'N/A' && item.asOf.trim() !== '');\n          let mostRecentActivity;\n          if (activitiesWithDates.length > 0) {\n            // Sort by asOf date (assuming format like \"dd MMM yyyy\")\n            mostRecentActivity = activitiesWithDates.sort((a, b) => {\n              // Simple date comparison - in a real scenario you'd want proper date parsing\n              return b.asOf.localeCompare(a.asOf);\n            })[0];\n          } else {\n            // Fallback to first activity if no valid dates\n            mostRecentActivity = interventionData[0];\n          }\n          validActivities.push(mostRecentActivity);\n        }\n        filteredData.push(...validActivities);\n      } else {\n        // For non-progress data (like target data), keep all items\n        filteredData.push(...interventionData);\n      }\n    });\n    return filteredData;\n  }\n  onFilterChange(ctrl) {\n    // Check if event structure has changed to use selectedValues instead of selVals\n    const selectedValues = ctrl.selectedValues || ctrl.selVals || [];\n    if (ctrl.id === 'projGroups') {\n      // Only relevant for global users now, since project group filter is hidden for LDE\n      this.projFilterCtrl.clearSelection();\n      if (!selectedValues.length) {\n        this.projFilterCtrl.options = [...this.projects];\n      } else {\n        this.projFilterCtrl.options = this.projects.filter(p => p.grouping && selectedValues.includes(p.grouping));\n        this.projFilterCtrl.options.sort((x, y) => x.name > y.name ? 1 : x.name < y.name ? -1 : 0);\n        // Auto-select all projects in the selected groups\n        this.projFilterCtrl.setSelectedValues(this.projFilterCtrl.options.map(o => o.id));\n      }\n      this.projFilterCtrl.items = [...this.projFilterCtrl.options];\n    } else if (ctrl.id === 'outputs') {\n      this.catFilterCtrl.clearSelection();\n      this.profFilterCtrl.clearSelection();\n      if (!selectedValues.length) {\n        this.catFilterCtrl.options = [...this.categories];\n        this.profFilterCtrl.options = [...this.profiles];\n      } else {\n        this.catFilterCtrl.options = this.categories.filter(c => selectedValues.includes(c.output));\n        this.catFilterCtrl.options.sort((x, y) => x.name > y.name ? 1 : x.name < y.name ? -1 : 0);\n        this.profFilterCtrl.options = this.profiles.filter(p => this.catFilterCtrl.options.findIndex(c => c.id === p.catId) > -1);\n        this.profFilterCtrl.options.sort((x, y) => x.name > y.name ? 1 : x.name < y.name ? -1 : 0);\n      }\n      this.catFilterCtrl.items = [...this.catFilterCtrl.options];\n      this.profFilterCtrl.items = [...this.profFilterCtrl.options];\n    } else if (ctrl.id === 'catIds') {\n      this.profFilterCtrl.clearSelection();\n      if (!selectedValues.length) {\n        this.profFilterCtrl.options = [...this.profiles];\n      } else {\n        this.profFilterCtrl.options = this.profiles.filter(p => selectedValues.includes(p.catId));\n        this.profFilterCtrl.options.sort((x, y) => x.name > y.name ? 1 : x.name < y.name ? -1 : 0);\n      }\n      this.profFilterCtrl.items = [...this.profFilterCtrl.options];\n    }\n    if (ctrl.id === 'period' || ctrl.id === 'year') this.filters[ctrl.id] = selectedValues[0];else this.filters[ctrl.id] = selectedValues;\n  }\n  onDataTypeChange(isTarget) {\n    this.filters.isTarget = isTarget || false;\n    this.filters.period = 0;\n    this.filters.periodEnd = 0;\n    this.filters.year = 0;\n    this.filters.yearEnd = 0;\n    // Reset hideNAPeriods to true when switching to Progress data\n    if (!this.filters.isTarget) {\n      this.hideNAPeriods = true;\n    }\n    // reapply filter\n    this.gridComponent.selectedVals.isTarget = isTarget;\n    this.getData();\n  }\n  onApprovalMode() {\n    this.filters.approvalMode = !this.filters.approvalMode;\n    this.gridComponent.approvalMode = this.filters.approvalMode;\n    if (this.filters.approvalMode) {\n      const pFrom = document.querySelector('#periodFrom');\n      if (pFrom) pFrom.value = '';\n    }\n    // reapply filter\n    this.getData();\n  }\n  onToggleNAPeriods() {\n    this.hideNAPeriods = !this.hideNAPeriods;\n    // Refresh the data to apply/remove the N/A filter\n    this.getData();\n  }\n  onPeriodChange(e) {\n    const target = e.target;\n    const date = target.value?.split('-');\n    if (target.id === 'periodFrom') {\n      if (!date || date.length < 2) {\n        this.filters.period = 0;\n        this.filters.year = 0;\n        this.strPeriod[0] = '';\n      } else {\n        this.filters.period = +date[1];\n        this.filters.year = +date[0];\n        this.strPeriod[0] = `01 ${getMonthName(this.filters.period)} ${this.filters.year}`;\n      }\n    } else {\n      if (!date || date.length < 2) {\n        this.filters.periodEnd = 0;\n        this.filters.yearEnd = 0;\n        this.strPeriod[1] = '';\n      } else {\n        this.filters.periodEnd = +date[1];\n        this.filters.yearEnd = +date[0];\n        this.strPeriod[1] = new Date(this.filters.yearEnd, this.filters.periodEnd, 0).getDate() + ` ${getMonthName(this.filters.periodEnd)} ${this.filters.yearEnd}`;\n      }\n    }\n  }\n  resetFilters() {\n    // Reset all filter controls\n    if (this.isGlobalUser && this.projGroupsFilterCtrl) {\n      this.projGroupsFilterCtrl.clearSelection();\n    }\n    if (this.projFilterCtrl) {\n      this.projFilterCtrl.clearSelection();\n      this.projFilterCtrl.options = [...this.projects];\n      this.projFilterCtrl.items = [...this.projFilterCtrl.options];\n    }\n    if (this.outputFilterCtrl) {\n      this.outputFilterCtrl.clearSelection();\n    }\n    if (this.catFilterCtrl) {\n      this.catFilterCtrl.clearSelection();\n    }\n    if (this.profFilterCtrl) {\n      this.profFilterCtrl.clearSelection();\n    }\n    if (this.isGlobalUser && this.partnerFilterCtrl) {\n      this.partnerFilterCtrl.clearSelection();\n    }\n    if (this.dStatusFilterCtrl) {\n      this.dStatusFilterCtrl.setSelectedValues([1, 2]);\n    }\n    if (this.regionFilterCtrl) {\n      this.regionFilterCtrl.clearSelection();\n    }\n    // Reset filters object\n    this.filters = {\n      isTarget: this.filters.isTarget,\n      approvalMode: this.filters.approvalMode,\n      dataStatus: [1, 2]\n    };\n    // Reset hideNAPeriods to default (true)\n    this.hideNAPeriods = true;\n    this.filtered = [''];\n    // Clear period dates\n    const periodFrom = document.getElementById('periodFrom');\n    const periodTo = document.getElementById('periodTo');\n    if (periodFrom) periodFrom.value = '';\n    if (periodTo) periodTo.value = '';\n    // Reset target-specific filters\n    if (this.filters.isTarget) {\n      if (this.projFilterCtrl) {\n        this.projFilterCtrl.clearSelection();\n      }\n    }\n    this.getData();\n  }\n  onFilterData() {\n    if (!this.filters.approvalMode) {\n      if (this.filters.period > 0 && this.filters.year > 0 && this.filters.periodEnd > 0 && this.filters.yearEnd > 0) {\n        const date = new Date(this.filters.year, this.filters.period - 1, 1);\n        const dateEnd = new Date(this.filters.yearEnd, this.filters.periodEnd - 1, 1);\n        if (dateEnd < date) {\n          this.messageService.error('<span class=\"fw-semibold\">Period (To)</span> should be greater ' + 'than <span class=\"fw-semibold\">Period (From)</span>.', 'Invalid period', {\n            enableHtml: true\n          });\n          return;\n        }\n      }\n    }\n    // reapply filter\n    this.getData();\n    // create filters\n    this.filtered = [''];\n    if (this.filters.projGroups?.length) this.filtered.push('Project Group');\n    if (this.filters.projIds?.length) this.filtered.push('Project');\n    if (this.filters.profIds?.length) this.filtered.push('Intervention');else if (this.filters.catIds?.length) this.filtered.push('Category');else if (this.filters.outputs?.length) this.filtered.push('Output');\n    if (this.filters.orgIds?.length) this.filtered.push('Partner');\n    if (this.filters.regions?.length) this.filtered.push('Region');\n    if (this.filters.period > 0 || this.filters.periodEnd > 0) this.filtered.push('Period');\n    this.filtered[0] = this.filtered.filter((f, i) => i > 0).join(', ');\n  }\n  createProfileTabs(selProfId) {\n    let selProfile;\n    if (!selProfId) selProfId = this.dataResult.interventions[0];\n    selProfile = this.interventions.find(i => i.id === selProfId);\n    if (selProfile) {\n      this.selTabId = selProfile.id;\n      this.gridComponent.selectedVals.profId = selProfile.id;\n      this.gridComponent.selectedVals.prof = selProfile.abbreviation;\n      if (!this.filters.isTarget) {\n        this.gridComponent.dynamicColumns = selProfile.variables.filter(v => [2, 4, 5].includes(v.type)); // ColumnVarType.Progress, Info, Static\n      } else {\n        this.gridComponent.dynamicColumns = selProfile.variables.filter(v => [0, 1, 3].includes(v.type)); // ColumnVarType.Target, Info, Static\n      }\n\n      this.gridComponent.renameAsOfCol = false;\n      if (this.filters.period && this.filters.periodEnd) this.gridComponent.renameAsOfCol = true;\n      this.gridComponent.initGrid(this.filters.isTarget);\n      this.gridComponent.refreshGridRows(this.dataResult.data);\n    }\n  }\n  getInterventionData(profId) {\n    this.gridComponent.working = true;\n    this.subscriptions.push(this.dataService.getInterventionData(profId, this.dataResult.filters).subscribe({\n      next: res => {\n        this.dataResult.data = [...res];\n      },\n      error: err => {\n        this.gridComponent.working = false;\n      },\n      complete: () => {\n        // reset grid filters, sorting, grouping\n        this.gridSortedBy = -1;\n        //this.gridGroupedBy = -1;\n        if (this.isPivotEnabled) this.enableGridGroupAndPivot();\n        this.gridComponent.gridApi.setFilterModel(null);\n        this.gridComponent.gridApi.setRowGroupPanelShow('never');\n        this.createProfileTabs(profId);\n        this.gridComponent.working = false;\n      }\n    }));\n  }\n  gridFiltered(col) {\n    if (col.startsWith('-')) {\n      col = col.replace('-', '');\n      this.gridFilteredBy = this.gridFilteredBy.filter(c => c !== col);\n    } else if (!this.gridFilteredBy.includes(col)) this.gridFilteredBy.push(col);\n    this.gridFilteredBy[0] = this.gridFilteredBy.slice(1, this.gridFilteredBy.length).join(', ');\n  }\n  enableGridSort() {\n    let isSortable = false;\n    if (this.gridSortedBy === -1) {\n      isSortable = true;\n      this.gridSortedBy = 0;\n    } else {\n      isSortable = false;\n      this.gridSortedBy = -1;\n    }\n    this.gridComponent.gridColumnApi.getColumns().forEach(col => {\n      const colId = col.getColId();\n      if (!colId.startsWith('new') && !colId.startsWith('cols-hidden')) {\n        let colDef = col.getColDef();\n        colDef.sortable = isSortable;\n        col.setColDef(colDef, col.getColDef());\n      }\n    });\n    this.gridComponent.gridApi.refreshHeader();\n    this.gridComponent.gridColumnApi.autoSizeAllColumns();\n  }\n  enableGridGroupAndPivot() {\n    if (this.isPivotEnabled) {\n      this.gridComponent.gridApi.setPivotMode(false);\n      this.gridComponent.gridApi.setSideBar(false);\n      this.gridComponent.gridColumnApi.getRowGroupColumns().forEach(col => {\n        this.gridComponent.gridColumnApi.removeRowGroupColumn(col.getColId());\n      });\n      this.isPivotEnabled = false;\n      return;\n    }\n    //this.gridComponent.gridApi.setPivotMode(true);\n    this.gridComponent.gridApi.setSideBar(['columns', 'filters']);\n    this.isPivotEnabled = true;\n  }\n  resetSortAndGroup() {\n    if (this.gridSortedBy > -1) {\n      this.gridComponent.defaultColDefs.sortable = false;\n      this.gridSortedBy = -1;\n    }\n    if (this.isPivotEnabled) this.enableGridGroupAndPivot();\n    /*if (this.gridGroupedBy > -1) {\n        this.gridComponent.defaultColDefs.enableRowGroup = false;\n        this.gridComponent.gridApi.setRowGroupPanelShow('never');\n        this.gridComponent.gridColumnApi.getRowGroupColumns().forEach(col => {      // remove all group cols except status\n            this.gridComponent.gridColumnApi.removeRowGroupColumn(col.getColId());\n        });\n        this.gridGroupedBy = -1;\n    }*/\n  }\n  /** Approve data */\n  onApproveData(event) {\n    if (!event.data.dateSubmitted) return;\n    let operation;\n    if (this.filters.isTarget) {\n      operation = this.dataService.approveTargetData([event.data.id], event.data.dateApproved ? true : false);\n    } else {\n      operation = this.dataService.approveActivityData([event.data.progressId], event.data.dateApproved ? true : false);\n    }\n    this.subscriptions.push(operation.subscribe({\n      error: err => {\n        console.error(err);\n      },\n      complete: () => {\n        if (event.data.dateApproved) {\n          event.data.dateApproved = null;\n          // change status\n          if (!this.filters.isTarget) {\n            if (event.data.eMonth) event.data.status = ActivityStatus.Completed;else event.data.status = ActivityStatus.Ongoing;\n          }\n        } else {\n          event.data.dateApproved = new Date();\n          // change status to 'Archived'\n          if (!this.filters.isTarget && event.data.status === ActivityStatus.Completed) event.data.status = ActivityStatus.Archived;\n        }\n        this.gridComponent.gridApi.refreshCells({\n          rowNodes: [event.node],\n          columns: ['dataStatus', 'status'],\n          force: true,\n          suppressFlash: true\n        });\n        this.messageService.success('The data approve status has been changed.', event.data.dateApproved ? 'Approved' : 'Approval retreated');\n      }\n    }));\n  }\n  /** Approve all data */\n  onApproveAll(isApproved) {\n    let approveIds = [];\n    this.gridComponent.gridApi.forEachNodeAfterFilter(node => {\n      if (node.data.dateSubmitted) {\n        if (this.filters.isTarget) approveIds.push(node.data.id);else approveIds.push(node.data.progressId);\n      }\n    });\n    const operation = this.filters.isTarget ? this.dataService.approveTargetData(approveIds, isApproved) : this.dataService.approveActivityData(approveIds, isApproved);\n    this.subscriptions.push(operation.subscribe({\n      error: err => {\n        console.error(err);\n      },\n      complete: () => {\n        this.gridComponent.gridApi.forEachNodeAfterFilter(node => {\n          if (isApproved) {\n            node.data.dateApproved = null;\n            // change status\n            if (!this.filters.isTarget) {\n              if (node.data.eMonth) node.data.status = ActivityStatus.Completed;else node.data.status = ActivityStatus.Ongoing;\n            }\n          } else if (node.data.dateSubmitted) {\n            node.data.dateApproved = new Date();\n            // change status to 'Archived'\n            if (!this.filters.isTarget && node.data.status === ActivityStatus.Completed) node.data.status = ActivityStatus.Archived;\n          } else {\n            node.data.dateApproved = null;\n            // change status\n            if (!this.filters.isTarget) {\n              if (node.data.eMonth) node.data.status = ActivityStatus.Completed;else node.data.status = ActivityStatus.Ongoing;\n            }\n          }\n        });\n        this.gridComponent.gridApi.refreshCells({\n          columns: ['dataStatus', 'status'],\n          force: true,\n          suppressFlash: true\n        });\n        const btnApproveAll = document.querySelector('btn-approve-all-header button');\n        if (!isApproved) {\n          btnApproveAll.classList.add('active');\n          btnApproveAll.title = 'Toggle approve all';\n        } else {\n          btnApproveAll.classList.remove('active');\n          btnApproveAll.title = 'Approve all';\n        }\n        this.messageService.success('The data approve status has been changed.', isApproved ? 'Approval retreated' : 'All approved');\n      }\n    }));\n  }\n  onDownload() {\n    this.downloading = true;\n    this.gridComponent.gridApi.exportDataAsExcel();\n    this.downloading = false;\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sb => sb.unsubscribe());\n  }\n  // Helper method to check if the partner's organization matches the user's organization\n  isUserPartnerOfProject(partner) {\n    if (!partner) return false;\n    // Convert to same type for comparison in case one is string and one is number\n    const partnerOrgId = partner.organizationId !== undefined ? Number(partner.organizationId) : undefined;\n    const partnerOrgIdAlt = partner.orgId !== undefined ? Number(partner.orgId) : undefined;\n    const userOrgIdNum = Number(this.userOrgId);\n    return partnerOrgId === userOrgIdNum || partnerOrgIdAlt === userOrgIdNum;\n  }\n  static #_ = this.ɵfac = function ViewDataComponent_Factory(t) {\n    return new (t || ViewDataComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.ProfileService), i0.ɵɵdirectiveInject(i3.ViewDataService), i0.ɵɵdirectiveInject(i4.SharedService), i0.ɵɵdirectiveInject(i5.MessageService), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: ViewDataComponent,\n    selectors: [[\"aims-view-data\"]],\n    viewQuery: function ViewDataComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(_c0, 5);\n        i0.ɵɵviewQuery(_c1, 5);\n        i0.ɵɵviewQuery(_c2, 5);\n        i0.ɵɵviewQuery(_c3, 5);\n        i0.ɵɵviewQuery(_c4, 5);\n        i0.ɵɵviewQuery(_c5, 5);\n        i0.ɵɵviewQuery(_c6, 5);\n        i0.ɵɵviewQuery(_c7, 5);\n        i0.ɵɵviewQuery(ReadOnlyGridComponent, 7);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.outputFilterCtrl = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.catFilterCtrl = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.profFilterCtrl = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.projGroupsFilterCtrl = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.projFilterCtrl = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.partnerFilterCtrl = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.dStatusFilterCtrl = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.regionFilterCtrl = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.gridComponent = _t.first);\n      }\n    },\n    hostVars: 3,\n    hostBindings: function ViewDataComponent_HostBindings(rf, ctx) {\n      if (rf & 2) {\n        i0.ɵɵattribute(\"data-qs-app-toolbar-fixed\", ctx.pgToolbar)(\"data-qs-app-header-fixed\", ctx.pgHeader)(\"data-qs-app-sidebar-push-toolbar\", ctx.pushToolbar);\n      }\n    },\n    decls: 25,\n    vars: 16,\n    consts: [[1, \"top-toolbar\"], [1, \"d-flex\", \"flex-stack\", \"gap-3\"], [\"tabindex\", \"0\", 1, \"text-center\"], [\"id\", \"dtProgress\", \"type\", \"radio\", \"name\", \"dataType\", \"value\", \"Progress\", \"checked\", \"checked\", 1, \"btn-check\", 3, \"change\"], [\"for\", \"dtProgress\", 1, \"btn\", \"btn-outline\", \"btn-outline-dashed\", \"btn-active-light-primary\", \"d-flex\", \"align-items-center\", \"px-4\", \"py-2\"], [1, \"fw-semibold\", \"d-block\", \"fs-6\"], [\"tabindex\", \"-1\", 1, \"text-center\"], [\"id\", \"dtTarget\", \"type\", \"radio\", \"name\", \"dataType\", \"value\", \"Target\", 1, \"btn-check\", 3, \"change\"], [\"for\", \"dtTarget\", 1, \"btn\", \"btn-outline\", \"btn-outline-dashed\", \"btn-active-light-info\", \"d-flex\", \"align-items-center\", \"px-4\", \"py-2\", 3, \"ngClass\"], [\"class\", \"form-check form-switch form-check-custom form-check-solid cursor-pointer ms-3\", 4, \"ngIf\"], [\"class\", \"notice bg-light-primary rounded border-primary border border-dashed text-truncate p-2 ms-5 fs-8\", \"style\", \"max-width: 50%\", 4, \"ngIf\"], [1, \"app-toolbar\"], [\"class\", \"w-100\", 3, \"showTable\", 4, \"ngIf\"], [\"class\", \"filter-toolbar d-flex flex-stack flex-wrap flex-md-nowrap\", 4, \"ngIf\"], [1, \"card\", \"card-custom\", \"blockui\"], [\"class\", \"py-3 px-6\", 4, \"ngIf\"], [\"class\", \"card-header\", 4, \"ngIf\"], [1, \"card-body\", \"p-0\", \"blockui\"], [4, \"ngIf\"], [\"class\", \"d-flex justify-content-center\", 4, \"ngIf\"], [3, \"ngClass\", \"hideNAPeriods\", \"filterApplied\", \"sortApplied\", \"approve\", \"approveAll\"], [1, \"form-check\", \"form-switch\", \"form-check-custom\", \"form-check-solid\", \"cursor-pointer\", \"ms-3\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"change\"], [1, \"form-check-label\", \"fw-semibold\", 3, \"ngClass\"], [\"type\", \"checkbox\", 1, \"form-check-input\", 3, \"checked\", \"change\"], [1, \"notice\", \"bg-light-primary\", \"rounded\", \"border-primary\", \"border\", \"border-dashed\", \"text-truncate\", \"p-2\", \"ms-5\", \"fs-8\", 2, \"max-width\", \"50%\"], [1, \"fw-semibold\"], [1, \"w-100\", 3, \"showTable\"], [1, \"filter-toolbar\", \"d-flex\", \"flex-stack\", \"flex-wrap\", \"flex-md-nowrap\"], [1, \"d-flex\", \"align-items-center\", \"flex-wrap\", \"flex-md-nowrap\"], [\"class\", \"filter-container\", 3, \"id\", \"placeholders\", \"minWidth\", \"options\", \"multiple\", \"showAll\", \"change\", 4, \"ngIf\"], [1, \"filter-container\", 3, \"id\", \"ngClass\", \"placeholders\", \"minWidth\", \"options\", \"multiple\", \"showSearch\", \"showAll\", \"change\"], [\"project\", \"\"], [1, \"bullet\", \"bg-secondary\", \"h-35px\", \"w-1px\", \"mx-4\"], [1, \"filter-container\", 3, \"id\", \"placeholders\", \"minWidth\", \"options\", \"multiple\", \"change\"], [\"output\", \"\"], [1, \"filter-container\", \"ms-2\", 3, \"id\", \"placeholders\", \"minWidth\", \"options\", \"multiple\", \"change\"], [\"category\", \"\"], [\"profile\", \"\"], [\"class\", \"filter-container \", 3, \"id\", \"placeholders\", \"minWidth\", \"options\", \"multiple\", \"change\", 4, \"ngIf\"], [1, \"filter-container\", \"ms-2\", 3, \"id\", \"placeholders\", \"minWidth\", \"options\", \"showSearch\", \"multiple\", \"selectedValues\", \"change\"], [\"status\", \"\"], [\"region\", \"\"], [\"class\", \"d-flex flex-stack gap-2\", 4, \"ngIf\"], [\"role\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-light-primary\", \"border\", \"border-primary\", \"text-truncate\", \"py-2\", \"px-3\", \"ms-2\", 3, \"click\"], [\"class\", \"cursor-pointer fs-8 ms-3\", 3, \"click\", 4, \"ngIf\"], [1, \"d-flex\", \"align-items-center\"], [1, \"bullet\", \"bg-secondary\", \"h-35px\", \"w-1px\", \"mx-6\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-icon\", \"btn-light\", \"btn-active-color-primary\", \"px-3\", \"w-auto\", 3, \"disabled\", \"click\"], [4, \"ngIf\", \"ngIfElse\"], [\"btnDSpinner\", \"\"], [1, \"filter-container\", 3, \"id\", \"placeholders\", \"minWidth\", \"options\", \"multiple\", \"showAll\", \"change\"], [\"pgroups\", \"\"], [\"partner\", \"\"], [1, \"d-flex\", \"flex-stack\", \"gap-2\"], [1, \"fs-7\", \"text-gray-800\"], [\"id\", \"periodTo\", \"type\", \"month\", \"placement\", \"left\", \"min\", \"2020-01\", 1, \"form-control\", \"form-control-sm\", 3, \"ngbTooltip\", \"change\"], [\"tpTo\", \"\"], [\"id\", \"periodFrom\", \"type\", \"month\", \"placement\", \"left\", \"min\", \"2020-01\", 1, \"form-control\", \"form-control-sm\", 3, \"ngbTooltip\", \"change\"], [\"tpFrom\", \"\"], [\"class\", \"fw-bold\", 4, \"ngIf\"], [1, \"fw-bold\"], [1, \"filter-container\", 3, \"id\", \"placeholders\", \"minWidth\", \"options\", \"showSearch\", \"change\"], [\"qtr\", \"\"], [1, \"filter-container\", \"ms-2\", 3, \"id\", \"placeholders\", \"minWidth\", \"options\", \"change\"], [\"year\", \"\"], [1, \"cursor-pointer\", \"fs-8\", \"ms-3\", 3, \"click\"], [1, \"fas\", \"fa-download\", \"me-2\"], [1, \"indicator-progress\", 2, \"display\", \"block\"], [1, \"spinner-border\", \"spinner-border-sm\", \"align-middle\", \"me-1\"], [1, \"py-3\", \"px-6\"], [1, \"card-header\"], [1, \"card-toolbar\", \"d-flex\", \"flex-stack\", \"flex-md-nowrap\", \"w-100\", \"m-0\"], [1, \"d-flex\", \"mw-85\"], [\"role\", \"tablist\", 1, \"nav\", \"nav-tabs\", \"nav-line-tabs\", \"d-flex\", \"fs-6\", \"border-0\"], [\"class\", \"nav-item\", \"role\", \"presentation\", 3, \"ngbTooltip\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex-end\"], [\"type\", \"button\", \"class\", \"btn btn-sm btn-icon px-3 h-30px w-auto\", 3, \"ngbTooltip\", \"ngClass\", \"click\", 4, \"ngIf\"], [\"groupTooltip\", \"\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-icon\", \"px-3\", \"h-30px\", \"w-auto\", \"ms-2\", 3, \"ngbTooltip\", \"ngClass\", \"click\"], [1, \"fas\", \"fa-sort\", \"me-2\"], [\"sortTooltip\", \"\"], [\"role\", \"presentation\", 1, \"nav-item\", 3, \"ngbTooltip\"], [\"role\", \"tab\", 1, \"nav-link\", \"justify-content-center\", \"text-active-gray-800\", \"text-hover-gray-800\", 3, \"ngClass\", \"tabindex\", \"click\"], [\"htmlTooltip\", \"\"], [1, \"text-start\", 2, \"line-height\", \"normal\"], [1, \"fs-8\", \"fw-semibold\", \"text-gray-600\", \"mb-1\"], [1, \"fs-7\", \"fw-bold\", \"text-gray-800\"], [1, \"fs-9\", \"text-gray-700\", \"mt-2\", \"mb-0\"], [\"type\", \"button\", 1, \"btn\", \"btn-sm\", \"btn-icon\", \"px-3\", \"h-30px\", \"w-auto\", 3, \"ngbTooltip\", \"ngClass\", \"click\"], [1, \"fas\", \"fa-bezier-curve\", \"me-2\"], [\"class\", \"fw-semibold\", 4, \"ngIf\"], [\"class\", \"text-start\", 4, \"ngIf\"], [1, \"text-start\"], [1, \"p-0\", \"fw-semibold\"], [1, \"d-flex\", \"justify-content-center\"], [1, \"notice\", \"bg-light\", \"rounded\", \"border-secondary\", \"border\", \"border-dashed\", \"text-center\", \"py-2\", \"px-5\", \"my-10\", \"fs-6\"]],\n    template: function ViewDataComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"div\", 2)(3, \"input\", 3);\n        i0.ɵɵlistener(\"change\", function ViewDataComponent_Template_input_change_3_listener() {\n          return ctx.onDataTypeChange();\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"label\", 4)(5, \"span\", 5);\n        i0.ɵɵtext(6, \"Progress\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵelementStart(7, \"div\", 6)(8, \"input\", 7);\n        i0.ɵɵlistener(\"change\", function ViewDataComponent_Template_input_change_8_listener() {\n          return ctx.onDataTypeChange(true);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(9, \"label\", 8)(10, \"span\", 5);\n        i0.ɵɵtext(11, \"Target\");\n        i0.ɵɵelementEnd()()();\n        i0.ɵɵtemplate(12, ViewDataComponent_label_12_Template, 4, 4, \"label\", 9)(13, ViewDataComponent_label_13_Template, 4, 5, \"label\", 9)(14, ViewDataComponent_div_14_Template, 4, 2, \"div\", 10);\n        i0.ɵɵelementEnd()();\n        i0.ɵɵelementStart(15, \"div\", 11);\n        i0.ɵɵtemplate(16, ViewDataComponent_aims_loading_16_Template, 1, 1, \"aims-loading\", 12)(17, ViewDataComponent_div_17_Template, 30, 52, \"div\", 13);\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(18, \"div\", 14);\n        i0.ɵɵtemplate(19, ViewDataComponent_aims_loading_19_Template, 1, 0, \"aims-loading\", 15)(20, ViewDataComponent_div_20_Template, 15, 9, \"div\", 16);\n        i0.ɵɵelementStart(21, \"div\", 17);\n        i0.ɵɵtemplate(22, ViewDataComponent_aims_working_22_Template, 1, 0, \"aims-working\", 18)(23, ViewDataComponent_div_23_Template, 3, 0, \"div\", 19);\n        i0.ɵɵelementStart(24, \"aims-readonly-grid\", 20);\n        i0.ɵɵlistener(\"filterApplied\", function ViewDataComponent_Template_aims_readonly_grid_filterApplied_24_listener($event) {\n          return ctx.gridFiltered($event);\n        })(\"sortApplied\", function ViewDataComponent_Template_aims_readonly_grid_sortApplied_24_listener($event) {\n          return ctx.gridSortedBy = $event;\n        })(\"approve\", function ViewDataComponent_Template_aims_readonly_grid_approve_24_listener($event) {\n          return ctx.onApproveData($event);\n        })(\"approveAll\", function ViewDataComponent_Template_aims_readonly_grid_approveAll_24_listener($event) {\n          return ctx.onApproveAll($event);\n        });\n        i0.ɵɵelementEnd()()();\n      }\n      if (rf & 2) {\n        i0.ɵɵadvance(9);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(12, _c23, ctx.filters.isTarget));\n        i0.ɵɵadvance(3);\n        i0.ɵɵproperty(\"ngIf\", ctx.isGlobalUser);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.filters.isTarget);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", ctx.filtered.length > 1 || ctx.gridFilteredBy.length > 1);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.working);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.working);\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.gridWorking);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.gridWorking && (ctx.interventions == null ? null : ctx.interventions.length));\n        i0.ɵɵadvance(2);\n        i0.ɵɵproperty(\"ngIf\", ctx.working && !(ctx.interventions == null ? null : ctx.interventions.length));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngIf\", !ctx.working && !ctx.gridWorking && !(ctx.interventions == null ? null : ctx.interventions.length));\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction1(14, _c24, ctx.gridWorking || !(ctx.interventions == null ? null : ctx.interventions.length)))(\"hideNAPeriods\", ctx.hideNAPeriods);\n      }\n    },\n    dependencies: [i6.NgClass, i6.NgForOf, i6.NgIf, i7.WorkingComponent, i8.LoadingComponent, i9.FilterDropdownList, i10.NgbTooltip, i11.ReadOnlyGridComponent],\n    styles: [\".app-toolbar[_ngcontent-%COMP%] {\\n  top: 50px !important;\\n  padding: 0.5rem 2.25rem;\\n  overflow-x: auto;\\n}\\n\\n@media (min-width: 992px) {\\n  .app-toolbar[_ngcontent-%COMP%] {\\n    height: auto;\\n  }\\n}\\n.top-toolbar[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  position: absolute;\\n  top: 0.5rem;\\n  left: calc(var(--qs-app-sidebar-width) + 20rem);\\n  z-index: 999;\\n}\\n\\n.filter-toolbar[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\nfilter-ddl[_ngcontent-%COMP%] {\\n  max-width: 130px;\\n}\\n\\ninput[type=month][_ngcontent-%COMP%] {\\n  max-width: 115px;\\n  font-size: smaller;\\n  padding: 8px 5px;\\n}\\n\\n.card-custom[_ngcontent-%COMP%] {\\n  border-radius: 0;\\n  height: calc(100vh - 135px);\\n  margin-top: 3.75rem;\\n}\\n.card-custom[_ngcontent-%COMP%]   .mw-85[_ngcontent-%COMP%] {\\n  max-width: 85%;\\n}\\n.card-custom[_ngcontent-%COMP%]   .card-header[_ngcontent-%COMP%] {\\n  min-height: 40px;\\n  padding-top: 5px;\\n  padding-bottom: 0;\\n  margin-bottom: 5px;\\n}\\n\\n\\n\\n\\n\\n.nav-item[_ngcontent-%COMP%] {\\n  cursor: pointer;\\n}\\n\\n.nav-line-tabs[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link.active[_ngcontent-%COMP%], .nav-line-tabs[_ngcontent-%COMP%]   .nav-item.show[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%], .nav-line-tabs[_ngcontent-%COMP%]   .nav-item[_ngcontent-%COMP%]   .nav-link[_ngcontent-%COMP%]:hover:not(.disabled) {\\n  border-bottom-width: 2px;\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n  });\n}", "map": {"version": 3, "names": ["fork<PERSON><PERSON>n", "ActivityStatus", "Region", "compareSort", "getMonthName", "ReadOnlyGridComponent", "i0", "ɵɵelementStart", "ɵɵlistener", "ViewDataComponent_label_12_Template_input_change_1_listener", "ɵɵrestoreView", "_r10", "ctx_r9", "ɵɵnextContext", "ɵɵresetView", "onApprovalMode", "ɵɵelementEnd", "ɵɵtext", "ɵɵadvance", "ɵɵproperty", "ɵɵpureFunction2", "_c8", "ctx_r0", "filters", "approvalMode", "ViewDataComponent_label_13_Template_input_change_1_listener", "_r12", "ctx_r11", "onToggleNAPeriods", "ctx_r1", "hideNAPeriods", "ɵɵtextInterpolate2", "ctx_r2", "filtered", "length", "gridFilteredBy", "ɵɵelement", "ViewDataComponent_div_17_filter_ddl_2_Template_filter_ddl_change_0_listener", "$event", "_r29", "ctx_r28", "onFilterChange", "ɵɵpureFunction0", "_c9", "ctx_r13", "projGroups", "ViewDataComponent_div_17_filter_ddl_13_Template_filter_ddl_change_0_listener", "_r32", "ctx_r31", "_c10", "ctx_r18", "orgs", "ɵɵtextInterpolate", "ctx_r38", "strPeriod", "ɵɵtemplate", "ViewDataComponent_div_17_div_19_ng_container_1_ng_template_4_span_2_Template", "ctx_r36", "ɵɵelementContainerStart", "ViewDataComponent_div_17_div_19_ng_container_1_Template_input_change_3_listener", "_r40", "ctx_r39", "onPeriodChange", "ViewDataComponent_div_17_div_19_ng_container_1_ng_template_4_Template", "ɵɵtemplateRefExtractor", "ɵɵelementContainerEnd", "_r37", "ctx_r41", "ViewDataComponent_div_17_div_19_ng_template_5_span_2_Template", "ctx_r34", "ViewDataComponent_div_17_div_19_ng_container_1_Template", "ViewDataComponent_div_17_div_19_Template_input_change_4_listener", "_r43", "ctx_r42", "ViewDataComponent_div_17_div_19_ng_template_5_Template", "ctx_r21", "_r35", "ViewDataComponent_div_17_ng_container_20_Template_filter_ddl_change_1_listener", "_r47", "ctx_r46", "ViewDataComponent_div_17_ng_container_20_Template_filter_ddl_change_3_listener", "ctx_r48", "_c11", "ctx_r22", "qtrs", "_c12", "years", "ViewDataComponent_div_17_a_23_Template_a_click_0_listener", "_r50", "ctx_r49", "resetFilters", "ViewDataComponent_div_17_filter_ddl_2_Template", "ViewDataComponent_div_17_Template_filter_ddl_change_3_listener", "_r52", "ctx_r51", "ViewDataComponent_div_17_Template_filter_ddl_change_6_listener", "ctx_r53", "ViewDataComponent_div_17_Template_filter_ddl_change_8_listener", "ctx_r54", "ViewDataComponent_div_17_Template_filter_ddl_change_10_listener", "ctx_r55", "ViewDataComponent_div_17_filter_ddl_13_Template", "ViewDataComponent_div_17_Template_filter_ddl_change_14_listener", "ctx_r56", "ViewDataComponent_div_17_Template_filter_ddl_change_16_listener", "ctx_r57", "ViewDataComponent_div_17_div_19_Template", "ViewDataComponent_div_17_ng_container_20_Template", "ViewDataComponent_div_17_Template_button_click_21_listener", "ctx_r58", "onFilterData", "ViewDataComponent_div_17_a_23_Template", "ViewDataComponent_div_17_Template_button_click_26_listener", "ctx_r59", "onDownload", "ViewDataComponent_div_17_ng_container_27_Template", "ViewDataComponent_div_17_ng_template_28_Template", "ctx_r4", "isGlobalUser", "ɵɵpureFunction1", "_c13", "_c14", "projects", "_c15", "outputs", "_c16", "categories", "_c17", "profiles", "_c18", "submitStatus", "_c19", "_c20", "regions", "<PERSON><PERSON><PERSON><PERSON>", "downloading", "working", "gridWorking", "interventions", "_r26", "prof_r68", "category", "code", "name", "abbreviation", "description", "ViewDataComponent_div_20_li_4_Template_a_click_1_listener", "restoredCtx", "_r74", "$implicit", "ctx_r73", "getInterventionData", "id", "ViewDataComponent_div_20_li_4_ng_template_3_Template", "_r71", "_c21", "ctx_r60", "selTabId", "ind_r69", "ViewDataComponent_div_20_button_6_Template_button_click_0_listener", "_r76", "ctx_r75", "enableGridGroupAndPivot", "_r63", "_c22", "ctx_r61", "isPivotEnabled", "ViewDataComponent_div_20_ng_template_7_span_0_Template", "ViewDataComponent_div_20_ng_template_7_div_1_Template", "ctx_r62", "ctx_r65", "gridSortedBy", "ViewDataComponent_div_20_ng_template_13_ng_container_0_Template", "ViewDataComponent_div_20_ng_template_13_div_1_Template", "ctx_r66", "ViewDataComponent_div_20_li_4_Template", "ViewDataComponent_div_20_button_6_Template", "ViewDataComponent_div_20_ng_template_7_Template", "ViewDataComponent_div_20_Template_button_click_9_listener", "_r82", "ctx_r81", "enableGridSort", "ViewDataComponent_div_20_ng_container_11_Template", "ViewDataComponent_div_20_ng_container_12_Template", "ViewDataComponent_div_20_ng_template_13_Template", "ctx_r6", "_r67", "ViewDataComponent", "constructor", "authService", "profService", "dataService", "sharedService", "messageService", "cdr", "pgToolbar", "pg<PERSON><PERSON><PERSON>", "pushToolbar", "isApprover", "userOrgId", "allInterventions", "dataStatus", "subscriptions", "roles", "currentUserValue", "includes", "isLocalUser", "Object", "values", "for<PERSON>ach", "v", "i", "push", "sort", "x", "y", "currYear", "Date", "getFullYear", "String", "org", "ngOnInit", "gridComponent", "getDropdownItems", "ngAfterViewInit", "setTimeout", "initializeProjectFilter", "projFilterCtrl", "options", "items", "showAll", "showSearch", "projGroupsFilterCtrl", "console", "log", "detectChanges", "initializePartnerFilter", "partnerFilterCtrl", "isLDEUser", "projectsApi", "getProjectsWithGrouping", "getProjectsFiltered", "endPoints", "projs", "cats", "getCategories", "profs", "getProfiles", "getOrgsList", "subscribe", "next", "proj", "grouping", "trim", "existingGroup", "find", "pg", "Array", "isArray", "map", "p", "partners", "organizations", "orgId", "organizationId", "projectId", "shortName", "cat", "outputExists", "findIndex", "o", "output", "filter", "c", "error", "err", "complete", "createProfileFilters", "getData", "prof", "catId", "categoryId", "filtersCopy", "JSON", "parse", "stringify", "result", "dataResult", "filteredInterventions", "currentUser", "intervention", "data", "some", "item", "profId", "partner", "fullName", "filterOutNAPeriods", "gridApi", "setFilterModel", "createProfileTabs", "dataByIntervention", "reduce", "groups", "filteredData", "keys", "interventionData", "validActivities", "isOngoingActivity", "status", "hasValidAsOf", "asOf", "hasValidProgressId", "progressId", "period", "periodEnd", "hasProgress<PERSON><PERSON>ues", "colVals", "key", "value", "undefined", "stringValue", "toString", "numValue", "parseFloat", "replace", "isNaN", "activitiesWithDates", "mostRecentActivity", "a", "b", "localeCompare", "ctrl", "<PERSON><PERSON><PERSON><PERSON>", "selVals", "clearSelection", "setSelectedValues", "catFilterCtrl", "profFilterCtrl", "onDataTypeChange", "year", "yearEnd", "selectedVals", "pFrom", "document", "querySelector", "e", "target", "date", "split", "getDate", "outputFilterCtrl", "dStatusFilterCtrl", "regionFilterCtrl", "periodFrom", "getElementById", "periodTo", "dateEnd", "enableHtml", "projIds", "profIds", "catIds", "orgIds", "f", "join", "selProfId", "selProfile", "dynamicColumns", "variables", "type", "renameAsOfCol", "initGrid", "refreshGridRows", "res", "setRowGroupPanelShow", "gridFiltered", "col", "startsWith", "slice", "isSortable", "gridColumnApi", "getColumns", "colId", "getColId", "colDef", "getColDef", "sortable", "setColDef", "refreshHeader", "autoSizeAllColumns", "setPivotMode", "setSideBar", "getRowGroupColumns", "removeRowGroupColumn", "resetSortAndGroup", "defaultColDefs", "onApproveData", "event", "dateSubmitted", "operation", "approveTargetData", "dateApproved", "approveActivityData", "eMonth", "Completed", "Ongoing", "Archived", "refresh<PERSON>ells", "rowNodes", "node", "columns", "force", "suppressFlash", "success", "onApproveAll", "isApproved", "approveIds", "forEachNodeAfterFilter", "btnApproveAll", "classList", "add", "title", "remove", "exportDataAsExcel", "ngOnDestroy", "sb", "unsubscribe", "isUserPartnerOfProject", "partnerOrgId", "Number", "partnerOrgIdAlt", "userOrgIdNum", "_", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "ProfileService", "i3", "ViewDataService", "i4", "SharedService", "i5", "MessageService", "ChangeDetectorRef", "_2", "selectors", "viewQuery", "ViewDataComponent_Query", "rf", "ctx", "ViewDataComponent_Template_input_change_3_listener", "ViewDataComponent_Template_input_change_8_listener", "ViewDataComponent_label_12_Template", "ViewDataComponent_label_13_Template", "ViewDataComponent_div_14_Template", "ViewDataComponent_aims_loading_16_Template", "ViewDataComponent_div_17_Template", "ViewDataComponent_aims_loading_19_Template", "ViewDataComponent_div_20_Template", "ViewDataComponent_aims_working_22_Template", "ViewDataComponent_div_23_Template", "ViewDataComponent_Template_aims_readonly_grid_filterApplied_24_listener", "ViewDataComponent_Template_aims_readonly_grid_sortApplied_24_listener", "ViewDataComponent_Template_aims_readonly_grid_approve_24_listener", "ViewDataComponent_Template_aims_readonly_grid_approveAll_24_listener", "_c23", "_c24"], "sources": ["D:\\UNDP\\AIMS3System\\AIMS3\\ClientApp\\src\\app\\modules\\data\\components\\view-data\\view-data.component.ts", "D:\\UNDP\\AIMS3System\\AIMS3\\ClientApp\\src\\app\\modules\\data\\components\\view-data\\view-data.component.html"], "sourcesContent": ["import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, HostBinding, OnDestroy, OnInit, ViewChild, AfterViewInit } from '@angular/core';\r\nimport { CellClickedEvent } from 'ag-grid-community';\r\nimport { Subscription, forkJoin } from 'rxjs';\r\nimport { FilterDropdownList } from '../../../../shared/components/filter-dropdown/filter-ddl.control';\r\nimport { ActivityStatus, Region } from '../../../../shared/enums';\r\nimport { MessageService } from '../../../../shared/services/message.service';\r\nimport { SharedService } from '../../../../shared/services/shared.service';\r\nimport { compareSort, getMonthName } from '../../../../shared/utilities';\r\nimport { Category } from '../../../admin/models/category.model';\r\nimport { Project } from '../../../admin/models/project.model';\r\nimport { AuthService } from '../../../auth';\r\nimport { ProfileInfo } from '../../../data-entry/models/profile.model';\r\nimport { ProfileService } from '../../../data-entry/services/profile.service';\r\nimport { IDataFilter, IDataIntervention } from '../../models/data.model';\r\nimport { ViewDataService } from '../../services/view-data.service';\r\nimport { ReadOnlyGridComponent } from './grid/readonly-grid.component';\r\n\r\n\r\n@Component({\r\n    selector: 'aims-view-data',\r\n    templateUrl: './view-data.component.html',\r\n    styleUrls: ['./view-data.component.scss']\r\n})\r\nexport class ViewDataComponent implements OnInit, OnDestroy, AfterViewInit {\r\n    @HostBinding('attr.data-qs-app-toolbar-fixed') pgToolbar = 'true';\r\n    @HostBinding('attr.data-qs-app-header-fixed') pgHeader = 'true';\r\n    @HostBinding('attr.data-qs-app-sidebar-push-toolbar') pushToolbar = 'true';\r\n    //appHeader: HTMLElement;\r\n\r\n    // **** Profiles ----------------------------------------------------------\r\n    working: boolean = false; gridWorking: boolean = false;\r\n    isApprover: boolean = false; isGlobalUser: boolean = false;\r\n    downloading: boolean = false;\r\n    userOrgId: number = 0;\r\n\r\n    projGroups: any[] = [];\r\n    projects: Project[] = [];\r\n    outputs = [];\r\n    categories: Category[] = [];\r\n    profiles: any = [];\r\n    orgs: any[] = [];\r\n    submitStatus = [{ id: 0, name: 'Draft' }, { id: 1, name: 'Submitted' }, { id: 2, name: 'Approved' }];\r\n    regions = [];\r\n    qtrs = [{ id: 1, name: 'Q-1' }, { id: 2, name: 'Q-2' }, { id: 3, name: 'Q-3' }, { id: 4, name: 'Q-4' }];\r\n    years = [];\r\n\r\n    interventions: ProfileInfo[] = [];\r\n    allInterventions: ProfileInfo[] = [];\r\n    selTabId: number = 0;\r\n\r\n    filters: IDataFilter = {\r\n        isTarget: false,\r\n        approvalMode: false,\r\n        dataStatus: [1,2]\r\n    };\r\n    filtered: string[] = [''];\r\n    strPeriod: string[] = ['', ''];\r\n\r\n    // Add property to control filtering of activities without meaningful progress data\r\n    hideNAPeriods: boolean = true; // Default to hiding activities with N/A periods or empty progress\r\n\r\n    @ViewChild('output')\r\n    private outputFilterCtrl: FilterDropdownList;\r\n    @ViewChild('category')\r\n    private catFilterCtrl: FilterDropdownList;\r\n    @ViewChild('profile')\r\n    private profFilterCtrl: FilterDropdownList;\r\n\r\n    @ViewChild('pgroups')\r\n    private projGroupsFilterCtrl: FilterDropdownList;\r\n    @ViewChild('project')\r\n    private projFilterCtrl: FilterDropdownList;\r\n    @ViewChild('partner')\r\n    private partnerFilterCtrl: FilterDropdownList;\r\n    @ViewChild('status')\r\n    private dStatusFilterCtrl: FilterDropdownList;\r\n    @ViewChild('region')\r\n    private regionFilterCtrl: FilterDropdownList;\r\n\r\n    // **** Grid --------------------------------------------------------------\r\n    @ViewChild(ReadOnlyGridComponent, { static: true })\r\n    private gridComponent: ReadOnlyGridComponent;\r\n\r\n    private subscriptions: Subscription[] = [];\r\n    constructor(\r\n        private authService: AuthService,\r\n        private profService: ProfileService,\r\n        private dataService: ViewDataService,\r\n        private sharedService: SharedService,\r\n        private messageService: MessageService,\r\n        private cdr: ChangeDetectorRef\r\n    ) {\r\n        const roles = this.authService.currentUserValue.roles;\r\n        this.isGlobalUser = roles.includes('Admin') || roles.includes('Approver') || roles.includes('LocalApprover') || roles.includes('Viewer');\r\n        this.isApprover = roles.includes('Admin') || roles.includes('Approver') || roles.includes('LocalApprover');\r\n        const isLocalUser = roles.includes('DataEntry') || roles.includes('LocalViewer') || roles.includes('LocalApprover');\r\n\r\n        this.filters.dataStatus = [1, 2];\r\n        //this.appHeader = document.querySelector('app-header');\r\n        Object.values(Region)\r\n            .forEach((v, i) => {\r\n                if ((typeof v === 'string' && v !== 'National'))\r\n                    this.regions.push({ id: i, name: v });\r\n            });\r\n        this.regions.sort((x, y) => x.name < y.name ? -1 : 1);\r\n\r\n        const currYear = new Date().getFullYear();\r\n        for (let i = 2020; i <= currYear+5; i++)\r\n            this.years.push({ id: i, name: String(i) });\r\n\r\n        // Initialize userOrgId\r\n        if (this.authService.currentUserValue.org) {\r\n            this.userOrgId = this.authService.currentUserValue.org.id;\r\n        }\r\n    }\r\n\r\n    ngOnInit(): void {\r\n        this.gridComponent.isGlobalUser = this.isGlobalUser;\r\n        this.gridComponent.isApprover = this.isApprover;\r\n        // this.gridComponent.selectedVals.org = this.authService.currentUserValue.org.shortName;\r\n        this.getDropdownItems();\r\n    }\r\n\r\n    // Initialize the project filter dropdown after view init\r\n    ngAfterViewInit(): void {\r\n        // Give the DOM time to initialize\r\n        setTimeout(() => {\r\n            this.initializeProjectFilter();\r\n        }, 500);\r\n    }\r\n\r\n    // Add this new method to handle project filter initialization\r\n    private initializeProjectFilter(): void {\r\n        // Make sure project filter is properly initialized\r\n        if (this.projFilterCtrl) {\r\n            // Ensure we have projects\r\n            if (!this.projects || this.projects.length === 0) {\r\n                return;\r\n            }\r\n            \r\n            // Force a refresh of the options and items\r\n            // Create fresh copies to avoid reference issues\r\n            this.projFilterCtrl.options = [...this.projects];\r\n            this.projFilterCtrl.items = [...this.projects];\r\n            \r\n            // Set the default filter options\r\n            this.projFilterCtrl.showAll = true;\r\n            this.projFilterCtrl.showSearch = true;\r\n            \r\n            // For global users only, also initialize project group filter\r\n            if (this.isGlobalUser && this.projGroupsFilterCtrl) {\r\n                console.log('Initializing project group filter with', this.projGroups.length, 'groups');\r\n                // Create fresh copies to avoid reference issues\r\n                this.projGroupsFilterCtrl.options = [...this.projGroups];\r\n                this.projGroupsFilterCtrl.items = [...this.projGroups];\r\n                this.projGroupsFilterCtrl.showAll = false;\r\n                this.projGroupsFilterCtrl.showSearch = true;\r\n            }\r\n            \r\n            // Force change detection\r\n            this.cdr.detectChanges();\r\n        }\r\n    }\r\n\r\n       // Add this new method to handle partner filter initialization\r\n       private initializePartnerFilter(): void {\r\n        // Only initialize for global users (since partner filter is hidden for local users)\r\n        if (this.isGlobalUser && this.partnerFilterCtrl) {\r\n            // Ensure we have organizations\r\n            if (!this.orgs || this.orgs.length === 0) {\r\n                // console.log('No organizations available to display in partner filter');\r\n                return;\r\n            }\r\n            \r\n            // console.log('Initializing partner filter with', this.orgs.length, 'organizations');\r\n            \r\n            // Force a refresh of the options and items\r\n            // Create fresh copies to avoid reference issues\r\n            this.partnerFilterCtrl.options = [...this.orgs];\r\n            this.partnerFilterCtrl.items = [...this.orgs];\r\n            \r\n            // Set the default filter options\r\n            this.partnerFilterCtrl.showAll = true;\r\n            this.partnerFilterCtrl.showSearch = true;\r\n            \r\n            // Force change detection\r\n            this.cdr.detectChanges();\r\n        }\r\n    }\r\n\r\n    // get categories and profiles\r\n    private getDropdownItems(): void {\r\n        this.working = true;\r\n\r\n        // For LDE users, use the filtered API endpoint\r\n        const roles = this.authService.currentUserValue.roles;\r\n        const isLDEUser = roles.includes('DataEntry') || \r\n                          roles.includes('LocalViewer') || \r\n                          roles.includes('LocalApprover');\r\n        \r\n        // Choose the appropriate API call based on user role\r\n        let projectsApi = this.sharedService.getProjectsWithGrouping();\r\n        if (!this.isGlobalUser && isLDEUser) {\r\n            // Use filtered API which automatically filters by user org ID on server side\r\n            console.log('Using filtered API for LDE user');\r\n            projectsApi = this.sharedService.getProjectsFiltered();\r\n        }\r\n\r\n        let endPoints: any = {\r\n            projs: projectsApi,\r\n            cats: this.sharedService.getCategories(),\r\n            profs: this.profService.getProfiles(),\r\n            orgs: this.sharedService.getOrgsList()\r\n        };\r\n\r\n        this.subscriptions.push(\r\n            forkJoin(endPoints).subscribe({\r\n                next: ({ projs, cats, profs, orgs }) => {\r\n                    // Debug log to check the data received\r\n                    // console.log('Projects from API:', projs);\r\n                    // console.log('Current user org ID:', this.userOrgId);\r\n                    \r\n                    // For GLOBAL users, show all projects and project groups\r\n                    if (this.isGlobalUser) {\r\n                        this.projects = projs;\r\n\r\n                        // Extract all project groups\r\n                        this.projGroups = [];\r\n                        projs.forEach(proj => {\r\n                            if (proj.grouping && proj.grouping.trim() !== '') {\r\n                                const existingGroup = this.projGroups.find(pg => pg.id === proj.grouping);\r\n                                if (!existingGroup) {\r\n                                    this.projGroups.push({ id: proj.grouping, name: proj.grouping });\r\n                                }\r\n                            }\r\n                        });\r\n                        \r\n                        // Sort project groups alphabetically\r\n                        this.projGroups.sort((x, y) => compareSort(x.name, y.name));\r\n                    } else {\r\n                        // For LDE users, use the filtered projects - project group filter is hidden in UI\r\n                        // Check if API returned IProjectIntervention[] or Project[]\r\n                        if (Array.isArray(projs) && projs.length > 0 && 'organizations' in projs[0]) {\r\n                            // Convert IProjectIntervention[] to Project[]\r\n                            console.log('Converting filtered projects to standard format');\r\n                            this.projects = projs.map(p => {\r\n                                return {\r\n                                    id: p.id,\r\n                                    name: p.name,\r\n                                    abbreviation: p.abbreviation,\r\n                                    grouping: p.grouping || '',\r\n                                    partners: p.organizations?.map(orgId => ({\r\n                                        organizationId: orgId,\r\n                                        projectId: p.id\r\n                                    }))\r\n                                };\r\n                            });\r\n                        } else {\r\n                            // Use projects as-is from API\r\n                            this.projects = projs;\r\n                        }\r\n                        \r\n                        console.log(`LDE user projects: ${this.projects.length}`);\r\n                        \r\n                        // Project groups are not needed for LDE users (hidden in UI)\r\n                        this.projGroups = [];\r\n                    }\r\n\r\n                     // Transform organizations to match the expected structure for filter dropdown\r\n                     this.orgs = [];\r\n                     orgs.forEach(org => {\r\n                         this.orgs.push({\r\n                             id: org.id,\r\n                             name: org.shortName\r\n                         });\r\n                     });\r\n                     this.orgs.sort((x, y) => compareSort(x.name, y.name));\r\n                     console.log('Transformed organizations for dropdown:', this.orgs);\r\n                     \r\n                    this.allInterventions = [...profs];\r\n\r\n                    this.categories = cats;\r\n                    this.outputs = [];\r\n                    this.categories.forEach(cat => {\r\n                        cat.name = cat.code + ' ' + cat.name;\r\n\r\n                        const outputExists = this.outputs.findIndex(o => o.name === cat.output) > -1;\r\n                        if (!outputExists) {\r\n                            this.outputs.push({\r\n                                id: cat.output,\r\n                                name: cat.output,\r\n                                categories: this.categories.filter(c => c.output === cat.output)\r\n                            });\r\n                        }\r\n                    });\r\n\r\n                    // Initialize for both global and local users immediately\r\n                    setTimeout(() => {\r\n                        this.initializeProjectFilter();\r\n                        this.initializePartnerFilter();\r\n                    }, 100);\r\n                },\r\n                error: (err) => {\r\n                    this.working = false;\r\n                },\r\n                complete: () => {\r\n                    this.createProfileFilters();\r\n                    this.cdr.detectChanges();\r\n                    this.working = false;\r\n                    this.getData();\r\n                }\r\n            })\r\n        );\r\n    }\r\n\r\n    private createProfileFilters(): void {\r\n        this.profiles = [];\r\n        this.allInterventions.forEach(prof => {\r\n            this.profiles.push({\r\n                id: prof.id,\r\n                catId: prof.categoryId,\r\n                name: prof.name\r\n            });\r\n        });\r\n\r\n        // sort it alphabetically\r\n        this.profiles.sort((x, y) => x.name > y.name ? 1 : -1);\r\n    }\r\n\r\n    // initial data when filter is applied\r\n    private dataResult: IDataIntervention;\r\n    private getData(): void {\r\n        this.gridWorking = true;\r\n        \r\n        // Create a deep copy of the filters to ensure we're sending a fresh object\r\n        const filtersCopy = JSON.parse(JSON.stringify(this.filters));\r\n\r\n        this.subscriptions.push(\r\n            this.dataService.getData(filtersCopy).subscribe({\r\n                next: (result) => {\r\n                    this.dataResult = result;\r\n                },\r\n                error: (err) => {\r\n                    this.gridWorking = false;\r\n                    this.messageService.error('Failed to fetch data. Please try again.', 'Data Fetch Error');\r\n                },\r\n                complete: () => {\r\n                    if (!this.dataResult || !this.dataResult.interventions || this.dataResult.interventions.length === 0) {\r\n                        this.interventions = [];\r\n                        this.gridWorking = false;\r\n                        return;\r\n                    }\r\n                    \r\n                    // Get interventions that match IDs in the data result\r\n                    let filteredInterventions = this.allInterventions.filter(i => \r\n                        this.dataResult.interventions.includes(i.id)\r\n                    );\r\n                    \r\n                    // For local users (non-global), filter interventions to only show those\r\n                    // associated with the user's organization\r\n                    if (!this.isGlobalUser) {\r\n                        const currentUser = this.authService.currentUserValue;\r\n                        if (currentUser.org) {\r\n                            // Filter interventions based on the data associated with the user's organization\r\n                            filteredInterventions = filteredInterventions.filter(intervention => {\r\n                                // Check if there's any data entry associated with this intervention \r\n                                // and the user's organization\r\n                                const orgId = currentUser.org.id;\r\n                                const data = this.dataResult.data;\r\n                                \r\n                                // Check if there's any data entry that matches both the intervention ID and org ID\r\n                                return data.some(item => {\r\n                                    // For progress data, we can check directly\r\n                                    if ('orgId' in item) {\r\n                                        return item.profId === intervention.id && item.orgId === orgId;\r\n                                    }\r\n                                    // For target data, check the partner field\r\n                                    return item.partner && (\r\n                                        item.partner.includes(currentUser.org.shortName) || \r\n                                        item.partner.includes(currentUser.org.fullName)\r\n                                    );\r\n                                });\r\n                            });\r\n                        }\r\n                    }\r\n                    \r\n                    this.interventions = filteredInterventions;\r\n                    this.interventions.sort((x, y) => compareSort(x.category.code, y.category.code) || compareSort(x.name, y.name));\r\n\r\n                    // Filter out data with N/A periods (applies to progress data only)\r\n                    if (this.dataResult.data && !this.filters.isTarget) {\r\n                        this.dataResult.data = this.filterOutNAPeriods(this.dataResult.data);\r\n                    }\r\n\r\n                    this.gridSortedBy = -1;\r\n                    //this.gridGroupedBy = -1;\r\n                    this.gridComponent.gridApi.setFilterModel(null);\r\n                    //this.gridComponent.gridApi.setRowGroupPanelShow('never');\r\n                    if (this.isPivotEnabled)\r\n                        this.enableGridGroupAndPivot();\r\n\r\n                    this.cdr.detectChanges();\r\n\r\n                    this.createProfileTabs();\r\n                    this.gridWorking = false;\r\n                }\r\n            })\r\n        );\r\n    }\r\n\r\n    /**\r\n     * Filter out data rows that have N/A periods (for progress data only) and\r\n     * activities without meaningful cumulative progress data in the filtered time series.\r\n     * This method prioritizes activities with meaningful data but ensures each intervention\r\n     * has at least one activity to prevent empty interventions.\r\n     * \r\n     * @param data The data array to filter\r\n     * @returns Filtered data array with balanced filtering approach\r\n     */\r\n    private filterOutNAPeriods(data: any[]): any[] {\r\n        // If hideNAPeriods is disabled, return all data\r\n        if (!this.hideNAPeriods) {\r\n            return data;\r\n        }\r\n        \r\n        // Group data by intervention (profId) to ensure each intervention has at least one activity\r\n        const dataByIntervention = data.reduce((groups, item) => {\r\n            const profId = item.profId || 'unknown';\r\n            if (!groups[profId]) {\r\n                groups[profId] = [];\r\n            }\r\n            groups[profId].push(item);\r\n            return groups;\r\n        }, {});\r\n        \r\n        const filteredData = [];\r\n        \r\n        // Process each intervention separately\r\n        Object.keys(dataByIntervention).forEach(profId => {\r\n            const interventionData = dataByIntervention[profId];\r\n            const validActivities = [];\r\n            \r\n            // For progress data, apply validation for time series filtering\r\n            if (interventionData.length > 0 && 'asOf' in interventionData[0]) {\r\n                \r\n                // First pass: find activities that meet strict criteria\r\n                for (const item of interventionData) {\r\n                    // Check if this is an ongoing activity - only apply strict filtering to ongoing activities\r\n                    const isOngoingActivity = item.status === 0; // ActivityStatus.Ongoing = 0\r\n                    \r\n                    // STEP 1: Basic validation - must have valid asOf and progressId\r\n                    const hasValidAsOf = item.asOf && \r\n                                       item.asOf !== 'N/A' && \r\n                                       item.asOf.trim() !== '';\r\n                    \r\n                    const hasValidProgressId = item.progressId && item.progressId > 0;\r\n                    \r\n                    if (hasValidAsOf && hasValidProgressId) {\r\n                        if (isOngoingActivity) {\r\n                            // STEP 2: For ongoing activities with time series filtering, validate meaningful progress data\r\n                            if (this.filters.period > 0 || this.filters.periodEnd > 0) {\r\n                                const hasProgressValues = item.colVals &&\r\n                                                        Object.keys(item.colVals).some(key => {\r\n                                                            const value = item.colVals[key];\r\n\r\n                                                            if (value === null || value === undefined || value === '') {\r\n                                                                return false;\r\n                                                            }\r\n\r\n                                                            const stringValue = value.toString().trim();\r\n\r\n                                                            // Filter out only truly empty/invalid values - allow zeros as valid cumulative progress\r\n                                                            if (stringValue === '' ||\r\n                                                                stringValue === 'N/A' ||\r\n                                                                stringValue === '-' ||\r\n                                                                stringValue === 'null' ||\r\n                                                                stringValue === 'undefined') {\r\n                                                                return false;\r\n                                                            }\r\n\r\n                                                            // For numeric values, allow zero and positive values as valid cumulative progress\r\n                                                            const numValue = parseFloat(stringValue.replace(/,/g, ''));\r\n                                                            if (!isNaN(numValue)) {\r\n                                                                return numValue >= 0; // Allow zero as valid cumulative progress\r\n                                                            }\r\n\r\n                                                            // For non-numeric values, they should be meaningful strings\r\n                                                            return stringValue.length > 0;\r\n                                                        });\r\n                                \r\n                                if (hasProgressValues) {\r\n                                    validActivities.push(item);\r\n                                }\r\n                            } else {\r\n                                // No time series filtering for ongoing activities, include if basic criteria are met\r\n                                validActivities.push(item);\r\n                            }\r\n                        } else {\r\n                            // For non-ongoing activities (Completed, Archived, Cancelled), apply more lenient filtering\r\n                            // Just check that it's not completely empty\r\n                            validActivities.push(item);\r\n                        }\r\n                    }\r\n                }\r\n                \r\n                // If no activities passed strict validation, include the most recent activity\r\n                // to ensure the intervention doesn't disappear completely\r\n                if (validActivities.length === 0 && interventionData.length > 0) {\r\n                    // Find the most recent activity (prefer those with valid asOf dates)\r\n                    const activitiesWithDates = interventionData.filter(item => \r\n                        item.asOf && item.asOf !== 'N/A' && item.asOf.trim() !== ''\r\n                    );\r\n                    \r\n                    let mostRecentActivity;\r\n                    if (activitiesWithDates.length > 0) {\r\n                        // Sort by asOf date (assuming format like \"dd MMM yyyy\")\r\n                        mostRecentActivity = activitiesWithDates.sort((a, b) => {\r\n                            // Simple date comparison - in a real scenario you'd want proper date parsing\r\n                            return b.asOf.localeCompare(a.asOf);\r\n                        })[0];\r\n                    } else {\r\n                        // Fallback to first activity if no valid dates\r\n                        mostRecentActivity = interventionData[0];\r\n                    }\r\n                    \r\n                    validActivities.push(mostRecentActivity);\r\n                }\r\n                \r\n                filteredData.push(...validActivities);\r\n            } else {\r\n                // For non-progress data (like target data), keep all items\r\n                filteredData.push(...interventionData);\r\n            }\r\n        });\r\n        \r\n        return filteredData;\r\n    }\r\n\r\n    onFilterChange(ctrl): void {\r\n        // Check if event structure has changed to use selectedValues instead of selVals\r\n        const selectedValues = ctrl.selectedValues || ctrl.selVals || [];\r\n\r\n        if (ctrl.id === 'projGroups') {\r\n            // Only relevant for global users now, since project group filter is hidden for LDE\r\n            this.projFilterCtrl.clearSelection();\r\n\r\n            if (!selectedValues.length) {\r\n                this.projFilterCtrl.options = [...this.projects];\r\n            } else {\r\n                this.projFilterCtrl.options = this.projects.filter(p => \r\n                    p.grouping && selectedValues.includes(p.grouping)\r\n                );\r\n                this.projFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));\r\n\r\n                // Auto-select all projects in the selected groups\r\n                this.projFilterCtrl.setSelectedValues(this.projFilterCtrl.options.map(o => o.id));\r\n            }\r\n            \r\n            this.projFilterCtrl.items = [...this.projFilterCtrl.options];\r\n        } else if (ctrl.id === 'outputs') {\r\n            this.catFilterCtrl.clearSelection();\r\n            this.profFilterCtrl.clearSelection();\r\n\r\n            if (!selectedValues.length) {\r\n                this.catFilterCtrl.options = [...this.categories];\r\n                this.profFilterCtrl.options = [...this.profiles];\r\n            } else {\r\n                this.catFilterCtrl.options = this.categories.filter(c => selectedValues.includes(c.output));\r\n                this.catFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));\r\n                this.profFilterCtrl.options = this.profiles.filter(p => this.catFilterCtrl.options.findIndex(c => c.id === p.catId) > -1);\r\n                this.profFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));\r\n            }\r\n\r\n            this.catFilterCtrl.items = [...this.catFilterCtrl.options];\r\n            this.profFilterCtrl.items = [...this.profFilterCtrl.options];\r\n        } else if (ctrl.id === 'catIds') {\r\n            this.profFilterCtrl.clearSelection();\r\n\r\n            if (!selectedValues.length) {\r\n                this.profFilterCtrl.options = [...this.profiles];\r\n            } else {\r\n                this.profFilterCtrl.options = this.profiles.filter(p => selectedValues.includes(p.catId));\r\n                this.profFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));\r\n            }\r\n            this.profFilterCtrl.items = [...this.profFilterCtrl.options];\r\n        }\r\n\r\n        if(ctrl.id === 'period' || ctrl.id === 'year')\r\n            this.filters[ctrl.id] = selectedValues[0];\r\n        else\r\n            this.filters[ctrl.id] = selectedValues;\r\n    }\r\n\r\n    onDataTypeChange(isTarget?: boolean): void {\r\n        this.filters.isTarget = isTarget || false;\r\n        this.filters.period = 0;\r\n        this.filters.periodEnd = 0;\r\n        this.filters.year = 0;\r\n        this.filters.yearEnd = 0;\r\n\r\n        // Reset hideNAPeriods to true when switching to Progress data\r\n        if (!this.filters.isTarget) {\r\n            this.hideNAPeriods = true;\r\n        }\r\n\r\n        // reapply filter\r\n        this.gridComponent.selectedVals.isTarget = isTarget;\r\n        this.getData();\r\n    }\r\n\r\n    onApprovalMode(): void {\r\n        this.filters.approvalMode = !this.filters.approvalMode;\r\n        this.gridComponent.approvalMode = this.filters.approvalMode;\r\n\r\n        if (this.filters.approvalMode) {\r\n            const pFrom = document.querySelector('#periodFrom') as HTMLInputElement;\r\n            if (pFrom)\r\n                pFrom.value = '';\r\n        }\r\n\r\n        // reapply filter\r\n        this.getData();\r\n    }\r\n\r\n    onToggleNAPeriods(): void {\r\n        this.hideNAPeriods = !this.hideNAPeriods;\r\n        \r\n        // Refresh the data to apply/remove the N/A filter\r\n        this.getData();\r\n    }\r\n\r\n    onPeriodChange(e: any): void {\r\n        const target = e.target as HTMLInputElement;\r\n        const date = target.value?.split('-');\r\n        \r\n        if (target.id === 'periodFrom') {\r\n            if (!date || date.length < 2) {\r\n                this.filters.period = 0;\r\n                this.filters.year = 0;\r\n                this.strPeriod[0] = '';\r\n            } else {\r\n                this.filters.period = +date[1];\r\n                this.filters.year = +date[0];\r\n                this.strPeriod[0] = `01 ${ getMonthName(this.filters.period) } ${ this.filters.year }`;\r\n            }\r\n        } else {\r\n            if (!date || date.length < 2) {\r\n                this.filters.periodEnd = 0;\r\n                this.filters.yearEnd = 0;\r\n                this.strPeriod[1] = '';\r\n            } else {\r\n                this.filters.periodEnd = +date[1];\r\n                this.filters.yearEnd = +date[0];\r\n                this.strPeriod[1] = new Date(this.filters.yearEnd, this.filters.periodEnd, 0).getDate() +\r\n                    ` ${getMonthName(this.filters.periodEnd)} ${this.filters.yearEnd}`;\r\n            }\r\n        }\r\n    }\r\n\r\n    resetFilters(): void {\r\n        // Reset all filter controls\r\n        if (this.isGlobalUser && this.projGroupsFilterCtrl) {\r\n            this.projGroupsFilterCtrl.clearSelection();\r\n        }\r\n        \r\n        if (this.projFilterCtrl) {\r\n            this.projFilterCtrl.clearSelection();\r\n            this.projFilterCtrl.options = [...this.projects];\r\n            this.projFilterCtrl.items = [...this.projFilterCtrl.options];\r\n        }\r\n        \r\n        if (this.outputFilterCtrl) {\r\n            this.outputFilterCtrl.clearSelection();\r\n        }\r\n        \r\n        if (this.catFilterCtrl) {\r\n            this.catFilterCtrl.clearSelection();\r\n        }\r\n        \r\n        if (this.profFilterCtrl) {\r\n            this.profFilterCtrl.clearSelection();\r\n        }\r\n        \r\n        if (this.isGlobalUser && this.partnerFilterCtrl) {\r\n            this.partnerFilterCtrl.clearSelection();\r\n        }\r\n        \r\n        if (this.dStatusFilterCtrl) {\r\n            this.dStatusFilterCtrl.setSelectedValues([1, 2]);\r\n        }\r\n        \r\n        if (this.regionFilterCtrl) {\r\n            this.regionFilterCtrl.clearSelection();\r\n        }\r\n\r\n        // Reset filters object\r\n        this.filters = {\r\n            isTarget: this.filters.isTarget,\r\n            approvalMode: this.filters.approvalMode,\r\n            dataStatus: [1, 2]\r\n        };\r\n        \r\n        // Reset hideNAPeriods to default (true)\r\n        this.hideNAPeriods = true;\r\n        \r\n        this.filtered = [''];\r\n\r\n        // Clear period dates\r\n        const periodFrom = document.getElementById('periodFrom') as HTMLInputElement;\r\n        const periodTo = document.getElementById('periodTo') as HTMLInputElement;\r\n        if (periodFrom) periodFrom.value = '';\r\n        if (periodTo) periodTo.value = '';\r\n        \r\n        // Reset target-specific filters\r\n        if (this.filters.isTarget) {\r\n            if (this.projFilterCtrl) {\r\n                this.projFilterCtrl.clearSelection();\r\n            }\r\n        }\r\n\r\n        this.getData();\r\n    }\r\n\r\n    onFilterData(): void {\r\n        if (!this.filters.approvalMode) {\r\n            if (this.filters.period > 0 && this.filters.year > 0 &&\r\n                this.filters.periodEnd > 0 && this.filters.yearEnd > 0) {\r\n                const date = new Date(this.filters.year, this.filters.period - 1, 1);\r\n                const dateEnd = new Date(this.filters.yearEnd, this.filters.periodEnd - 1, 1);\r\n\r\n                if (dateEnd < date) {\r\n                    this.messageService.error('<span class=\"fw-semibold\">Period (To)</span> should be greater ' +\r\n                        'than <span class=\"fw-semibold\">Period (From)</span>.', 'Invalid period', { enableHtml: true });\r\n                    return;\r\n                }\r\n            }\r\n        }\r\n\r\n        // reapply filter\r\n        this.getData();\r\n\r\n        // create filters\r\n        this.filtered = [''];\r\n        if (this.filters.projGroups?.length)\r\n            this.filtered.push('Project Group');\r\n        if (this.filters.projIds?.length)\r\n            this.filtered.push('Project');\r\n        if (this.filters.profIds?.length)\r\n            this.filtered.push('Intervention');\r\n        else if (this.filters.catIds?.length)\r\n            this.filtered.push('Category');\r\n        else if (this.filters.outputs?.length)\r\n            this.filtered.push('Output');\r\n        if (this.filters.orgIds?.length)\r\n            this.filtered.push('Partner');\r\n        if (this.filters.regions?.length)\r\n            this.filtered.push('Region');\r\n        if (this.filters.period > 0 || this.filters.periodEnd > 0)\r\n            this.filtered.push('Period');\r\n\r\n        this.filtered[0] = this.filtered.filter((f, i) => i > 0).join(', ');\r\n    }\r\n\r\n    private createProfileTabs(selProfId?: number): void {\r\n        let selProfile;\r\n\r\n        if (!selProfId)\r\n            selProfId = this.dataResult.interventions[0];\r\n\r\n        selProfile = this.interventions.find(i => i.id === selProfId);\r\n\r\n        if (selProfile) {\r\n            this.selTabId = selProfile.id;\r\n            this.gridComponent.selectedVals.profId = selProfile.id;\r\n            this.gridComponent.selectedVals.prof = selProfile.abbreviation;\r\n    \r\n            if (!this.filters.isTarget) {\r\n                this.gridComponent.dynamicColumns = selProfile.variables\r\n                    .filter(v => [2, 4, 5].includes(v.type)); // ColumnVarType.Progress, Info, Static\r\n            } else {\r\n                this.gridComponent.dynamicColumns = selProfile.variables\r\n                    .filter(v => [0, 1, 3].includes(v.type)); // ColumnVarType.Target, Info, Static\r\n            }\r\n\r\n            this.gridComponent.renameAsOfCol = false;\r\n            if(this.filters.period && this.filters.periodEnd)\r\n                this.gridComponent.renameAsOfCol = true;\r\n            \r\n            this.gridComponent.initGrid(this.filters.isTarget);\r\n            this.gridComponent.refreshGridRows(this.dataResult.data);\r\n        }\r\n    }\r\n\r\n    getInterventionData(profId: number): void {\r\n        this.gridComponent.working = true;\r\n\r\n        this.subscriptions.push(\r\n            this.dataService.getInterventionData(profId, this.dataResult.filters).subscribe({\r\n            next: (res) => {\r\n                this.dataResult.data = [...res];\r\n            },\r\n            error: (err) => {\r\n                this.gridComponent.working = false;\r\n            },\r\n            complete: () => {\r\n                // reset grid filters, sorting, grouping\r\n                this.gridSortedBy = -1;\r\n                //this.gridGroupedBy = -1;\r\n                if (this.isPivotEnabled)\r\n                    this.enableGridGroupAndPivot();\r\n\r\n                this.gridComponent.gridApi.setFilterModel(null);\r\n                this.gridComponent.gridApi.setRowGroupPanelShow('never');\r\n\r\n                this.createProfileTabs(profId);\r\n                this.gridComponent.working = false;\r\n            }\r\n        }));\r\n    }\r\n\r\n    gridFilteredBy: string[] = ['-'];\r\n    gridFiltered(col: string): void {\r\n        if (col.startsWith('-')) {\r\n            col = col.replace('-', '');\r\n            this.gridFilteredBy = this.gridFilteredBy.filter(c => c !== col);\r\n        } else if (!this.gridFilteredBy.includes(col))\r\n            this.gridFilteredBy.push(col);\r\n        \r\n        this.gridFilteredBy[0] = this.gridFilteredBy\r\n            .slice(1, this.gridFilteredBy.length).join(', ');\r\n    }\r\n\r\n    gridSortedBy: number = -1;\r\n    enableGridSort(): void {\r\n        let isSortable = false;\r\n        if (this.gridSortedBy === -1) {\r\n            isSortable = true;\r\n            this.gridSortedBy = 0;\r\n        } else {\r\n            isSortable = false;\r\n            this.gridSortedBy = -1;\r\n        }\r\n        this.gridComponent.gridColumnApi.getColumns().forEach(col => {\r\n            const colId = col.getColId();\r\n            if (!colId.startsWith('new') && !colId.startsWith('cols-hidden')) {\r\n                let colDef = col.getColDef();\r\n                colDef.sortable = isSortable;\r\n                col.setColDef(colDef, col.getColDef());\r\n            }\r\n        });\r\n\r\n        this.gridComponent.gridApi.refreshHeader();\r\n        this.gridComponent.gridColumnApi.autoSizeAllColumns();\r\n    }\r\n    /*\r\n    gridGroupedBy: number = -1;\r\n    enableGridGroup(): void {\r\n        let enableRowGroup = false;\r\n        if (this.gridGroupedBy === -1) {\r\n            enableRowGroup = true;\r\n            this.gridComponent.gridApi.setRowGroupPanelShow('always');\r\n            this.gridGroupedBy = 0;\r\n        } else {\r\n            enableRowGroup = false;\r\n            this.gridComponent.gridApi.setRowGroupPanelShow('never');\r\n            this.gridComponent.gridColumnApi.getRowGroupColumns().forEach(col => {\r\n                this.gridComponent.gridColumnApi.removeRowGroupColumn(col.getColId());\r\n            });\r\n            this.gridGroupedBy = -1;\r\n        }\r\n        this.gridComponent.gridColumnApi.getColumns().forEach(col => {\r\n            const colId = col.getColId();\r\n            if (!colId.startsWith('cols-hidden')) {\r\n                let colDef = col.getColDef();\r\n                colDef.enableRowGroup = enableRowGroup;\r\n                col.setColDef(colDef, col.getColDef());\r\n            }\r\n        });\r\n    }\r\n    */\r\n    isPivotEnabled: boolean = false;\r\n    enableGridGroupAndPivot(): void {\r\n        if (this.isPivotEnabled) {\r\n            this.gridComponent.gridApi.setPivotMode(false);\r\n            this.gridComponent.gridApi.setSideBar(false);\r\n\r\n            this.gridComponent.gridColumnApi.getRowGroupColumns().forEach(col => {\r\n                this.gridComponent.gridColumnApi.removeRowGroupColumn(col.getColId());\r\n            });\r\n\r\n            this.isPivotEnabled = false;\r\n            return;\r\n        }\r\n\r\n        //this.gridComponent.gridApi.setPivotMode(true);\r\n        this.gridComponent.gridApi.setSideBar(['columns', 'filters']);\r\n        this.isPivotEnabled = true;\r\n    }\r\n\r\n    private resetSortAndGroup(): void {\r\n        if (this.gridSortedBy > -1) {\r\n            this.gridComponent.defaultColDefs.sortable = false;\r\n            this.gridSortedBy = -1;\r\n        }\r\n\r\n        if (this.isPivotEnabled)\r\n            this.enableGridGroupAndPivot();\r\n        /*if (this.gridGroupedBy > -1) {\r\n            this.gridComponent.defaultColDefs.enableRowGroup = false;\r\n            this.gridComponent.gridApi.setRowGroupPanelShow('never');\r\n            this.gridComponent.gridColumnApi.getRowGroupColumns().forEach(col => {      // remove all group cols except status\r\n                this.gridComponent.gridColumnApi.removeRowGroupColumn(col.getColId());\r\n            });\r\n            this.gridGroupedBy = -1;\r\n        }*/\r\n    }\r\n\r\n    /** Approve data */\r\n    onApproveData(event: CellClickedEvent): void {\r\n        if (!event.data.dateSubmitted)\r\n            return;\r\n\r\n        let operation;\r\n        if (this.filters.isTarget) {\r\n            operation =\r\n                this.dataService.approveTargetData([event.data.id],\r\n                    event.data.dateApproved ? true : false)\r\n        } else {\r\n            operation = \r\n                this.dataService.approveActivityData([event.data.progressId],\r\n                    event.data.dateApproved ? true : false);\r\n        }\r\n\r\n        this.subscriptions.push(\r\n            operation.subscribe({\r\n                error: (err) => {\r\n                    console.error(err);\r\n                },\r\n                complete: () => {\r\n                    if (event.data.dateApproved) {\r\n                        event.data.dateApproved = null;\r\n\r\n                        // change status\r\n                        if (!this.filters.isTarget) {\r\n                            if (event.data.eMonth)\r\n                                event.data.status = ActivityStatus.Completed;\r\n                            else\r\n                                event.data.status = ActivityStatus.Ongoing;\r\n                        }\r\n                    } else {\r\n                        event.data.dateApproved = new Date();\r\n\r\n                        // change status to 'Archived'\r\n                        if (!this.filters.isTarget && event.data.status === ActivityStatus.Completed)\r\n                            event.data.status = ActivityStatus.Archived;\r\n                    }\r\n\r\n                    this.gridComponent.gridApi.refreshCells({\r\n                        rowNodes: [event.node],\r\n                        columns: ['dataStatus','status'],\r\n                        force: true,\r\n                        suppressFlash: true\r\n                    });\r\n\r\n                    this.messageService.success('The data approve status has been changed.',\r\n                        event.data.dateApproved ? 'Approved' : 'Approval retreated');\r\n                }\r\n        }));\r\n    }\r\n\r\n    /** Approve all data */\r\n    onApproveAll(isApproved?: boolean): void {\r\n        let approveIds: number[] = [];\r\n\r\n        this.gridComponent.gridApi.forEachNodeAfterFilter(node => {\r\n            if (node.data.dateSubmitted) {\r\n                if (this.filters.isTarget)\r\n                    approveIds.push(node.data.id);\r\n                else\r\n                    approveIds.push(node.data.progressId);\r\n            }\r\n        });\r\n\r\n        const operation = this.filters.isTarget\r\n            ? this.dataService.approveTargetData(approveIds, isApproved)\r\n            : this.dataService.approveActivityData(approveIds, isApproved);\r\n\r\n        this.subscriptions.push(\r\n            operation.subscribe({\r\n                error: (err) => {\r\n                    console.error(err);\r\n                },\r\n                complete: () => {\r\n                    this.gridComponent.gridApi.forEachNodeAfterFilter(node => {\r\n                        if (isApproved) {\r\n                            node.data.dateApproved = null;\r\n\r\n                            // change status\r\n                            if (!this.filters.isTarget) {\r\n                                if (node.data.eMonth)\r\n                                    node.data.status = ActivityStatus.Completed;\r\n                                else\r\n                                    node.data.status = ActivityStatus.Ongoing;\r\n                            }\r\n                        } else if (node.data.dateSubmitted) {\r\n                            node.data.dateApproved = new Date();\r\n\r\n                            // change status to 'Archived'\r\n                            if (!this.filters.isTarget && node.data.status === ActivityStatus.Completed)\r\n                                node.data.status = ActivityStatus.Archived;\r\n                        } else {\r\n                            node.data.dateApproved = null;\r\n\r\n                            // change status\r\n                            if (!this.filters.isTarget) {\r\n                                if (node.data.eMonth)\r\n                                    node.data.status = ActivityStatus.Completed;\r\n                                else\r\n                                    node.data.status = ActivityStatus.Ongoing;\r\n                            }\r\n                        }\r\n                    });\r\n\r\n                    this.gridComponent.gridApi.refreshCells({\r\n                        columns: ['dataStatus','status'],\r\n                        force: true,\r\n                        suppressFlash: true\r\n                    });\r\n\r\n                    const btnApproveAll: HTMLButtonElement = document.querySelector('btn-approve-all-header button');\r\n                    if (!isApproved) {\r\n                        btnApproveAll.classList.add('active');\r\n                        btnApproveAll.title = 'Toggle approve all';\r\n                    } else {\r\n                        btnApproveAll.classList.remove('active');\r\n                        btnApproveAll.title = 'Approve all';\r\n                    }\r\n\r\n                    this.messageService.success('The data approve status has been changed.',\r\n                        isApproved ? 'Approval retreated' : 'All approved');\r\n                }\r\n            }));\r\n    }\r\n\r\n    onDownload(): void {\r\n        this.downloading = true;\r\n        this.gridComponent.gridApi.exportDataAsExcel();\r\n        this.downloading = false;\r\n    }\r\n\r\n    ngOnDestroy() {\r\n        this.subscriptions.forEach((sb) => sb.unsubscribe());\r\n    }\r\n\r\n    // Helper method to check if the partner's organization matches the user's organization\r\n    private isUserPartnerOfProject(partner: any): boolean {\r\n        if (!partner) return false;\r\n        \r\n        // Convert to same type for comparison in case one is string and one is number\r\n        const partnerOrgId = partner.organizationId !== undefined ? Number(partner.organizationId) : undefined;\r\n        const partnerOrgIdAlt = partner.orgId !== undefined ? Number(partner.orgId) : undefined;\r\n        const userOrgIdNum = Number(this.userOrgId);\r\n        \r\n        return partnerOrgId === userOrgIdNum || partnerOrgIdAlt === userOrgIdNum;\r\n    }\r\n}", "<div class=\"top-toolbar\">\r\n    <div class=\"d-flex flex-stack gap-3\">\r\n        <div class=\"text-center\" tabindex=\"0\">\r\n            <input id=\"dtProgress\" type=\"radio\" class=\"btn-check\" name=\"dataType\" value=\"Progress\" checked=\"checked\"\r\n                   (change)=\"onDataTypeChange()\">\r\n            <label for=\"dtProgress\" class=\"btn btn-outline btn-outline-dashed btn-active-light-primary d-flex align-items-center px-4 py-2\">\r\n                <span class=\"fw-semibold d-block fs-6\">Progress</span>\r\n            </label>\r\n        </div>\r\n        <div class=\"text-center\" tabindex=\"-1\">\r\n            <input id=\"dtTarget\" type=\"radio\" class=\"btn-check\" name=\"dataType\" value=\"Target\" (change)=\"onDataTypeChange(true)\">\r\n            <label for=\"dtTarget\" class=\"btn btn-outline btn-outline-dashed btn-active-light-info d-flex align-items-center px-4 py-2\"\r\n                   [ngClass]=\"{'border-info':filters.isTarget}\">\r\n                <span class=\"fw-semibold d-block fs-6\">Target</span>\r\n            </label>\r\n        </div>\r\n        <label class=\"form-check form-switch form-check-custom form-check-solid cursor-pointer ms-3\" *ngIf=\"isGlobalUser\">\r\n            <input class=\"form-check-input\" type=\"checkbox\" (change)=\"onApprovalMode()\">\r\n            <span class=\"form-check-label fw-semibold\" [ngClass]=\"{'text-primary':filters.approvalMode, 'text-muted': !filters.approvalMode}\">Approval mode</span>\r\n        </label>\r\n        <label class=\"form-check form-switch form-check-custom form-check-solid cursor-pointer ms-3\" *ngIf=\"!filters.isTarget\">\r\n            <input class=\"form-check-input\" type=\"checkbox\" [checked]=\"hideNAPeriods\" (change)=\"onToggleNAPeriods()\">\r\n            <span class=\"form-check-label fw-semibold\" [ngClass]=\"{'text-primary':hideNAPeriods, 'text-muted': !hideNAPeriods}\">Hide empty progress (ongoing activities)</span>\r\n        </label>\r\n        <div class=\"notice bg-light-primary rounded border-primary border border-dashed text-truncate p-2 ms-5 fs-8\"\r\n             style=\"max-width: 50%\" *ngIf=\"filtered.length > 1 || gridFilteredBy.length > 1\">\r\n            <span class=\"fw-semibold\">Filtered by:</span> {{ filtered[0] }}{{filtered.length > 1 && gridFilteredBy.length > 1 ? ', ' + gridFilteredBy[0] : gridFilteredBy[0]}}\r\n        </div>\r\n    </div>\r\n</div>\r\n<div class=\"app-toolbar\">\r\n    <aims-loading class=\"w-100\" [showTable]=\"false\" *ngIf=\"working\"></aims-loading>\r\n    <div class=\"filter-toolbar d-flex flex-stack flex-wrap flex-md-nowrap\" *ngIf=\"!working\">\r\n        <!-- Filters -->\r\n        <div class=\"d-flex align-items-center flex-wrap flex-md-nowrap\">\r\n            <filter-ddl [id]=\"'projGroups'\" #pgroups class=\"filter-container\" [placeholders]=\"['Proj. group']\" [minWidth]=\"130\"\r\n                        [options]=\"projGroups\" (change)=\"onFilterChange($event)\" [multiple]=\"true\" [showAll]=\"false\"\r\n                        *ngIf=\"isGlobalUser\">\r\n            </filter-ddl>\r\n            <filter-ddl [id]=\"'projIds'\" #project class=\"filter-container\" [ngClass]=\"{'ms-2': isGlobalUser}\" [placeholders]=\"['Project']\" [minWidth]=\"150\"\r\n                        [options]=\"projects\" (change)=\"onFilterChange($event)\" [multiple]=\"true\" [showSearch]=\"true\" [showAll]=\"true\">\r\n            </filter-ddl>\r\n            <div class=\"bullet bg-secondary h-35px w-1px mx-4\"></div>\r\n            <filter-ddl [id]=\"'outputs'\" #output class=\"filter-container\" [placeholders]=\"['Output']\" [minWidth]=\"100\"\r\n                        [options]=\"outputs\" (change)=\"onFilterChange($event)\" [multiple]=\"true\">\r\n            </filter-ddl>\r\n            <filter-ddl [id]=\"'catIds'\" #category class=\"filter-container ms-2\" [placeholders]=\"['Category','Categories']\" [minWidth]=\"140\"\r\n                        [options]=\"categories\" (change)=\"onFilterChange($event)\" [multiple]=\"true\">\r\n            </filter-ddl>\r\n            <filter-ddl [id]=\"'profIds'\" #profile class=\"filter-container ms-2\" [placeholders]=\"['Intervention']\" [minWidth]=\"120\"\r\n                        [options]=\"profiles\" (change)=\"onFilterChange($event)\" [multiple]=\"true\">\r\n            </filter-ddl>\r\n            <div class=\"bullet bg-secondary h-35px w-1px mx-4\"></div>\r\n            <filter-ddl [id]=\"'orgIds'\" #partner class=\"filter-container \" [placeholders]=\"['Partner']\" [minWidth]=\"130\"\r\n                        [options]=\"orgs\" (change)=\"onFilterChange($event)\" [multiple]=\"true\" *ngIf=\"isGlobalUser\">\r\n            </filter-ddl>\r\n            <filter-ddl [id]=\"'dataStatus'\" #status class=\"filter-container ms-2\" [placeholders]=\"['Data type']\" [minWidth]=\"130\"\r\n                        [options]=\"submitStatus\" [showSearch]=\"false\" [multiple]=\"true\" [selectedValues]=\"[1,2]\" (change)=\"onFilterChange($event)\">\r\n            </filter-ddl>\r\n            <filter-ddl [id]=\"'regions'\" #region class=\"filter-container ms-2\" [placeholders]=\"['Region']\" [minWidth]=\"130\"\r\n                        [options]=\"regions\" [multiple]=\"true\" (change)=\"onFilterChange($event)\">\r\n            </filter-ddl>\r\n            <div class=\"bullet bg-secondary h-35px w-1px mx-4\"></div>\r\n            <div class=\"d-flex flex-stack gap-2\" *ngIf=\"!filters.isTarget\">\r\n                <ng-container *ngIf=\"!filters.approvalMode\">\r\n                    <span class=\"fs-7 text-gray-800\">From</span>\r\n                    <input id=\"periodFrom\" type=\"month\" class=\"form-control form-control-sm\" (change)=\"onPeriodChange($event)\"\r\n                           [ngbTooltip]=\"tpFrom\" placement=\"left\" min=\"2020-01\" />\r\n                    <ng-template #tpFrom>\r\n                        <span>Period: From date</span>\r\n                        <span class=\"fw-bold\" *ngIf=\"strPeriod[0]\"><br />{{ strPeriod[0] }}</span>\r\n                    </ng-template>\r\n                </ng-container>\r\n                <span class=\"fs-7 text-gray-800\">{{ filters.approvalMode ? 'As of' : 'To' }}</span>\r\n                <input id=\"periodTo\" type=\"month\" class=\"form-control form-control-sm\" (change)=\"onPeriodChange($event)\"\r\n                       [ngbTooltip]=\"tpTo\" placement=\"left\" min=\"2020-01\" />\r\n                <ng-template #tpTo>\r\n                    <span>Period: To date</span>\r\n                    <span class=\"fw-bold\" *ngIf=\"!filters.approvalMode && strPeriod[1]\"><br/>{{ strPeriod[1] }}</span>\r\n                </ng-template>\r\n            </div>\r\n            <ng-container *ngIf=\"filters.isTarget\">\r\n                <filter-ddl [id]=\"'period'\" #qtr class=\"filter-container\" [placeholders]=\"['Qtr']\" [minWidth]=\"80\"\r\n                            [options]=\"qtrs\" [showSearch]=\"false\" (change)=\"onFilterChange($event)\"></filter-ddl>\r\n                <filter-ddl [id]=\"'year'\" #year class=\"filter-container ms-2\" [placeholders]=\"['Year']\" [minWidth]=\"80\"\r\n                            [options]=\"years\" (change)=\"onFilterChange($event)\"></filter-ddl>\r\n            </ng-container>\r\n            <button class=\"btn btn-sm btn-light-primary border border-primary text-truncate py-2 px-3 ms-2\"\r\n                    role=\"button\" (click)=\"onFilterData()\">\r\n                Apply filter\r\n            </button>\r\n            <a class=\"cursor-pointer fs-8 ms-3\" (click)=\"resetFilters()\" *ngIf=\"filtered.length > 1\">Reset</a>\r\n        </div>\r\n        <div class=\"d-flex align-items-center\">\r\n            <div class=\"bullet bg-secondary h-35px w-1px mx-6\"></div>\r\n            <!-- Download -->\r\n            <button type=\"button\" class=\"btn btn-sm btn-icon btn-light btn-active-color-primary px-3 w-auto\"\r\n                    [disabled]=\"downloading || working || gridWorking || !interventions?.length\" (click)=\"onDownload()\">\r\n                <ng-container *ngIf=\"!downloading; else btnDSpinner\">\r\n                    <i class=\"fas fa-download me-2\"></i> Download\r\n                </ng-container>\r\n                <ng-template #btnDSpinner>\r\n                    <span class=\"indicator-progress\" style=\"display: block\">\r\n                        <span class=\"spinner-border spinner-border-sm align-middle me-1\"></span>\r\n                        Downloading...\r\n                    </span>\r\n                </ng-template>\r\n            </button>\r\n        </div>\r\n    </div>\r\n</div>\r\n\r\n<div class=\"card card-custom blockui\">\r\n    <aims-loading class=\"py-3 px-6\" *ngIf=\"gridWorking\"></aims-loading>\r\n    <div class=\"card-header\" *ngIf=\"!gridWorking && interventions?.length\">\r\n        <div class=\"card-toolbar d-flex flex-stack flex-md-nowrap w-100 m-0\">\r\n            <!--begin::Tab nav-->\r\n            <div class=\"d-flex mw-85\">\r\n                <ul class=\"nav nav-tabs nav-line-tabs d-flex fs-6 border-0\" role=\"tablist\">\r\n                    <li class=\"nav-item\" role=\"presentation\" *ngFor=\"let prof of interventions; let ind = index\" [ngbTooltip]=\"htmlTooltip\">\r\n                        <a role=\"tab\" class=\"nav-link justify-content-center text-active-gray-800 text-hover-gray-800\"\r\n                           [ngClass]=\"{ 'active': selTabId === prof.id }\" (click)=\"getInterventionData(prof.id)\"\r\n                           [tabindex]=\"ind === 0 ? -1 : null\">{{ prof.name }}</a>\r\n                        <ng-template #htmlTooltip>\r\n                            <div class=\"text-start\" style=\"line-height: normal\">\r\n                                <p class=\"fs-8 fw-semibold text-gray-600 mb-1\">{{ prof.category.code }} {{ prof.category.name }}</p>\r\n                                <p class=\"fs-7 fw-bold text-gray-800\">{{ prof.name }} ({{ prof.abbreviation }})</p>\r\n                                <p class=\"fs-9 text-gray-700 mt-2 mb-0\">{{ prof.description }}</p>\r\n                            </div>\r\n                        </ng-template>\r\n                    </li>\r\n                </ul>\r\n            </div>\r\n            <div class=\"flex-end\">\r\n                <!-- PivotPanel / Group -->\r\n                <button type=\"button\" class=\"btn btn-sm btn-icon px-3 h-30px w-auto\" [ngbTooltip]=\"groupTooltip\"\r\n                        [ngClass]=\"{'btn-light': !isPivotEnabled, 'btn-light-primary border border-primary': isPivotEnabled}\"\r\n                        (click)=\"enableGridGroupAndPivot()\" *ngIf=\"!filters.approvalMode\">\r\n                    <i class=\"fas fa-bezier-curve me-2\"></i>Pivot\r\n                </button>\r\n                <ng-template #groupTooltip>\r\n                    <span class=\"fw-semibold\" *ngIf=\"!isPivotEnabled\">Toggle Grouping and Pivot</span>\r\n                    <div class=\"text-start\" *ngIf=\"isPivotEnabled\">\r\n                        <p class=\"p-0 fw-semibold\">Pivot and Grouping enabled</p>\r\n                    </div>\r\n                </ng-template> \r\n                <!-- Sort -->\r\n                <button type=\"button\" class=\"btn btn-sm btn-icon px-3 h-30px w-auto ms-2\" [ngbTooltip]=\"sortTooltip\"\r\n                        [ngClass]=\"{'btn-light': gridSortedBy < 0, 'btn-light-primary border border-primary': gridSortedBy >= 0}\"\r\n                        (click)=\"enableGridSort()\">\r\n                    <i class=\"fas fa-sort me-2\"></i>\r\n                    <ng-container *ngIf=\"gridSortedBy <= 0\">Sort</ng-container>\r\n                    <ng-container *ngIf=\"gridSortedBy > 0\">Sorted by {{gridSortedBy}} field{{gridSortedBy > 1 ? 's' : ''}}</ng-container>\r\n                </button>\r\n                <ng-template #sortTooltip>\r\n                    <ng-container *ngIf=\"gridSortedBy < 0\"><span class=\"fw-semibold\">Enable sorting</span> on each column.</ng-container>\r\n                    <div class=\"text-start\" *ngIf=\"gridSortedBy >= 0\">\r\n                        <p class=\"p-0 fw-semibold\">Sorting enabled</p>\r\n                        <p>Click on a column header to sort by that column. Use <code>Shift</code> key to sort by multiple columns.</p>\r\n                    </div>\r\n                </ng-template>\r\n            </div>\r\n        </div>\r\n    </div>\r\n    <div class=\"card-body p-0 blockui\">\r\n        <aims-working *ngIf=\"working && !interventions?.length\"></aims-working>\r\n        <div class=\"d-flex justify-content-center\" *ngIf=\"!working && !gridWorking && !interventions?.length\">\r\n            <div class=\"notice bg-light rounded border-secondary border border-dashed text-center py-2 px-5 my-10 fs-6\">\r\n                No intervention is selected or no intervention found.\r\n            </div>\r\n        </div>\r\n\r\n        <!-- AIMS Readonly Grid -->\r\n        <aims-readonly-grid [ngClass]=\"{ 'd-none': gridWorking || !interventions?.length }\"\r\n                            [hideNAPeriods]=\"hideNAPeriods\"\r\n                            (filterApplied)=\"gridFiltered($event)\" (sortApplied)=\"gridSortedBy=$event\"\r\n                            (approve)=\"onApproveData($event)\" (approveAll)=\"onApproveAll($event)\" >\r\n        </aims-readonly-grid>\r\n    </div>\r\n</div>"], "mappings": "AAEA,SAAuBA,QAAQ,QAAQ,MAAM;AAE7C,SAASC,cAAc,EAAEC,MAAM,QAAQ,0BAA0B;AAGjE,SAASC,WAAW,EAAEC,YAAY,QAAQ,8BAA8B;AAQxE,SAASC,qBAAqB,QAAQ,gCAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;ICC9DC,EAAA,CAAAC,cAAA,gBAAkH;IAC9DD,EAAA,CAAAE,UAAA,oBAAAC,4DAAA;MAAAH,EAAA,CAAAI,aAAA,CAAAC,IAAA;MAAA,MAAAC,MAAA,GAAAN,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAAF,MAAA,CAAAG,cAAA,EAAgB;IAAA,EAAC;IAA3ET,EAAA,CAAAU,YAAA,EAA4E;IAC5EV,EAAA,CAAAC,cAAA,eAAkI;IAAAD,EAAA,CAAAW,MAAA,oBAAa;IAAAX,EAAA,CAAAU,YAAA,EAAO;;;;IAA3GV,EAAA,CAAAY,SAAA,GAAsF;IAAtFZ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAC,MAAA,CAAAC,OAAA,CAAAC,YAAA,GAAAF,MAAA,CAAAC,OAAA,CAAAC,YAAA,EAAsF;;;;;;IAErIlB,EAAA,CAAAC,cAAA,gBAAuH;IACzCD,EAAA,CAAAE,UAAA,oBAAAiB,4DAAA;MAAAnB,EAAA,CAAAI,aAAA,CAAAgB,IAAA;MAAA,MAAAC,OAAA,GAAArB,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAAa,OAAA,CAAAC,iBAAA,EAAmB;IAAA,EAAC;IAAxGtB,EAAA,CAAAU,YAAA,EAAyG;IACzGV,EAAA,CAAAC,cAAA,eAAoH;IAAAD,EAAA,CAAAW,MAAA,+CAAwC;IAAAX,EAAA,CAAAU,YAAA,EAAO;;;;IADnHV,EAAA,CAAAY,SAAA,GAAyB;IAAzBZ,EAAA,CAAAa,UAAA,YAAAU,MAAA,CAAAC,aAAA,CAAyB;IAC9BxB,EAAA,CAAAY,SAAA,GAAwE;IAAxEZ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAc,eAAA,IAAAC,GAAA,EAAAQ,MAAA,CAAAC,aAAA,GAAAD,MAAA,CAAAC,aAAA,EAAwE;;;;;IAEvHxB,EAAA,CAAAC,cAAA,cACqF;IACvDD,EAAA,CAAAW,MAAA,mBAAY;IAAAX,EAAA,CAAAU,YAAA,EAAO;IAACV,EAAA,CAAAW,MAAA,GAClD;IAAAX,EAAA,CAAAU,YAAA,EAAM;;;;IAD4CV,EAAA,CAAAY,SAAA,GAClD;IADkDZ,EAAA,CAAAyB,kBAAA,MAAAC,MAAA,CAAAC,QAAA,SAAAD,MAAA,CAAAC,QAAA,CAAAC,MAAA,QAAAF,MAAA,CAAAG,cAAA,CAAAD,MAAA,cAAAF,MAAA,CAAAG,cAAA,MAAAH,MAAA,CAAAG,cAAA,SAClD;;;;;IAIJ7B,EAAA,CAAA8B,SAAA,uBAA+E;;;IAAnD9B,EAAA,CAAAa,UAAA,oBAAmB;;;;;;;IAIvCb,EAAA,CAAAC,cAAA,yBAEiC;IADED,EAAA,CAAAE,UAAA,oBAAA6B,4EAAAC,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAA6B,IAAA;MAAA,MAAAC,OAAA,GAAAlC,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAA0B,OAAA,CAAAC,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IAEpEhC,EAAA,CAAAU,YAAA,EAAa;;;;IAHDV,EAAA,CAAAa,UAAA,oBAAmB,iBAAAb,EAAA,CAAAoC,eAAA,IAAAC,GAAA,+BAAAC,OAAA,CAAAC,UAAA;;;;;;;IAkB/BvC,EAAA,CAAAC,cAAA,yBACsG;IAAzED,EAAA,CAAAE,UAAA,oBAAAsC,6EAAAR,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAqC,IAAA;MAAA,MAAAC,OAAA,GAAA1C,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAAkC,OAAA,CAAAP,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IAC9DhC,EAAA,CAAAU,YAAA,EAAa;;;;IAFDV,EAAA,CAAAa,UAAA,gBAAe,iBAAAb,EAAA,CAAAoC,eAAA,IAAAO,IAAA,+BAAAC,OAAA,CAAAC,IAAA;;;;;IAiBf7C,EAAA,CAAAC,cAAA,eAA2C;IAAAD,EAAA,CAAA8B,SAAA,SAAM;IAAA9B,EAAA,CAAAW,MAAA,GAAkB;IAAAX,EAAA,CAAAU,YAAA,EAAO;;;;IAAzBV,EAAA,CAAAY,SAAA,GAAkB;IAAlBZ,EAAA,CAAA8C,iBAAA,CAAAC,OAAA,CAAAC,SAAA,IAAkB;;;;;IADnEhD,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAW,MAAA,wBAAiB;IAAAX,EAAA,CAAAU,YAAA,EAAO;IAC9BV,EAAA,CAAAiD,UAAA,IAAAC,4EAAA,mBAA0E;;;;IAAnDlD,EAAA,CAAAY,SAAA,GAAkB;IAAlBZ,EAAA,CAAAa,UAAA,SAAAsC,OAAA,CAAAH,SAAA,IAAkB;;;;;;IANjDhD,EAAA,CAAAoD,uBAAA,GAA4C;IACxCpD,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAW,MAAA,WAAI;IAAAX,EAAA,CAAAU,YAAA,EAAO;IAC5CV,EAAA,CAAAC,cAAA,gBAC8D;IADWD,EAAA,CAAAE,UAAA,oBAAAmD,gFAAArB,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAkD,IAAA;MAAA,MAAAC,OAAA,GAAAvD,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAA+C,OAAA,CAAAC,cAAA,CAAAxB,MAAA,CAAsB;IAAA,EAAC;IAA1GhC,EAAA,CAAAU,YAAA,EAC8D;IAC9DV,EAAA,CAAAiD,UAAA,IAAAQ,qEAAA,iCAAAzD,EAAA,CAAA0D,sBAAA,CAGc;IAClB1D,EAAA,CAAA2D,qBAAA,EAAe;;;;IALJ3D,EAAA,CAAAY,SAAA,GAAqB;IAArBZ,EAAA,CAAAa,UAAA,eAAA+C,IAAA,CAAqB;;;;;IAW5B5D,EAAA,CAAAC,cAAA,eAAoE;IAAAD,EAAA,CAAA8B,SAAA,SAAK;IAAA9B,EAAA,CAAAW,MAAA,GAAkB;IAAAX,EAAA,CAAAU,YAAA,EAAO;;;;IAAzBV,EAAA,CAAAY,SAAA,GAAkB;IAAlBZ,EAAA,CAAA8C,iBAAA,CAAAe,OAAA,CAAAb,SAAA,IAAkB;;;;;IAD3FhD,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAW,MAAA,sBAAe;IAAAX,EAAA,CAAAU,YAAA,EAAO;IAC5BV,EAAA,CAAAiD,UAAA,IAAAa,6DAAA,mBAAkG;;;;IAA3E9D,EAAA,CAAAY,SAAA,GAA2C;IAA3CZ,EAAA,CAAAa,UAAA,UAAAkD,OAAA,CAAA9C,OAAA,CAAAC,YAAA,IAAA6C,OAAA,CAAAf,SAAA,IAA2C;;;;;;IAf1EhD,EAAA,CAAAC,cAAA,cAA+D;IAC3DD,EAAA,CAAAiD,UAAA,IAAAe,uDAAA,2BAQe;IACfhE,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAW,MAAA,GAA2C;IAAAX,EAAA,CAAAU,YAAA,EAAO;IACnFV,EAAA,CAAAC,cAAA,gBAC4D;IADWD,EAAA,CAAAE,UAAA,oBAAA+D,iEAAAjC,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAA8D,IAAA;MAAA,MAAAC,OAAA,GAAAnE,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAA2D,OAAA,CAAAX,cAAA,CAAAxB,MAAA,CAAsB;IAAA,EAAC;IAAxGhC,EAAA,CAAAU,YAAA,EAC4D;IAC5DV,EAAA,CAAAiD,UAAA,IAAAmB,sDAAA,iCAAApE,EAAA,CAAA0D,sBAAA,CAGc;IAClB1D,EAAA,CAAAU,YAAA,EAAM;;;;;IAhBaV,EAAA,CAAAY,SAAA,GAA2B;IAA3BZ,EAAA,CAAAa,UAAA,UAAAwD,OAAA,CAAApD,OAAA,CAAAC,YAAA,CAA2B;IASTlB,EAAA,CAAAY,SAAA,GAA2C;IAA3CZ,EAAA,CAAA8C,iBAAA,CAAAuB,OAAA,CAAApD,OAAA,CAAAC,YAAA,kBAA2C;IAErElB,EAAA,CAAAY,SAAA,GAAmB;IAAnBZ,EAAA,CAAAa,UAAA,eAAAyD,IAAA,CAAmB;;;;;;;;IAM9BtE,EAAA,CAAAoD,uBAAA,GAAuC;IACnCpD,EAAA,CAAAC,cAAA,yBACoF;IAAlCD,EAAA,CAAAE,UAAA,oBAAAqE,+EAAAvC,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAoE,IAAA;MAAA,MAAAC,OAAA,GAAAzE,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAAiE,OAAA,CAAAtC,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IAAChC,EAAA,CAAAU,YAAA,EAAa;IACjGV,EAAA,CAAAC,cAAA,yBACgE;IAAlCD,EAAA,CAAAE,UAAA,oBAAAwE,+EAAA1C,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAoE,IAAA;MAAA,MAAAG,OAAA,GAAA3E,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAAmE,OAAA,CAAAxC,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IAAChC,EAAA,CAAAU,YAAA,EAAa;IACjFV,EAAA,CAAA2D,qBAAA,EAAe;;;;IAJC3D,EAAA,CAAAY,SAAA,GAAe;IAAfZ,EAAA,CAAAa,UAAA,gBAAe,iBAAAb,EAAA,CAAAoC,eAAA,IAAAwC,IAAA,8BAAAC,OAAA,CAAAC,IAAA;IAEf9E,EAAA,CAAAY,SAAA,GAAa;IAAbZ,EAAA,CAAAa,UAAA,cAAa,iBAAAb,EAAA,CAAAoC,eAAA,KAAA2C,IAAA,8BAAAF,OAAA,CAAAG,KAAA;;;;;;IAO7BhF,EAAA,CAAAC,cAAA,YAAyF;IAArDD,EAAA,CAAAE,UAAA,mBAAA+E,0DAAA;MAAAjF,EAAA,CAAAI,aAAA,CAAA8E,IAAA;MAAA,MAAAC,OAAA,GAAAnF,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA2E,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAA6BpF,EAAA,CAAAW,MAAA,YAAK;IAAAX,EAAA,CAAAU,YAAA,EAAI;;;;;IAO9FV,EAAA,CAAAoD,uBAAA,GAAqD;IACjDpD,EAAA,CAAA8B,SAAA,YAAoC;IAAC9B,EAAA,CAAAW,MAAA,iBACzC;IAAAX,EAAA,CAAA2D,qBAAA,EAAe;;;;;IAEX3D,EAAA,CAAAC,cAAA,eAAwD;IACpDD,EAAA,CAAA8B,SAAA,eAAwE;IACxE9B,EAAA,CAAAW,MAAA,uBACJ;IAAAX,EAAA,CAAAU,YAAA,EAAO;;;;;;;;;;;;;;;;IAzEvBV,EAAA,CAAAC,cAAA,cAAwF;IAGhFD,EAAA,CAAAiD,UAAA,IAAAoC,8CAAA,yBAGa;IACbrF,EAAA,CAAAC,cAAA,yBAC0H;IAAzFD,EAAA,CAAAE,UAAA,oBAAAoF,+DAAAtD,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAmF,IAAA;MAAA,MAAAC,OAAA,GAAAxF,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAAgF,OAAA,CAAArD,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IAClEhC,EAAA,CAAAU,YAAA,EAAa;IACbV,EAAA,CAAA8B,SAAA,cAAyD;IACzD9B,EAAA,CAAAC,cAAA,yBACoF;IAApDD,EAAA,CAAAE,UAAA,oBAAAuF,+DAAAzD,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAmF,IAAA;MAAA,MAAAG,OAAA,GAAA1F,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAAkF,OAAA,CAAAvD,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IACjEhC,EAAA,CAAAU,YAAA,EAAa;IACbV,EAAA,CAAAC,cAAA,yBACuF;IAApDD,EAAA,CAAAE,UAAA,oBAAAyF,+DAAA3D,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAmF,IAAA;MAAA,MAAAK,OAAA,GAAA5F,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAAoF,OAAA,CAAAzD,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IACpEhC,EAAA,CAAAU,YAAA,EAAa;IACbV,EAAA,CAAAC,cAAA,0BACqF;IAApDD,EAAA,CAAAE,UAAA,oBAAA2F,gEAAA7D,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAmF,IAAA;MAAA,MAAAO,OAAA,GAAA9F,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAAsF,OAAA,CAAA3D,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IAClEhC,EAAA,CAAAU,YAAA,EAAa;IACbV,EAAA,CAAA8B,SAAA,eAAyD;IACzD9B,EAAA,CAAAiD,UAAA,KAAA8C,+CAAA,yBAEa;IACb/F,EAAA,CAAAC,cAAA,0BACuI;IAAlCD,EAAA,CAAAE,UAAA,oBAAA8F,gEAAAhE,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAmF,IAAA;MAAA,MAAAU,OAAA,GAAAjG,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAAyF,OAAA,CAAA9D,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IACtIhC,EAAA,CAAAU,YAAA,EAAa;IACbV,EAAA,CAAAC,cAAA,0BACoF;IAAlCD,EAAA,CAAAE,UAAA,oBAAAgG,gEAAAlE,MAAA;MAAAhC,EAAA,CAAAI,aAAA,CAAAmF,IAAA;MAAA,MAAAY,OAAA,GAAAnG,EAAA,CAAAO,aAAA;MAAA,OAAUP,EAAA,CAAAQ,WAAA,CAAA2F,OAAA,CAAAhE,cAAA,CAAAH,MAAA,CAAsB;IAAA,EAAC;IACnFhC,EAAA,CAAAU,YAAA,EAAa;IACbV,EAAA,CAAA8B,SAAA,eAAyD;IACzD9B,EAAA,CAAAiD,UAAA,KAAAmD,wCAAA,kBAiBM,KAAAC,iDAAA;IAONrG,EAAA,CAAAC,cAAA,kBAC+C;IAAzBD,EAAA,CAAAE,UAAA,mBAAAoG,2DAAA;MAAAtG,EAAA,CAAAI,aAAA,CAAAmF,IAAA;MAAA,MAAAgB,OAAA,GAAAvG,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAA+F,OAAA,CAAAC,YAAA,EAAc;IAAA,EAAC;IAC1CxG,EAAA,CAAAW,MAAA,sBACJ;IAAAX,EAAA,CAAAU,YAAA,EAAS;IACTV,EAAA,CAAAiD,UAAA,KAAAwD,sCAAA,gBAAkG;IACtGzG,EAAA,CAAAU,YAAA,EAAM;IACNV,EAAA,CAAAC,cAAA,eAAuC;IACnCD,EAAA,CAAA8B,SAAA,eAAyD;IAEzD9B,EAAA,CAAAC,cAAA,kBAC4G;IAAvBD,EAAA,CAAAE,UAAA,mBAAAwG,2DAAA;MAAA1G,EAAA,CAAAI,aAAA,CAAAmF,IAAA;MAAA,MAAAoB,OAAA,GAAA3G,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAmG,OAAA,CAAAC,UAAA,EAAY;IAAA,EAAC;IACvG5G,EAAA,CAAAiD,UAAA,KAAA4D,iDAAA,2BAEe,KAAAC,gDAAA,iCAAA9G,EAAA,CAAA0D,sBAAA;IAOnB1D,EAAA,CAAAU,YAAA,EAAS;;;;;IAtEIV,EAAA,CAAAY,SAAA,GAAkB;IAAlBZ,EAAA,CAAAa,UAAA,SAAAkG,MAAA,CAAAC,YAAA,CAAkB;IAEnBhH,EAAA,CAAAY,SAAA,GAAgB;IAAhBZ,EAAA,CAAAa,UAAA,iBAAgB,YAAAb,EAAA,CAAAiH,eAAA,KAAAC,IAAA,EAAAH,MAAA,CAAAC,YAAA,mBAAAhH,EAAA,CAAAoC,eAAA,KAAA+E,IAAA,+BAAAJ,MAAA,CAAAK,QAAA;IAIhBpH,EAAA,CAAAY,SAAA,GAAgB;IAAhBZ,EAAA,CAAAa,UAAA,iBAAgB,iBAAAb,EAAA,CAAAoC,eAAA,KAAAiF,IAAA,+BAAAN,MAAA,CAAAO,OAAA;IAGhBtH,EAAA,CAAAY,SAAA,GAAe;IAAfZ,EAAA,CAAAa,UAAA,gBAAe,iBAAAb,EAAA,CAAAoC,eAAA,KAAAmF,IAAA,+BAAAR,MAAA,CAAAS,UAAA;IAGfxH,EAAA,CAAAY,SAAA,GAAgB;IAAhBZ,EAAA,CAAAa,UAAA,iBAAgB,iBAAAb,EAAA,CAAAoC,eAAA,KAAAqF,IAAA,+BAAAV,MAAA,CAAAW,QAAA;IAKsD1H,EAAA,CAAAY,SAAA,GAAkB;IAAlBZ,EAAA,CAAAa,UAAA,SAAAkG,MAAA,CAAAC,YAAA,CAAkB;IAExFhH,EAAA,CAAAY,SAAA,GAAmB;IAAnBZ,EAAA,CAAAa,UAAA,oBAAmB,iBAAAb,EAAA,CAAAoC,eAAA,KAAAuF,IAAA,+BAAAZ,MAAA,CAAAa,YAAA,2DAAA5H,EAAA,CAAAoC,eAAA,KAAAyF,IAAA;IAGnB7H,EAAA,CAAAY,SAAA,GAAgB;IAAhBZ,EAAA,CAAAa,UAAA,iBAAgB,iBAAAb,EAAA,CAAAoC,eAAA,KAAA0F,IAAA,+BAAAf,MAAA,CAAAgB,OAAA;IAIU/H,EAAA,CAAAY,SAAA,GAAuB;IAAvBZ,EAAA,CAAAa,UAAA,UAAAkG,MAAA,CAAA9F,OAAA,CAAA+G,QAAA,CAAuB;IAkB9ChI,EAAA,CAAAY,SAAA,GAAsB;IAAtBZ,EAAA,CAAAa,UAAA,SAAAkG,MAAA,CAAA9F,OAAA,CAAA+G,QAAA,CAAsB;IAUyBhI,EAAA,CAAAY,SAAA,GAAyB;IAAzBZ,EAAA,CAAAa,UAAA,SAAAkG,MAAA,CAAApF,QAAA,CAAAC,MAAA,KAAyB;IAM/E5B,EAAA,CAAAY,SAAA,GAA4E;IAA5EZ,EAAA,CAAAa,UAAA,aAAAkG,MAAA,CAAAkB,WAAA,IAAAlB,MAAA,CAAAmB,OAAA,IAAAnB,MAAA,CAAAoB,WAAA,MAAApB,MAAA,CAAAqB,aAAA,kBAAArB,MAAA,CAAAqB,aAAA,CAAAxG,MAAA,EAA4E;IACjE5B,EAAA,CAAAY,SAAA,GAAoB;IAApBZ,EAAA,CAAAa,UAAA,UAAAkG,MAAA,CAAAkB,WAAA,CAAoB,aAAAI,IAAA;;;;;IAe/CrI,EAAA,CAAA8B,SAAA,uBAAmE;;;;;IAW3C9B,EAAA,CAAAC,cAAA,cAAoD;IACDD,EAAA,CAAAW,MAAA,GAAiD;IAAAX,EAAA,CAAAU,YAAA,EAAI;IACpGV,EAAA,CAAAC,cAAA,YAAsC;IAAAD,EAAA,CAAAW,MAAA,GAAyC;IAAAX,EAAA,CAAAU,YAAA,EAAI;IACnFV,EAAA,CAAAC,cAAA,YAAwC;IAAAD,EAAA,CAAAW,MAAA,GAAsB;IAAAX,EAAA,CAAAU,YAAA,EAAI;;;;IAFnBV,EAAA,CAAAY,SAAA,GAAiD;IAAjDZ,EAAA,CAAAyB,kBAAA,KAAA6G,QAAA,CAAAC,QAAA,CAAAC,IAAA,OAAAF,QAAA,CAAAC,QAAA,CAAAE,IAAA,KAAiD;IAC1DzI,EAAA,CAAAY,SAAA,GAAyC;IAAzCZ,EAAA,CAAAyB,kBAAA,KAAA6G,QAAA,CAAAG,IAAA,QAAAH,QAAA,CAAAI,YAAA,MAAyC;IACvC1I,EAAA,CAAAY,SAAA,GAAsB;IAAtBZ,EAAA,CAAA8C,iBAAA,CAAAwF,QAAA,CAAAK,WAAA,CAAsB;;;;;;;;;IAR1E3I,EAAA,CAAAC,cAAA,aAAwH;IAElED,EAAA,CAAAE,UAAA,mBAAA0I,0DAAA;MAAA,MAAAC,WAAA,GAAA7I,EAAA,CAAAI,aAAA,CAAA0I,IAAA;MAAA,MAAAR,QAAA,GAAAO,WAAA,CAAAE,SAAA;MAAA,MAAAC,OAAA,GAAAhJ,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAwI,OAAA,CAAAC,mBAAA,CAAAX,QAAA,CAAAY,EAAA,CAA4B;IAAA,EAAC;IAClDlJ,EAAA,CAAAW,MAAA,GAAe;IAAAX,EAAA,CAAAU,YAAA,EAAI;IACzDV,EAAA,CAAAiD,UAAA,IAAAkG,oDAAA,iCAAAnJ,EAAA,CAAA0D,sBAAA,CAMc;IAClB1D,EAAA,CAAAU,YAAA,EAAK;;;;;;;IAXwFV,EAAA,CAAAa,UAAA,eAAAuI,IAAA,CAA0B;IAEhHpJ,EAAA,CAAAY,SAAA,GAA8C;IAA9CZ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAiH,eAAA,IAAAoC,IAAA,EAAAC,OAAA,CAAAC,QAAA,KAAAjB,QAAA,CAAAY,EAAA,EAA8C,aAAAM,OAAA;IACXxJ,EAAA,CAAAY,SAAA,GAAe;IAAfZ,EAAA,CAAA8C,iBAAA,CAAAwF,QAAA,CAAAG,IAAA,CAAe;;;;;;;;;;IAa7DzI,EAAA,CAAAC,cAAA,iBAE0E;IAAlED,EAAA,CAAAE,UAAA,mBAAAuJ,mEAAA;MAAAzJ,EAAA,CAAAI,aAAA,CAAAsJ,IAAA;MAAA,MAAAC,OAAA,GAAA3J,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAmJ,OAAA,CAAAC,uBAAA,EAAyB;IAAA,EAAC;IACvC5J,EAAA,CAAA8B,SAAA,YAAwC;IAAA9B,EAAA,CAAAW,MAAA,aAC5C;IAAAX,EAAA,CAAAU,YAAA,EAAS;;;;;;IAJ4DV,EAAA,CAAAa,UAAA,eAAAgJ,IAAA,CAA2B,YAAA7J,EAAA,CAAAc,eAAA,IAAAgJ,IAAA,GAAAC,OAAA,CAAAC,cAAA,EAAAD,OAAA,CAAAC,cAAA;;;;;IAM5FhK,EAAA,CAAAC,cAAA,eAAkD;IAAAD,EAAA,CAAAW,MAAA,gCAAyB;IAAAX,EAAA,CAAAU,YAAA,EAAO;;;;;IAClFV,EAAA,CAAAC,cAAA,cAA+C;IAChBD,EAAA,CAAAW,MAAA,iCAA0B;IAAAX,EAAA,CAAAU,YAAA,EAAI;;;;;IAF7DV,EAAA,CAAAiD,UAAA,IAAAgH,sDAAA,mBAAkF,IAAAC,qDAAA;;;;IAAvDlK,EAAA,CAAAa,UAAA,UAAAsJ,OAAA,CAAAH,cAAA,CAAqB;IACvBhK,EAAA,CAAAY,SAAA,GAAoB;IAApBZ,EAAA,CAAAa,UAAA,SAAAsJ,OAAA,CAAAH,cAAA,CAAoB;;;;;IAS7ChK,EAAA,CAAAoD,uBAAA,GAAwC;IAAApD,EAAA,CAAAW,MAAA,WAAI;IAAAX,EAAA,CAAA2D,qBAAA,EAAe;;;;;IAC3D3D,EAAA,CAAAoD,uBAAA,GAAuC;IAAApD,EAAA,CAAAW,MAAA,GAA+D;IAAAX,EAAA,CAAA2D,qBAAA,EAAe;;;;IAA9E3D,EAAA,CAAAY,SAAA,GAA+D;IAA/DZ,EAAA,CAAAyB,kBAAA,eAAA2I,OAAA,CAAAC,YAAA,YAAAD,OAAA,CAAAC,YAAA,oBAA+D;;;;;IAGtGrK,EAAA,CAAAoD,uBAAA,GAAuC;IAAApD,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAW,MAAA,qBAAc;IAAAX,EAAA,CAAAU,YAAA,EAAO;IAACV,EAAA,CAAAW,MAAA,uBAAe;IAAAX,EAAA,CAAA2D,qBAAA,EAAe;;;;;IACrH3D,EAAA,CAAAC,cAAA,cAAkD;IACnBD,EAAA,CAAAW,MAAA,sBAAe;IAAAX,EAAA,CAAAU,YAAA,EAAI;IAC9CV,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAW,MAAA,4DAAqD;IAAAX,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAW,MAAA,YAAK;IAAAX,EAAA,CAAAU,YAAA,EAAO;IAACV,EAAA,CAAAW,MAAA,wCAAgC;IAAAX,EAAA,CAAAU,YAAA,EAAI;;;;;IAHnHV,EAAA,CAAAiD,UAAA,IAAAqH,+DAAA,2BAAqH,IAAAC,sDAAA;;;;IAAtGvK,EAAA,CAAAa,UAAA,SAAA2J,OAAA,CAAAH,YAAA,KAAsB;IACZrK,EAAA,CAAAY,SAAA,GAAuB;IAAvBZ,EAAA,CAAAa,UAAA,SAAA2J,OAAA,CAAAH,YAAA,MAAuB;;;;;;IA1ChErK,EAAA,CAAAC,cAAA,cAAuE;IAKvDD,EAAA,CAAAiD,UAAA,IAAAwH,sCAAA,iBAWK;IACTzK,EAAA,CAAAU,YAAA,EAAK;IAETV,EAAA,CAAAC,cAAA,cAAsB;IAElBD,EAAA,CAAAiD,UAAA,IAAAyH,0CAAA,qBAIS,IAAAC,+CAAA,iCAAA3K,EAAA,CAAA0D,sBAAA;IAQT1D,EAAA,CAAAC,cAAA,iBAEmC;IAA3BD,EAAA,CAAAE,UAAA,mBAAA0K,0DAAA;MAAA5K,EAAA,CAAAI,aAAA,CAAAyK,IAAA;MAAA,MAAAC,OAAA,GAAA9K,EAAA,CAAAO,aAAA;MAAA,OAASP,EAAA,CAAAQ,WAAA,CAAAsK,OAAA,CAAAC,cAAA,EAAgB;IAAA,EAAC;IAC9B/K,EAAA,CAAA8B,SAAA,aAAgC;IAChC9B,EAAA,CAAAiD,UAAA,KAAA+H,iDAAA,2BAA2D,KAAAC,iDAAA;IAE/DjL,EAAA,CAAAU,YAAA,EAAS;IACTV,EAAA,CAAAiD,UAAA,KAAAiI,gDAAA,iCAAAlL,EAAA,CAAA0D,sBAAA,CAMc;IAClB1D,EAAA,CAAAU,YAAA,EAAM;;;;;IA1C4DV,EAAA,CAAAY,SAAA,GAAkB;IAAlBZ,EAAA,CAAAa,UAAA,YAAAsK,MAAA,CAAA/C,aAAA,CAAkB;IAkBnCpI,EAAA,CAAAY,SAAA,GAA2B;IAA3BZ,EAAA,CAAAa,UAAA,UAAAsK,MAAA,CAAAlK,OAAA,CAAAC,YAAA,CAA2B;IAUElB,EAAA,CAAAY,SAAA,GAA0B;IAA1BZ,EAAA,CAAAa,UAAA,eAAAuK,IAAA,CAA0B,YAAApL,EAAA,CAAAc,eAAA,IAAAgJ,IAAA,EAAAqB,MAAA,CAAAd,YAAA,MAAAc,MAAA,CAAAd,YAAA;IAIjFrK,EAAA,CAAAY,SAAA,GAAuB;IAAvBZ,EAAA,CAAAa,UAAA,SAAAsK,MAAA,CAAAd,YAAA,MAAuB;IACvBrK,EAAA,CAAAY,SAAA,GAAsB;IAAtBZ,EAAA,CAAAa,UAAA,SAAAsK,MAAA,CAAAd,YAAA,KAAsB;;;;;IAajDrK,EAAA,CAAA8B,SAAA,mBAAuE;;;;;IACvE9B,EAAA,CAAAC,cAAA,cAAsG;IAE9FD,EAAA,CAAAW,MAAA,8DACJ;IAAAX,EAAA,CAAAU,YAAA,EAAM;;;;;;;;;ADlJlB,OAAM,MAAO2K,iBAAiB;EA6D1BC,YACYC,WAAwB,EACxBC,WAA2B,EAC3BC,WAA4B,EAC5BC,aAA4B,EAC5BC,cAA8B,EAC9BC,GAAsB;IALtB,KAAAL,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,aAAa,GAAbA,aAAa;IACb,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,GAAG,GAAHA,GAAG;IAlEgC,KAAAC,SAAS,GAAG,MAAM;IACnB,KAAAC,QAAQ,GAAG,MAAM;IACT,KAAAC,WAAW,GAAG,MAAM;IAC1E;IAEA;IACA,KAAA7D,OAAO,GAAY,KAAK;IAAE,KAAAC,WAAW,GAAY,KAAK;IACtD,KAAA6D,UAAU,GAAY,KAAK;IAAE,KAAAhF,YAAY,GAAY,KAAK;IAC1D,KAAAiB,WAAW,GAAY,KAAK;IAC5B,KAAAgE,SAAS,GAAW,CAAC;IAErB,KAAA1J,UAAU,GAAU,EAAE;IACtB,KAAA6E,QAAQ,GAAc,EAAE;IACxB,KAAAE,OAAO,GAAG,EAAE;IACZ,KAAAE,UAAU,GAAe,EAAE;IAC3B,KAAAE,QAAQ,GAAQ,EAAE;IAClB,KAAA7E,IAAI,GAAU,EAAE;IAChB,KAAA+E,YAAY,GAAG,CAAC;MAAEsB,EAAE,EAAE,CAAC;MAAET,IAAI,EAAE;IAAO,CAAE,EAAE;MAAES,EAAE,EAAE,CAAC;MAAET,IAAI,EAAE;IAAW,CAAE,EAAE;MAAES,EAAE,EAAE,CAAC;MAAET,IAAI,EAAE;IAAU,CAAE,CAAC;IACpG,KAAAV,OAAO,GAAG,EAAE;IACZ,KAAAjD,IAAI,GAAG,CAAC;MAAEoE,EAAE,EAAE,CAAC;MAAET,IAAI,EAAE;IAAK,CAAE,EAAE;MAAES,EAAE,EAAE,CAAC;MAAET,IAAI,EAAE;IAAK,CAAE,EAAE;MAAES,EAAE,EAAE,CAAC;MAAET,IAAI,EAAE;IAAK,CAAE,EAAE;MAAES,EAAE,EAAE,CAAC;MAAET,IAAI,EAAE;IAAK,CAAE,CAAC;IACvG,KAAAzD,KAAK,GAAG,EAAE;IAEV,KAAAoD,aAAa,GAAkB,EAAE;IACjC,KAAA8D,gBAAgB,GAAkB,EAAE;IACpC,KAAA3C,QAAQ,GAAW,CAAC;IAEpB,KAAAtI,OAAO,GAAgB;MACnB+G,QAAQ,EAAE,KAAK;MACf9G,YAAY,EAAE,KAAK;MACnBiL,UAAU,EAAE,CAAC,CAAC,EAAC,CAAC;KACnB;IACD,KAAAxK,QAAQ,GAAa,CAAC,EAAE,CAAC;IACzB,KAAAqB,SAAS,GAAa,CAAC,EAAE,EAAE,EAAE,CAAC;IAE9B;IACA,KAAAxB,aAAa,GAAY,IAAI,CAAC,CAAC;IAwBvB,KAAA4K,aAAa,GAAmB,EAAE;IAiuB1C,KAAAvK,cAAc,GAAa,CAAC,GAAG,CAAC;IAYhC,KAAAwI,YAAY,GAAW,CAAC,CAAC;IAsBzB;;;;;;;;;;;;;;;;;;;;;;;;;;IA0BA,KAAAL,cAAc,GAAY,KAAK;IApxB3B,MAAMqC,KAAK,GAAG,IAAI,CAACd,WAAW,CAACe,gBAAgB,CAACD,KAAK;IACrD,IAAI,CAACrF,YAAY,GAAGqF,KAAK,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,eAAe,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,QAAQ,CAAC;IACxI,IAAI,CAACP,UAAU,GAAGK,KAAK,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,UAAU,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,eAAe,CAAC;IAC1G,MAAMC,WAAW,GAAGH,KAAK,CAACE,QAAQ,CAAC,WAAW,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,aAAa,CAAC,IAAIF,KAAK,CAACE,QAAQ,CAAC,eAAe,CAAC;IAEnH,IAAI,CAACtL,OAAO,CAACkL,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IAChC;IACAM,MAAM,CAACC,MAAM,CAAC9M,MAAM,CAAC,CAChB+M,OAAO,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACd,IAAK,OAAOD,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,UAAU,EAC1C,IAAI,CAAC7E,OAAO,CAAC+E,IAAI,CAAC;QAAE5D,EAAE,EAAE2D,CAAC;QAAEpE,IAAI,EAAEmE;MAAC,CAAE,CAAC;IAC7C,CAAC,CAAC;IACN,IAAI,CAAC7E,OAAO,CAACgF,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACvE,IAAI,GAAGwE,CAAC,CAACxE,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;IAErD,MAAMyE,QAAQ,GAAG,IAAIC,IAAI,EAAE,CAACC,WAAW,EAAE;IACzC,KAAK,IAAIP,CAAC,GAAG,IAAI,EAAEA,CAAC,IAAIK,QAAQ,GAAC,CAAC,EAAEL,CAAC,EAAE,EACnC,IAAI,CAAC7H,KAAK,CAAC8H,IAAI,CAAC;MAAE5D,EAAE,EAAE2D,CAAC;MAAEpE,IAAI,EAAE4E,MAAM,CAACR,CAAC;IAAC,CAAE,CAAC;IAE/C;IACA,IAAI,IAAI,CAACtB,WAAW,CAACe,gBAAgB,CAACgB,GAAG,EAAE;MACvC,IAAI,CAACrB,SAAS,GAAG,IAAI,CAACV,WAAW,CAACe,gBAAgB,CAACgB,GAAG,CAACpE,EAAE;;EAEjE;EAEAqE,QAAQA,CAAA;IACJ,IAAI,CAACC,aAAa,CAACxG,YAAY,GAAG,IAAI,CAACA,YAAY;IACnD,IAAI,CAACwG,aAAa,CAACxB,UAAU,GAAG,IAAI,CAACA,UAAU;IAC/C;IACA,IAAI,CAACyB,gBAAgB,EAAE;EAC3B;EAEA;EACAC,eAAeA,CAAA;IACX;IACAC,UAAU,CAAC,MAAK;MACZ,IAAI,CAACC,uBAAuB,EAAE;IAClC,CAAC,EAAE,GAAG,CAAC;EACX;EAEA;EACQA,uBAAuBA,CAAA;IAC3B;IACA,IAAI,IAAI,CAACC,cAAc,EAAE;MACrB;MACA,IAAI,CAAC,IAAI,CAACzG,QAAQ,IAAI,IAAI,CAACA,QAAQ,CAACxF,MAAM,KAAK,CAAC,EAAE;QAC9C;;MAGJ;MACA;MACA,IAAI,CAACiM,cAAc,CAACC,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC1G,QAAQ,CAAC;MAChD,IAAI,CAACyG,cAAc,CAACE,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC3G,QAAQ,CAAC;MAE9C;MACA,IAAI,CAACyG,cAAc,CAACG,OAAO,GAAG,IAAI;MAClC,IAAI,CAACH,cAAc,CAACI,UAAU,GAAG,IAAI;MAErC;MACA,IAAI,IAAI,CAACjH,YAAY,IAAI,IAAI,CAACkH,oBAAoB,EAAE;QAChDC,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE,IAAI,CAAC7L,UAAU,CAACX,MAAM,EAAE,QAAQ,CAAC;QACvF;QACA,IAAI,CAACsM,oBAAoB,CAACJ,OAAO,GAAG,CAAC,GAAG,IAAI,CAACvL,UAAU,CAAC;QACxD,IAAI,CAAC2L,oBAAoB,CAACH,KAAK,GAAG,CAAC,GAAG,IAAI,CAACxL,UAAU,CAAC;QACtD,IAAI,CAAC2L,oBAAoB,CAACF,OAAO,GAAG,KAAK;QACzC,IAAI,CAACE,oBAAoB,CAACD,UAAU,GAAG,IAAI;;MAG/C;MACA,IAAI,CAACrC,GAAG,CAACyC,aAAa,EAAE;;EAEhC;EAEG;EACQC,uBAAuBA,CAAA;IAC9B;IACA,IAAI,IAAI,CAACtH,YAAY,IAAI,IAAI,CAACuH,iBAAiB,EAAE;MAC7C;MACA,IAAI,CAAC,IAAI,CAAC1L,IAAI,IAAI,IAAI,CAACA,IAAI,CAACjB,MAAM,KAAK,CAAC,EAAE;QACtC;QACA;;MAGJ;MAEA;MACA;MACA,IAAI,CAAC2M,iBAAiB,CAACT,OAAO,GAAG,CAAC,GAAG,IAAI,CAACjL,IAAI,CAAC;MAC/C,IAAI,CAAC0L,iBAAiB,CAACR,KAAK,GAAG,CAAC,GAAG,IAAI,CAAClL,IAAI,CAAC;MAE7C;MACA,IAAI,CAAC0L,iBAAiB,CAACP,OAAO,GAAG,IAAI;MACrC,IAAI,CAACO,iBAAiB,CAACN,UAAU,GAAG,IAAI;MAExC;MACA,IAAI,CAACrC,GAAG,CAACyC,aAAa,EAAE;;EAEhC;EAEA;EACQZ,gBAAgBA,CAAA;IACpB,IAAI,CAACvF,OAAO,GAAG,IAAI;IAEnB;IACA,MAAMmE,KAAK,GAAG,IAAI,CAACd,WAAW,CAACe,gBAAgB,CAACD,KAAK;IACrD,MAAMmC,SAAS,GAAGnC,KAAK,CAACE,QAAQ,CAAC,WAAW,CAAC,IAC3BF,KAAK,CAACE,QAAQ,CAAC,aAAa,CAAC,IAC7BF,KAAK,CAACE,QAAQ,CAAC,eAAe,CAAC;IAEjD;IACA,IAAIkC,WAAW,GAAG,IAAI,CAAC/C,aAAa,CAACgD,uBAAuB,EAAE;IAC9D,IAAI,CAAC,IAAI,CAAC1H,YAAY,IAAIwH,SAAS,EAAE;MACjC;MACAL,OAAO,CAACC,GAAG,CAAC,iCAAiC,CAAC;MAC9CK,WAAW,GAAG,IAAI,CAAC/C,aAAa,CAACiD,mBAAmB,EAAE;;IAG1D,IAAIC,SAAS,GAAQ;MACjBC,KAAK,EAAEJ,WAAW;MAClBK,IAAI,EAAE,IAAI,CAACpD,aAAa,CAACqD,aAAa,EAAE;MACxCC,KAAK,EAAE,IAAI,CAACxD,WAAW,CAACyD,WAAW,EAAE;MACrCpM,IAAI,EAAE,IAAI,CAAC6I,aAAa,CAACwD,WAAW;KACvC;IAED,IAAI,CAAC9C,aAAa,CAACU,IAAI,CACnBpN,QAAQ,CAACkP,SAAS,CAAC,CAACO,SAAS,CAAC;MAC1BC,IAAI,EAAEA,CAAC;QAAEP,KAAK;QAAEC,IAAI;QAAEE,KAAK;QAAEnM;MAAI,CAAE,KAAI;QACnC;QACA;QACA;QAEA;QACA,IAAI,IAAI,CAACmE,YAAY,EAAE;UACnB,IAAI,CAACI,QAAQ,GAAGyH,KAAK;UAErB;UACA,IAAI,CAACtM,UAAU,GAAG,EAAE;UACpBsM,KAAK,CAAClC,OAAO,CAAC0C,IAAI,IAAG;YACjB,IAAIA,IAAI,CAACC,QAAQ,IAAID,IAAI,CAACC,QAAQ,CAACC,IAAI,EAAE,KAAK,EAAE,EAAE;cAC9C,MAAMC,aAAa,GAAG,IAAI,CAACjN,UAAU,CAACkN,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACxG,EAAE,KAAKmG,IAAI,CAACC,QAAQ,CAAC;cACzE,IAAI,CAACE,aAAa,EAAE;gBAChB,IAAI,CAACjN,UAAU,CAACuK,IAAI,CAAC;kBAAE5D,EAAE,EAAEmG,IAAI,CAACC,QAAQ;kBAAE7G,IAAI,EAAE4G,IAAI,CAACC;gBAAQ,CAAE,CAAC;;;UAG5E,CAAC,CAAC;UAEF;UACA,IAAI,CAAC/M,UAAU,CAACwK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKpN,WAAW,CAACmN,CAAC,CAACvE,IAAI,EAAEwE,CAAC,CAACxE,IAAI,CAAC,CAAC;SAC9D,MAAM;UACH;UACA;UACA,IAAIkH,KAAK,CAACC,OAAO,CAACf,KAAK,CAAC,IAAIA,KAAK,CAACjN,MAAM,GAAG,CAAC,IAAI,eAAe,IAAIiN,KAAK,CAAC,CAAC,CAAC,EAAE;YACzE;YACAV,OAAO,CAACC,GAAG,CAAC,iDAAiD,CAAC;YAC9D,IAAI,CAAChH,QAAQ,GAAGyH,KAAK,CAACgB,GAAG,CAACC,CAAC,IAAG;cAC1B,OAAO;gBACH5G,EAAE,EAAE4G,CAAC,CAAC5G,EAAE;gBACRT,IAAI,EAAEqH,CAAC,CAACrH,IAAI;gBACZC,YAAY,EAAEoH,CAAC,CAACpH,YAAY;gBAC5B4G,QAAQ,EAAEQ,CAAC,CAACR,QAAQ,IAAI,EAAE;gBAC1BS,QAAQ,EAAED,CAAC,CAACE,aAAa,EAAEH,GAAG,CAACI,KAAK,KAAK;kBACrCC,cAAc,EAAED,KAAK;kBACrBE,SAAS,EAAEL,CAAC,CAAC5G;iBAChB,CAAC;eACL;YACL,CAAC,CAAC;WACL,MAAM;YACH;YACA,IAAI,CAAC9B,QAAQ,GAAGyH,KAAK;;UAGzBV,OAAO,CAACC,GAAG,CAAC,sBAAsB,IAAI,CAAChH,QAAQ,CAACxF,MAAM,EAAE,CAAC;UAEzD;UACA,IAAI,CAACW,UAAU,GAAG,EAAE;;QAGvB;QACA,IAAI,CAACM,IAAI,GAAG,EAAE;QACdA,IAAI,CAAC8J,OAAO,CAACW,GAAG,IAAG;UACf,IAAI,CAACzK,IAAI,CAACiK,IAAI,CAAC;YACX5D,EAAE,EAAEoE,GAAG,CAACpE,EAAE;YACVT,IAAI,EAAE6E,GAAG,CAAC8C;WACb,CAAC;QACN,CAAC,CAAC;QACF,IAAI,CAACvN,IAAI,CAACkK,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKpN,WAAW,CAACmN,CAAC,CAACvE,IAAI,EAAEwE,CAAC,CAACxE,IAAI,CAAC,CAAC;QACrD0F,OAAO,CAACC,GAAG,CAAC,yCAAyC,EAAE,IAAI,CAACvL,IAAI,CAAC;QAElE,IAAI,CAACqJ,gBAAgB,GAAG,CAAC,GAAG8C,KAAK,CAAC;QAElC,IAAI,CAACxH,UAAU,GAAGsH,IAAI;QACtB,IAAI,CAACxH,OAAO,GAAG,EAAE;QACjB,IAAI,CAACE,UAAU,CAACmF,OAAO,CAAC0D,GAAG,IAAG;UAC1BA,GAAG,CAAC5H,IAAI,GAAG4H,GAAG,CAAC7H,IAAI,GAAG,GAAG,GAAG6H,GAAG,CAAC5H,IAAI;UAEpC,MAAM6H,YAAY,GAAG,IAAI,CAAChJ,OAAO,CAACiJ,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC/H,IAAI,KAAK4H,GAAG,CAACI,MAAM,CAAC,GAAG,CAAC,CAAC;UAC5E,IAAI,CAACH,YAAY,EAAE;YACf,IAAI,CAAChJ,OAAO,CAACwF,IAAI,CAAC;cACd5D,EAAE,EAAEmH,GAAG,CAACI,MAAM;cACdhI,IAAI,EAAE4H,GAAG,CAACI,MAAM;cAChBjJ,UAAU,EAAE,IAAI,CAACA,UAAU,CAACkJ,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACF,MAAM,KAAKJ,GAAG,CAACI,MAAM;aAClE,CAAC;;QAEV,CAAC,CAAC;QAEF;QACA9C,UAAU,CAAC,MAAK;UACZ,IAAI,CAACC,uBAAuB,EAAE;UAC9B,IAAI,CAACU,uBAAuB,EAAE;QAClC,CAAC,EAAE,GAAG,CAAC;MACX,CAAC;MACDsC,KAAK,EAAGC,GAAG,IAAI;QACX,IAAI,CAAC3I,OAAO,GAAG,KAAK;MACxB,CAAC;MACD4I,QAAQ,EAAEA,CAAA,KAAK;QACX,IAAI,CAACC,oBAAoB,EAAE;QAC3B,IAAI,CAACnF,GAAG,CAACyC,aAAa,EAAE;QACxB,IAAI,CAACnG,OAAO,GAAG,KAAK;QACpB,IAAI,CAAC8I,OAAO,EAAE;MAClB;KACH,CAAC,CACL;EACL;EAEQD,oBAAoBA,CAAA;IACxB,IAAI,CAACrJ,QAAQ,GAAG,EAAE;IAClB,IAAI,CAACwE,gBAAgB,CAACS,OAAO,CAACsE,IAAI,IAAG;MACjC,IAAI,CAACvJ,QAAQ,CAACoF,IAAI,CAAC;QACf5D,EAAE,EAAE+H,IAAI,CAAC/H,EAAE;QACXgI,KAAK,EAAED,IAAI,CAACE,UAAU;QACtB1I,IAAI,EAAEwI,IAAI,CAACxI;OACd,CAAC;IACN,CAAC,CAAC;IAEF;IACA,IAAI,CAACf,QAAQ,CAACqF,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACvE,IAAI,GAAGwE,CAAC,CAACxE,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;EAC1D;EAIQuI,OAAOA,CAAA;IACX,IAAI,CAAC7I,WAAW,GAAG,IAAI;IAEvB;IACA,MAAMiJ,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,SAAS,CAAC,IAAI,CAACtQ,OAAO,CAAC,CAAC;IAE5D,IAAI,CAACmL,aAAa,CAACU,IAAI,CACnB,IAAI,CAACrB,WAAW,CAACuF,OAAO,CAACI,WAAW,CAAC,CAACjC,SAAS,CAAC;MAC5CC,IAAI,EAAGoC,MAAM,IAAI;QACb,IAAI,CAACC,UAAU,GAAGD,MAAM;MAC5B,CAAC;MACDZ,KAAK,EAAGC,GAAG,IAAI;QACX,IAAI,CAAC1I,WAAW,GAAG,KAAK;QACxB,IAAI,CAACwD,cAAc,CAACiF,KAAK,CAAC,yCAAyC,EAAE,kBAAkB,CAAC;MAC5F,CAAC;MACDE,QAAQ,EAAEA,CAAA,KAAK;QACX,IAAI,CAAC,IAAI,CAACW,UAAU,IAAI,CAAC,IAAI,CAACA,UAAU,CAACrJ,aAAa,IAAI,IAAI,CAACqJ,UAAU,CAACrJ,aAAa,CAACxG,MAAM,KAAK,CAAC,EAAE;UAClG,IAAI,CAACwG,aAAa,GAAG,EAAE;UACvB,IAAI,CAACD,WAAW,GAAG,KAAK;UACxB;;QAGJ;QACA,IAAIuJ,qBAAqB,GAAG,IAAI,CAACxF,gBAAgB,CAACwE,MAAM,CAAC7D,CAAC,IACtD,IAAI,CAAC4E,UAAU,CAACrJ,aAAa,CAACmE,QAAQ,CAACM,CAAC,CAAC3D,EAAE,CAAC,CAC/C;QAED;QACA;QACA,IAAI,CAAC,IAAI,CAAClC,YAAY,EAAE;UACpB,MAAM2K,WAAW,GAAG,IAAI,CAACpG,WAAW,CAACe,gBAAgB;UACrD,IAAIqF,WAAW,CAACrE,GAAG,EAAE;YACjB;YACAoE,qBAAqB,GAAGA,qBAAqB,CAAChB,MAAM,CAACkB,YAAY,IAAG;cAChE;cACA;cACA,MAAM3B,KAAK,GAAG0B,WAAW,CAACrE,GAAG,CAACpE,EAAE;cAChC,MAAM2I,IAAI,GAAG,IAAI,CAACJ,UAAU,CAACI,IAAI;cAEjC;cACA,OAAOA,IAAI,CAACC,IAAI,CAACC,IAAI,IAAG;gBACpB;gBACA,IAAI,OAAO,IAAIA,IAAI,EAAE;kBACjB,OAAOA,IAAI,CAACC,MAAM,KAAKJ,YAAY,CAAC1I,EAAE,IAAI6I,IAAI,CAAC9B,KAAK,KAAKA,KAAK;;gBAElE;gBACA,OAAO8B,IAAI,CAACE,OAAO,KACfF,IAAI,CAACE,OAAO,CAAC1F,QAAQ,CAACoF,WAAW,CAACrE,GAAG,CAAC8C,SAAS,CAAC,IAChD2B,IAAI,CAACE,OAAO,CAAC1F,QAAQ,CAACoF,WAAW,CAACrE,GAAG,CAAC4E,QAAQ,CAAC,CAClD;cACL,CAAC,CAAC;YACN,CAAC,CAAC;;;QAIV,IAAI,CAAC9J,aAAa,GAAGsJ,qBAAqB;QAC1C,IAAI,CAACtJ,aAAa,CAAC2E,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKpN,WAAW,CAACmN,CAAC,CAACzE,QAAQ,CAACC,IAAI,EAAEyE,CAAC,CAAC1E,QAAQ,CAACC,IAAI,CAAC,IAAI3I,WAAW,CAACmN,CAAC,CAACvE,IAAI,EAAEwE,CAAC,CAACxE,IAAI,CAAC,CAAC;QAE/G;QACA,IAAI,IAAI,CAACgJ,UAAU,CAACI,IAAI,IAAI,CAAC,IAAI,CAAC5Q,OAAO,CAAC+G,QAAQ,EAAE;UAChD,IAAI,CAACyJ,UAAU,CAACI,IAAI,GAAG,IAAI,CAACM,kBAAkB,CAAC,IAAI,CAACV,UAAU,CAACI,IAAI,CAAC;;QAGxE,IAAI,CAACxH,YAAY,GAAG,CAAC,CAAC;QACtB;QACA,IAAI,CAACmD,aAAa,CAAC4E,OAAO,CAACC,cAAc,CAAC,IAAI,CAAC;QAC/C;QACA,IAAI,IAAI,CAACrI,cAAc,EACnB,IAAI,CAACJ,uBAAuB,EAAE;QAElC,IAAI,CAACgC,GAAG,CAACyC,aAAa,EAAE;QAExB,IAAI,CAACiE,iBAAiB,EAAE;QACxB,IAAI,CAACnK,WAAW,GAAG,KAAK;MAC5B;KACH,CAAC,CACL;EACL;EAEA;;;;;;;;;EASQgK,kBAAkBA,CAACN,IAAW;IAClC;IACA,IAAI,CAAC,IAAI,CAACrQ,aAAa,EAAE;MACrB,OAAOqQ,IAAI;;IAGf;IACA,MAAMU,kBAAkB,GAAGV,IAAI,CAACW,MAAM,CAAC,CAACC,MAAM,EAAEV,IAAI,KAAI;MACpD,MAAMC,MAAM,GAAGD,IAAI,CAACC,MAAM,IAAI,SAAS;MACvC,IAAI,CAACS,MAAM,CAACT,MAAM,CAAC,EAAE;QACjBS,MAAM,CAACT,MAAM,CAAC,GAAG,EAAE;;MAEvBS,MAAM,CAACT,MAAM,CAAC,CAAClF,IAAI,CAACiF,IAAI,CAAC;MACzB,OAAOU,MAAM;IACjB,CAAC,EAAE,EAAE,CAAC;IAEN,MAAMC,YAAY,GAAG,EAAE;IAEvB;IACAjG,MAAM,CAACkG,IAAI,CAACJ,kBAAkB,CAAC,CAAC5F,OAAO,CAACqF,MAAM,IAAG;MAC7C,MAAMY,gBAAgB,GAAGL,kBAAkB,CAACP,MAAM,CAAC;MACnD,MAAMa,eAAe,GAAG,EAAE;MAE1B;MACA,IAAID,gBAAgB,CAAChR,MAAM,GAAG,CAAC,IAAI,MAAM,IAAIgR,gBAAgB,CAAC,CAAC,CAAC,EAAE;QAE9D;QACA,KAAK,MAAMb,IAAI,IAAIa,gBAAgB,EAAE;UACjC;UACA,MAAME,iBAAiB,GAAGf,IAAI,CAACgB,MAAM,KAAK,CAAC,CAAC,CAAC;UAE7C;UACA,MAAMC,YAAY,GAAGjB,IAAI,CAACkB,IAAI,IACXlB,IAAI,CAACkB,IAAI,KAAK,KAAK,IACnBlB,IAAI,CAACkB,IAAI,CAAC1D,IAAI,EAAE,KAAK,EAAE;UAE1C,MAAM2D,kBAAkB,GAAGnB,IAAI,CAACoB,UAAU,IAAIpB,IAAI,CAACoB,UAAU,GAAG,CAAC;UAEjE,IAAIH,YAAY,IAAIE,kBAAkB,EAAE;YACpC,IAAIJ,iBAAiB,EAAE;cACnB;cACA,IAAI,IAAI,CAAC7R,OAAO,CAACmS,MAAM,GAAG,CAAC,IAAI,IAAI,CAACnS,OAAO,CAACoS,SAAS,GAAG,CAAC,EAAE;gBACvD,MAAMC,iBAAiB,GAAGvB,IAAI,CAACwB,OAAO,IACd9G,MAAM,CAACkG,IAAI,CAACZ,IAAI,CAACwB,OAAO,CAAC,CAACzB,IAAI,CAAC0B,GAAG,IAAG;kBACjC,MAAMC,KAAK,GAAG1B,IAAI,CAACwB,OAAO,CAACC,GAAG,CAAC;kBAE/B,IAAIC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAKC,SAAS,IAAID,KAAK,KAAK,EAAE,EAAE;oBACvD,OAAO,KAAK;;kBAGhB,MAAME,WAAW,GAAGF,KAAK,CAACG,QAAQ,EAAE,CAACrE,IAAI,EAAE;kBAE3C;kBACA,IAAIoE,WAAW,KAAK,EAAE,IAClBA,WAAW,KAAK,KAAK,IACrBA,WAAW,KAAK,GAAG,IACnBA,WAAW,KAAK,MAAM,IACtBA,WAAW,KAAK,WAAW,EAAE;oBAC7B,OAAO,KAAK;;kBAGhB;kBACA,MAAME,QAAQ,GAAGC,UAAU,CAACH,WAAW,CAACI,OAAO,CAAC,IAAI,EAAE,EAAE,CAAC,CAAC;kBAC1D,IAAI,CAACC,KAAK,CAACH,QAAQ,CAAC,EAAE;oBAClB,OAAOA,QAAQ,IAAI,CAAC,CAAC,CAAC;;kBAG1B;kBACA,OAAOF,WAAW,CAAC/R,MAAM,GAAG,CAAC;gBACjC,CAAC,CAAC;gBAE1B,IAAI0R,iBAAiB,EAAE;kBACnBT,eAAe,CAAC/F,IAAI,CAACiF,IAAI,CAAC;;eAEjC,MAAM;gBACH;gBACAc,eAAe,CAAC/F,IAAI,CAACiF,IAAI,CAAC;;aAEjC,MAAM;cACH;cACA;cACAc,eAAe,CAAC/F,IAAI,CAACiF,IAAI,CAAC;;;;QAKtC;QACA;QACA,IAAIc,eAAe,CAACjR,MAAM,KAAK,CAAC,IAAIgR,gBAAgB,CAAChR,MAAM,GAAG,CAAC,EAAE;UAC7D;UACA,MAAMqS,mBAAmB,GAAGrB,gBAAgB,CAAClC,MAAM,CAACqB,IAAI,IACpDA,IAAI,CAACkB,IAAI,IAAIlB,IAAI,CAACkB,IAAI,KAAK,KAAK,IAAIlB,IAAI,CAACkB,IAAI,CAAC1D,IAAI,EAAE,KAAK,EAAE,CAC9D;UAED,IAAI2E,kBAAkB;UACtB,IAAID,mBAAmB,CAACrS,MAAM,GAAG,CAAC,EAAE;YAChC;YACAsS,kBAAkB,GAAGD,mBAAmB,CAAClH,IAAI,CAAC,CAACoH,CAAC,EAAEC,CAAC,KAAI;cACnD;cACA,OAAOA,CAAC,CAACnB,IAAI,CAACoB,aAAa,CAACF,CAAC,CAAClB,IAAI,CAAC;YACvC,CAAC,CAAC,CAAC,CAAC,CAAC;WACR,MAAM;YACH;YACAiB,kBAAkB,GAAGtB,gBAAgB,CAAC,CAAC,CAAC;;UAG5CC,eAAe,CAAC/F,IAAI,CAACoH,kBAAkB,CAAC;;QAG5CxB,YAAY,CAAC5F,IAAI,CAAC,GAAG+F,eAAe,CAAC;OACxC,MAAM;QACH;QACAH,YAAY,CAAC5F,IAAI,CAAC,GAAG8F,gBAAgB,CAAC;;IAE9C,CAAC,CAAC;IAEF,OAAOF,YAAY;EACvB;EAEAvQ,cAAcA,CAACmS,IAAI;IACf;IACA,MAAMC,cAAc,GAAGD,IAAI,CAACC,cAAc,IAAID,IAAI,CAACE,OAAO,IAAI,EAAE;IAEhE,IAAIF,IAAI,CAACpL,EAAE,KAAK,YAAY,EAAE;MAC1B;MACA,IAAI,CAAC2E,cAAc,CAAC4G,cAAc,EAAE;MAEpC,IAAI,CAACF,cAAc,CAAC3S,MAAM,EAAE;QACxB,IAAI,CAACiM,cAAc,CAACC,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC1G,QAAQ,CAAC;OACnD,MAAM;QACH,IAAI,CAACyG,cAAc,CAACC,OAAO,GAAG,IAAI,CAAC1G,QAAQ,CAACsJ,MAAM,CAACZ,CAAC,IAChDA,CAAC,CAACR,QAAQ,IAAIiF,cAAc,CAAChI,QAAQ,CAACuD,CAAC,CAACR,QAAQ,CAAC,CACpD;QACD,IAAI,CAACzB,cAAc,CAACC,OAAO,CAACf,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAMD,CAAC,CAACvE,IAAI,GAAGwE,CAAC,CAACxE,IAAI,GAAI,CAAC,GAAKuE,CAAC,CAACvE,IAAI,GAAGwE,CAAC,CAACxE,IAAI,GAAI,CAAC,CAAC,GAAG,CAAE,CAAC;QAEhG;QACA,IAAI,CAACoF,cAAc,CAAC6G,iBAAiB,CAAC,IAAI,CAAC7G,cAAc,CAACC,OAAO,CAAC+B,GAAG,CAACW,CAAC,IAAIA,CAAC,CAACtH,EAAE,CAAC,CAAC;;MAGrF,IAAI,CAAC2E,cAAc,CAACE,KAAK,GAAG,CAAC,GAAG,IAAI,CAACF,cAAc,CAACC,OAAO,CAAC;KAC/D,MAAM,IAAIwG,IAAI,CAACpL,EAAE,KAAK,SAAS,EAAE;MAC9B,IAAI,CAACyL,aAAa,CAACF,cAAc,EAAE;MACnC,IAAI,CAACG,cAAc,CAACH,cAAc,EAAE;MAEpC,IAAI,CAACF,cAAc,CAAC3S,MAAM,EAAE;QACxB,IAAI,CAAC+S,aAAa,CAAC7G,OAAO,GAAG,CAAC,GAAG,IAAI,CAACtG,UAAU,CAAC;QACjD,IAAI,CAACoN,cAAc,CAAC9G,OAAO,GAAG,CAAC,GAAG,IAAI,CAACpG,QAAQ,CAAC;OACnD,MAAM;QACH,IAAI,CAACiN,aAAa,CAAC7G,OAAO,GAAG,IAAI,CAACtG,UAAU,CAACkJ,MAAM,CAACC,CAAC,IAAI4D,cAAc,CAAChI,QAAQ,CAACoE,CAAC,CAACF,MAAM,CAAC,CAAC;QAC3F,IAAI,CAACkE,aAAa,CAAC7G,OAAO,CAACf,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAMD,CAAC,CAACvE,IAAI,GAAGwE,CAAC,CAACxE,IAAI,GAAI,CAAC,GAAKuE,CAAC,CAACvE,IAAI,GAAGwE,CAAC,CAACxE,IAAI,GAAI,CAAC,CAAC,GAAG,CAAE,CAAC;QAC/F,IAAI,CAACmM,cAAc,CAAC9G,OAAO,GAAG,IAAI,CAACpG,QAAQ,CAACgJ,MAAM,CAACZ,CAAC,IAAI,IAAI,CAAC6E,aAAa,CAAC7G,OAAO,CAACyC,SAAS,CAACI,CAAC,IAAIA,CAAC,CAACzH,EAAE,KAAK4G,CAAC,CAACoB,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QACzH,IAAI,CAAC0D,cAAc,CAAC9G,OAAO,CAACf,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAMD,CAAC,CAACvE,IAAI,GAAGwE,CAAC,CAACxE,IAAI,GAAI,CAAC,GAAKuE,CAAC,CAACvE,IAAI,GAAGwE,CAAC,CAACxE,IAAI,GAAI,CAAC,CAAC,GAAG,CAAE,CAAC;;MAGpG,IAAI,CAACkM,aAAa,CAAC5G,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC4G,aAAa,CAAC7G,OAAO,CAAC;MAC1D,IAAI,CAAC8G,cAAc,CAAC7G,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC6G,cAAc,CAAC9G,OAAO,CAAC;KAC/D,MAAM,IAAIwG,IAAI,CAACpL,EAAE,KAAK,QAAQ,EAAE;MAC7B,IAAI,CAAC0L,cAAc,CAACH,cAAc,EAAE;MAEpC,IAAI,CAACF,cAAc,CAAC3S,MAAM,EAAE;QACxB,IAAI,CAACgT,cAAc,CAAC9G,OAAO,GAAG,CAAC,GAAG,IAAI,CAACpG,QAAQ,CAAC;OACnD,MAAM;QACH,IAAI,CAACkN,cAAc,CAAC9G,OAAO,GAAG,IAAI,CAACpG,QAAQ,CAACgJ,MAAM,CAACZ,CAAC,IAAIyE,cAAc,CAAChI,QAAQ,CAACuD,CAAC,CAACoB,KAAK,CAAC,CAAC;QACzF,IAAI,CAAC0D,cAAc,CAAC9G,OAAO,CAACf,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAMD,CAAC,CAACvE,IAAI,GAAGwE,CAAC,CAACxE,IAAI,GAAI,CAAC,GAAKuE,CAAC,CAACvE,IAAI,GAAGwE,CAAC,CAACxE,IAAI,GAAI,CAAC,CAAC,GAAG,CAAE,CAAC;;MAEpG,IAAI,CAACmM,cAAc,CAAC7G,KAAK,GAAG,CAAC,GAAG,IAAI,CAAC6G,cAAc,CAAC9G,OAAO,CAAC;;IAGhE,IAAGwG,IAAI,CAACpL,EAAE,KAAK,QAAQ,IAAIoL,IAAI,CAACpL,EAAE,KAAK,MAAM,EACzC,IAAI,CAACjI,OAAO,CAACqT,IAAI,CAACpL,EAAE,CAAC,GAAGqL,cAAc,CAAC,CAAC,CAAC,CAAC,KAE1C,IAAI,CAACtT,OAAO,CAACqT,IAAI,CAACpL,EAAE,CAAC,GAAGqL,cAAc;EAC9C;EAEAM,gBAAgBA,CAAC7M,QAAkB;IAC/B,IAAI,CAAC/G,OAAO,CAAC+G,QAAQ,GAAGA,QAAQ,IAAI,KAAK;IACzC,IAAI,CAAC/G,OAAO,CAACmS,MAAM,GAAG,CAAC;IACvB,IAAI,CAACnS,OAAO,CAACoS,SAAS,GAAG,CAAC;IAC1B,IAAI,CAACpS,OAAO,CAAC6T,IAAI,GAAG,CAAC;IACrB,IAAI,CAAC7T,OAAO,CAAC8T,OAAO,GAAG,CAAC;IAExB;IACA,IAAI,CAAC,IAAI,CAAC9T,OAAO,CAAC+G,QAAQ,EAAE;MACxB,IAAI,CAACxG,aAAa,GAAG,IAAI;;IAG7B;IACA,IAAI,CAACgM,aAAa,CAACwH,YAAY,CAAChN,QAAQ,GAAGA,QAAQ;IACnD,IAAI,CAACgJ,OAAO,EAAE;EAClB;EAEAvQ,cAAcA,CAAA;IACV,IAAI,CAACQ,OAAO,CAACC,YAAY,GAAG,CAAC,IAAI,CAACD,OAAO,CAACC,YAAY;IACtD,IAAI,CAACsM,aAAa,CAACtM,YAAY,GAAG,IAAI,CAACD,OAAO,CAACC,YAAY;IAE3D,IAAI,IAAI,CAACD,OAAO,CAACC,YAAY,EAAE;MAC3B,MAAM+T,KAAK,GAAGC,QAAQ,CAACC,aAAa,CAAC,aAAa,CAAqB;MACvE,IAAIF,KAAK,EACLA,KAAK,CAACxB,KAAK,GAAG,EAAE;;IAGxB;IACA,IAAI,CAACzC,OAAO,EAAE;EAClB;EAEA1P,iBAAiBA,CAAA;IACb,IAAI,CAACE,aAAa,GAAG,CAAC,IAAI,CAACA,aAAa;IAExC;IACA,IAAI,CAACwP,OAAO,EAAE;EAClB;EAEAxN,cAAcA,CAAC4R,CAAM;IACjB,MAAMC,MAAM,GAAGD,CAAC,CAACC,MAA0B;IAC3C,MAAMC,IAAI,GAAGD,MAAM,CAAC5B,KAAK,EAAE8B,KAAK,CAAC,GAAG,CAAC;IAErC,IAAIF,MAAM,CAACnM,EAAE,KAAK,YAAY,EAAE;MAC5B,IAAI,CAACoM,IAAI,IAAIA,IAAI,CAAC1T,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAI,CAACX,OAAO,CAACmS,MAAM,GAAG,CAAC;QACvB,IAAI,CAACnS,OAAO,CAAC6T,IAAI,GAAG,CAAC;QACrB,IAAI,CAAC9R,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;OACzB,MAAM;QACH,IAAI,CAAC/B,OAAO,CAACmS,MAAM,GAAG,CAACkC,IAAI,CAAC,CAAC,CAAC;QAC9B,IAAI,CAACrU,OAAO,CAAC6T,IAAI,GAAG,CAACQ,IAAI,CAAC,CAAC,CAAC;QAC5B,IAAI,CAACtS,SAAS,CAAC,CAAC,CAAC,GAAG,MAAOlD,YAAY,CAAC,IAAI,CAACmB,OAAO,CAACmS,MAAM,CAAE,IAAK,IAAI,CAACnS,OAAO,CAAC6T,IAAK,EAAE;;KAE7F,MAAM;MACH,IAAI,CAACQ,IAAI,IAAIA,IAAI,CAAC1T,MAAM,GAAG,CAAC,EAAE;QAC1B,IAAI,CAACX,OAAO,CAACoS,SAAS,GAAG,CAAC;QAC1B,IAAI,CAACpS,OAAO,CAAC8T,OAAO,GAAG,CAAC;QACxB,IAAI,CAAC/R,SAAS,CAAC,CAAC,CAAC,GAAG,EAAE;OACzB,MAAM;QACH,IAAI,CAAC/B,OAAO,CAACoS,SAAS,GAAG,CAACiC,IAAI,CAAC,CAAC,CAAC;QACjC,IAAI,CAACrU,OAAO,CAAC8T,OAAO,GAAG,CAACO,IAAI,CAAC,CAAC,CAAC;QAC/B,IAAI,CAACtS,SAAS,CAAC,CAAC,CAAC,GAAG,IAAImK,IAAI,CAAC,IAAI,CAAClM,OAAO,CAAC8T,OAAO,EAAE,IAAI,CAAC9T,OAAO,CAACoS,SAAS,EAAE,CAAC,CAAC,CAACmC,OAAO,EAAE,GACnF,IAAI1V,YAAY,CAAC,IAAI,CAACmB,OAAO,CAACoS,SAAS,CAAC,IAAI,IAAI,CAACpS,OAAO,CAAC8T,OAAO,EAAE;;;EAGlF;EAEA3P,YAAYA,CAAA;IACR;IACA,IAAI,IAAI,CAAC4B,YAAY,IAAI,IAAI,CAACkH,oBAAoB,EAAE;MAChD,IAAI,CAACA,oBAAoB,CAACuG,cAAc,EAAE;;IAG9C,IAAI,IAAI,CAAC5G,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAAC4G,cAAc,EAAE;MACpC,IAAI,CAAC5G,cAAc,CAACC,OAAO,GAAG,CAAC,GAAG,IAAI,CAAC1G,QAAQ,CAAC;MAChD,IAAI,CAACyG,cAAc,CAACE,KAAK,GAAG,CAAC,GAAG,IAAI,CAACF,cAAc,CAACC,OAAO,CAAC;;IAGhE,IAAI,IAAI,CAAC2H,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAAChB,cAAc,EAAE;;IAG1C,IAAI,IAAI,CAACE,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACF,cAAc,EAAE;;IAGvC,IAAI,IAAI,CAACG,cAAc,EAAE;MACrB,IAAI,CAACA,cAAc,CAACH,cAAc,EAAE;;IAGxC,IAAI,IAAI,CAACzN,YAAY,IAAI,IAAI,CAACuH,iBAAiB,EAAE;MAC7C,IAAI,CAACA,iBAAiB,CAACkG,cAAc,EAAE;;IAG3C,IAAI,IAAI,CAACiB,iBAAiB,EAAE;MACxB,IAAI,CAACA,iBAAiB,CAAChB,iBAAiB,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;;IAGpD,IAAI,IAAI,CAACiB,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAAClB,cAAc,EAAE;;IAG1C;IACA,IAAI,CAACxT,OAAO,GAAG;MACX+G,QAAQ,EAAE,IAAI,CAAC/G,OAAO,CAAC+G,QAAQ;MAC/B9G,YAAY,EAAE,IAAI,CAACD,OAAO,CAACC,YAAY;MACvCiL,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC;KACpB;IAED;IACA,IAAI,CAAC3K,aAAa,GAAG,IAAI;IAEzB,IAAI,CAACG,QAAQ,GAAG,CAAC,EAAE,CAAC;IAEpB;IACA,MAAMiU,UAAU,GAAGV,QAAQ,CAACW,cAAc,CAAC,YAAY,CAAqB;IAC5E,MAAMC,QAAQ,GAAGZ,QAAQ,CAACW,cAAc,CAAC,UAAU,CAAqB;IACxE,IAAID,UAAU,EAAEA,UAAU,CAACnC,KAAK,GAAG,EAAE;IACrC,IAAIqC,QAAQ,EAAEA,QAAQ,CAACrC,KAAK,GAAG,EAAE;IAEjC;IACA,IAAI,IAAI,CAACxS,OAAO,CAAC+G,QAAQ,EAAE;MACvB,IAAI,IAAI,CAAC6F,cAAc,EAAE;QACrB,IAAI,CAACA,cAAc,CAAC4G,cAAc,EAAE;;;IAI5C,IAAI,CAACzD,OAAO,EAAE;EAClB;EAEAxK,YAAYA,CAAA;IACR,IAAI,CAAC,IAAI,CAACvF,OAAO,CAACC,YAAY,EAAE;MAC5B,IAAI,IAAI,CAACD,OAAO,CAACmS,MAAM,GAAG,CAAC,IAAI,IAAI,CAACnS,OAAO,CAAC6T,IAAI,GAAG,CAAC,IAChD,IAAI,CAAC7T,OAAO,CAACoS,SAAS,GAAG,CAAC,IAAI,IAAI,CAACpS,OAAO,CAAC8T,OAAO,GAAG,CAAC,EAAE;QACxD,MAAMO,IAAI,GAAG,IAAInI,IAAI,CAAC,IAAI,CAAClM,OAAO,CAAC6T,IAAI,EAAE,IAAI,CAAC7T,OAAO,CAACmS,MAAM,GAAG,CAAC,EAAE,CAAC,CAAC;QACpE,MAAM2C,OAAO,GAAG,IAAI5I,IAAI,CAAC,IAAI,CAAClM,OAAO,CAAC8T,OAAO,EAAE,IAAI,CAAC9T,OAAO,CAACoS,SAAS,GAAG,CAAC,EAAE,CAAC,CAAC;QAE7E,IAAI0C,OAAO,GAAGT,IAAI,EAAE;UAChB,IAAI,CAAC3J,cAAc,CAACiF,KAAK,CAAC,iEAAiE,GACvF,sDAAsD,EAAE,gBAAgB,EAAE;YAAEoF,UAAU,EAAE;UAAI,CAAE,CAAC;UACnG;;;;IAKZ;IACA,IAAI,CAAChF,OAAO,EAAE;IAEd;IACA,IAAI,CAACrP,QAAQ,GAAG,CAAC,EAAE,CAAC;IACpB,IAAI,IAAI,CAACV,OAAO,CAACsB,UAAU,EAAEX,MAAM,EAC/B,IAAI,CAACD,QAAQ,CAACmL,IAAI,CAAC,eAAe,CAAC;IACvC,IAAI,IAAI,CAAC7L,OAAO,CAACgV,OAAO,EAAErU,MAAM,EAC5B,IAAI,CAACD,QAAQ,CAACmL,IAAI,CAAC,SAAS,CAAC;IACjC,IAAI,IAAI,CAAC7L,OAAO,CAACiV,OAAO,EAAEtU,MAAM,EAC5B,IAAI,CAACD,QAAQ,CAACmL,IAAI,CAAC,cAAc,CAAC,CAAC,KAClC,IAAI,IAAI,CAAC7L,OAAO,CAACkV,MAAM,EAAEvU,MAAM,EAChC,IAAI,CAACD,QAAQ,CAACmL,IAAI,CAAC,UAAU,CAAC,CAAC,KAC9B,IAAI,IAAI,CAAC7L,OAAO,CAACqG,OAAO,EAAE1F,MAAM,EACjC,IAAI,CAACD,QAAQ,CAACmL,IAAI,CAAC,QAAQ,CAAC;IAChC,IAAI,IAAI,CAAC7L,OAAO,CAACmV,MAAM,EAAExU,MAAM,EAC3B,IAAI,CAACD,QAAQ,CAACmL,IAAI,CAAC,SAAS,CAAC;IACjC,IAAI,IAAI,CAAC7L,OAAO,CAAC8G,OAAO,EAAEnG,MAAM,EAC5B,IAAI,CAACD,QAAQ,CAACmL,IAAI,CAAC,QAAQ,CAAC;IAChC,IAAI,IAAI,CAAC7L,OAAO,CAACmS,MAAM,GAAG,CAAC,IAAI,IAAI,CAACnS,OAAO,CAACoS,SAAS,GAAG,CAAC,EACrD,IAAI,CAAC1R,QAAQ,CAACmL,IAAI,CAAC,QAAQ,CAAC;IAEhC,IAAI,CAACnL,QAAQ,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,QAAQ,CAAC+O,MAAM,CAAC,CAAC2F,CAAC,EAAExJ,CAAC,KAAKA,CAAC,GAAG,CAAC,CAAC,CAACyJ,IAAI,CAAC,IAAI,CAAC;EACvE;EAEQhE,iBAAiBA,CAACiE,SAAkB;IACxC,IAAIC,UAAU;IAEd,IAAI,CAACD,SAAS,EACVA,SAAS,GAAG,IAAI,CAAC9E,UAAU,CAACrJ,aAAa,CAAC,CAAC,CAAC;IAEhDoO,UAAU,GAAG,IAAI,CAACpO,aAAa,CAACqH,IAAI,CAAC5C,CAAC,IAAIA,CAAC,CAAC3D,EAAE,KAAKqN,SAAS,CAAC;IAE7D,IAAIC,UAAU,EAAE;MACZ,IAAI,CAACjN,QAAQ,GAAGiN,UAAU,CAACtN,EAAE;MAC7B,IAAI,CAACsE,aAAa,CAACwH,YAAY,CAAChD,MAAM,GAAGwE,UAAU,CAACtN,EAAE;MACtD,IAAI,CAACsE,aAAa,CAACwH,YAAY,CAAC/D,IAAI,GAAGuF,UAAU,CAAC9N,YAAY;MAE9D,IAAI,CAAC,IAAI,CAACzH,OAAO,CAAC+G,QAAQ,EAAE;QACxB,IAAI,CAACwF,aAAa,CAACiJ,cAAc,GAAGD,UAAU,CAACE,SAAS,CACnDhG,MAAM,CAAC9D,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACL,QAAQ,CAACK,CAAC,CAAC+J,IAAI,CAAC,CAAC,CAAC,CAAC;OACjD,MAAM;QACH,IAAI,CAACnJ,aAAa,CAACiJ,cAAc,GAAGD,UAAU,CAACE,SAAS,CACnDhG,MAAM,CAAC9D,CAAC,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACL,QAAQ,CAACK,CAAC,CAAC+J,IAAI,CAAC,CAAC,CAAC,CAAC;;;MAGlD,IAAI,CAACnJ,aAAa,CAACoJ,aAAa,GAAG,KAAK;MACxC,IAAG,IAAI,CAAC3V,OAAO,CAACmS,MAAM,IAAI,IAAI,CAACnS,OAAO,CAACoS,SAAS,EAC5C,IAAI,CAAC7F,aAAa,CAACoJ,aAAa,GAAG,IAAI;MAE3C,IAAI,CAACpJ,aAAa,CAACqJ,QAAQ,CAAC,IAAI,CAAC5V,OAAO,CAAC+G,QAAQ,CAAC;MAClD,IAAI,CAACwF,aAAa,CAACsJ,eAAe,CAAC,IAAI,CAACrF,UAAU,CAACI,IAAI,CAAC;;EAEhE;EAEA5I,mBAAmBA,CAAC+I,MAAc;IAC9B,IAAI,CAACxE,aAAa,CAACtF,OAAO,GAAG,IAAI;IAEjC,IAAI,CAACkE,aAAa,CAACU,IAAI,CACnB,IAAI,CAACrB,WAAW,CAACxC,mBAAmB,CAAC+I,MAAM,EAAE,IAAI,CAACP,UAAU,CAACxQ,OAAO,CAAC,CAACkO,SAAS,CAAC;MAChFC,IAAI,EAAG2H,GAAG,IAAI;QACV,IAAI,CAACtF,UAAU,CAACI,IAAI,GAAG,CAAC,GAAGkF,GAAG,CAAC;MACnC,CAAC;MACDnG,KAAK,EAAGC,GAAG,IAAI;QACX,IAAI,CAACrD,aAAa,CAACtF,OAAO,GAAG,KAAK;MACtC,CAAC;MACD4I,QAAQ,EAAEA,CAAA,KAAK;QACX;QACA,IAAI,CAACzG,YAAY,GAAG,CAAC,CAAC;QACtB;QACA,IAAI,IAAI,CAACL,cAAc,EACnB,IAAI,CAACJ,uBAAuB,EAAE;QAElC,IAAI,CAAC4D,aAAa,CAAC4E,OAAO,CAACC,cAAc,CAAC,IAAI,CAAC;QAC/C,IAAI,CAAC7E,aAAa,CAAC4E,OAAO,CAAC4E,oBAAoB,CAAC,OAAO,CAAC;QAExD,IAAI,CAAC1E,iBAAiB,CAACN,MAAM,CAAC;QAC9B,IAAI,CAACxE,aAAa,CAACtF,OAAO,GAAG,KAAK;MACtC;KACH,CAAC,CAAC;EACP;EAGA+O,YAAYA,CAACC,GAAW;IACpB,IAAIA,GAAG,CAACC,UAAU,CAAC,GAAG,CAAC,EAAE;MACrBD,GAAG,GAAGA,GAAG,CAACnD,OAAO,CAAC,GAAG,EAAE,EAAE,CAAC;MAC1B,IAAI,CAAClS,cAAc,GAAG,IAAI,CAACA,cAAc,CAAC6O,MAAM,CAACC,CAAC,IAAIA,CAAC,KAAKuG,GAAG,CAAC;KACnE,MAAM,IAAI,CAAC,IAAI,CAACrV,cAAc,CAAC0K,QAAQ,CAAC2K,GAAG,CAAC,EACzC,IAAI,CAACrV,cAAc,CAACiL,IAAI,CAACoK,GAAG,CAAC;IAEjC,IAAI,CAACrV,cAAc,CAAC,CAAC,CAAC,GAAG,IAAI,CAACA,cAAc,CACvCuV,KAAK,CAAC,CAAC,EAAE,IAAI,CAACvV,cAAc,CAACD,MAAM,CAAC,CAAC0U,IAAI,CAAC,IAAI,CAAC;EACxD;EAGAvL,cAAcA,CAAA;IACV,IAAIsM,UAAU,GAAG,KAAK;IACtB,IAAI,IAAI,CAAChN,YAAY,KAAK,CAAC,CAAC,EAAE;MAC1BgN,UAAU,GAAG,IAAI;MACjB,IAAI,CAAChN,YAAY,GAAG,CAAC;KACxB,MAAM;MACHgN,UAAU,GAAG,KAAK;MAClB,IAAI,CAAChN,YAAY,GAAG,CAAC,CAAC;;IAE1B,IAAI,CAACmD,aAAa,CAAC8J,aAAa,CAACC,UAAU,EAAE,CAAC5K,OAAO,CAACuK,GAAG,IAAG;MACxD,MAAMM,KAAK,GAAGN,GAAG,CAACO,QAAQ,EAAE;MAC5B,IAAI,CAACD,KAAK,CAACL,UAAU,CAAC,KAAK,CAAC,IAAI,CAACK,KAAK,CAACL,UAAU,CAAC,aAAa,CAAC,EAAE;QAC9D,IAAIO,MAAM,GAAGR,GAAG,CAACS,SAAS,EAAE;QAC5BD,MAAM,CAACE,QAAQ,GAAGP,UAAU;QAC5BH,GAAG,CAACW,SAAS,CAACH,MAAM,EAAER,GAAG,CAACS,SAAS,EAAE,CAAC;;IAE9C,CAAC,CAAC;IAEF,IAAI,CAACnK,aAAa,CAAC4E,OAAO,CAAC0F,aAAa,EAAE;IAC1C,IAAI,CAACtK,aAAa,CAAC8J,aAAa,CAACS,kBAAkB,EAAE;EACzD;EA4BAnO,uBAAuBA,CAAA;IACnB,IAAI,IAAI,CAACI,cAAc,EAAE;MACrB,IAAI,CAACwD,aAAa,CAAC4E,OAAO,CAAC4F,YAAY,CAAC,KAAK,CAAC;MAC9C,IAAI,CAACxK,aAAa,CAAC4E,OAAO,CAAC6F,UAAU,CAAC,KAAK,CAAC;MAE5C,IAAI,CAACzK,aAAa,CAAC8J,aAAa,CAACY,kBAAkB,EAAE,CAACvL,OAAO,CAACuK,GAAG,IAAG;QAChE,IAAI,CAAC1J,aAAa,CAAC8J,aAAa,CAACa,oBAAoB,CAACjB,GAAG,CAACO,QAAQ,EAAE,CAAC;MACzE,CAAC,CAAC;MAEF,IAAI,CAACzN,cAAc,GAAG,KAAK;MAC3B;;IAGJ;IACA,IAAI,CAACwD,aAAa,CAAC4E,OAAO,CAAC6F,UAAU,CAAC,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;IAC7D,IAAI,CAACjO,cAAc,GAAG,IAAI;EAC9B;EAEQoO,iBAAiBA,CAAA;IACrB,IAAI,IAAI,CAAC/N,YAAY,GAAG,CAAC,CAAC,EAAE;MACxB,IAAI,CAACmD,aAAa,CAAC6K,cAAc,CAACT,QAAQ,GAAG,KAAK;MAClD,IAAI,CAACvN,YAAY,GAAG,CAAC,CAAC;;IAG1B,IAAI,IAAI,CAACL,cAAc,EACnB,IAAI,CAACJ,uBAAuB,EAAE;IAClC;;;;;;;;EAQJ;EAEA;EACA0O,aAAaA,CAACC,KAAuB;IACjC,IAAI,CAACA,KAAK,CAAC1G,IAAI,CAAC2G,aAAa,EACzB;IAEJ,IAAIC,SAAS;IACb,IAAI,IAAI,CAACxX,OAAO,CAAC+G,QAAQ,EAAE;MACvByQ,SAAS,GACL,IAAI,CAAChN,WAAW,CAACiN,iBAAiB,CAAC,CAACH,KAAK,CAAC1G,IAAI,CAAC3I,EAAE,CAAC,EAC9CqP,KAAK,CAAC1G,IAAI,CAAC8G,YAAY,GAAG,IAAI,GAAG,KAAK,CAAC;KAClD,MAAM;MACHF,SAAS,GACL,IAAI,CAAChN,WAAW,CAACmN,mBAAmB,CAAC,CAACL,KAAK,CAAC1G,IAAI,CAACsB,UAAU,CAAC,EACxDoF,KAAK,CAAC1G,IAAI,CAAC8G,YAAY,GAAG,IAAI,GAAG,KAAK,CAAC;;IAGnD,IAAI,CAACvM,aAAa,CAACU,IAAI,CACnB2L,SAAS,CAACtJ,SAAS,CAAC;MAChByB,KAAK,EAAGC,GAAG,IAAI;QACX1C,OAAO,CAACyC,KAAK,CAACC,GAAG,CAAC;MACtB,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAK;QACX,IAAIyH,KAAK,CAAC1G,IAAI,CAAC8G,YAAY,EAAE;UACzBJ,KAAK,CAAC1G,IAAI,CAAC8G,YAAY,GAAG,IAAI;UAE9B;UACA,IAAI,CAAC,IAAI,CAAC1X,OAAO,CAAC+G,QAAQ,EAAE;YACxB,IAAIuQ,KAAK,CAAC1G,IAAI,CAACgH,MAAM,EACjBN,KAAK,CAAC1G,IAAI,CAACkB,MAAM,GAAGpT,cAAc,CAACmZ,SAAS,CAAC,KAE7CP,KAAK,CAAC1G,IAAI,CAACkB,MAAM,GAAGpT,cAAc,CAACoZ,OAAO;;SAErD,MAAM;UACHR,KAAK,CAAC1G,IAAI,CAAC8G,YAAY,GAAG,IAAIxL,IAAI,EAAE;UAEpC;UACA,IAAI,CAAC,IAAI,CAAClM,OAAO,CAAC+G,QAAQ,IAAIuQ,KAAK,CAAC1G,IAAI,CAACkB,MAAM,KAAKpT,cAAc,CAACmZ,SAAS,EACxEP,KAAK,CAAC1G,IAAI,CAACkB,MAAM,GAAGpT,cAAc,CAACqZ,QAAQ;;QAGnD,IAAI,CAACxL,aAAa,CAAC4E,OAAO,CAAC6G,YAAY,CAAC;UACpCC,QAAQ,EAAE,CAACX,KAAK,CAACY,IAAI,CAAC;UACtBC,OAAO,EAAE,CAAC,YAAY,EAAC,QAAQ,CAAC;UAChCC,KAAK,EAAE,IAAI;UACXC,aAAa,EAAE;SAClB,CAAC;QAEF,IAAI,CAAC3N,cAAc,CAAC4N,OAAO,CAAC,2CAA2C,EACnEhB,KAAK,CAAC1G,IAAI,CAAC8G,YAAY,GAAG,UAAU,GAAG,oBAAoB,CAAC;MACpE;KACP,CAAC,CAAC;EACP;EAEA;EACAa,YAAYA,CAACC,UAAoB;IAC7B,IAAIC,UAAU,GAAa,EAAE;IAE7B,IAAI,CAAClM,aAAa,CAAC4E,OAAO,CAACuH,sBAAsB,CAACR,IAAI,IAAG;MACrD,IAAIA,IAAI,CAACtH,IAAI,CAAC2G,aAAa,EAAE;QACzB,IAAI,IAAI,CAACvX,OAAO,CAAC+G,QAAQ,EACrB0R,UAAU,CAAC5M,IAAI,CAACqM,IAAI,CAACtH,IAAI,CAAC3I,EAAE,CAAC,CAAC,KAE9BwQ,UAAU,CAAC5M,IAAI,CAACqM,IAAI,CAACtH,IAAI,CAACsB,UAAU,CAAC;;IAEjD,CAAC,CAAC;IAEF,MAAMsF,SAAS,GAAG,IAAI,CAACxX,OAAO,CAAC+G,QAAQ,GACjC,IAAI,CAACyD,WAAW,CAACiN,iBAAiB,CAACgB,UAAU,EAAED,UAAU,CAAC,GAC1D,IAAI,CAAChO,WAAW,CAACmN,mBAAmB,CAACc,UAAU,EAAED,UAAU,CAAC;IAElE,IAAI,CAACrN,aAAa,CAACU,IAAI,CACnB2L,SAAS,CAACtJ,SAAS,CAAC;MAChByB,KAAK,EAAGC,GAAG,IAAI;QACX1C,OAAO,CAACyC,KAAK,CAACC,GAAG,CAAC;MACtB,CAAC;MACDC,QAAQ,EAAEA,CAAA,KAAK;QACX,IAAI,CAACtD,aAAa,CAAC4E,OAAO,CAACuH,sBAAsB,CAACR,IAAI,IAAG;UACrD,IAAIM,UAAU,EAAE;YACZN,IAAI,CAACtH,IAAI,CAAC8G,YAAY,GAAG,IAAI;YAE7B;YACA,IAAI,CAAC,IAAI,CAAC1X,OAAO,CAAC+G,QAAQ,EAAE;cACxB,IAAImR,IAAI,CAACtH,IAAI,CAACgH,MAAM,EAChBM,IAAI,CAACtH,IAAI,CAACkB,MAAM,GAAGpT,cAAc,CAACmZ,SAAS,CAAC,KAE5CK,IAAI,CAACtH,IAAI,CAACkB,MAAM,GAAGpT,cAAc,CAACoZ,OAAO;;WAEpD,MAAM,IAAII,IAAI,CAACtH,IAAI,CAAC2G,aAAa,EAAE;YAChCW,IAAI,CAACtH,IAAI,CAAC8G,YAAY,GAAG,IAAIxL,IAAI,EAAE;YAEnC;YACA,IAAI,CAAC,IAAI,CAAClM,OAAO,CAAC+G,QAAQ,IAAImR,IAAI,CAACtH,IAAI,CAACkB,MAAM,KAAKpT,cAAc,CAACmZ,SAAS,EACvEK,IAAI,CAACtH,IAAI,CAACkB,MAAM,GAAGpT,cAAc,CAACqZ,QAAQ;WACjD,MAAM;YACHG,IAAI,CAACtH,IAAI,CAAC8G,YAAY,GAAG,IAAI;YAE7B;YACA,IAAI,CAAC,IAAI,CAAC1X,OAAO,CAAC+G,QAAQ,EAAE;cACxB,IAAImR,IAAI,CAACtH,IAAI,CAACgH,MAAM,EAChBM,IAAI,CAACtH,IAAI,CAACkB,MAAM,GAAGpT,cAAc,CAACmZ,SAAS,CAAC,KAE5CK,IAAI,CAACtH,IAAI,CAACkB,MAAM,GAAGpT,cAAc,CAACoZ,OAAO;;;QAGzD,CAAC,CAAC;QAEF,IAAI,CAACvL,aAAa,CAAC4E,OAAO,CAAC6G,YAAY,CAAC;UACpCG,OAAO,EAAE,CAAC,YAAY,EAAC,QAAQ,CAAC;UAChCC,KAAK,EAAE,IAAI;UACXC,aAAa,EAAE;SAClB,CAAC;QAEF,MAAMM,aAAa,GAAsB1E,QAAQ,CAACC,aAAa,CAAC,+BAA+B,CAAC;QAChG,IAAI,CAACsE,UAAU,EAAE;UACbG,aAAa,CAACC,SAAS,CAACC,GAAG,CAAC,QAAQ,CAAC;UACrCF,aAAa,CAACG,KAAK,GAAG,oBAAoB;SAC7C,MAAM;UACHH,aAAa,CAACC,SAAS,CAACG,MAAM,CAAC,QAAQ,CAAC;UACxCJ,aAAa,CAACG,KAAK,GAAG,aAAa;;QAGvC,IAAI,CAACpO,cAAc,CAAC4N,OAAO,CAAC,2CAA2C,EACnEE,UAAU,GAAG,oBAAoB,GAAG,cAAc,CAAC;MAC3D;KACH,CAAC,CAAC;EACX;EAEA7S,UAAUA,CAAA;IACN,IAAI,CAACqB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACuF,aAAa,CAAC4E,OAAO,CAAC6H,iBAAiB,EAAE;IAC9C,IAAI,CAAChS,WAAW,GAAG,KAAK;EAC5B;EAEAiS,WAAWA,CAAA;IACP,IAAI,CAAC9N,aAAa,CAACO,OAAO,CAAEwN,EAAE,IAAKA,EAAE,CAACC,WAAW,EAAE,CAAC;EACxD;EAEA;EACQC,sBAAsBA,CAACpI,OAAY;IACvC,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAE1B;IACA,MAAMqI,YAAY,GAAGrI,OAAO,CAAC/B,cAAc,KAAKwD,SAAS,GAAG6G,MAAM,CAACtI,OAAO,CAAC/B,cAAc,CAAC,GAAGwD,SAAS;IACtG,MAAM8G,eAAe,GAAGvI,OAAO,CAAChC,KAAK,KAAKyD,SAAS,GAAG6G,MAAM,CAACtI,OAAO,CAAChC,KAAK,CAAC,GAAGyD,SAAS;IACvF,MAAM+G,YAAY,GAAGF,MAAM,CAAC,IAAI,CAACtO,SAAS,CAAC;IAE3C,OAAOqO,YAAY,KAAKG,YAAY,IAAID,eAAe,KAAKC,YAAY;EAC5E;EAAC,QAAAC,CAAA,G;qBAjhCQrP,iBAAiB,EAAArL,EAAA,CAAA2a,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7a,EAAA,CAAA2a,iBAAA,CAAAG,EAAA,CAAAC,cAAA,GAAA/a,EAAA,CAAA2a,iBAAA,CAAAK,EAAA,CAAAC,eAAA,GAAAjb,EAAA,CAAA2a,iBAAA,CAAAO,EAAA,CAAAC,aAAA,GAAAnb,EAAA,CAAA2a,iBAAA,CAAAS,EAAA,CAAAC,cAAA,GAAArb,EAAA,CAAA2a,iBAAA,CAAA3a,EAAA,CAAAsb,iBAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAjBlQ,iBAAiB;IAAAmQ,SAAA;IAAAC,SAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;;;;;;;;;uBAyDf5b,qBAAqB;;;;;;;;;;;;;;;;;;;;;;;;;;QChFpCC,EAAA,CAAAC,cAAA,aAAyB;QAIND,EAAA,CAAAE,UAAA,oBAAA2b,mDAAA;UAAA,OAAUD,GAAA,CAAA/G,gBAAA,EAAkB;QAAA,EAAC;QADpC7U,EAAA,CAAAU,YAAA,EACqC;QACrCV,EAAA,CAAAC,cAAA,eAAgI;QACrFD,EAAA,CAAAW,MAAA,eAAQ;QAAAX,EAAA,CAAAU,YAAA,EAAO;QAG9DV,EAAA,CAAAC,cAAA,aAAuC;QACgDD,EAAA,CAAAE,UAAA,oBAAA4b,mDAAA;UAAA,OAAUF,GAAA,CAAA/G,gBAAA,CAAiB,IAAI,CAAC;QAAA,EAAC;QAApH7U,EAAA,CAAAU,YAAA,EAAqH;QACrHV,EAAA,CAAAC,cAAA,eACoD;QACTD,EAAA,CAAAW,MAAA,cAAM;QAAAX,EAAA,CAAAU,YAAA,EAAO;QAG5DV,EAAA,CAAAiD,UAAA,KAAA8Y,mCAAA,mBAGQ,KAAAC,mCAAA,wBAAAC,iCAAA;QASZjc,EAAA,CAAAU,YAAA,EAAM;QAEVV,EAAA,CAAAC,cAAA,eAAyB;QACrBD,EAAA,CAAAiD,UAAA,KAAAiZ,0CAAA,2BAA+E,KAAAC,iCAAA;QA+EnFnc,EAAA,CAAAU,YAAA,EAAM;QAENV,EAAA,CAAAC,cAAA,eAAsC;QAClCD,EAAA,CAAAiD,UAAA,KAAAmZ,0CAAA,2BAAmE,KAAAC,iCAAA;QAmDnErc,EAAA,CAAAC,cAAA,eAAmC;QAC/BD,EAAA,CAAAiD,UAAA,KAAAqZ,0CAAA,2BAAuE,KAAAC,iCAAA;QAQvEvc,EAAA,CAAAC,cAAA,8BAG2F;QADvED,EAAA,CAAAE,UAAA,2BAAAsc,wEAAAxa,MAAA;UAAA,OAAiB4Z,GAAA,CAAA3E,YAAA,CAAAjV,MAAA,CAAoB;QAAA,EAAC,yBAAAya,sEAAAza,MAAA;UAAA,OAAA4Z,GAAA,CAAAvR,YAAA,GAAArI,MAAA;QAAA,uBAAA0a,kEAAA1a,MAAA;UAAA,OAC3B4Z,GAAA,CAAAtD,aAAA,CAAAtW,MAAA,CAAqB;QAAA,EADM,wBAAA2a,qEAAA3a,MAAA;UAAA,OACU4Z,GAAA,CAAApC,YAAA,CAAAxX,MAAA,CAAoB;QAAA,EAD9B;QAE1DhC,EAAA,CAAAU,YAAA,EAAqB;;;QArKVV,EAAA,CAAAY,SAAA,GAA4C;QAA5CZ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAiH,eAAA,KAAA2V,IAAA,EAAAhB,GAAA,CAAA3a,OAAA,CAAA+G,QAAA,EAA4C;QAIuChI,EAAA,CAAAY,SAAA,GAAkB;QAAlBZ,EAAA,CAAAa,UAAA,SAAA+a,GAAA,CAAA5U,YAAA,CAAkB;QAIlBhH,EAAA,CAAAY,SAAA,GAAuB;QAAvBZ,EAAA,CAAAa,UAAA,UAAA+a,GAAA,CAAA3a,OAAA,CAAA+G,QAAA,CAAuB;QAKxFhI,EAAA,CAAAY,SAAA,GAAsD;QAAtDZ,EAAA,CAAAa,UAAA,SAAA+a,GAAA,CAAAja,QAAA,CAAAC,MAAA,QAAAga,GAAA,CAAA/Z,cAAA,CAAAD,MAAA,KAAsD;QAMtC5B,EAAA,CAAAY,SAAA,GAAa;QAAbZ,EAAA,CAAAa,UAAA,SAAA+a,GAAA,CAAA1T,OAAA,CAAa;QACUlI,EAAA,CAAAY,SAAA,GAAc;QAAdZ,EAAA,CAAAa,UAAA,UAAA+a,GAAA,CAAA1T,OAAA,CAAc;QAiFrDlI,EAAA,CAAAY,SAAA,GAAiB;QAAjBZ,EAAA,CAAAa,UAAA,SAAA+a,GAAA,CAAAzT,WAAA,CAAiB;QACxBnI,EAAA,CAAAY,SAAA,GAA2C;QAA3CZ,EAAA,CAAAa,UAAA,UAAA+a,GAAA,CAAAzT,WAAA,KAAAyT,GAAA,CAAAxT,aAAA,kBAAAwT,GAAA,CAAAxT,aAAA,CAAAxG,MAAA,EAA2C;QAmDlD5B,EAAA,CAAAY,SAAA,GAAuC;QAAvCZ,EAAA,CAAAa,UAAA,SAAA+a,GAAA,CAAA1T,OAAA,MAAA0T,GAAA,CAAAxT,aAAA,kBAAAwT,GAAA,CAAAxT,aAAA,CAAAxG,MAAA,EAAuC;QACV5B,EAAA,CAAAY,SAAA,GAAwD;QAAxDZ,EAAA,CAAAa,UAAA,UAAA+a,GAAA,CAAA1T,OAAA,KAAA0T,GAAA,CAAAzT,WAAA,MAAAyT,GAAA,CAAAxT,aAAA,kBAAAwT,GAAA,CAAAxT,aAAA,CAAAxG,MAAA,EAAwD;QAOhF5B,EAAA,CAAAY,SAAA,GAA+D;QAA/DZ,EAAA,CAAAa,UAAA,YAAAb,EAAA,CAAAiH,eAAA,KAAA4V,IAAA,EAAAjB,GAAA,CAAAzT,WAAA,MAAAyT,GAAA,CAAAxT,aAAA,kBAAAwT,GAAA,CAAAxT,aAAA,CAAAxG,MAAA,GAA+D,kBAAAga,GAAA,CAAApa,aAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}