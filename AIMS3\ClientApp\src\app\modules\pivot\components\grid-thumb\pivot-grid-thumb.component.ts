import { Component, Input, OnInit } from '@angular/core';
import {
    CellFocusedEvent, ColDef, ColGroupDef,
    ColumnApi, ColumnState, GridApi, GridOptions, GridReadyEvent
} from 'ag-grid-community';
import 'ag-grid-enterprise';
import { LicenseManager } from 'ag-grid-enterprise';
import { environment } from '../../../../../environments/environment';
import { ActivityStatus, ColDataType, ColumnVarType, Region, TargetPeriod } from '../../../../shared/enums';
import { MessageService } from '../../../../shared/services/message.service';
import { SabaAttachmentRenderer } from '../../../aims-grid/controls/cell-renderers/attachment/attachment.control';
import { SabaCheckboxRenderer } from '../../../aims-grid/controls/cell-renderers/checkbox/checkbox.control';
import { SabaSelectValueRenderer } from '../../../aims-grid/controls/cell-renderers/select/select-value.control';
import { GridCustomHeader } from '../../../aims-grid/controls/grid-header/grid-header.control';
import { DynamicColumn } from '../../../aims-grid/models/dynamic-column.model';
import { GridService } from '../../../aims-grid/services/grid.service';
import { IProgressDataView, ITargetDataView } from '../../../data/models/data.model';
import { IPivot } from '../../models/pivot.model';

@Component({
    selector: 'aims-pivot-grid-thumb',
    templateUrl: './pivot-grid-thumb.component.html',
    styleUrls: ['./pivot-grid-thumb.component.scss']
})
export class PivotGridThumbnailComponent implements OnInit {
    working: boolean = false;
    public isDarkMode: boolean = false;

    public gridApi: GridApi;
    public gridColumnApi: ColumnApi;

    public gridOptions: GridOptions;
    public columnDefs: (ColDef | ColGroupDef)[];
    public defaultColDefs: ColDef;
    public components = {
        'sabaCheckboxRenderer': SabaCheckboxRenderer,
        'sabaAttachmentRenderer': SabaAttachmentRenderer,
        'sabaSelectValRenderer': SabaSelectValueRenderer
    };
    public tooltipShowDelay: number = 500;

    private gridColumns = {
        commonCols: [],
        progressActIdCol: null, // activity id
        progressStatusCols: [], // file, status, start, end months
        progressCommCol: null,  // community column
        progressAsOfCol: null,  // as of 
        targetInfoCols: [],     // year, qtr
    };

    // data from grid's parent
    private dynamicColumns: DynamicColumn[] = [];

    // *** DATA -------------
    @Input() rowData: (ITargetDataView | IProgressDataView)[] = [];
    @Input() pivot: IPivot;

    constructor(
        private gridService: GridService,
        private messageService: MessageService
    ) {
        if (document.querySelector('[data-theme="dark"'))
            this.isDarkMode = true;

        // AG-GRID
        LicenseManager.setLicenseKey(environment.agGridConfig.licenseKey);

        // set all defaults
        // default grid options
        this.gridOptions = {
            groupHeaderHeight: 25,
            headerHeight: 30,
            rowHeight: 20, rowDragManaged: true,
            rowSelection: 'multiple',
            enableRangeSelection: true, enableFillHandle: false,
            scrollbarWidth: 5,
            suppressRowClickSelection: true,
            groupDisplayType: 'groupRows',
            groupDefaultExpanded: -1,
            groupAllowUnbalanced: true,
            groupMaintainOrder: true,
            groupIncludeTotalFooter: true,
            pivotRowTotals: 'after',
            pivotColumnGroupTotals: 'after',
            pivotMode: true, suppressContextMenu: true,
            autoGroupColumnDef: { headerName: 'Group' },
            onColumnRowGroupChanged: this.onRowGroupChanged
        };

        // default column configurations
        this.defaultColDefs = {
            sortable: false,
            resizable: true,
            filter: true,
            editable: false,
            minWidth: 50,
            suppressFillHandle: true,
            suppressMenu: true,
            enablePivot: true,
            enableRowGroup: true,
            enableValue: true,
            comparator: this.gridService.getComparator
        };
    }

    // init the default readonly pivot grid
    ngOnInit() {
        try {
            // add default common column definitions
            this.gridColumns.progressStatusCols = [{   // file
                field: 'file', colId: 'file',
                headerName: 'File', headerClass: 'bg-header-color-1',
                headerTooltip: 'All files linked to an activity.',
                headerComponent: GridCustomHeader,
                headerComponentParams: { colType: ColDataType.Attachment, showInfoIcon: true },
                //cellRenderer: 'sabaAttachmentRenderer',
                enableRowGroup: false, enablePivot: false, enableValue: true,
                width: 80, //comparator: this.gridService.getNumComparator,
                sortable: false,
                suppressMenu: true, suppressMovable: true
            },
            {   // status
                colId: 'status',
                field: 'status',
                headerName: 'Status', headerClass: 'bg-header-color-1',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: true,
                    colType: ColDataType.SelectSingle
                },
                width: 110, suppressMovable: true,
                enableValue: false,
                valueGetter: (params) => ActivityStatus[params.data?.status || 0]
            },
            {  // sMonth: Not required
                colId: 'sMonth',
                field: 'sMonth',
                headerName: 'Start date', headerClass: 'bg-header-color-1',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: true,
                    colType: ColDataType.Date  // MMM/yyyy
                },
                enableValue: false,
                valueFormatter: this.gridService.getValueFormatter,
                //filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                lockPinned: true, comparator: this.gridService.getDateComparator,
                width: 130, suppressMovable: true
            },
            {   // eMonth: Not always required
                colId: 'eMonth',
                field: 'eMonth',
                headerName: 'End date', headerClass: 'bg-header-color-1',
                headerTooltip: 'The date when the implementation of the activity has been completed.',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: false,
                    colType: ColDataType.Date,  // MMM/yyyy
                    showInfoIcon: true
                },
                enableValue: false,
                valueFormatter: this.gridService.getValueFormatter,
                //filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                lockPinned: true, comparator: this.gridService.getDateComparator,
                width: 130, suppressMovable: true
            }];

            // location columns
            this.gridColumns.commonCols = [{  // region
                field: 'region',
                headerName: 'Region', headerClass: 'bg-header-color-2',
                headerComponent: GridCustomHeader,
                width: 130,
                enableValue: false,
                valueGetter: (params) => {
                    const regions = params.data?.region?.split(',') || [];
                    let result = [];
                    regions.forEach(r => result.push(Region[r]));
                    return result.join(',');
                },
                suppressMovable: true, columnGroupShow: 'open'
            },
            {   // province: always required
                field: 'province',
                headerName: 'Province', headerClass: 'bg-header-color-2',
                headerComponent: GridCustomHeader,
                width: 140, enableValue: true
            },
            {   // district: always required
                field: 'district',
                headerName: 'District', headerClass: 'bg-header-color-2',
                headerComponent: GridCustomHeader,
                width: 145, enableValue: false,
                suppressMovable: true
            }];

            // progress community column
            this.gridColumns.progressCommCol = {   // community
                field: 'community',
                headerName: 'Community', headerClass: 'bg-header-color-2',
                headerComponent: GridCustomHeader,
                width: 170, suppressMovable: true, enableValue: false
                //filterParams: { valueFormatter: this.gridService.getFilterValueFormatter }
            };

            // progress static columns
            this.gridColumns.progressAsOfCol = {  // as of
                colId: 'asOf',
                field: 'asOf',
                headerName: 'As of', headerClass: 'bg-header-color-4',
                headerTooltip: 'As of the end of this month.',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: true,
                    colType: ColDataType.Date,  // MMM/yyyy
                    showInfoIcon: true
                },
                enableValue: false,
                valueFormatter: this.gridService.getValueFormatter,
                //filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                comparator: this.gridService.getDateComparator,
                width: 110, suppressMovable: true
            };

            // target info static columns
            let years = [{ id: new Date().getFullYear(), name: new Date().getFullYear().toString() }];
            for (let i = 1; i <= 3; i++)
                years.push({ id: years[0].id + i, name: `${years[0].id + i}` });

            this.gridColumns.targetInfoCols = [{    // year
                colId: 'year',
                field: 'year',
                headerName: 'Year', headerClass: 'bg-header-color-3',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: true,
                    colType: ColDataType.SelectSingle,
                    showInfoIcon: false
                },
                cellEditorParams: {
                    values: [{ id: 0, name: 'All years' }, ...years]
                },
                cellRenderer: 'sabaSelectValRenderer',
                enableValue: false,
                valueFormatter: this.gridService.getValueFormatter,
                //filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                comparator: this.gridService.getNumComparator,
                width: 95, suppressMovable: true
            },
            {   // quarter
                coldId: 'qtr',
                field: 'qtr',
                headerName: 'Qtr', headerClass: 'bg-header-color-3',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: true,
                    colType: ColDataType.SelectSingle,
                    showInfoIcon: false
                },
                enableValue: false,
                valueGetter: (params) => TargetPeriod[params.data?.qtr || 1],
                lockPinned: true,
                width: 90, suppressMovable: true
            }];
        } catch (ex) {
            console.error(ex);
            this.messageService.error('Something went wrong.');
        }
    }

    // init as per profile and project selected
    initGrid(): void {
        try {
            // by reaching this, the gridColumns are already asssigned to by the parent
            this.working = true;

            this.columnDefs = [];
            this.gridApi.setColumnDefs(this.columnDefs);

            // create columns
            if (!this.pivot.isTarget) {
                //this.columnDefs = this.columnDefs.concat(this.gridColumns.progressActIdCol);

                // status group and columns
                this.columnDefs.push({
                    groupId: 'ProgressStatus',
                    headerName: 'Status', headerClass: 'bg-header-group-color-1',
                    suppressMovable: true,
                    //marryChildren: true,
                    openByDefault: true,
                    children: [...this.gridColumns.progressStatusCols]
                });

                // location cols
                this.columnDefs.push({
                    groupId: `${ColumnVarType.ProgressStatic}`,
                    headerName: 'Location', headerClass: 'bg-header-group-color-2',
                    suppressMovable: true,
                    //marryChildren: true,
                    openByDefault: true,
                    children: [...this.gridColumns.commonCols, this.gridColumns.progressCommCol]
                });

                // add info and dynamic cols
                let infoCols = [];
                let progCols = [];

                this.dynamicColumns = this.pivot.prof.variables
                    .filter(v => [2, 4, 5].includes(v.type)); // ColumnVarType.Progress, Info, Static;

                this.dynamicColumns.forEach(col => {
                    // update static cols props
                    if (col.type === ColumnVarType.ProgressStatic) {
                        let staticCol = this.gridColumns.commonCols.concat(this.gridColumns.progressCommCol)
                            .find(sc => sc.field === col.name.toLowerCase());
                        if (staticCol) {
                            if (staticCol.field === 'community')
                                this.gridColumns.progressCommCol.colId = col.id;
                            staticCol.colId = col.id;
                            staticCol.headerName = col.displayName || col.name;
                            staticCol.headerTooltip = col.description;
                            staticCol.headerComponentParams = {
                                enableEdit: false,
                                isRequired: col.isRequired,
                                colType: col.fieldType,
                                showInfoIcon: col.description?.length > 0
                            }
                        }
                        return;
                    }

                    // add dynamic column
                    let colDef: ColDef = {
                        colId: `${col.id}`,
                        field: 'colVals.dCol' + col.id,
                        headerName: col.displayName || col.name,
                        headerTooltip: col.description,
                        headerComponent: GridCustomHeader,
                        headerComponentParams: {
                            enableEdit: false,
                            isRequired: col.isRequired,
                            type: col.type,
                            colType: col.fieldType,
                            colOrder: col.order,
                            showInfoIcon: col.description?.length > 0,
                            pivot: true
                        },
                        cellEditorParams: this.gridService.getCellEditorParams(col),
                        valueFormatter: this.gridService.getValueFormatter,
                        cellRenderer: this.gridService.getCellRenderer(col.fieldType),
                        cellClassRules: {
                            'text-end': this.gridService.getCellTextAlign, //'text-danger': this.gridService.getCellClassRules
                        },
                        enableRowGroup: this.gridService.getEnableGrouping(col.fieldType),
                        enableValue: this.gridService.getEnableGrouping(col.fieldType),
                        lockPinned: true,
                        //width: getColWidth(col.name + (col.isRequired ? '*':''), [false, col.description?.length > 0])
                    };

                    // attach number comparator for attachment
                    /*if (col.fieldType === ColDataType.Attachment)
                        colDef.comparator = this.gridService.getNumComparator;*/

                    // attach num and date comparators
                    if (col.fieldType >= ColDataType.Number && col.fieldType <= ColDataType.GPS) {
                        //colDef.comparator = this.gridService.getNumComparator;
                        colDef.valueGetter = (params) => this.gridService.getNumericValue(col.id, params);
                        colDef.valueParser = (params) => parseFloat(params.newValue);
                    }

                    /*
                    if (col.fieldType === ColDataType.Date) {
                        colDef.filterParams = { valueFormatter: this.gridService.getFilterValueFormatter };
                        colDef.comparator = this.gridService.getDateComparator;
                    }*/

                    if (col.type === ColumnVarType.ProgressInfo) {
                        colDef.headerClass = 'bg-header-color-3';
                        infoCols.push(colDef);
                    } else if (col.type === ColumnVarType.Progress) {
                        colDef.headerClass = 'bg-header-color-4';
                        progCols.push(colDef);
                    }
                });

                // sort dynamic cols by order and push to grid's columns
                //infoCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);
                //progCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);

                this.columnDefs.push({
                    groupId: `${ColumnVarType.ProgressInfo}`,
                    headerName: 'Info', headerClass: 'bg-header-group-color-3',
                    //marryChildren: true,
                    openByDefault: true,
                    children: [...infoCols]
                });

                this.columnDefs.push({
                    groupId: `${ColumnVarType.Progress}`,
                    headerName: 'Cumulative progress', headerClass: 'bg-header-group-color-4',
                    //marryChildren: true,
                    openByDefault: true,
                    children: [this.gridColumns.progressAsOfCol, ...progCols]
                });
                // ---------------------------------------------------------------
            } else {      // target
                // ---------------------------------------------------------------
                // location cols
                this.columnDefs.push({
                    groupId: `${ColumnVarType.TargetStatic}`,
                    headerName: 'Location', headerClass: 'bg-header-group-color-2',
                    suppressMovable: true,
                    //marryChildren: true,
                    openByDefault: true,
                    children: [...this.gridColumns.commonCols]
                });

                // add info and dynamic cols for target
                let infoCols = [];
                let targetCols = [];

                this.dynamicColumns = this.pivot.prof.variables
                    .filter(v => [0, 1, 3].includes(v.type)); // ColumnVarType.Target, Info, Static

                this.dynamicColumns.forEach(col => {
                    // update static cols props
                    if (col.type === ColumnVarType.TargetStatic) {
                        let staticCol = this.gridColumns.commonCols.find(sc => sc.field === col.name.toLowerCase());
                        if (staticCol) {
                            staticCol.colId = col.id;
                            staticCol.headerName = col.displayName || col.name;
                            staticCol.headerTooltip = col.description;
                            staticCol.headerComponentParams = {
                                enableEdit: false,
                                isRequired: col.isRequired,
                                colType: col.fieldType,
                                showInfoIcon: col.description?.length > 0
                            }
                        }
                        return;
                    }

                    // add dynamic column
                    let colDef: ColDef = {
                        colId: `${col.id}`,
                        field: 'colVals.dCol' + col.id,
                        headerName: col.displayName || col.name,
                        headerTooltip: col.description,
                        headerComponent: GridCustomHeader,
                        headerComponentParams: {
                            enableEdit: false,
                            isRequired: col.isRequired,
                            type: col.type,
                            colType: col.fieldType,
                            colOrder: col.order,
                            showInfoIcon: col.description?.length > 0,
                            pivot: true
                        },
                        cellEditorParams: this.gridService.getCellEditorParams(col),
                        valueFormatter: this.gridService.getValueFormatter,
                        cellRenderer: this.gridService.getCellRenderer(col.fieldType),
                        cellClassRules: { 'text-end': this.gridService.getCellTextAlign },
                        enableRowGroup: this.gridService.getEnableGrouping(col.fieldType),
                        enableValue: this.gridService.getEnableGrouping(col.fieldType),
                        //width: getColWidth(col.name + (col.isRequired ? '*' : ''), [false, col.description?.length > 0]),
                        lockPinned: true
                    };

                    // attach comparator for attachment columns
                    /*if (col.fieldType === ColDataType.Attachment)
                        colDef.comparator = this.gridService.getNumComparator;*/

                    // attach num and date comparators
                    if (col.fieldType >= ColDataType.Number && col.fieldType <= ColDataType.GPS) {
                        //colDef.comparator = this.gridService.getNumComparator;
                        colDef.valueGetter = (params) => this.gridService.getNumericValue(col.id, params);
                        colDef.valueParser = (params) => parseFloat(params.newValue);
                    }

                    /*if (col.fieldType === ColDataType.Date) {
                        colDef.filterParams = { valueFormatter: this.gridService.getFilterValueFormatter };
                        colDef.comparator = this.gridService.getDateComparator;
                    }*/

                    if (col.type === ColumnVarType.TargetInfo) {
                        colDef.headerClass = 'bg-header-color-3';
                        infoCols.push(colDef);
                    } else if (col.type === ColumnVarType.Target) {
                        colDef.headerClass = 'bg-header-color-4';
                        targetCols.push(colDef);
                    }
                });

                // sort dynamic cols by order and push to grid's columns
                //infoCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);
                //targetCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);

                // make first col always visible in the group
                if (targetCols.length)
                    targetCols[0].columnGroupShow = null;

                this.columnDefs.push({
                    groupId: `${ColumnVarType.TargetInfo}`,
                    headerName: 'Info', headerClass: 'bg-header-group-color-3',
                    //marryChildren: true,
                    openByDefault: true,
                    children: [...this.gridColumns.targetInfoCols, ...infoCols]
                });

                this.columnDefs.push({
                    groupId: `${ColumnVarType.Target}`,
                    headerName: 'Target', headerClass: 'bg-header-group-color-3',
                    //marryChildren: true,
                    openByDefault: true,
                    children: [...targetCols]
                });
            }

            setTimeout(() => this.gridReady(), 100);
            
            this.working = false;
        } catch (ex) {
            console.error(ex);
            this.messageService.error('Something went wrong.');
        } 
    }

    // init'ed and ready
    onGridReady(params: GridReadyEvent) {
        this.gridApi = params.api;
        this.gridColumnApi = params.columnApi;
        this.initGrid();
    }

    // *** Other grid's functions --------------------------------
    onCellFocused(event: CellFocusedEvent): void {
        this.gridApi.deselectAll();
    }

    private gridReady(): void {
        this.refreshGridRows(this.rowData);

        // set pivot columns state
        if (this.pivot.pivotColumns) {
            const colStates = JSON.parse(this.pivot.pivotColumns) as ColumnState[];
            this.gridColumnApi.applyColumnState({
                state: colStates,
                applyOrder: true
            });
        }

        // apply filters
        if (this.pivot.filtersApplied) {
            this.gridApi.setFilterModel(
                JSON.parse(this.pivot.filtersApplied)
            );
        }

        // change group column name
        this.onRowGroupChanged();

        // autosize cols
        this.gridColumnApi.autoSizeAllColumns();
    }

    onRowGroupChanged() {
        if (!this.gridColumnApi)
            return;

        const rowGroupColumns = this.gridColumnApi.getRowGroupColumns();

        if (rowGroupColumns.length > 0) {
            const firstRowGroupColumn = rowGroupColumns[0];

            this.gridOptions.autoGroupColumnDef.headerName =
                firstRowGroupColumn.getColDef().headerName;

            this.gridApi.setAutoGroupColumnDef(this.gridOptions.autoGroupColumnDef);
            this.gridApi.refreshHeader();
        }
    }

    // *** DATA
    refreshGridRows(data: IProgressDataView[] | ITargetDataView[]): void {
        this.gridApi.setDefaultColDef(this.defaultColDefs);
        this.rowData = [...data];
    }
}