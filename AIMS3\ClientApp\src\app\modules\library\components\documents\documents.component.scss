.dropdown-toggle:after {
    display: none;
}
.dropdown-menu {
    a {
        cursor: pointer;
    }
}
td {
  a {
    cursor: pointer;

    &:hover {
      color: var(--qs-primary);
    }
  }
}
tr > .file-name button {
    display: none;
}
tr:hover > .file-name button {
  display: initial;
}
.col-red {
    i {
      color: var(--qs-danger);
    }
    &:hover {
        background-color: var(--qs-danger);
    }
}
.col-blue {
  i {
    color: darkblue;
  }
  &:hover {
    background-color: darkblue;
  }
}
.col-yellow {
  color: darkgoldenrod;

  &:hover {
    background-color: darkgoldenrod;
  }
}
.col-green-haze {
  color: #44B6AE;

  &:hover {
    background-color: #44B6AE;
  }
}

.col-red-haze {
  color: #F36A5A;

  &:hover {
    background-color: #F36A5A;
  }
}

.col-purple-sharp {
  color: #796799;

  &:hover {
    background-color: #796799;
  }
}

.col-red:hover, .col-blue:hover, .col-yellow:hover,
.col-green-haze:hover, .col-red-haze:hover,
.col-purple-sharp:hover {
    i {
        color: #fff;
    }
}

input[type="file"] {
    padding-right: 0;
}
input[type="file"]::-webkit-file-upload-button {
  float: right;
  margin-right: 0;
  border-right: 0;
  border-left: 1px solid var(--qs-input-border-color);
}

.progress {
  height: 12px;
  --bs-progress-bar-color: var(--qs-text-gray-100);
  --bs-progress-bar-bg: var(--qs-primary);
}
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.modal.show {
  opacity: 1;
}

.modal-backdrop.show {
  opacity: 0.5;
}
