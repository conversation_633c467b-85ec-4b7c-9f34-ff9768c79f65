import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { Project } from '../../admin/models/project.model';
import { Inventory, InventoryList, InvFilter } from '../models/inventory.model';

const API_URL = `${environment.apiUrl}/inventory`;

@Injectable({ providedIn: 'root' })
export class InventoryService {
    constructor(private http: HttpClient) { }

    getInventoryRecords(filters: InvFilter): Observable<InventoryList[]> {
        return this.http.post<InventoryList[]>(`${API_URL}/get-filtered`, filters);
    }

    getProjectsList(): Observable<Project[]> {
        return this.http.get<Project[]>(`${environment.apiUrl}/projects/inventory`);
    }

    addInventory(inv: Inventory): Observable<void> {
        return this.http.post<void>(API_URL, inv);
    }

    updateInventory(inv: Inventory): Observable<void> {
        return this.http.put<void>(API_URL, inv);
    }

    changeStatus(invId: number): Observable<void> {
        return this.http.put<void>(`${API_URL}/${invId}/status`, invId);
    }

    deleteInventoryRecord(invId: number): Observable<void> {
        return this.http.delete<void>(`${API_URL}/${invId}`);
    }
}