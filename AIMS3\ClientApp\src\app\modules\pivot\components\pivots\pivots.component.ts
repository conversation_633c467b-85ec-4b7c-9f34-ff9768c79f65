import { AfterViewInit, ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { Subscription } from 'rxjs';
import { MessageService } from '../../../../shared/services/message.service';
import { AppUtilities, compareSort } from '../../../../shared/utilities';
import { MenuComponent } from '../../../../_theme/core/components';
import { IProjectIntervention } from '../../../admin/models/project.model';
import { AuthService } from '../../../auth';
import { IProgressDataView, ITargetDataView } from '../../../data/models/data.model';
import { ViewDataService } from '../../../data/services/view-data.service';
import { IPivot } from '../../models/pivot.model';
import { PivotService } from '../../services/pivot.service';
import { PivotModalComponent } from '../pivot-modal/pivot-modal.component';

@Component({
    selector: 'aims-pivots',
    templateUrl: 'pivots.component.html',
    styleUrls: ['pivots.component.scss']
})
export class PivotsComponent implements OnInit, AfterViewInit, OnDestroy {
    working: boolean = false;
    isAdmin: boolean = false;

    @Input() interventions: any[] = [];
    selProfId: number = 0;
    selProjId: number = 0;

    @Output() initd = new EventEmitter<PivotsComponent>();

    pivots: IPivot[] = [];

    @Input() orgs: any[] = [];
    @Input() projects: IProjectIntervention[] = [];

    @ViewChild(PivotModalComponent, { static: true })
    private pivotModalComponent: PivotModalComponent;

    public rowData: (ITargetDataView | IProgressDataView)[] = [];

    private subscriptions: Subscription[] = [];
    constructor(
        private authService: AuthService,
        private pivotService: PivotService,
        private dataService: ViewDataService,
        private messageService: MessageService,
        private cdr: ChangeDetectorRef
    ) {
        this.isAdmin = this.authService.currentUserValue.roles.includes('Admin');
    }

    ngOnInit() {
        this.pivotModalComponent.isAdmin = this.isAdmin;
        this.pivotModalComponent.projects = this.projects;

        // this is already called when main component triggers profiles dropdown
        // this.getPivotTables();
    }

    ngAfterViewInit() {
        this.initd.emit(this);

        AppUtilities().initSelect2('#profiles');
        $('#profiles').on('change', (e) => {
            const selVal = $(e.target).val() || 0;
            this.selProfId = +selVal;

            if (this.selProfId > 0)
                this.getData();
            else
                this.getPivotTables();
        });
    }

    onProfileSelect(profId: number): void {
        setTimeout(() => {
            $('#profiles').val(profId).trigger('change');
        }, 50);
    }

    getPivotTables(): void {
        this.working = true;

        this.subscriptions.push(
            this.pivotService.getPivotTables(this.selProfId).subscribe({
                next: (pivots: IPivot[]) => {
                    this.pivots = pivots;
                },
                error: (e) => {
                    console.log(e);
                    this.working = false;
                }, complete: () => {
                    this.pivots.forEach(pivot => {
                        this.interventions.forEach(ip => {
                            const prof = ip.profiles.find(p => p.id === pivot.profId);
                            if (prof) {
                                pivot.prof = prof;
                                return;
                            }
                        });
                    });

                    this.cdr.detectChanges();
                    if (this.pivots.length)
                        MenuComponent.reinitialization();

                    this.pivotElems = document.querySelectorAll('.pivot-card');
                    this.pivotElems.forEach((pivot: HTMLElement) => {
                        // attach drang-n-drop handlers
                        pivot.addEventListener('dragstart', (e) => this.handleDragStart(pivot, e), false);
                        pivot.addEventListener('dragenter', (e) => this.handleDragEnter(pivot), false);
                        pivot.addEventListener('dragover', (e) => this.handleDragOver(pivot, e), false);
                        pivot.addEventListener('dragleave', (e) => this.handleDragLeave(pivot), false);
                        pivot.addEventListener('drop', (e) => this.handleDrop(pivot, e), false);
                        pivot.addEventListener('dragend', (e) => this.handleDragEnd(pivot, e), false);
                    });

                    this.working = false;
                }
            })
        );
    }

    private getData(): void {
        this.working = true;

        this.subscriptions.push(
            this.dataService.getInterventionData(this.selProfId, {
                isTarget: false,
                approvalMode: false,
                projIds: this.selProjId > 0 ? [this.selProjId] : []
            }).subscribe({
                next: (res) => {
                    this.rowData = [...res];
                },
                error: (err) => {
                    this.working = false;
                    console.log(err);
                },
                complete: () => {
                    this.getPivotTables();
                    this.working = false;
                }
            }));
    }

    onAddEdit(pivot?: IPivot): void {
        if (!pivot) {
            pivot = {
                id: 0,
                name: 'New pivot',
                profId: this.selProfId,
                isTarget: false,
                order: this.pivots.length + 1
            };

            this.interventions.forEach(i => {
                const prof = i.profiles.find(p => p.id === this.selProfId);
                if (prof) {
                    pivot.prof = prof;
                    return;
                }
            });
        }

        if (!this.pivotModalComponent.orgs.length)
            this.pivotModalComponent.orgs = this.orgs;

        if (this.selProjId)
            this.pivotModalComponent.dataFilters.projIds = [this.selProjId];
        else
            this.pivotModalComponent.dataFilters.projIds = [];
        this.pivotModalComponent.ngOnInit(pivot);
    }

    onView(): void {
        let pivot: IPivot = {
            id: 0,
            name: 'Playground',
            profId: this.selProfId,
            isTarget: false,
            order: 1
        };

        this.interventions.forEach(i => {
            const prof = i.profiles.find(p => p.id === this.selProfId);
            if (prof) {
                pivot.prof = prof;
                return;
            }
        });

        if (this.selProjId)
            this.pivotModalComponent.dataFilters.projIds = [this.selProjId];
        else
            this.pivotModalComponent.dataFilters.projIds = [];
        this.pivotModalComponent.ngOnInit(pivot);
    }

    onDelete(pivotId: number, name: string): void {
        name = `<span class="text-primary">${name}</span>`;

        this.messageService.confirmMessage('Confirm Delete',
            `Are you sure you want to delete the pivot: '${name}'?`,
            () => {
                this.working = true;

                this.subscriptions.push(
                    this.pivotService.deletePivot(pivotId).subscribe({
                        next: () => {
                            this.pivots = this.pivots.filter(p => p.id !== pivotId);
                        },
                        error: (err) => {
                            this.working = false;
                            console.error(err);
                        },
                        complete: () => {
                            this.messageService.success('The pivot table has been deleted successfully.');
                            this.working = false;
                        }
                    })
                );
            }, true, 'Delete');
    }

    /** Pivots drag-n-drop and ordering */
    private pivotElems = document.querySelectorAll('.pivot-card');
    private dragSrcEl: HTMLElement;
    private handleDragStart(elem: HTMLElement, event): void {
        elem.style.opacity = '0.4';

        this.dragSrcEl = elem;

        event.dataTransfer.effectAllowed = 'move';
        event.dataTransfer.setData('pivotId', elem.dataset.pivotId);
    }

    private handleDragOver(elem: HTMLElement, event: DragEvent): boolean {
        if (event.preventDefault)
            event.preventDefault();

        elem.classList.add('over');
        event.dataTransfer.dropEffect = 'move';

        return false;
    }

    private handleDragEnter(elem: HTMLElement): void {
        elem.classList.add('over');
    }

    private handleDragLeave(elem: HTMLElement): void {
        elem.classList.remove('over');
    }

    private handleDrop(elem: HTMLElement, event: DragEvent): boolean {
        if (event.stopPropagation)
            event.stopPropagation();

        if (this.dragSrcEl !== elem) {
            const draggedPivotId = +event.dataTransfer.getData('pivotId');
            const draggedPivot = this.pivots.find(p => p.id === draggedPivotId);
            draggedPivot.order = draggedPivot.order > +elem.dataset.order
                ? +elem.dataset.order - 1
                : (+elem.dataset.order + 1);

            this.pivots.sort((x, y) => compareSort(x.order, y.order));
            this.pivots.forEach((p, i) => { p.order = i + 1; });
            this.savePivotsOrder();
        }

        return false;
    }

    private handleDragEnd(elem: HTMLElement, event: DragEvent): void {
        elem.style.opacity = '1';

        this.pivotElems.forEach(pivot => {
            pivot.classList.remove('over');
        });
    }

    private savePivotsOrder(): void {
        const pivotIds = this.pivots.map(p => p.id);

        this.subscriptions.push(
            this.pivotService.savePivotsOrder(pivotIds).subscribe({
                error: (err) => {
                    this.messageService.error('Pivots order could not save successfully.');
                    console.error(err);
                }
            })
        );
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}