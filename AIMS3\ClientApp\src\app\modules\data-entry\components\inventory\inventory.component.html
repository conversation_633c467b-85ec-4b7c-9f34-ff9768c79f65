<aims-loading *ngIf="working"></aims-loading>
<div class="card card-custom card-stretch" [ngClass]="{'d-none':working}">
    <!-- begin::Header -->
    <div class="card-header">
        <div class="card-title" style="max-width:80%;">
            <!-- Filters -->
            <filter-ddl [id]="'output'" class="filter-container w-100px" [placeholders]="['Output']" [minWidth]="100"
                        [options]="outputs" (change)="onFilterChange($event)" [multiple]="true" #output>
            </filter-ddl>
            <filter-ddl [id]="'category'" class="filter-container w-120px ms-2" [placeholders]="['Category','Categories']" [minWidth]="130"
                        [options]="categories" (change)="onFilterChange($event)" [multiple]="true" #category>
            </filter-ddl>
            <filter-ddl [id]="'profile'" class="filter-container w-140px ms-2" [placeholders]="['Intervention']" [minWidth]="140"
                        [options]="profiles" (change)="onFilterChange($event)" [multiple]="true" #profile>
            </filter-ddl>
            <filter-ddl [id]="'project'" class="filter-container w-130px ms-2" [placeholders]="['Project']" [minWidth]="130"
                        [options]="projects" (change)="onFilterChange($event)" [multiple]="true" #project>
            </filter-ddl>
            <filter-ddl [id]="'partner'" class="filter-container w-130px ms-2" [placeholders]="['Partner']" [minWidth]="130"
                        [options]="orgs" (change)="onFilterChange($event)" [multiple]="true" #partner *ngIf="(['ga'] | isAuth)">
            </filter-ddl>
            <filter-ddl [id]="'status'" class="filter-container w-100px ms-2" [placeholders]="['Status']" [minWidth]="130"
                        [options]="statuses" [showSearch]="false" #status
                        (change)="onFilterChange($event)">
            </filter-ddl>

            <button role="button" class="btn btn-sm btn-light-primary border border-primary py-2 px-3 ms-2" (click)="getInventories()">Apply filter</button>
            <div class="fs-7 ms-3" *ngIf="lblFiltered" style="max-width:20%;">
                <span class="fs-9 text-muted text-uppercase">Filtered: </span>
                <a class="fs-9 cursor-pointer" (click)="resetFilters()">Reset</a><br />
                <span class="text-primary">{{ lblFiltered }}</span>
            </div>
        </div>
        <div class="card-toolbar">
            <!-- Add New -->
            <button type="button" class="btn btn-sm btn-primary px-4 py-2 ms-3" (click)="addEditInventory()" *ngIf="(['admin'] | isAuth)">
                <span [inlineSVG]="'./assets/media/icons/duotune/arrows/arr075.svg'" class="svg-icon svg-icon-2"></span>
                Add new
            </button>
            <!-- Download -->
            <button class="btn btn-sm btn-bg-light btn-active-color-primary ms-2" (click)="onDownloadTable()">
                <i class="fas fa-download"></i> Download
            </button>
        </div>
    </div>
    <!-- end::Header -->
    <!-- begin::Body -->
    <div class="card-body">
        <div class="table-responsive">
            <table id="table_inventory" class="table table-sm table-hover fs-8 table-row-dashed align-middle">
                <thead>
                    <tr class="text-start text-gray-900 fw-bold gs-0">
                        <th data-orderable="false"></th>
                        <th class="d-none"></th>
                        <th>Intervention profile</th>
                        <th data-orderable="false">
                            Overview<br />
                            <a [routerLink]="'/data-entry/intervention-profiles'" target="_blank">
                                Variable explanation &nbsp;
                                <i class="fas fa-external-link text-muted ms-2 fs-8" ngbTooltip="Opens in a new tab."></i>
                            </a>
                        </th>
                        <th>Project</th>
                        <th [ngClass]="{'d-none': !(['ga','gv'] | isAuth)}">IP</th>
                        <th data-orderable="false">
                            Description
                            <i class="la la-info-circle icon-md text-muted ms-2" ngbTooltip="Based on BoQ"></i>
                        </th>
                        <th>Status</th>
                        <th class="text-end" data-orderable="false"></th>
                    </tr>
                </thead>
                <tbody class="text-gray-800">
                    <tr *ngFor="let item of inventories; let ind = index;">
                        <td class="text-center"></td>
                        <td class="d-none">{{ ind+1 }}</td>
                        <td [attr.data-content-id]="item.profile.id">
                            <p class="text-muted fs-8 m-0">Output: {{ item.profile.category.output }}</p>
                            <p class="text-muted fs-8 m-0">Category: {{ item.profile.category.code }} {{ item.profile.category.name }}</p>
                            <p class="text-gray-900 m-0">{{ item.profile.name }} ({{ item.profile.abbreviation }})</p>
                        </td>
                        <td [attr.data-content-id]="item.profile.id">
                            <p>{{ item.profile.description }}</p>
                            <span class="badge badge-light-primary fw-semibold fs-9 me-1 mb-1" *ngFor="let v of item.profile.variables"
                                    [ngbTooltip]="v.displayName">{{ v.name }}</span>
                        </td>
                        <td [attr.data-content-id]="item.project.id">{{ item.project.name }}</td>
                        <td [title]="item.organization.fullName" [ngClass]="{'d-none': !(['ga','gv'] | isAuth)}">{{ item.organization.shortName }}</td>
                        <td [innerHtml]="item.details | nlHtml"></td>
                        <td class="fw-semibold">
                            <span class="badge badge-light-success" *ngIf="item.isActive else statusReq">Activated</span>
                            <ng-template #statusReq>
                                <span class="badge badge-light-primary">Requested</span>
                            </ng-template>
                        </td>
                        <td class="text-end fs-6">
                            <ng-container *ngIf="(['admin'] | isAuth)">
                                <button class="btn btn-sm btn-icon w-30px h-30px btn-bg-light btn-active-color-primary ms-2" ngbTooltip="Actions"
                                        data-qs-menu-trigger="click" data-qs-menu-placement="bottom-end" data-qs-menu-flip="top-end">
                                    <i class="bi bi-three-dots fs-5"></i>
                                </button>
                                <!-- Dropdown menu -->
                                <div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown py-lg-1 w-lg-225px" data-qs-menu="true">
                                    <div class="menu-item menu-lg-down-accordion">
                                        <a class="menu-link py-3" (click)="addEditInventory(item)">
                                            <span class="menu-icon">
                                                <span [inlineSVG]="'./assets/media/icons/duotune/general/gen055.svg'" class="svg-icon svg-icon-3"></span>
                                            </span><span class="menu-title">Edit</span>
                                        </a>
                                    </div>
                                    <div class="menu-item menu-lg-down-accordion" *ngIf="!item.isActive else voidApproval">
                                        <a class="menu-link py-3" (click)="toggleActiveStatus(item.id, item.isActive)">
                                            <span class="menu-icon">
                                                <span [inlineSVG]="'./assets/media/icons/duotune/general/gen026.svg'" class="svg-icon svg-icon-3"></span>
                                            </span><span class="menu-title">Approve</span>
                                        </a>
                                    </div>
                                    <ng-template #voidApproval>
                                        <div class="menu-item menu-lg-down-accordion">
                                            <a class="menu-link py-3" (click)="toggleActiveStatus(item.id, item.isActive)">
                                                <span class="menu-icon">
                                                    <span [inlineSVG]="'./assets/media/icons/duotune/general/gen026.svg'" class="svg-icon svg-icon-3 text-muted"></span>
                                                </span><span class="menu-title">Deactivate</span>
                                            </a>
                                        </div>
                                    </ng-template>
                                    <div class="menu-item separator"></div>
                                    <div class="menu-item menu-lg-down-accordion">
                                        <a class="menu-link py-3" (click)="deleteInventory(item.id)">
                                            <span class="menu-icon">
                                                <span [inlineSVG]="'./assets/media/icons/duotune/general/trash.svg'" class="svg-icon svg-icon-3 text-danger"></span>
                                            </span><span class="menu-title text-danger">Delete</span>
                                        </a>
                                    </div>
                                </div>
                            </ng-container>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<inventory-form-modal #invForm [orgs]="orgs" [interventions]="interventions" (done)="getInventories()"></inventory-form-modal>