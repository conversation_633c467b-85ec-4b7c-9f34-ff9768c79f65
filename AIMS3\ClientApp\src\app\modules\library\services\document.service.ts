import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { InvFilter } from '../../data-entry/models/inventory.model';
import { DocType } from '../models/doc-type.model';
import { Doc, FieldValue, IDoc } from '../models/document.model';

const API_URL = `${environment.apiUrl}/documents`;

@Injectable({ providedIn: 'root' })
export class DocumentService {
    constructor(private http: HttpClient) { }

    getAllDocuments(docTypeId?: number, all: boolean = false): Observable<IDoc[]> {
        if (docTypeId)
            return this.http.get<IDoc[]>(`${API_URL}/type/${docTypeId}`);
        if (all)
            return this.http.get<IDoc[]>(`${API_URL}/all`);

        return this.http.get<IDoc[]>(API_URL);
    }

    getDocumentsFiltered(filters: InvFilter, docTypeId?: number, all: boolean = false): Observable<IDoc[]> {
        if (docTypeId)
            return this.http.post<IDoc[]>(`${API_URL}/type/${docTypeId}/filtered`, filters);
        if (all)
            return this.http.post<IDoc[]>(`${API_URL}/all/filtered`, filters);

        return this.http.post<IDoc[]>(`${API_URL}/filtered`, filters);
    }

    getDocuments(progTargetId: number, isTarget: boolean, colId?: number, docTypeId?: number): Observable<IDoc[]> {
        const docType = docTypeId > 0 ? '/doc-type/' + docTypeId : '';

        if(colId > 0)
            return this.http.get<IDoc[]>(`${API_URL}${docType}/${progTargetId}/${isTarget}/${colId}`);
        else
            return this.http.get<IDoc[]>(`${API_URL}${docType}/${progTargetId}/${isTarget}`);
    }

    getDocument(docId: number): Observable<IDoc> {
        return this.http.get<IDoc>(`${API_URL}/file/${docId}`);
    }

    addLinkedDocument(doc: Doc): Observable<void> {
        return this.http.post<void>(`${API_URL}/link`, doc);
    }

    updateDocument(doc: Doc): Observable<void> {
        return this.http.put<void>(API_URL, doc);
    }

    deleteDocument(docId: number, activityId?: number): Observable<void> {
        if (activityId > 0)
            return this.http.delete<void>(`${API_URL}/${docId}/activities/${activityId}`);
        else
            return this.http.delete<void>(`${API_URL}/${docId}`);
    }

    changeStatus(docId: number): Observable<void> {
        return this.http.put<void>(`${API_URL}/${docId}/status`, docId);
    }

    // Document Types
    getDocumentTypes(library?: boolean): Observable<DocType[]> {
        if(library)
            return this.http.get<DocType[]>(`${API_URL}/doc-types/library`);

        return this.http.get<DocType[]>(`${API_URL}/doc-types`);
    }

    // Document activities
    getActivities(): Observable<FieldValue[]> {
        return this.http.get<FieldValue[]>(`${API_URL}/activities`);
    }
}