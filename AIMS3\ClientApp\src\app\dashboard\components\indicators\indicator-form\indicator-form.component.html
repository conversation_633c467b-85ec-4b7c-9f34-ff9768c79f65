<app-modal #modal [modalConfig]="modalConfig" *ngIf="(['admin'] | isAuth)">
    <form id="catergoryForm" class="form" [formGroup]="form">
        <div class="row">
            <div class="form-group col-md-6">
                <label class="required">Project group</label>
                <filter-ddl [id]="'projGroups'" #groups class="filter-container" [placeholders]="['Project group']" [minWidth]="130"
                            [options]="projGroups" (change)="onFilterChange($event)" [multiple]="true" [showAll]="false">
                </filter-ddl>
                <div class="fv-plugins-message-container" *ngIf="f.groups.dirty && f.groups.invalid">
                    <div class="fv-help-block">
                        <span role="alert">Please select a project group.</span>
                    </div>
                </div>
            </div>
            <div class="form-group col-md-6">
                <label class="required">Type</label>
                <i class="la la-info-circle icon-md text-muted ms-2" ngbTooltip="Write down a new Indicator type and press enter; or, select from the existing ones."></i>
                <select id="type" class="form-select form-select-sm" data-control="select2" data-tags="true" data-max-selection="1" multiple
                        data-placeholder="Select or enter new">
                    <option *ngFor="let typ of types" [value]="typ.id">{{ typ.name }}</option>
                </select>
                <div class="fv-plugins-message-container" *ngIf="f.type.dirty && f.type.invalid">
                    <div class="fv-help-block">
                        <span role="alert">Please select an indicator type.</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mb-8">
            <div class="col-md-2">
                <label class="form-control-label mt-2">Type data filters</label>
            </div>
            <div class="col-md-2">
                <filter-ddl [id]="'ind_projIds'" #indProjects class="filter-container" [placeholders]="['Project']" [minWidth]="130"
                            [options]="projects" (change)="onFilterChange($event)" [multiple]="true">
                </filter-ddl>
            </div>
            <div class="col-md-2">
                <filter-ddl [id]="'ind_orgIds'" #indPartners class="filter-container" [placeholders]="['Partner']" [minWidth]="130"
                            [options]="orgs" (change)="onFilterChange($event)" [multiple]="true">
                </filter-ddl>
            </div>
            <div class="col-md-3">
                <filter-ddl [id]="'ind_regions'" #indRegions class="filter-container" [placeholders]="['Region']" [minWidth]="130"
                            [options]="regions" [multiple]="true" (change)="onFilterChange($event)">
                </filter-ddl>
            </div>
            <div class="col-md-1 mt-2 fs-8"><a class="cursor-pointer" (click)="resetFilters()">Reset</a></div>
        </div>
        <div class="row">
            <div class="form-group col-md-9">
                <label class="required">Indicator</label>
                <textarea class="form-control" type="text" formControlName="name" rows="2" placeholder="Indicator name"
                          [ngClass]="{ 'is-invalid': form.controls['name'].touched && form.controls['name'].invalid }"
                          maxlength="2000">
                </textarea>
                <ng-container [ngTemplateOutlet]="formError"
                              [ngTemplateOutletContext]="{
                        validation: 'required',
                        message: 'Indicator name is required.',
                        control: form.controls['name']
                        }"></ng-container>
            </div>
            <div class="col-md-3">
                <label>Activity status filter</label>
                <filter-ddl [id]="'actStatus'" #indActStatus class="filter-container" [placeholders]="['Status', 'Status']" [minWidth]="130"
                            [options]="actStatus" [showSearch]="false" [multiple]="true" [selectedValues]="[2,3]" (change)="onFilterChange($event)">
                </filter-ddl>
            </div>
        </div>
        <div class="row">
            <div class="col-md-9">
                <div class="form-group">
                    <label>Target <code>(Formula)</code></label>
                    <input id="formula1" class="form-control" type="text"
                           (blur)="validateFormulaInputs()" (keyup)="validateFormulaInputs()"
                           placeholder="Target formula" maxlength="1000" autocomplete="off" />
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label>Unit/Prefix Target Value</label>
                    <input name="f1Info" class="form-control" type="text" formControlName="f1Info" maxlength="50" />
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-9">
                <div class="form-group">
                    <label>Target Disaggregation <code>(Formula)</code></label>
                    <input id="formula2" class="form-control" type="text"
                           (blur)="validateFormulaInputs()" (keyup)="validateFormulaInputs()"
                           placeholder="Disaggregation formula" maxlength="1000" autocomplete="off" />
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label>Unit/Prefix for Disagg. Value</label>
                    <input name="f2Info" class="form-control" type="text" formControlName="f2Info" maxlength="50" />
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-9">
                <div class="form-group">
                    <label>Progress <code>(Formula)</code></label>
                    <input id="formula3" class="form-control" type="text"
                           (blur)="validateFormulaInputs()" (keyup)="validateFormulaInputs()"
                           placeholder="Progress formula" maxlength="1000" autocomplete="off" />
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group">
                    <label>Unit/Prefix for Value</label>
                    <input name="f3Info" class="form-control" type="text" formControlName="f3Info" maxlength="50" />
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-md-9">
                <div class="form-group mb-0">
                    <label>Progress Disaggregation <code>(Formula)</code></label>
                    <input id="formula4" class="form-control" type="text"
                           (blur)="validateFormulaInputs()" (keyup)="validateFormulaInputs()"
                           placeholder="Disaggregation formula" maxlength="1000" autocomplete="off" />
                </div>
            </div>
            <div class="col-md-3">
                <div class="form-group mb-0">
                    <label>Unit/Prefix for Disagg. Value</label>
                    <input name="f4Info" class="form-control" type="text" formControlName="f4Info" maxlength="50" />
                </div>
            </div>
        </div>
    </form>

    <ng-template #formError let-control="control" let-message="message" let-validation="validation">
        <ng-container *ngIf="control.hasError(validation) && control.dirty">
            <div class="fv-plugins-message-container">
                <div class="fv-help-block">
                    <span role="alert">{{ message }}</span>
                </div>
            </div>
        </ng-container>
    </ng-template>
</app-modal>