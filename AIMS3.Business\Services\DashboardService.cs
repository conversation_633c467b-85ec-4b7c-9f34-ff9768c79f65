using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using AIMS3.Data;
using AIMS3.Business.Models;
using AIMS3.Business.Models.DashboardModels;

namespace AIMS3.Business.Services
{
    public class DashboardService : IDashboardService
    {
        private readonly AIMS3DbContext _context;

        public DashboardService(AIMS3DbContext context)
        {
            _context = context;
        }

        public async Task<object> GetReportsDashboardData(DataFilterModel filters)
        {
            var model = new DashboardOutput
            {
                Summary = new DashboardSummary(),
                TimeSeries = new List<DashboardTimeSeries>(),
                GeographicCoverage = new List<DashboardGeographicCoverage>(),
                ActivitiesByStatus = new Dictionary<AIMS3.Data.ActivityStatus, int>()
            };

            // Get activities by status
            var activities = await _context.Activities
                .Where(a => !a.DateDeleted.HasValue)
                .GroupBy(a => a.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            foreach (var activity in activities)
            {
                model.ActivitiesByStatus[activity.Status] = activity.Count;
            }

            // Get geographic coverage by province
            var provinces = await _context.Activities
                .Where(a => !a.DateDeleted.HasValue && !string.IsNullOrEmpty(a.ProvinceIds))
                .ToListAsync();

            var provinceGroups = provinces
                .SelectMany(a => a.ProvinceIds.Split(new[] { ',' }, StringSplitOptions.RemoveEmptyEntries))
                .GroupBy(p => p)
                .Select(g => new DashboardGeographicCoverage
                {
                    Region = Region.National, // Default to National since we don't have region mapping
                    ActivityCount = g.Count(),
                    Budget = 0, // These would need to be calculated based on actual data
                    Expenditure = 0,
                    Beneficiaries = 0
                })
                .ToList();

            model.GeographicCoverage = provinceGroups;

            // Calculate totals
            model.TotalBudget = 0; // These would need to be calculated based on actual data
            model.TotalExpenditure = 0;
            model.TotalBeneficiaries = 0;

            // Get time series data
            var timeSeries = await _context.Activities
                .Where(a => !a.DateDeleted.HasValue && a.StartDate.HasValue)
                .GroupBy(a => new { Year = a.StartDate.Value.Year, Month = a.StartDate.Value.Month })
                .Select(g => new DashboardTimeSeries
                {
                    Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                    ActivityCount = g.Count(),
                    Budget = 0, // These would need to be calculated based on actual data
                    Expenditure = 0,
                    Beneficiaries = 0
                })
                .OrderBy(ts => ts.Date)
                .ToListAsync();

            model.TimeSeries = timeSeries;

            // Update summary
            model.Summary.TotalActivities = activities.Sum(a => a.Count);
            model.Summary.ActiveActivities = activities.Where(a => a.Status == AIMS3.Data.ActivityStatus.Ongoing).Sum(a => a.Count);
            model.Summary.CompletedActivities = activities.Where(a => a.Status == AIMS3.Data.ActivityStatus.Completed).Sum(a => a.Count);
            model.Summary.TotalOutputs = await _context.InterventionProfiles.CountAsync(p => !p.DateDeleted.HasValue);
            model.Summary.TotalIndicators = await _context.Indicators.CountAsync(i => !i.DateDeleted.HasValue);
            model.Summary.TotalTargets = await _context.Targets.CountAsync(t => !t.DateDeleted.HasValue);

            return model;
        }

        public async Task<object> GetReportsSummaryStats(DataFilterModel filters)
        {
            var activities = await _context.Activities
                .Where(a => !a.DateDeleted.HasValue)
                .GroupBy(a => a.Status)
                .Select(g => new { Status = g.Key, Count = g.Count() })
                .ToListAsync();

            return new
            {
                TotalActivities = activities.Sum(a => a.Count),
                ActiveActivities = activities.Where(a => a.Status == AIMS3.Data.ActivityStatus.Ongoing).Sum(a => a.Count),
                CompletedActivities = activities.Where(a => a.Status == AIMS3.Data.ActivityStatus.Completed).Sum(a => a.Count),
                TotalOutputs = await _context.InterventionProfiles.CountAsync(p => !p.DateDeleted.HasValue),
                TotalIndicators = await _context.Indicators.CountAsync(i => !i.DateDeleted.HasValue),
                TotalTargets = await _context.Targets.CountAsync(t => !t.DateDeleted.HasValue)
            };
        }

        public async Task<object> GetReportsTimeSeriesData(DataFilterModel filters)
        {
            return await _context.Activities
                .Where(a => !a.DateDeleted.HasValue && a.StartDate.HasValue)
                .GroupBy(a => new { Year = a.StartDate.Value.Year, Month = a.StartDate.Value.Month })
                .Select(g => new
                {
                    Date = new DateTime(g.Key.Year, g.Key.Month, 1),
                    ActivityCount = g.Count(),
                    Budget = 0, // These would need to be calculated based on actual data
                    Expenditure = 0,
                    Beneficiaries = 0
                })
                .OrderBy(ts => ts.Date)
                .ToListAsync();
        }

        public async Task<IEnumerable<GpsPointModel>> GetActivityDataPoints(DataFilterModel filters)
        {
            // Since Activity model doesn't have Latitude/Longitude, we'll return empty list
            return await Task.FromResult(new List<GpsPointModel>());
        }

        public async Task<IEnumerable<ActivityStatusModel>> GetActivitiesForDistrict(int distId, DataFilterModel filters)
        {
            return await _context.Activities
                .Where(a => !a.DateDeleted.HasValue && a.DistrictIds.Contains(distId.ToString()))
                .Select(a => new ActivityStatusModel
                {
                    Id = a.Id,
                    Name = a.UniqueId,
                    Status = a.Status,
                    Progress = 0, // This would need to be calculated based on actual data
                    StartDate = a.StartDate,
                    EndDate = a.EndDate
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<PivotTableModel>> GetPivotTables(int? orgId = null)
        {
            return await _context.PivotTables
                .Where(p => !p.DateDeleted.HasValue)
                .Select(p => new PivotTableModel
                {
                    Id = p.Id,
                    Name = p.Name,
                    InterventionProfileId = p.InterventionProfileId,
                    IsTarget = p.IsTarget,
                    Order = p.Order,
                    PivotColumns = p.PivotColumns,
                    FiltersApplied = p.FiltersApplied
                })
                .ToListAsync();
        }

        public async Task<IEnumerable<ResultsDataModel>> GetData(DataFilterModel filters)
        {
            return await _context.Activities
                .Where(a => !a.DateDeleted.HasValue)
                .Select(a => new ResultsDataModel
                {
                    InterventionId = a.InterventionProfileId,
                    TargetData = new List<TargetDataResultModel>(),
                    ProgressData = new List<ProgressDataResultModel>
                    {
                        new ProgressDataResultModel
                        {
                            Id = a.Id,
                            ActivityId = a.UniqueId,
                            Status = (byte)a.Status,
                            SMonth = a.StartDate.HasValue ? a.StartDate.Value.ToString("dd/MM/yyyy") : null,
                            EMonth = a.EndDate.HasValue ? a.EndDate.Value.ToString("dd/MM/yyyy") : null,
                            Region = a.Regions,
                            Province = a.ProvinceIds,
                            District = a.DistrictIds,
                            Community = a.CommunityIds,
                            AsOf = a.ActivitiesProgress.Any() ? a.ActivitiesProgress.First().AsOfDate.HasValue ? a.ActivitiesProgress.First().AsOfDate.Value.ToString("dd/MM/yyyy") : null : null,
                            Month = a.ActivitiesProgress.Any() ? a.ActivitiesProgress.First().Month : null,
                            Year = a.ActivitiesProgress.Any() ? a.ActivitiesProgress.First().Year : null
                        }
                    }
                })
                .ToListAsync();
        }

        public async Task<CategoryStatisticsModel> GetCategoryStatistics(DataFilterModel filters)
        {
            var statistics = new CategoryStatisticsModel
            {
                TotalOutputs = await _context.InterventionProfiles
                    .Where(p => !p.DateDeleted.HasValue)
                    .CountAsync(),

                TotalCategories = await _context.Categories
                    .Where(c => !c.DateDeleted.HasValue)
                    .CountAsync(),

                TotalBeneficiaries = await _context.Activities
                    .Where(a => !a.DateDeleted.HasValue)
                    .Include(a => a.ColumnValues)
                    .Select(a => a.ColumnValues
                        .Where(cv => cv.DynamicColumnId == 1) // Assuming 1 is the ID for beneficiaries column
                        .Select(cv => cv.Value)
                        .FirstOrDefault())
                    .ToListAsync()
                    .ContinueWith(t => t.Result.Sum(v => int.TryParse(v, out int val) ? val : 0))
            };

            return statistics;
        }
    }
} 