import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Subject, Subscription, lastValueFrom } from 'rxjs';
import { ModalComponent, ModalConfig } from '../../../../_theme/partials';
import { Project } from '../../../../modules/admin/models/project.model';
import { FilterDropdownList } from '../../../../shared/components/filter-dropdown/filter-ddl.control';
import { ActivityStatus, OperationResult } from '../../../../shared/enums';
import { MessageService } from '../../../../shared/services/message.service';
import { AppUtilities } from '../../../../shared/utilities';
import { IDynCol, IIndFilters, IIndicator, Indicator } from '../../../models/indicator.model';
import { IndicatorService } from '../../../services/indicator.service';
import { DynamicScriptLoaderService } from '../../../services/script-loader.service';

declare const $: any;

@Component({
    selector: 'indicator-form-modal',
    templateUrl: './indicator-form.component.html',
    styleUrls: ['./indicator-form.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class IndicatorFormComponent implements OnInit, OnDestroy {
    working: boolean = false;

    indicator: Indicator;
    numericColumns: IDynCol[] = [];
    form: FormGroup;
    @Input() types: any[] = [];
    projGroups: any[] = [];

    @ViewChild('modal') private modalComponent: ModalComponent;
    modalConfig: ModalConfig = {
        modalTitle: 'Add new indicator',
        cancelButtonLabel: 'Cancel',
        doneButtonLabel: 'Add',
        disableDoneButton: true,
        options: { size: 'lg' },
        shouldDo: () => this.save(),
        shouldCancel: () => { this.ngOnDestroy(); return true; }
    };
    @Output() done = new EventEmitter<IIndicator>();

    private expBuilderOptions = {
        variables: [], expression: '',
        variableSpecialCharacters: ['.', '_','#','^','!','~','$','%','@',':','&','|']
    };
    private f1ExpBuilder: any;
    private f2ExpBuilder: any;
    private f3ExpBuilder: any;
    private f4ExpBuilder: any;

    projects: Project[] = [];
    orgs: any[] = [];
    regions = [];
    actStatus = [];

    private filters: IIndFilters = {
        actStatus: [1,2]
    };

    @ViewChild('groups')
    private projGroupsFilterCtrl: FilterDropdownList;
    @ViewChild('indProjects')
    private projFilterCtrl: FilterDropdownList;
    @ViewChild('indPartners')
    private partnerFilterCtrl: FilterDropdownList;
    @ViewChild('indActStatus')
    private actStatusFilterCtrl: FilterDropdownList;
    @ViewChild('indRegions')
    private regionFilterCtrl: FilterDropdownList;

    private subscriptions: Subscription[] = [];
    constructor(
        private indService: IndicatorService,
        private messageService: MessageService,
        private dynScriptLoaderService: DynamicScriptLoaderService
    ) {
        this.indicator = new Indicator(0, '', '', '', 1);

        Object.values(ActivityStatus)
            .forEach((v, i) => {
                if ((typeof v === 'string'))
                    this.actStatus.push({ id: i, name: v });
            });
    }

    ngOnInit(open?: boolean) {
        this.initForm();

        if (open) {
            this.modalComponent?.open(true).then();

            if (this.indicator?.id > 0) {
                this.modalConfig.modalTitle = 'Edit indicator';
                this.modalConfig.doneButtonLabel = 'Save changes';
            } else {
                this.modalConfig.modalTitle = 'Add new indicator';
                this.modalConfig.doneButtonLabel = 'Add';
            }

            if ($.fn.select2)
                AppUtilities().initSelect2('#type', true);
            else
                AppUtilities().initSelect2();

            $('#type').on('change', (e: any) => {
                const selVal = $(e.target).val() || '';
                if (selVal && Array.isArray)
                    this.f.type.setValue(selVal[0]);
            });

            $('#type').val(this.indicator.typeName).trigger('change');

            // select groups selected
            const projGroups = this.indicator.projectGroups.split(',')
                .filter(pg => pg && pg.length);
            this.projGroupsFilterCtrl.clearSelection();
            this.projGroupsFilterCtrl.setSelectedValues(projGroups);

            // filter projects based on project grouping(s) selected
            this.projFilterCtrl.options = this.projects.filter(p => projGroups.includes(p.grouping));
            this.projFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));

            // apply filters to dropdowns
            this.resetFilters();
            if (this.indicator.dataFilters) {
                this.filters = JSON.parse(this.indicator.dataFilters);

                if (this.filters['ProjIds'])
                    this.filters.projIds = this.filters['ProjIds'];
                if (this.filters['OrgIds'])
                    this.filters.orgIds = this.filters['OrgIds'];
                if (this.filters['Regions'])
                    this.filters.regions = this.filters['Regions'];
                if (this.filters['ActStatus'])
                    this.filters.actStatus = this.filters['ActStatus'];

                if (this.filters.projIds?.length)
                    this.projFilterCtrl.setSelectedValues(this.filters.projIds);
                if (this.filters.orgIds?.length)
                    this.partnerFilterCtrl.setSelectedValues(this.filters.orgIds);
                if (this.filters.regions?.length)
                    this.regionFilterCtrl.setSelectedValues(this.filters.regions);
                if (this.filters.actStatus?.length)
                    this.actStatusFilterCtrl.setSelectedValues(this.filters.actStatus);
            }

            $('#formula1,#formula2,#formula3,#formula4').val('')
                .removeClass('is-invalid').removeClass('is-valid');

            if (!this.expBuilderOptions.variables.length)
                this.buildExpressions();
            else
                this.initExpressions();
        }
    }

    // convenient getter for easy access to form fields
    get f() {
        return this.form.controls;
    }

    private initForm() {
        this.form = new FormGroup({
            id: new FormControl({ value: this.indicator.id, disabled: true }),
            name: new FormControl(this.indicator.name, Validators.required),
            groups: new FormControl(this.indicator.projectGroups, Validators.required),
            type: new FormControl(this.indicator.typeName, Validators.required),
            f1Info: new FormControl(this.indicator.f1Info),
            f2Info: new FormControl(this.indicator.f2Info),
            f3Info: new FormControl(this.indicator.f3Info),
            f4Info: new FormControl(this.indicator.f4Info)
        });

        this.subscriptions.push(
            this.form.valueChanges.subscribe((res) => {
                this.modalConfig.disableDoneButton = !this.form.valid;
                this.validateFormulaInputs();
            })
        );
    }

    private buildExpressions(): void {
        this.numericColumns.forEach(col => {
            this.expBuilderOptions.variables.push({
                variableId: `${col.interventionId}_${col.id}`,
                name: col.name.replace(/[~`*()+={}\[\];\'\"<>,\/\\\-]/g, ':')
            });
        });

        // load external js library for formula
        this.dynScriptLoaderService.load('expression-builder').then(res => {
            this.initExpressions();
        }).catch(error => console.log(error));
    }

    private initExpressions(): void {
        // set expression in the options and init each
        this.expBuilderOptions.expression = this.indicator.formula1;
        this.f1ExpBuilder = $('#formula1').expressionBuilder(this.expBuilderOptions);
        this.expBuilderOptions.expression = this.indicator.formula2;
        this.f2ExpBuilder = $('#formula2').expressionBuilder(this.expBuilderOptions);
        this.expBuilderOptions.expression = this.indicator.formula3;
        this.f3ExpBuilder = $('#formula3').expressionBuilder(this.expBuilderOptions);
        this.expBuilderOptions.expression = this.indicator.formula4;
        this.f4ExpBuilder = $('#formula4').expressionBuilder(this.expBuilderOptions);
    }

    validateFormulaInputs(): boolean {
        if (!this.f1ExpBuilder || !this.f1ExpBuilder.isValid()) {
            this.modalConfig.disableDoneButton = true;
            return;
        }

        if (this.f2ExpBuilder && !this.f2ExpBuilder.isValid()) {
            this.modalConfig.disableDoneButton = true;
            return;
        }

        if (!this.f3ExpBuilder || !this.f3ExpBuilder.isValid()) {
            this.modalConfig.disableDoneButton = true;
            return;
        }

        if (this.f4ExpBuilder && !this.f4ExpBuilder.isValid()) {
            this.modalConfig.disableDoneButton = true;
            return;
        }

        if (this.form.valid) {
            this.modalConfig.disableDoneButton = false;
            return true;
        }
        return false;
    }

    onFilterChange(ctrl): void {
        if (ctrl.id === 'projGroups') {
            this.f.groups.setValue(ctrl.selVals.join(','));
            // filter ind projs based on it
            // first clear it
            this.projFilterCtrl.clearSelection();

            if (!ctrl.selVals.length)
                this.projFilterCtrl.options = this.projects;
            else {
                this.projFilterCtrl.options = this.projects.filter(p => ctrl.selVals.includes(p.grouping));
                this.projFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));
            }
        } else {
            ctrl.id = ctrl.id.replace('ind_', '');
            this.filters[ctrl.id] = ctrl.selVals;
        }
    }

    resetFilters(): void {
        this.projFilterCtrl.clearSelection();
        this.partnerFilterCtrl.clearSelection();
        this.actStatusFilterCtrl.setSelectedValues([1,2]);
        this.regionFilterCtrl.clearSelection();

        this.filters = { actStatus: [1,2] };
    }

    async save(): Promise<boolean> {
        const result = new Subject<boolean>();

        // validate
        if (!this.validateFormulaInputs())
            return;

        this.modalConfig.working = true;

        this.indicator.typeName = this.f.type.value;

        this.indicator = new Indicator(this.indicator.id, this.f.groups.value,
            this.f.type.value, this.f.name.value, this.indicator.order,
            this.f1ExpBuilder.getExpression(), this.f.f1Info.value,
            this.f2ExpBuilder.getExpression(), this.f.f2Info.value,
            this.f3ExpBuilder.getExpression(), this.f.f3Info.value,
            this.f4ExpBuilder.getExpression(), this.f.f4Info.value,
            JSON.stringify(this.filters), 'client');

        //console.log(this.indicator);
        //this.modalConfig.working = false;
        //return;
        // update indicator info
        let operation = this.indicator.id > 0 ?
            this.indService.updateIndicator(this.indicator) :
            this.indService.addIndicator(this.indicator);

        let _ind: IIndicator;
        this.subscriptions.push(operation.subscribe({
            next: (res) => {
                if (res.result === OperationResult.Succeeded)
                    _ind = res.data;
            },
                error: (err) => {
                    this.modalConfig.working = false;
                    console.log(err);

                    result.next(false);
                    result.complete();
                },
                complete: () => {
                    let successMsg = 'The indicator has been added successfully.';
                    if (this.indicator.id > 0)
                        successMsg = "The indicator has been updated successfully.";

                    this.messageService.success(successMsg);

                    // notify to refresh indicators table
                    this.done.emit(_ind);

                    this.modalConfig.working = false;
                    result.next(true);
                    result.complete();
                }
            })
        );

        return await lastValueFrom(result.asObservable());
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}