<div id="page_toolbar" class="page-toolbar" [ngClass]="toolbarBgColor">
    <aims-loading class="col-5" [showTable]="false" *ngIf="working"></aims-loading>
    <div class="page-toolbar-container d-flex flex-stack flex-wrap flex-md-nowrap" [ngClass]="{'d-none': working}">
        <!--begin::Toolbar left-->
        <div class="d-flex align-items-center flex-shrink-0">
            <div class="project-filter me-5 min-w-200px" title="Project">
                <select id="projFilter" class="form-select form-select-sm form-select-transparent"
                    data-control="select2" data-placeholder="Select a project">
                    <option value="" selected></option>
                    <option value="0" *ngIf="isAdmin">- NONE - </option>
                    <option *ngFor="let proj of projects" [value]="proj.id">{{ proj.name }}</option>
                </select>
            </div>
            <ng-container *ngIf="gridFilters.project || isAdmin">
                <!-- Filters -->
                <filter-ddl [id]="'output'" #output class="filter-control w-130px" [placeholders]="['Output']"
                    [minWidth]="130" [options]="outputs" (change)="onFilterChange($event)" [multiple]="true"
                    [highlightSelected]="!gridFilters.project">
                </filter-ddl>
                <filter-ddl [id]="'category'" #category class="filter-control w-130px ms-2"
                    [placeholders]="['Category']" [minWidth]="130" [options]="categories"
                    (change)="onFilterChange($event)" [multiple]="true" [highlightSelected]="!gridFilters.project">
                </filter-ddl>
                <filter-ddl [id]="'profile'" #profile class="filter-control w-150px ms-2"
                    [placeholders]="['Intervention']" [minWidth]="150" [options]="profiles"
                    (change)="onFilterChange($event)" [multiple]="true" [highlightSelected]="!gridFilters.project">
                </filter-ddl>
                <a class="btn-link btn-reset ms-3" (click)="resetFilters()" *ngIf="filtered">Reset filter</a>
                <!-- Pivot -->
                <ng-container *ngIf="isAdmin && !gridFilters.project">
                    <div class="bullet bg-secondary h-35px w-1px mx-6"></div>
                    <button type="button" class="btn btn-sm btn-icon px-3 w-auto" ngbTooltip="Pivot setting"
                        (click)="onPivot()"
                        [ngClass]="{'btn-light': !pivotPanel, 'btn-light-primary border border-primary': pivotPanel}">
                        <i class="fas fa-bezier-curve me-2"></i> Pivot
                    </button>
                </ng-container>
            </ng-container>
        </div>

        <!--begin::Toolbar right-->
        <div class="d-flex align-items-center overflow-auto" *ngIf="gridFilters.project || isAdmin">
            <a class="btn-link ms-3" [routerLink]="'/data-entry/intervention-profiles'" target="_blank"
                title="Variable explanation">
                <span class="text-truncate">
                    Variables
                    <i class="fas fa-external-link ms-1 fs-8" title="Opens in a new tab."></i>
                </span>
            </a>
        </div>
    </div>
</div>

<div class="grid-panel" [ngClass]="{'d-flex flex-stack': pivotPanel}">
    <div class="card card-custom card-stretch-2 blockui"
        [ngClass]="{'col-9': pivotPanel === 'sm','col-6': pivotPanel === 'lg'}">
        <!-- Profile Tabs -->
        <div class="card-header-custom" [ngClass]="toolbarBgColor"
            *ngIf="(gridFilters.project || isAdmin) else selProject">
            <ul id="interventions" class="nav nav-tabs" role="tablist"
                [ngClass]="{'d-none': working, 'border-radius': pivotPanel}">
                <ng-container *ngIf="(['admin'] | isAuth) else readOnlyTab">
                    <li class="nav-item" *ngFor="let prof of profTabs; let ind = index">
                        <div class="nav-link d-flex flex-stack" [ngClass]="{ 'active': selTabId === prof.id }">
                            <a class="nav-title" title="{{ [prof.name,prof.abbreviation].join(' ') }}"
                                (click)="onSelectProfileTab(prof.id)">
                                <span class="nav-text">{{ prof.name }}</span>
                            </a>
                            <a class="nav-icon-btn" title="Edit" (click)="onManageProfile($event, prof.id)">
                                <i class="fas fa-chevron-down"></i>
                            </a>
                        </div>
                    </li>
                    <!-- More Tabs Indicator -->
                    <li class="nav-item" *ngIf="btnAllTabs"
                        ngbTooltip="More interventions can be accessed using the button on the right.">
                        <div class="nav-link">
                            <a class="nav-title" style="padding: 0 0.75rem;" (click)="onMoreTabsMenu()">
                                <span class="nav-text fw-semibold">...</span>
                            </a>
                        </div>
                    </li>
                    <!-- New Profile -->
                    <li class="nav-item">
                        <a class="nav-link btn-add-new" ngbTooltip="Add new intervention profile"
                            (click)="onManageProfile($event)">
                            <span class="nav-icon"><i class="fas fa-plus"></i></span>
                            <span class="nav-text">Add new</span>
                        </a>
                    </li>
                </ng-container>
                <ng-template #readOnlyTab>
                    <li class="nav-item" *ngFor="let prof of profTabs" [ngbTooltip]="htmlTooltip"
                        (click)="onSelectProfileTab(prof.id)">
                        <div class="nav-link" [ngClass]="{ 'active': selTabId === prof.id }">
                            <a class="nav-title">
                                <span class="nav-text">{{ prof.name }}</span>
                            </a>
                        </div>
                        <ng-template #htmlTooltip>
                            <div class="text-start" style="line-height: normal">
                                <p class="fs-8 fw-semibold text-gray-600 mb-1">{{ prof.category.code }} {{
                                    prof.category.name }}</p>
                                <p class="fs-7 fw-bold text-gray-800">{{ prof.name }} ({{ prof.abbreviation }})</p>
                                <p class="fs-9 text-gray-700 mt-2 mb-0">{{ prof.description }}</p>
                            </div>
                        </ng-template>
                    </li>

                    <!-- More Tabs Indicator -->
                    <li class="nav-item" *ngIf="btnAllTabs"
                        ngbTooltip="More interventions can be accessed using the button on the right.">
                        <div class="nav-link">
                            <a class="nav-title" style="padding: 0 0.75rem;" (click)="onMoreTabsMenu()">
                                <span class="nav-text fw-semibold">...</span>
                            </a>
                        </div>
                    </li>
                </ng-template>

                <!-- All Profiles -->
                <li class="nav-item dropdown push" [ngClass]="{ 'd-none': !btnAllTabs }">
                    <a id="moreProfileTabs" class="nav-link px-3" data-qs-menu-trigger="click"
                        ngbTooltip="More Interventions" data-qs-menu-placement="bottom-end" data-qs-menu-flip="top-end">
                        <span class="nav-icon w-auto"><i class="fas fa-chevron-down"></i></span>
                    </a>
                    <!-- Dropdown menu -->
                    <div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown py-2 fs-7" data-qs-menu="true">
                        <div class="menu-item menu-lg-down-accordion" *ngFor="let prof of interventions">
                            <a class="menu-link px-6 py-3" *ngIf="selTabId !== prof.id else profSelected"
                                (click)="onSelectProfileTab(prof.id)">
                                <span class="menu-title ms-6">{{ prof.name }} ({{ prof.abbreviation }})</span>
                            </a>
                            <ng-template #profSelected>
                                <a class="menu-link flex-stack bg-light-primary text-primary px-4 py-3"
                                    title="Selected intervention profile" (click)="onSelectProfileTab(prof.id)">
                                    <i class="fas fa-check text-primary"></i>
                                    <span class="menu-title ms-4">{{ prof.name }} ({{ prof.abbreviation }})</span>
                                </a>
                            </ng-template>
                        </div>
                    </div>
                </li>
            </ul>
        </div>
        <ng-template #selProject>
            <div class="card-body card-body-custom">
                <div class="d-flex justify-content-center">
                    <div
                        class="notice bg-light rounded border-secondary border border-dashed text-center py-2 px-5 my-10 fs-6">
                        Please select a project.
                    </div>
                </div>
            </div>
        </ng-template>
        <!-- begin::Body -->
        <div class="card-body card-body-custom blockui" [ngClass]="{ 'd-none': !gridFilters.project && !isAdmin }">
            <aims-working *ngIf="gridWorking"></aims-working>
            <div class="grid-toolbar d-flex flex-stack flex-wrap flex-md-nowrap px-10 py-2"
                *ngIf="selTabId > 0 else noProfile">
                <div class="d-flex align-items-center">
                    <button type="button" class="btn btn-sm dropdown-toggle fw-bold border px-3" [ngClass]="{ 'btn-light-primary btn-active-primary border-primary': gridFilters.type === 'Progress',
                                'btn-light-info btn-active-info border-info': gridFilters.type === 'Targets' }"
                        data-qs-menu-trigger="click" data-qs-menu-placement="bottom-start">
                        {{ gridFilters.type }}
                    </button>
                    <div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown w-150px py-2 fs-7"
                        data-qs-menu="true">
                        <div class="menu-item menu-lg-down-accordion">
                            <a class="menu-link flex-stack bg-light-secondary text-primary px-4 py-3"
                                *ngIf="gridFilters.type === 'Progress' else ProgressNotSel"
                                (click)="onTypeChange('Progress')">
                                <i class="fas fa-check text-primary"></i>
                                <span class="menu-title ms-4">Progress</span>
                            </a>
                            <ng-template #ProgressNotSel>
                                <a class="menu-link px-6 py-3" (click)="onTypeChange('Progress')">
                                    <span class="menu-title ms-6">Progress</span>
                                </a>
                            </ng-template>
                            <a class="menu-link flex-stack bg-light-secondary text-primary px-4 py-3"
                                *ngIf="gridFilters.type === 'Targets' else TargetNotSel"
                                (click)="onTypeChange('Targets')">
                                <i class="fas fa-check text-primary"></i>
                                <span class="menu-title ms-4">Targets</span>
                            </a>
                            <ng-template #TargetNotSel>
                                <a class="menu-link px-6 py-3" (click)="onTypeChange('Targets')">
                                    <span class="menu-title ms-6">Targets</span>
                                </a>
                            </ng-template>
                        </div>
                    </div>
                    <!-- Partner -->
                    <div class="w-150px ms-6 border"
                        [ngClass]="{ 'd-none': !gridFilters.project || pivotPanel === 'lg', 'border-danger': gridFilters.orgId > 0 && gridFilters.orgId !== userOrgId }"
                        ngbTooltip="IP (Partner)" style="border-radius: 0.375rem;"
                        *ngIf="isAdmin || (['ga','gv'] | isAuth)">
                        <select id="partner" class="form-select form-select-sm form-select-solid" data-control="select2"
                            data-placeholder="Select partner">
                            <option *ngFor="let org of orgs" [value]="org.id" [title]="org.tooltip"
                                [selected]="org.selected">{{ org.name }}</option>
                        </select>
                    </div>
                    <div class="bullet bg-secondary h-35px w-1px mx-6"></div>
                    <ng-container *ngIf="gridFilters.project">
                        <!-- Group -->
                        <ng-container *ngIf="!pivotPanel">
                            <button type="button" class="btn btn-sm btn-icon px-3 w-auto" [ngbTooltip]="groupTooltip"
                                [ngClass]="{'btn-light': gridGroupedBy < 0, 'btn-light-primary border border-primary': gridGroupedBy >= 0}"
                                (click)="enableGridGroup()">
                                <i class="fas fa-layer-group me-2"></i>
                                <ng-container *ngIf="gridGroupedBy <= 0">Group</ng-container>
                                <ng-container *ngIf="gridGroupedBy > 0">Grouped by {{gridGroupedBy}}
                                    field{{gridGroupedBy > 1 ? 's' : ''}}</ng-container>
                            </button>
                            <ng-template #groupTooltip>
                                <ng-container *ngIf="gridGroupedBy < 0"><span class="fw-semibold">Enable grouping</span>
                                    for columns.</ng-container>
                                <div class="text-start" *ngIf="gridGroupedBy >= 0">
                                    <p class="p-0 fw-semibold">Grouping enabled</p>
                                    <p>Drag a column to the <span class="fw-semibold">Group panel</span> above the sheet
                                        to group by that column. You can have multiple levels of grouping.</p>
                                </div>
                            </ng-template>
                            <!-- Sort -->
                            <button type="button" class="btn btn-sm btn-icon px-3 w-auto mx-2"
                                [ngbTooltip]="sortTooltip"
                                [ngClass]="{'btn-light': gridSortedBy < 0, 'btn-light-primary border border-primary': gridSortedBy >= 0}"
                                (click)="enableGridSort()">
                                <i class="fas fa-sort me-2"></i>
                                <ng-container *ngIf="gridSortedBy <= 0">Sort</ng-container>
                                <ng-container *ngIf="gridSortedBy > 0">Sorted by {{gridSortedBy}} field{{gridSortedBy >
                                    1 ? 's' : ''}}</ng-container>
                            </button>
                            <ng-template #sortTooltip>
                                <ng-container *ngIf="gridSortedBy < 0"><span class="fw-semibold">Enable sorting</span>
                                    on each column.</ng-container>
                                <div class="text-start" *ngIf="gridSortedBy >= 0">
                                    <p class="p-0 fw-semibold">Sorting enabled</p>
                                    <p>Click on a column header to sort by that column. Use <code>Shift</code> key to
                                        sort by multiple columns.</p>
                                </div>
                            </ng-template>
                        </ng-container>
                        <!-- Pivot -->
                        <button type="button" class="btn btn-sm btn-icon px-3 w-auto" (click)="onPivot()"
                            [ngClass]="{'btn-light': !pivotPanel, 'btn-light-primary border border-primary': pivotPanel}">
                            <i class="fas fa-bezier-curve me-2"></i>
                            Pivot
                        </button>
                        <!-- Submission Log -->
                        <button type="button" class="btn btn-sm btn-icon px-3 w-auto ms-2" 
                            ngbTooltip="View submission log"
                            (click)="onSubmissionLog()">
                            <i class="fas fa-list-alt me-2"></i>
                            Submission Log
                        </button>
                        <div class="notice bg-light-primary rounded border-primary border border-dashed text-truncate ms-4 p-2 fs-8"
                            style="max-width: 35%" *ngIf="gridFilteredBy.length > 1">
                            <span class="fw-semibold">Filtered by:</span> {{ gridFilteredBy[0] }}
                        </div>
                    </ng-container>
                </div>
                <div class="d-flex align-items-center" *ngIf="gridFilters.project">
                    <!-- Progress Month -->
                    <ng-container *ngIf="gridFilters.type === 'Progress'">
                        <button class="btn btn-sm btn-icon btn-light btn-active-color-primary px-1"
                            ngbTooltip="Previous month" *ngIf="!pivotPanel" (click)="onMonthChange($event,true)">
                            <i class="la la-angle-left"></i>
                        </button>
                        <input id="progressMonth" type="month"
                            class="form-control form-control-sm border border-1 border-primary fw-bold w-130px mx-1"
                            [ngClass]="{'d-none': pivotPanel === 'lg'}" ngbTooltip="Progress month" min="2020-01"
                            max="2050-12" (change)="onMonthChange($event)" />
                        <button class="btn btn-sm btn-icon btn-light btn-active-color-primary px-1"
                            ngbTooltip="Next month" *ngIf="!pivotPanel" (click)="onMonthChange($event)">
                            <i class="la la-angle-right"></i>
                        </button>
                    </ng-container>
                    <!-- Save -->
                    <button type="button" class="btn btn-sm btn-icon btn-light-primary px-3 w-auto mx-3"
                        *ngIf="!isViewer" ngbTooltip="Save (Ctrl + S)" placement="bottom"
                        [disabled]="saving || !pendingSave || submitting" (click)="onSave()">
                        <ng-container *ngIf="!saving; else btnSpinner">
                            <span [inlineSVG]="'./assets/media/icons/duotune/general/save.svg'"
                                class="svg-icon svg-icon-2 me-1"></span>
                            Save
                        </ng-container>
                        <ng-template #btnSpinner>
                            <span class="indicator-progress" style="display: block">
                                <span class="spinner-border spinner-border-sm align-middle me-1"></span>
                                Saving...
                            </span>
                        </ng-template>
                    </button>
                    <!-- Submit -->
                    <button type="button" class="btn btn-sm btn-icon btn-light-primary px-3 w-auto"
                        *ngIf="!submitStatus && !isViewer" placement="bottom" [disabled]="submitting || pendingSave"
                        (click)="onSubmit()">
                        <ng-container *ngIf="!submitting; else btnSubmitSpinner">
                            <span *ngIf="gridFilters.type === 'Progress'">Submit Progress</span>
                            <span *ngIf="gridFilters.type === 'Targets'">
                                Submit Targets
                                <small class="d-block fs-9 text-muted">
                                    (Targets auto-submit on save)
                                </small>
                            </span>
                        </ng-container>
                        <ng-template #btnSubmitSpinner>
                            <span class="indicator-progress" style="display: block">
                                <span class="spinner-border spinner-border-sm align-middle me-1"></span>
                                Submitting...
                            </span>
                        </ng-template>
                    </button>
                    
                    <!-- Submitted Status Display (Non-clickable) -->
                    <button type="button"
                        class="btn btn-sm btn-icon btn-light-primary px-3 w-auto border border-primary"
                        *ngIf="submitStatus==='submitted'" disabled>
                        <span [inlineSVG]="'./assets/media/icons/duotune/arrows/arr085.svg'"
                            class="svg-icon svg-icon-2 me-1"></span>
                        Submitted
                    </button>
                    <button type="button"
                        class="btn btn-sm btn-icon btn-light-success px-3 w-auto border border-success"
                        *ngIf="submitStatus==='approved'" disabled>
                        <span [inlineSVG]="'./assets/media/icons/duotune/arrows/arr085.svg'"
                            class="svg-icon svg-icon-2 me-1"></span>
                        Approved
                    </button>
                    <ng-container *ngIf="pivotPanel !== 'lg'">
                        <div class="bullet bg-secondary h-35px w-1px mx-6"></div>
                        <!-- Download -->
                        <button type="button" class="btn btn-sm btn-icon btn-light btn-active-color-primary px-3 w-auto"
                            [disabled]="downloading || saving || working || gridWorking" (click)="onDownload()"
                            *ngIf="!pivotPanel">
                            <ng-container *ngIf="!downloading; else btnDSpinner">
                                <i class="fas fa-download me-2"></i> Download
                            </ng-container>
                            <ng-template #btnDSpinner>
                                <span class="indicator-progress" style="display: block">
                                    <span class="spinner-border spinner-border-sm align-middle me-1"></span>
                                    Downloading...
                                </span>
                            </ng-template>
                        </button>
                        <button type="button" class="btn btn-sm btn-icon btn-light btn-active-color-primary px-3"
                            ngbTooltip="Download" *ngIf="pivotPanel === 'sm'"
                            [disabled]="downloading || saving || working || gridWorking" style="margin-right:-1.5rem;"
                            (click)="onDownload()">
                            <i class="fas fa-download" *ngIf="!downloading; else btnDSpinner"></i>
                            <ng-template #btnDSpinner>
                                <span class="indicator-progress" style="display: block">
                                    <span class="spinner-border spinner-border-sm align-middle"></span>
                                </span>
                            </ng-template>
                        </button>
                    </ng-container>
                </div>
            </div>

            <!-- AIMS Grid -->
            <aims-grid id="aims-grid" [ngClass]="{ 'd-none': selTabId === 0 }" (colDone)="onColumnDone($event)"
                (filterApplied)="gridFiltered($event)" (sortApplied)="gridSortedBy=$event"
                (cellChanged)="onCellValueChanged($event)"></aims-grid>

            <ng-template #noProfile>
                <aims-working *ngIf="working"></aims-working>
                <div class="d-flex justify-content-center" *ngIf="!working">
                    <div
                        class="notice bg-light rounded border-secondary border border-dashed text-center py-2 px-5 my-10 fs-6">
                        No intervention selected or no intervention is found.
                    </div>
                </div>
            </ng-template>
        </div>
    </div>

    <!-- Pivot Panel -->
    <div class="pivot-panel {{toolbarBgColor}}" [ngClass]="{'col-3':pivotPanel === 'sm', 'col-6':pivotPanel === 'lg'}"
        *ngIf="pivotPanel">
        <div class="card ms-2">
            <div class="card-header">
                <h6>Pivot Panel</h6>
                <div class="card-toolbar">
                    <button type="button" class="btn btn-sm btn-icon btn-active-color-primary"
                        ngbTooltip="Expand pivot panel" *ngIf="pivotPanel === 'sm'" (click)="onPivot('lg')">
                        <span [inlineSVG]="'./assets/media/icons/duotune/arrows/arr079.svg'"
                            class="svg-icon svg-icon-2"></span>
                    </button>
                    <button type="button" class="btn btn-sm btn-icon btn-active-color-primary"
                        ngbTooltip="Collapse pivot panel" *ngIf="pivotPanel === 'lg'" (click)="onPivot('sm')">
                        <span [inlineSVG]="'./assets/media/icons/duotune/arrows/arr080.svg'"
                            class="svg-icon svg-icon-2"></span>
                    </button>
                    <button type="button" class="btn btn-sm btn-icon btn-active-color-primary ms-2" ngbTooltip="Close"
                        (click)="onPivot()">
                        <span [inlineSVG]="'./assets/media/icons/duotune/arrows/arr061.svg'"
                            class="svg-icon svg-icon-2"></span>
                    </button>
                </div>
            </div>
            <aims-pivots class="card-body blockui" [interventions]="categories" [projects]="projects"
                (initd)="onPivotsInitd($event)"></aims-pivots>
        </div>
    </div>
</div>

<!-- Dropdown menu modal form: Add/Edit Profile -->
<profile-form-modal id="profileForm" [outputs]="outputs" (done)="onProfileDone($event)"></profile-form-modal>