.symbol-circle:has(.col-red) {
  .border {
    border-color: red !important;
  }

  &:hover {
    background-color: red;
  }
}

.symbol-circle:has(col-blue) {
  .border {
    border-color: darkblue !important;
  }

  &:hover {
    background-color: darkblue;
  }
}
.symbol-circle:has(.col-yellow) {
  .border {
    border-color: darkgoldenrod !important;
  }

  &:hover {
    background-color: darkgoldenrod;
  }
}
.symbol-circle:has(.col-green-haze) {
  .border {
    border-color: #44B6AE !important;
  }

  &:hover {
    background-color: #44B6AE;
  }
}
.symbol-circle:has(.col-red-haze) {
  .border {
    border-color: #F36A5A !important;
  }

  &:hover {
    background-color: #F36A5A;
  }
}
.symbol-circle:has(.col-purple-sharp) {
  .border {
    border-color: #796799 !important;
  }

  &:hover {
    background-color: #796799;
  }
}
.modal {
  background-color: rgba(0, 0, 0, 0.5);
}

.modal.show {
  opacity: 1;
}

.modal-backdrop.show {
  opacity: 0.5;
}

.modal-body {
  display: flex;
  flex-direction: column;
}
