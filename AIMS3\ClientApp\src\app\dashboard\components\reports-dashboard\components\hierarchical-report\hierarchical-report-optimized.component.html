<div class="hierarchical-report-container">
  <!-- Loading & Error States -->
  <div *ngIf="loading" class="text-center p-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-3">Loading report data...</p>
  </div>
  
  <div *ngIf="error" class="alert alert-danger mt-3" role="alert">
    {{ error }}
    <button type="button" class="btn btn-sm btn-outline-danger ms-3" (click)="loadReport()">
      Try Again
    </button>
  </div>
  
  <!-- Report Header with Export Options -->
  <div class="d-flex justify-content-between align-items-center mb-4" *ngIf="!loading && !error">
    <h3 class="m-0">Hierarchical Project Report</h3>
    <div class="export-buttons">
      <button type="button" class="btn btn-sm btn-light-danger me-2" (click)="loadReport()">
        <i class="bi bi-arrow-clockwise me-1"></i> Reload Data
      </button>
      <button type="button" class="btn btn-sm btn-light-primary me-2" (click)="exportToExcel()">
        <i class="bi bi-file-earmark-excel me-1"></i> Export to Excel
      </button>
      <button type="button" class="btn btn-sm btn-light-primary" (click)="exportToPdf()">
        <i class="bi bi-file-earmark-pdf me-1"></i> Export to PDF
      </button>
    </div>
  </div>
  
  <!-- Memory Usage Warning -->
  <div class="alert alert-warning" *ngIf="hierarchicalData?.length > 10">
    <strong>Performance Mode:</strong> Showing limited data to optimize memory usage. 
    Large datasets are automatically truncated to improve browser performance.
  </div>
  
  <!-- No Data State -->
  <div *ngIf="!loading && !error && (!hierarchicalData || hierarchicalData.length === 0)" class="alert alert-info">
    No data found with the current filters. Try adjusting your filters or expanding your search criteria.
  </div>
  
  <!-- Main Report Content -->
  <div *ngIf="!loading && !error && hierarchicalData && hierarchicalData.length > 0" class="report-content">
    <!-- Project Groups with TrackBy -->
    <div *ngFor="let group of hierarchicalData; trackBy: trackByGroupId" class="card mb-4">
      <!-- Group Header -->
      <div class="card-header bg-light d-flex justify-content-between align-items-center cursor-pointer" 
           (click)="toggleGroup(group.id)">
        <div class="d-flex align-items-center">
          <i class="bi" 
             [ngClass]="isGroupExpanded(group.id) ? 'bi-dash-square' : 'bi-plus-square'"></i>
          <h4 class="ms-2 mb-0">{{ group.name }}</h4>
        </div>
        <div class="badge bg-primary rounded-pill">{{ group.summary?.totalProjects || 0 }} Projects</div>
      </div>
      
      <!-- Group Summary -->
      <div class="card-body bg-light border-bottom" *ngIf="isGroupExpanded(group.id)">
        <div class="row">
          <div class="col-md-3">
            <div class="fw-bold text-muted">Total Budget</div>
            <div class="fs-5">{{ formatCurrency(group.summary?.totalBudget || 0) }}</div>
          </div>
          <div class="col-md-3">
            <div class="fw-bold text-muted">Cash Distributed</div>
            <div class="fs-5">{{ formatCurrency(group.summary?.totalCashDistributed || 0) }}</div>
          </div>
          <div class="col-md-2">
            <div class="fw-bold text-muted">Progress</div>
            <div class="progress mt-2" style="height: 10px;">
              <div class="progress-bar bg-success" 
                   [style.width]="(group.summary?.progress || 0) + '%'" 
                   [attr.aria-valuenow]="group.summary?.progress || 0" 
                   aria-valuemin="0" 
                   aria-valuemax="100">
              </div>
            </div>
            <div class="small text-end mt-1">{{ formatPercentage(group.summary?.progress || 0) }}</div>
          </div>
          <div class="col-md-2">
            <div class="fw-bold text-muted">Beneficiaries</div>
            <div class="fs-5">{{ formatNumber(group.summary?.beneficiaries || 0) }}</div>
          </div>
          <div class="col-md-2">
            <div class="fw-bold text-muted">Activities</div>
            <div class="fs-5">{{ formatNumber(group.summary?.totalActivities || 0) }}</div>
          </div>
        </div>
      </div>
      
      <!-- Projects List with TrackBy -->
      <div class="projects-list" *ngIf="isGroupExpanded(group.id) && group.projects">
        <div *ngFor="let project of group.projects; trackBy: trackByProjectId" class="project-item border-bottom p-0">
          <!-- Project Header -->
          <div class="d-flex justify-content-between align-items-center p-3 cursor-pointer"
               [ngClass]="isProjectExpanded(project.id) ? 'bg-light-primary' : ''"
               (click)="toggleProject(project.id)">
            <div class="d-flex align-items-center">
              <i class="bi" 
                 [ngClass]="isProjectExpanded(project.id) ? 'bi-dash-circle' : 'bi-plus-circle'"></i>
              <div class="ms-2">
                <h5 class="mb-0">{{ project.code }}: {{ project.name }}</h5>
                <div class="small text-muted">{{ project.startDate | date:'mediumDate' }} - {{ project.endDate | date:'mediumDate' }}</div>
              </div>
            </div>
            <div class="badge" 
                 [ngClass]="{
                   'bg-success': project.status === 'completed',
                   'bg-primary': project.status === 'active',
                   'bg-warning': project.status === 'planned',
                   'bg-danger': project.status === 'suspended'
                 }">
              {{ project.status | titlecase }}
            </div>
          </div>
          
          <!-- Project Summary -->
          <div class="project-summary p-3 border-top border-bottom bg-light" *ngIf="isProjectExpanded(project.id)">
            <div class="row">
              <div class="col-md-3">
                <div class="fw-bold text-muted">Budget</div>
                <div class="fs-5">{{ formatCurrency(project.budget || 0) }}</div>
              </div>
              <div class="col-md-3">
                <div class="fw-bold text-muted">Cash Distributed</div>
                <div class="fs-5">{{ formatCurrency(project.cashDistributed || 0) }}</div>
              </div>
              <div class="col-md-2">
                <div class="fw-bold text-muted">Progress</div>
                <div class="progress mt-2" style="height: 8px;">
                  <div class="progress-bar bg-success" 
                       [style.width]="(project.progress || 0) + '%'" 
                       [attr.aria-valuenow]="project.progress || 0" 
                       aria-valuemin="0" 
                       aria-valuemax="100">
                  </div>
                </div>
                <div class="small text-end mt-1">{{ formatPercentage(project.progress || 0) }}</div>
              </div>
              <div class="col-md-2">
                <div class="fw-bold text-muted">Beneficiaries</div>
                <div class="fs-5">{{ formatNumber(project.summary?.beneficiaries || 0) }}</div>
              </div>
              <div class="col-md-2">
                <div class="fw-bold text-muted">Interventions</div>
                <div class="fs-5">{{ project.summary?.totalInterventions || 0 }}</div>
              </div>
            </div>
          </div>
          
          <!-- Interventions List with TrackBy -->
          <div class="interventions-list ps-4" *ngIf="isProjectExpanded(project.id) && project.interventions">
            <div *ngFor="let intervention of project.interventions; trackBy: trackByInterventionId" class="intervention-item border-bottom">
              <!-- Intervention Header -->
              <div class="d-flex justify-content-between align-items-center p-3 cursor-pointer"
                   [ngClass]="isInterventionExpanded(intervention.id) ? 'bg-light-success' : ''"
                   (click)="toggleIntervention(intervention.id)">
                <div class="d-flex align-items-center">
                  <i class="bi" 
                     [ngClass]="isInterventionExpanded(intervention.id) ? 'bi-dash-circle-fill' : 'bi-plus-circle-fill'"></i>
                  <div class="ms-2">
                    <h6 class="mb-0">{{ intervention.name }}</h6>
                    <div class="small text-muted">Category: {{ intervention.categoryName }}</div>
                  </div>
                </div>
                <div class="badge bg-info rounded-pill">{{ intervention.summary?.totalActivities || 0 }} Activities</div>
              </div>
              
              <!-- Intervention Summary -->
              <div class="intervention-summary p-3 border-top border-bottom bg-light-subtle" *ngIf="isInterventionExpanded(intervention.id)">
                <div class="row">
                  <div class="col-md-3">
                    <div class="fw-bold text-muted">Budget</div>
                    <div>{{ formatCurrency(intervention.summary?.totalBudget || 0) }}</div>
                  </div>
                  <div class="col-md-3">
                    <div class="fw-bold text-muted">Cash Distributed</div>
                    <div>{{ formatCurrency(intervention.summary?.totalCashDistributed || 0) }}</div>
                  </div>
                  <div class="col-md-2">
                    <div class="fw-bold text-muted">Progress</div>
                    <div class="progress mt-2" style="height: 6px;">
                      <div class="progress-bar bg-success" 
                           [style.width]="(intervention.summary?.progress || 0) + '%'" 
                           [attr.aria-valuenow]="intervention.summary?.progress || 0" 
                           aria-valuemin="0" 
                           aria-valuemax="100">
                      </div>
                    </div>
                    <div class="small text-end mt-1">{{ formatPercentage(intervention.summary?.progress || 0) }}</div>
                  </div>
                  <div class="col-md-2">
                    <div class="fw-bold text-muted">Beneficiaries</div>
                    <div>{{ formatNumber(intervention.summary?.beneficiaries || 0) }}</div>
                  </div>
                  <div class="col-md-2">
                    <div class="fw-bold text-muted">Activities</div>
                    <div>{{ intervention.summary?.totalActivities || 0 }}</div>
                  </div>
                </div>
                
                <!-- Intervention Description -->
                <div class="mt-3" *ngIf="intervention.description">
                  <div class="fw-bold text-muted">Description</div>
                  <p class="mb-0">{{ intervention.description }}</p>
                </div>
                
                <!-- Dynamic Indicators Button -->
                <div class="mt-3" *ngIf="hasDynamicColumns(intervention.activities)">
                  <button type="button" class="btn btn-sm btn-outline-primary" 
                          (click)="openCategoryModal(intervention.categoryId, intervention.categoryName)">
                    <i class="bi bi-graph-up me-1"></i>
                    View Dynamic Indicators
                  </button>
                </div>
              </div>
              
              <!-- Activities List - Only show if intervention is expanded and activities are loaded -->
              <div class="activities-table p-3" 
                   *ngIf="isInterventionExpanded(intervention.id) && shouldLoadActivities(intervention.id) && intervention.activities?.length > 0">
                <h6 class="mb-3">
                  Activities 
                  <span class="badge bg-secondary ms-2" *ngIf="intervention.activities.length >= maxActivitiesPerIntervention">
                    Showing {{ maxActivitiesPerIntervention }} of {{ intervention.activities.length }}
                  </span>
                </h6>
                
                <!-- Simplified Activities Table for Performance -->
                <div class="table-responsive">
                  <table class="table table-bordered table-hover">
                    <thead class="table-light">
                      <tr>
                        <th>ID</th>
                        <th>Status</th>
                        <th>Location</th>
                        <th>Progress</th>
                        <th>Budget</th>
                        <th>Cash Distributed</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let activity of getLimitedActivities(intervention.activities); trackBy: trackByActivityId">
                        <td>{{ activity.uniqueId }}</td>
                        <td>
                          <span class="badge" 
                                [ngClass]="{
                                  'bg-success': activity.status === 'completed',
                                  'bg-primary': activity.status === 'ongoing',
                                  'bg-warning': activity.status === 'planned',
                                  'bg-secondary': activity.status === 'archived',
                                  'bg-danger': activity.status === 'cancelled' || activity.status === 'suspended'
                                }">
                            {{ activity.status | titlecase }}
                          </span>
                        </td>
                        <td>{{ activity.location?.provinceName }}, {{ activity.location?.districtName }}</td>
                        <td>
                          <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-success" 
                                 [style.width]="(activity.progress || 0) + '%'" 
                                 [attr.aria-valuenow]="activity.progress || 0" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                            </div>
                          </div>
                          <div class="small text-end mt-1">{{ formatPercentage(activity.progress || 0) }}</div>
                        </td>
                        <td>{{ formatCurrency(activity.budget || 0) }}</td>
                        <td>{{ formatCurrency(activity.cashDistributed || 0) }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                
                <!-- Simplified Dynamic Columns Section -->
                <div class="dynamic-indicators-section" *ngIf="hasDynamicColumns(intervention.activities)">
                  <h6 class="mb-3">
                    Dynamic Indicators
                    <span class="badge bg-info ms-2">Limited View for Performance</span>
                  </h6>
                  
                  <!-- Show only limited dynamic columns -->
                  <div class="table-responsive" *ngIf="getDynamicColumnNames(intervention.activities).length > 0">
                    <table class="table table-bordered table-hover">
                      <thead class="table-light">
                        <tr>
                          <th>Activity</th>
                          <th *ngFor="let column of getDynamicColumnNames(intervention.activities).slice(0, 5); trackBy: trackByColumnName">
                            {{ column }}
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let activity of getLimitedActivities(intervention.activities).slice(0, 10); trackBy: trackByActivityId">
                          <td>{{ activity.uniqueId }}</td>
                          <td *ngFor="let columnName of getDynamicColumnNames(intervention.activities).slice(0, 5); trackBy: trackByColumnName">
                            {{ getActivityDynamicColumnValue(activity, columnName) }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  
                  <!-- Load More Button -->
                  <div class="text-center mt-3" *ngIf="intervention.activities.length > maxActivitiesPerIntervention">
                    <button type="button" class="btn btn-outline-primary btn-sm" 
                            (click)="openCategoryModal(intervention.categoryId, intervention.categoryName)">
                      View All {{ intervention.activities.length }} Activities with Full Indicators
                    </button>
                  </div>
                </div>
              </div>
              
              <!-- Load Activities Button -->
              <div class="text-center p-3" 
                   *ngIf="isInterventionExpanded(intervention.id) && !shouldLoadActivities(intervention.id)">
                <button type="button" class="btn btn-outline-primary" 
                        (click)="loadActivitiesForIntervention(intervention.id)">
                  <i class="bi bi-download me-1"></i>
                  Load Activities ({{ intervention.activities?.length || 0 }} items)
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Category Modal for Dynamic Indicators -->
<app-category-modal
  [isVisible]="categoryModalVisible"
  [categoryId]="selectedCategoryId"
  [dynamicColumns]="categoryDynamicColumns"
  [activities]="categoryActivities"
  (closeModal)="closeCategoryModal()">
</app-category-modal> 