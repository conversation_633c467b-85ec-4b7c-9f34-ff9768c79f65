import { ChangeDetector<PERSON><PERSON>, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { Subscription, forkJoin } from 'rxjs';
import { Project } from '../../../modules/admin/models/project.model';
import { AuthService } from '../../../modules/auth';
import { FilterDropdownList } from '../../../shared/components/filter-dropdown/filter-ddl.control';
import { ActivityStatus, Region } from '../../../shared/enums';
import { MessageService } from '../../../shared/services/message.service';
import { SharedService } from '../../../shared/services/shared.service';
import { compareSort } from '../../../shared/utilities';
import { IIndFilters, IIndicator, Indicator } from '../../models/indicator.model';
import { IndicatorService } from '../../services/indicator.service';
import { IndicatorFormComponent } from './indicator-form/indicator-form.component';

@Component({
    selector: 'indicators',
    templateUrl: './indicators.component.html',
    styleUrls: ['./indicators.component.scss']
})
export class IndicatorsComponent implements OnInit, OnDestroy {
    loading: boolean = false; working: boolean = false;
    isAdmin: boolean = false;

    indicators: IIndicator[] = [];
    selTab: 'formula' | 'performance' = 'performance';

    @ViewChild(IndicatorFormComponent, { static: true })
    private indFormComponent: IndicatorFormComponent;

    types: any[] = [];
    projects: Project[] = [];
    projGroups: any[] = [];
    orgs: any[] = [];
    submitStatus = [{ id: 0, name: 'Draft' }, { id: 1, name: 'Submitted' }, { id: 2, name: 'Approved' }];
    regions = [];

    filters: IIndFilters = {
        dataStatus: [1, 2]
    };
    filtered: boolean = false;

    @ViewChild('type')
    private typeFilterCtrl: FilterDropdownList;
    @ViewChild('group')
    private groupFilterCtrl: FilterDropdownList;
    //@ViewChild('project')
    //private projFilterCtrl: FilterDropdownList;
    @ViewChild('partner')
    private partnerFilterCtrl: FilterDropdownList;
    @ViewChild('status')
    private dStatusFilterCtrl: FilterDropdownList;
    @ViewChild('region')
    private regionFilterCtrl: FilterDropdownList;

    private subscriptions: Subscription[] = [];
    constructor(
        private authService: AuthService,
        private indService: IndicatorService,
        private sharedService: SharedService,
        private messageService: MessageService,
        private cdr: ChangeDetectorRef
    ) {
        this.isAdmin = this.authService.currentUserValue.roles.includes('Admin');

        Object.values(Region)
            .forEach((v, i) => {
                if ((typeof v === 'string' && v !== 'National'))
                    this.regions.push({ id: i, name: v });
            });
        this.regions.sort((x, y) => x.name < y.name ? -1 : 1);
    }

    ngOnInit(): void {
        if (this.isAdmin)
            this.selTab = 'formula';

        this.getDropdownItems();
    }

    // get projects, partners
    private getDropdownItems(): void {
        this.loading = true;

        let endPoints: any = {
            projs: this.sharedService.getProjectsWithGrouping(),
            orgs: this.sharedService.getOrgsList()
        };

        if (this.isAdmin)
            endPoints = { ...endPoints, 'cols': this.indService.getNumericColumns() };

        this.subscriptions.push(
            forkJoin(endPoints).subscribe({
                next: ({ projs, orgs, cols }) => {
                    this.projects = projs;
                    this.projects.forEach(proj => {
                        if (proj.grouping) {
                            if (this.projGroups.findIndex(pg => pg.id === proj.grouping) === -1)
                                this.projGroups.push({ id: proj.grouping, name: proj.grouping });
                        }
                    });
                    this.projGroups.sort((x, y) => compareSort(x.name, y.name));

                    orgs.forEach(org => {
                        this.orgs.push({
                            id: org.id,
                            name: org.shortName
                        });
                    });

                    if (this.isAdmin)
                        this.indFormComponent.numericColumns = cols;
                },
                error: (err) => {
                    console.log(err);
                    this.loading = false;
                },
                complete: () => {
                    if (this.isAdmin) {
                        this.indFormComponent.projects = this.projects;
                        this.indFormComponent.projGroups = this.projGroups;
                        this.indFormComponent.orgs = this.orgs;
                        this.indFormComponent.regions = this.regions;
                    }

                    this.loading = false;
                    this.getIndicators();
                }
            })
        );
    }

    private getIndicators(): void {
        this.working = true;

        if ($.fn.dataTable.isDataTable('#table_indicators'))
            $('#table_indicators').DataTable({ retrieve: true }).destroy();

        this.subscriptions.push(
        this.indService.getIndicators(this.filters).subscribe({
            next: (inds) => {
                this.indicators = inds;
            },
            error: (e) => {
                console.log(e);
                this.working = false;
            },
            complete: () => {
                // build formula text expressions
                this.indicators.forEach(ind => {
                    ind.f1Text = ind.formula1;
                    ind.f2Text = ind.formula2;
                    ind.f3Text = ind.formula3;
                    ind.f4Text = ind.formula4;

                    // types
                    if (this.types.findIndex(t => t.id === ind.typeName) === -1)
                        this.types.push({ id: ind.typeName, name: ind.typeName });
                    this.types.sort((x, y) => compareSort(x.name, y.name));

                    // replace filter ids with values
                    if (ind.dataFilters)
                        ind.filtersApplied = this.getFilterValues(ind.dataFilters);
                });
                
                if (this.isAdmin) {
                    this.indFormComponent.numericColumns.forEach(col => {
                        const varId = `[${col.interventionId}_${col.id}]`;
                        this.indicators.forEach(ind => {
                            if (ind.f1Text?.indexOf(varId) > -1)
                                ind.f1Text = ind.f1Text.split(varId).join(col.name);
                            if (ind.f2Text?.indexOf(varId) > -1)
                                ind.f2Text = ind.f2Text.split(varId).join(col.name);
                            if (ind.f3Text?.indexOf(varId) > -1)
                                ind.f3Text = ind.f3Text.split(varId).join(col.name);
                            if (ind.f4Text?.indexOf(varId) > -1)
                                ind.f4Text = ind.f4Text.split(varId).join(col.name);
                        });
                    });
                }

                this.cdr.detectChanges();
                this.bindDataTable();
                this.working = false;
            }
        }));
    }

    // replaces filter ids with values
    private getFilterValues(filtersApplied: any): string {
        let dataFilters = JSON.parse(filtersApplied) as IIndFilters;
        filtersApplied = [];

        if (dataFilters['ProjIds'])
            dataFilters.projIds = dataFilters['ProjIds'];
        if (dataFilters['OrgIds'])
            dataFilters.orgIds = dataFilters['OrgIds'];
        if (dataFilters['Regions'])
            dataFilters.regions = dataFilters['Regions'];
        if (dataFilters['ActStatus'])
            dataFilters.actStatus = dataFilters['ActStatus'];

        // projects
        if (dataFilters.projIds?.length) {
            let fApplied = this.projects.filter(p =>
                dataFilters.projIds.includes(p.id))
                .map(p => p.abbreviation);
            
            fApplied = fApplied.length === this.projects.length
                ? ['All'] : fApplied;
            filtersApplied.push(`<span class="text-gray-600">Project:</span> ${fApplied.join(', ')}`);
        }

        // partners
        if (dataFilters.orgIds?.length) {
            let fApplied = this.orgs.filter(o =>
                dataFilters.orgIds.includes(o.id))
                .map(o => o.name);
            
            fApplied = fApplied.length === this.orgs.length
                ? ['All'] : fApplied;
            filtersApplied.push(`<span class="text-gray-600">Partner:</span> ${fApplied.join(', ')}`);
        }

        // regions
        if (dataFilters.regions?.length) {
            let fApplied = this.regions.filter(r =>
                dataFilters.regions.includes(r.id))
                .map(r => r.name);

            fApplied = fApplied.length === this.regions.length
                ? ['All'] : fApplied;
            filtersApplied.push(`<span class="text-gray-600">Region:</span> ${fApplied.join(', ')}`);
        }

        // activity status
        //if (dataFilters.actStatus?.length) {
        //    let fApplied = [];
        //    dataFilters.actStatus.forEach(s => fApplied.push(ActivityStatus[s]));

        //    fApplied = fApplied.length === 4 ? ['All'] : fApplied;
        //    filtersApplied.push(`<span class="text-gray-600">Activity status:</span> ${fApplied.join(', ')}`);
        //}

        filtersApplied = filtersApplied.join('<br/>');
        return filtersApplied ? `<span class="text-primary">Filters: [</span>${filtersApplied}<span class="text-primary">]</span>` : '';
    }

    private bindDataTable(): void {
        let groups = [];
        let rowSpans = [1, 1];

        $('#table_indicators').DataTable({
            retrieve: false,
            pageLength: 25,
            order: [[2, 'asc'], [3, 'asc'], [4, 'asc']],
            autoWidth: false,
            dom: "Bt<'row'<'col-3'i><'col-9'p>>",
            buttons: [{
                extend: 'excelHtml5',
                className: 'd-none',
                filename: 'aims3-' + this.selTab + '-indicators',
                title: 'AIMS3 Variables',
                exportOptions: {
                    columns: ':not(.text-end)'
                }
            }],
            rowCallback: (row: Node, data: any, index: number, indexFull?: number) => {
                row.childNodes[0].textContent = `${indexFull + 1}`;

                // merge three cols after index and hidden cols
                for (let i = 2; i <= 3; i++) {
                    if (index === 0) {
                        groups[i] = row.childNodes[i] as HTMLTableCellElement;
                        rowSpans[i] = 1;
                        groups[i].classList.remove('d-none');
                        groups[i].removeAttribute('rowspan');
                    } else {
                        const td = row.childNodes[i] as HTMLTableCellElement;
                        if (groups[i].dataset['contentId'] === td.dataset['contentId']) {
                            rowSpans[i]++;
                            td.classList.add('d-none');
                            groups[i].setAttribute('rowspan', rowSpans[i]);
                        } else {
                            rowSpans[i] = 1;
                            td.classList.remove('d-none');
                            td.removeAttribute('rowspan');
                            groups[i] = row.childNodes[i];
                        }
                    }
                }
            }
        } as DataTables.Settings);
        $('#table_indicators th').off('click', this.onSort).on('click', (e) => this.onSort(e));
    }

    onSelTab(type: any): void {
        if (this.selTab === type)
            return;
        this.selTab = type;
    }

    onSearch(e: any): void {
        $('#table_indicators').DataTable({ retrieve: true })
        .search(e.target.value || '').draw();
    }

    onSort(e: any): void {
        const target = e.target as HTMLElement;
        if (target.hasAttribute('data-orderable'))
            return;

        if (target.classList.contains('sorting_desc') && !target.classList.contains('noSort')) {
            target.classList.add('noSort');
            return;
        }

        if (target.classList.contains('noSort')) {
            setTimeout(() => {
                $('#table_indicators').DataTable({
                    retrieve: true
                }).order([1, 'asc']).draw();
                target.classList.remove('noSort');
            }, this.indicators.length * 2);
        }
    }

    onFilterChange(ctrl): void {
        if (ctrl.id === 'period' || ctrl.id === 'year')
            this.filters[ctrl.id] = ctrl.selVals[0];
        else
            this.filters[ctrl.id] = ctrl.selVals;
    }

    onPeriodChange(e: any): void {
        const target = e.target as HTMLInputElement;
        const date = target.value?.split('-');

        if (target.id === 'periodFrom') {
            if (!date) {
                this.filters.period = 0;
                this.filters.year = 0;
            } else {
                this.filters.period = +date[1];
                this.filters.year = +date[0];
            }
        } else {
            if (!date) {
                this.filters.periodEnd = 0;
                this.filters.yearEnd = 0;
            } else {
                this.filters.periodEnd = +date[1];
                this.filters.yearEnd = +date[0];
            }
        }
    }

    resetFilters(): void {
        this.typeFilterCtrl.clearSelection();
        this.groupFilterCtrl.clearSelection();
        //this.projFilterCtrl.clearSelection();
        this.partnerFilterCtrl.clearSelection();
        this.dStatusFilterCtrl.setSelectedValues([1,2]);
        this.regionFilterCtrl.clearSelection();

        const pFrom = document.querySelector('#periodFrom') as HTMLInputElement;
        if (pFrom)
            pFrom.value = '';
        const pTo = document.querySelector('#periodTo') as HTMLInputElement;
        if (pTo)
            pTo.value = '';

        this.filters = { dataStatus: [1,2] };

        // reapply filter
        this.getIndicators();
        this.filtered = false;
    }

    onFilterData(): void {
        if (this.filters.period > 0 && this.filters.year > 0 &&
            this.filters.periodEnd > 0 && this.filters.yearEnd > 0) {
            const date = new Date(this.filters.year, this.filters.period - 1, 1);
            const dateEnd = new Date(this.filters.yearEnd, this.filters.periodEnd - 1, 1);

            if (dateEnd < date) {
                this.messageService.error('<span class="fw-semibold">Period (To)</span> should be greater ' +
                    'than <span class="fw-semibold">Period (From)</span>.', 'Invalid period', { enableHtml: true });
                return;
            }
        }

        // reapply filter
        this.getIndicators();
        this.filtered = true;
    }

    onAdd(order?: number): void {
        this.indFormComponent.indicator = new Indicator(
            0, '', '', '',
            order > 0 ? (order+1) * -1 : this.indicators.length + 1
        );

        this.indFormComponent.ngOnInit(true);
    }

    onEdit(ind: IIndicator): void {
        this.indFormComponent.indicator = new Indicator(ind.id, ind.projectGroups,
            ind.typeName,ind.name, ind.order, ind.formula1, ind.f1Info,
            ind.formula2, ind.f2Info, ind.formula3, ind.f3Info,
            ind.formula4, ind.f4Info, ind.dataFilters
        );

        this.indFormComponent.ngOnInit(true);
    }

    onDone(ind: IIndicator): void {
        if ($.fn.dataTable.isDataTable('#table_indicators'))
            $('#table_indicators').DataTable({ retrieve: true }).destroy();

        // build/rebuild formula text
        ind.f1Text = ind.formula1;
        ind.f2Text = ind.formula2;
        ind.f3Text = ind.formula3;
        ind.f4Text = ind.formula4;

        this.indFormComponent.numericColumns.forEach(col => {
            const varId = `[${col.interventionId}_${col.id}]`;
            if (ind.f1Text?.indexOf(varId) > -1)
                ind.f1Text = ind.f1Text.split(varId).join(col.name);
            if (ind.f2Text?.indexOf(varId) > -1)
                ind.f2Text = ind.f2Text.split(varId).join(col.name);
            if (ind.f3Text?.indexOf(varId) > -1)
                ind.f3Text = ind.f3Text.split(varId).join(col.name);
            if (ind.f4Text?.indexOf(varId) > -1)
                ind.f4Text = ind.f4Text.split(varId).join(col.name);
        });

        // add to types or groups, if new
        if (this.types.findIndex(t => t.id === ind.typeName) === -1)
            this.types.push({ id: ind.typeName, name: ind.typeName });

        // replace filter ids with values
        if (ind.dataFilters)
            ind.filtersApplied = this.getFilterValues(ind.dataFilters);

        const indIndex = this.indicators.findIndex(i => i.id === ind.id);
        if (indIndex > -1)
            this.indicators[indIndex] = ind;
        else
            this.indicators.push(ind);

        // replace all filters to match the added/updated type
        let newUpdFilters: IIndFilters = JSON.parse(ind.dataFilters);
        this.indicators.forEach(_ind => {
            if (_ind.typeName === ind.typeName) {
                const filters: IIndFilters = JSON.parse(_ind.dataFilters);
                newUpdFilters.actStatus = filters.actStatus;
                _ind.dataFilters = JSON.stringify(newUpdFilters);
                _ind.filtersApplied = ind.filtersApplied;
            }
        });

        this.cdr.detectChanges();
        this.bindDataTable();
    }

    onDelete(indId: number): void {
        this.messageService.confirmMessage('Confirm Delete', `Are you sure you want to delete this indicator?`,
            () => {
                this.working = true;
                if ($.fn.dataTable.isDataTable('#table_indicators'))
                    $('#table_indicators').DataTable({ retrieve: true }).destroy();

                this.subscriptions.push(
                    this.indService.deleteIndicator(indId).subscribe({
                        next: () => {
                            this.indicators = this.indicators.filter(i => i.id !== indId);
                        },
                        error: (err) => {
                            this.working = false;
                            console.error(err);
                        },
                        complete: () => {
                            this.cdr.detectChanges();
                            this.bindDataTable();
                            this.messageService.success('The indicator has been deleted successfully.');
                            this.working = false;
                        }
                    })
                );
            }, true, 'Delete');
    }

    onDownloadTable(): void {
        const btnDl: HTMLButtonElement = document.querySelector('.dt-buttons button:first-child');
        if (btnDl)
            btnDl.click();
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}