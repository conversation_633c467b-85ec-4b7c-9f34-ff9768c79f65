<!--begin::Header container-->
<div class="app-container" id="qs_app_header_container" [ngClass]="headerContainerCssClass">
    <ng-container *ngIf="appSidebarDefaultCollapseDesktopEnabled">
        <div class="app-sidebar-collapse-d-flex align-items-center me-3">
            <!--begin::sidebar toggle-->
            <div class="btn btn-icon w-auto px-0 btn-color-muted btn-active-icon-primary" data-qs-toggle="true"
                 data-qs-toggle-target="body" data-qs-toggle-mode="on" data-qs-toggle-name="app-sidebar-collapse">
                <span [inlineSVG]="'./assets/media/icons/duotune/arrows/arr080.svg'" class="svg-icon svg-icon-1"></span>
            </div>
            <!--end::sidebar toggle-->
        </div>
    </ng-container>

    <ng-container *ngIf="appSidebarDisplay">
        <!--begin::sidebar mobile toggle-->
        <div class="d-flex align-items-center d-lg-none ms-n2 me-2" title="Show sidebar menu">
            <div class="btn btn-icon btn-active-color-primary w-35px h-35px" id="qs_app_sidebar_mobile_toggle">
                <span [inlineSVG]="'./assets/media/icons/duotune/abstract/abs015.svg'" class="svg-icon svg-icon-1"></span>
            </div>
        </div>
        <!--end::sidebar mobile toggle-->
        <!--begin::Mobile logo-->
        <div class="d-flex align-items-center flex-grow-1 flex-lg-grow-0">
            <a routerLink="/dashboard" class="d-lg-none">
                <img alt="Logo" src="./assets/media/logos/aims-logo.svg" class="app-sidebar-logo-default" />
            </a>
        </div>
        <!--end::Mobile logo-->
    </ng-container>

    <ng-container *ngIf="!appSidebarDisplay">
        <!--begin::Logo-->
        <div class="d-flex align-items-center flex-grow-1 flex-lg-grow-0 me-lg-15">
            <a routerLink="/dashboard">
                <ng-container *ngIf="currentLayoutType === 'dark-header'">
                    <img alt="Logo" src="./assets/media/logos/aims-logo-white.svg"
                         class="h-20px h-lg-30px app-sidebar-logo-default" />
                </ng-container>
                <ng-container *ngIf="currentLayoutType !== 'dark-header'">
                    <img alt="Logo" src="./assets/media/logos/aims-logo.svg"
                         class="h-20px h-lg-30px app-sidebar-logo-default theme-light-show" />
                    <img alt="Logo" src="./assets/media/logos/aims-logo-white.svg"
                         class="h-20px h-lg-30px app-sidebar-logo-default theme-dark-show" />
                </ng-container>
            </a>
        </div>
        <!--end::Logo-->
    </ng-container>

    <!--begin::Header wrapper-->
    <div class="d-flex align-items-stretch justify-content-between flex-lg-grow-1" id="qs_app_header_wrapper">
        <ng-container *ngIf="appHeaderDefaultContent === 'menu' && appHeaderDefaulMenuDisplay">
            <!--begin::Menu wrapper-->
            <div class="app-header-menu app-header-mobile-drawer align-items-stretch"
                 data-qs-drawer="true" data-qs-drawer-name="app-header-menu" data-qs-drawer-activate="{default: true, lg: false}"
                 data-qs-drawer-overlay="true" data-qs-drawer-width="225px" data-qs-drawer-direction="end"
                 data-qs-drawer-toggle="#qs_app_header_menu_toggle" data-qs-swapper="true"
                 data-qs-swapper-mode="{default: 'append', lg: 'prepend'}"
                 data-qs-swapper-parent="{default: '#qs_app_body', lg: '#qs_app_header_wrapper'}">
                <!--begin::Menu-->
                <app-header-menu id="qs_app_header_menu" class="menu menu-rounded menu-column menu-lg-row my-5 my-lg-0 align-items-stretch fw-semibold px-2 px-lg-0"
                                 data-qs-menu="true"></app-header-menu>
                <!--end::Menu-->
            </div>
            <!--end::Menu wrapper-->
        </ng-container>

        <ng-container *ngIf="appHeaderDefaultContent === 'page-title' && appPageTitleDisplay else noPageTitle">
            <app-page-title #qsPageTitle class="page-title d-flex"></app-page-title>
        </ng-container>
        <ng-template #noPageTitle><div class="d-flex"></div></ng-template>
        <app-navbar class="app-navbar flex-shrink-0" [appHeaderDefaulMenuDisplay]="appHeaderDefaulMenuDisplay"
                    [isRtl]="false"></app-navbar>
    </div>
    <!--end::Header wrapper-->
</div>
<!--end::Header container-->