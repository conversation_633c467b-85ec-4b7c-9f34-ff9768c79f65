import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'fileSize' })
export class FileSizePipe implements PipeTransform {
    transform(fileSize: number): string {
        if (!fileSize) return '';

        let size: string = fileSize + ' B';
        if (fileSize >= 1023 && fileSize <= 1048576)
            size = Math.round(fileSize / 1024) + ' KB';
        else if (fileSize > 1048576 && fileSize <= 1073741824)
            size = Math.round(fileSize / 1048576) + ' MB';

        return size;
    }
}