#aims-grid {
    height: calc(100vh - 171px);
}
.project-filter {
  display: flex;
  flex: 0 0 auto;
  align-items: center;
  width: 35%;
  height: 32px;
  border: 2px solid var(--qs-primary);
  border-radius: 0.375rem;

  .select2-selection--single {
    border: none !important;
    background-color: transparent;
    padding-top: 0 !important;
    padding-bottom: 0 !important;
    font-size: 1.2rem;
  }
}
.menu-sub {
  z-index: 101;
  background-color: var(--qs-menu-dropdown-bg-color);
  box-shadow: var(--qs-menu-dropdown-box-shadow);
}
.col-5 {
  .is-loading {
    margin-left: 1.25rem;
  }
}
.page-toolbar {
  position: relative;
  margin-top: -50px;
  padding-top: 10px;
  padding-bottom: 2px;
  z-index: 100;

  @media (min-width: 990px) {
    margin-left: 1em;
    margin-right: 13em;
  }

  @media (max-width: 990px) {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .page-toolbar-container {
    padding-left: 1rem;
    padding-right: 1.5rem;

    @media (min-width: 990px) {
      border-right: 1px solid var(--qs-text-gray-200);
    }
  }

  .select2-selection__placeholder {
    color: var(--qs-text-gray-800) !important;
  }

  .select2-selection__rendered {
    color: var(--qs-text-gray-100) !important;
  }
}
.grid-panel {
    button { line-height: normal; }
}
.filter-control {
  .text-muted {
      color: var(--qs-text-gray-800) !important;
  }
}
.btn-link {
  color: var(--qs-text-muted);
  cursor: pointer;

  &.btn-reset {
    color: var(--qs-text-gray-800);
    min-width: 80px;
  }

  &:hover,&.btn-reset:hover {
    color: var(--qs-text-primary) !important;
  }
}

.nav.nav-tabs:not(.nav-line-tabs) {
  max-height: 34px;
  overflow: hidden;

  .btn-add-new {
    padding: 0.6rem 0.75rem 0.5rem 0.75rem;
    color: var(--qs-text-primary);
    border-bottom: 0;
    min-width: 100px;
    cursor: pointer;

    .nav-icon {
      padding: 0 6px 0 4px;
      width: auto;

      i {
        color: var(--qs-text-primary);
      }
    }
  }

  .btn-add-new:hover {
    background-color: var(--qs-primary-light);
    /*transition: background-color .5s ease, border-color .5s ease, box-shadow .5s ease, -webkit-box-shadow .5s ease;*/
  }

  &.border-radius {
    border-radius: 0 var(--bs-card-border-radius) 0 0;
  }
}

html[data-theme="light"] {
  app-header, .card-header-custom, .pivot-panel, .pivot-panel .card-header, .bg-color {
    transition: background-color 1s ease;

    &.bg-color-1 {
      background-color: #1f4a4c;
    }

    &.bg-color-2 {
      background-color: #2b6dbd;
    }

    &.bg-color-3 {
      background-color: #355C7D;
    }

    &.bg-color-4 {
      background-color: #006C84;
    }

    &.bg-color-5 {
      background-color: #7a39f3;
    }

    &.bg-color-6 {
      background-color: #cc4200;
    }

    &.bg-color-7 {
      background-color: #5F264A;
    }

    &.bg-color-8 {
      background-color: #3A1078;
    }

    &.bg-color-9 {
      background-color: #790252;
    }

    &.bg-color-10 {
      background-color: #0F4C75;
    }
  }

  [class*="bg-color"] {
    .page-toolbar-container {
      @media (min-width: 990px) {
        border-right: 1px solid var(--qs-text-muted);
      }
    }

    .page-heading {
      color: white;
    }

    .form-select {
      color: var(--qs-text-gray-300) !important;
    }

    .project-filter {
      border: none;

      .form-select {
        &:hover {
          background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23E4E6EF' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e")
        }
      }
    }

    .filter-control {
      .text-muted {
        color: var(--qs-text-gray-300) !important;
      }

      .form-select {
        border: 0;
        background-color: rgba(0,0,0,.05);

        &:hover, &:focus {
          background-color: rgba(0,0,0,.1);
          background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3e%3cpath fill='none' stroke='%23E4E6EF' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m2 5 6 6 6-6'/%3e%3c/svg%3e")
        }
      }
    }

    .btn-reset {
      color: var(--qs-text-gray-300);
    }

    .nav.nav-tabs {
      background-color: rgba(0,0,0,0.1);
    }

    .nav-item:after {
      border-right-color: var(--qs-text-muted) !important;
    }

    .nav-link {
      border: 0;

      .nav-title, .nav-icon i {
        color: var(--qs-text-gray-300) !important;

        &:hover, .nav-text:hover {
          color: var(--qs-text-gray-200) !important;
        }
      }

      &:hover {
        background-color: rgba(0,0,0,.15) !important;
        color: var(--qs-text-gray-200) !important;
      }
    }

    .nav-link:not(.active) {
      .nav-text {
        color: var(--qs-text-gray-300) !important;
      }
    }
  }
}

  .card-stretch-2 {
    border-radius: 0;
    /*margin: 25px -30px 0 -30px;*/
  }

  .card-header-custom {
    padding: 10px 0 0 0;
  }

  .card-body.card-body-custom {
    padding: 0;
  }

.angular-editor-textarea {
  color: var(--qs-input-color);
  background-color: var(--qs-input-bg);
  border: 1px solid var(--qs-input-border-color);
  box-shadow: none !important;
  min-height: calc(1.5em + 1rem + 2px);
  padding: 0.5rem 0.65rem;
  font-size: 0.925rem;
  border-radius: 0.375rem;
}
.angular-editor-textarea:focus-visible {
  outline: 1px solid var(--qs-input-focus-border-color);
}

.pivot-panel {
  height: calc(100vh - 80px);

  .card {
    height: 100%;
    margin-top: 5px;
    padding-top: 5px;
    border-radius: 0;

    .card-header {
      padding: .65rem 1rem .25rem 1rem;
      min-height: 33px;

      h6 {
        padding: 0;
        margin: 0;
      }

      .card-toolbar {
        margin: 0;
        padding: 0;
        align-items: initial;

        button {
          height: 18px;
          width: 18px;
        }
      }
    }

    .card-body {
      background-color: var(--qs-card-bg);
      padding: .5rem 1rem;
      border-bottom: 1px solid #eee;
      margin-bottom: 5px;
    }
  }
}
.pivot-panel {
  &[class*="bg-color"] {
    .card {
      background-color: transparent;
    }

    .card-header {
      h6 {
        color: var(--qs-text-gray-300);
      }

      border-radius: var(--bs-card-border-radius) 0 0 0;
      background-color: rgba(0,0,0,.1);
    }
  }
}
