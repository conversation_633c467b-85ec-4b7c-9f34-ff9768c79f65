import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'fileExt' })
export class FileExtPipe implements PipeTransform {
    transform(fn: string): string {
        let c = /(?:\.([^.]+))?$/.exec(fn)[1];
        if (c) {
            c = c.toLowerCase();
            if (c.indexOf('pdf') > -1)
                return 'fa-file-pdf';
            else if (c.indexOf('xls') > -1)
                return 'fa-file-excel';
            else if (c.indexOf('doc') > -1)
                return 'fa-file-word';
            else if (c.indexOf('ppt') > -1 || c.indexOf('pps') > -1)
                return 'fa-file-powerpoint';
            else if (c.indexOf('jpg') > -1 || c.indexOf('jpeg') > -1 || c.indexOf('png') > -1)
                return 'fa-file-image'
            else if (c.indexOf('zip') > -1)
                return 'fa-file-archive'
            else
                return 'fa-file';
        }
    }
}