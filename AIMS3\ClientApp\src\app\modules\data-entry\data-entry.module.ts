import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DataTablesModule } from 'angular-datatables';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { AuthorizationPipe } from '../../shared/pipes/auth.pipe';
import { SharedModule } from '../../shared/shared.module';
import { ModalsModule } from '../../_theme/partials';
import { AimsGridModule } from '../aims-grid/aims-grid.module';
import { AimsPivotModule } from '../pivot/pivot.module';
import { ProfilesComponent } from './components/interventions/profiles.component';
import { InventoryFormComponent } from './components/inventory/inventory-form/inventory-form.component';
import { InventoryComponent } from './components/inventory/inventory.component';
import { InterventionProfileFormComponent } from './components/main/intervention-form/profile-form.component';
import { DataEntryMainComponent } from './components/main/main.component';
import { DataEntryRoutingModule } from './data-entry-routing.module';
import { DataService } from './services/data.service';
import { InventoryService } from './services/inventory.service';
import { ProfileService } from './services/profile.service';

@NgModule({
    declarations: [
        InventoryComponent,
        InventoryFormComponent,
        ProfilesComponent,
        DataEntryMainComponent,
        InterventionProfileFormComponent
    ],
    imports: [
        FormsModule,
        ReactiveFormsModule,
        DataEntryRoutingModule,
        SharedModule,
        InlineSVGModule,
        DataTablesModule,
        ModalsModule,
        AimsGridModule,
        AimsPivotModule
    ],
    providers: [
        InventoryService,
        ProfileService,
        AuthorizationPipe,
        DataService
    ]
})
export class DataEntryModule { }