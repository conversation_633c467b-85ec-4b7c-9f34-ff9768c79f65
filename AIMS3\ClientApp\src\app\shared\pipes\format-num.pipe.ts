import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'formatNum' })
export class FormatNumPipe implements PipeTransform {
    transform(num: any, round?: boolean, decPlaces?: number): any {
        if (!num || num === undefined || num.length === 0) return '';
        if (!isNaN(num)) {
            if (round)
                num = Math.round(+num);
            num = (+num).toFixed(decPlaces || 6).replace(/[.,]0+$/, '');
        }
        return num.toString().replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,");
    }
}