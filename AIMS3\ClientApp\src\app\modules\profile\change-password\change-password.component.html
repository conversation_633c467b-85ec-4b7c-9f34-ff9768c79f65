<div class="card card-custom card-stretch">
    <!--begin::Form-->
    <form id="change_password" class="form" [formGroup]="form" (ngSubmit)="form.valid && change()">
        <!--begin::Header-->
        <div class="card-header">
            <div class="card-title align-items-start flex-column">
                <h3 class="card-label fw-bolder fs-3 mb-1">Change password</h3>
                <span class="text-muted fw-bold fs-7">Change your current password</span>
            </div>
            <div class="card-toolbar">
                <button type="submit" class="btn btn-primary" [disabled]="!form.valid || f.password.value !== f.cPassword.value || working">
                    <span class="indicator-label" *ngIf="!working; else btnSpinner">Change</span>
                    <ng-template #btnSpinner>
                        <span class="indicator-progress" style="display: block">
                            Please wait...
                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                        </span>
                    </ng-template>
                </button>
            </div>
        </div>
        <!--end::Header-->
        <!--begin::Body-->
        <div class="card-body">
            <div class="form-group row">
                <label class="col-xl-3 col-lg-3 col-form-label required">Current password</label>
                <div class="col-lg-4 col-xl-3">
                    <input name="currentPassword" class="form-control" type="password" placeholder="Current password" formControlName="currentPass"
                           [ngClass]="{ 'is-invalid': form.controls['currentPass'].dirty && form.controls['currentPass'].invalid }" />
                    <ng-container [ngTemplateOutlet]="formError"
                                  [ngTemplateOutletContext]="{
                                    validation: 'required',
                                    message: 'Current password is required.',
                                    control: form.controls['currentPass']
                                  }"></ng-container>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-xl-3 col-lg-3 col-form-label required">New password</label>
                <div class="col-lg-4 col-xl-3">
                    <input name="newPassword" class="form-control" type="password" placeholder="New password" formControlName="password"
                           [ngClass]="{ 'is-invalid': form.controls['password'].dirty && form.controls['password'].invalid }" minlength="5" />
                    <ng-container [ngTemplateOutlet]="formError"
                                  [ngTemplateOutletContext]="{
                                    validation: 'required',
                                    message: 'New password is required.',
                                    control: form.controls['password']
                                  }"></ng-container>
                    <ng-container [ngTemplateOutlet]="formError"
                                  [ngTemplateOutletContext]="{
                                    validation: 'minLength',
                                    message: 'Password must have at least 5 characters.',
                                    control: form.controls['password']
                                  }"></ng-container>
                </div>
            </div>
            <div class="form-group row">
                <label class="col-xl-3 col-lg-3 col-form-label required">Confirm password</label>
                <div class="col-lg-4 col-xl-3">
                    <input name="newPassword" class="form-control" type="password" placeholder="Confirm password" formControlName="cPassword"
                           [ngClass]="{ 'is-invalid': form.controls['cPassword'].dirty && form.controls['cPassword'].invalid }" />
                    <ng-container [ngTemplateOutlet]="formError"
                                  [ngTemplateOutletContext]="{
                                    validation: 'required',
                                    message: 'Confirm Password is required.',
                                    control: form.controls['cPassword']
                                  }"></ng-container>

                    <ng-container *ngIf="form.controls['cPassword'].dirty && form.controls['password'].value !== form.controls['cPassword'].value">
                        <div class="fv-plugins-message-container">
                            <div class="fv-help-block">
                                'New passsword' and 'Confirm password' didn't match.
                            </div>
                        </div>
                    </ng-container>
                </div>
            </div>
        </div>
        <!--end::Body-->
    </form>
    <!--end::Form-->
</div>


<ng-template #formError let-control="control" let-message="message" let-validation="validation">
    <ng-container *ngIf="control.hasError(validation) && control.dirty">
        <div class="fv-plugins-message-container">
            <div class="fv-help-block">
                <span role="alert">{{ message }}</span>
            </div>
        </div>
    </ng-container>
</ng-template>