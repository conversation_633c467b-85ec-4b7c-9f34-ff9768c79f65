<div class="top-toolbar">
    <div class="d-flex flex-stack gap-3">
        <div class="text-center" tabindex="0">
            <input id="dtProgress" type="radio" class="btn-check" name="dataType" value="Progress" checked="checked"
                   (change)="onDataTypeChange()">
            <label for="dtProgress" class="btn btn-outline btn-outline-dashed btn-active-light-primary d-flex align-items-center px-4 py-2">
                <span class="fw-semibold d-block fs-6">Progress</span>
            </label>
        </div>
        <div class="text-center" tabindex="-1">
            <input id="dtTarget" type="radio" class="btn-check" name="dataType" value="Target" (change)="onDataTypeChange(true)">
            <label for="dtTarget" class="btn btn-outline btn-outline-dashed btn-active-light-info d-flex align-items-center px-4 py-2"
                   [ngClass]="{'border-info':filters.isTarget}">
                <span class="fw-semibold d-block fs-6">Target</span>
            </label>
        </div>
        <label class="form-check form-switch form-check-custom form-check-solid cursor-pointer ms-3" *ngIf="isGlobalUser">
            <input class="form-check-input" type="checkbox" (change)="onApprovalMode()">
            <span class="form-check-label fw-semibold" [ngClass]="{'text-primary':filters.approvalMode, 'text-muted': !filters.approvalMode}">Approval mode</span>
        </label>
        <label class="form-check form-switch form-check-custom form-check-solid cursor-pointer ms-3" *ngIf="!filters.isTarget">
            <input class="form-check-input" type="checkbox" [checked]="hideNAPeriods" (change)="onToggleNAPeriods()">
            <span class="form-check-label fw-semibold" [ngClass]="{'text-primary':hideNAPeriods, 'text-muted': !hideNAPeriods}">Hide empty progress (ongoing activities)</span>
        </label>
        <div class="notice bg-light-primary rounded border-primary border border-dashed text-truncate p-2 ms-5 fs-8"
             style="max-width: 50%" *ngIf="filtered.length > 1 || gridFilteredBy.length > 1">
            <span class="fw-semibold">Filtered by:</span> {{ filtered[0] }}{{filtered.length > 1 && gridFilteredBy.length > 1 ? ', ' + gridFilteredBy[0] : gridFilteredBy[0]}}
        </div>
    </div>
</div>
<div class="app-toolbar">
    <aims-loading class="w-100" [showTable]="false" *ngIf="working"></aims-loading>
    <div class="filter-toolbar d-flex flex-stack flex-wrap flex-md-nowrap" *ngIf="!working">
        <!-- Filters -->
        <div class="d-flex align-items-center flex-wrap flex-md-nowrap">
            <filter-ddl [id]="'projGroups'" #pgroups class="filter-container" [placeholders]="['Proj. group']" [minWidth]="130"
                        [options]="projGroups" (change)="onFilterChange($event)" [multiple]="true" [showAll]="false"
                        *ngIf="isGlobalUser">
            </filter-ddl>
            <filter-ddl [id]="'projIds'" #project class="filter-container" [ngClass]="{'ms-2': isGlobalUser}" [placeholders]="['Project']" [minWidth]="150"
                        [options]="projects" (change)="onFilterChange($event)" [multiple]="true" [showSearch]="true" [showAll]="true">
            </filter-ddl>
            <div class="bullet bg-secondary h-35px w-1px mx-4"></div>
            <filter-ddl [id]="'outputs'" #output class="filter-container" [placeholders]="['Output']" [minWidth]="100"
                        [options]="outputs" (change)="onFilterChange($event)" [multiple]="true">
            </filter-ddl>
            <filter-ddl [id]="'catIds'" #category class="filter-container ms-2" [placeholders]="['Category','Categories']" [minWidth]="140"
                        [options]="categories" (change)="onFilterChange($event)" [multiple]="true">
            </filter-ddl>
            <filter-ddl [id]="'profIds'" #profile class="filter-container ms-2" [placeholders]="['Intervention']" [minWidth]="120"
                        [options]="profiles" (change)="onFilterChange($event)" [multiple]="true">
            </filter-ddl>
            <div class="bullet bg-secondary h-35px w-1px mx-4"></div>
            <filter-ddl [id]="'orgIds'" #partner class="filter-container " [placeholders]="['Partner']" [minWidth]="130"
                        [options]="orgs" (change)="onFilterChange($event)" [multiple]="true" *ngIf="isGlobalUser">
            </filter-ddl>
            <filter-ddl [id]="'dataStatus'" #status class="filter-container ms-2" [placeholders]="['Data type']" [minWidth]="130"
                        [options]="submitStatus" [showSearch]="false" [multiple]="true" [selectedValues]="[1,2]" (change)="onFilterChange($event)">
            </filter-ddl>
            <filter-ddl [id]="'regions'" #region class="filter-container ms-2" [placeholders]="['Region']" [minWidth]="130"
                        [options]="regions" [multiple]="true" (change)="onFilterChange($event)">
            </filter-ddl>
            <div class="bullet bg-secondary h-35px w-1px mx-4"></div>
            <div class="d-flex flex-stack gap-2" *ngIf="!filters.isTarget">
                <ng-container *ngIf="!filters.approvalMode">
                    <span class="fs-7 text-gray-800">From</span>
                    <input id="periodFrom" type="month" class="form-control form-control-sm" (change)="onPeriodChange($event)"
                           [ngbTooltip]="tpFrom" placement="left" min="2020-01" />
                    <ng-template #tpFrom>
                        <span>Period: From date</span>
                        <span class="fw-bold" *ngIf="strPeriod[0]"><br />{{ strPeriod[0] }}</span>
                    </ng-template>
                </ng-container>
                <span class="fs-7 text-gray-800">{{ filters.approvalMode ? 'As of' : 'To' }}</span>
                <input id="periodTo" type="month" class="form-control form-control-sm" (change)="onPeriodChange($event)"
                       [ngbTooltip]="tpTo" placement="left" min="2020-01" />
                <ng-template #tpTo>
                    <span>Period: To date</span>
                    <span class="fw-bold" *ngIf="!filters.approvalMode && strPeriod[1]"><br/>{{ strPeriod[1] }}</span>
                </ng-template>
            </div>
            <ng-container *ngIf="filters.isTarget">
                <filter-ddl [id]="'period'" #qtr class="filter-container" [placeholders]="['Qtr']" [minWidth]="80"
                            [options]="qtrs" [showSearch]="false" (change)="onFilterChange($event)"></filter-ddl>
                <filter-ddl [id]="'year'" #year class="filter-container ms-2" [placeholders]="['Year']" [minWidth]="80"
                            [options]="years" (change)="onFilterChange($event)"></filter-ddl>
            </ng-container>
            <button class="btn btn-sm btn-light-primary border border-primary text-truncate py-2 px-3 ms-2"
                    role="button" (click)="onFilterData()">
                Apply filter
            </button>
            <a class="cursor-pointer fs-8 ms-3" (click)="resetFilters()" *ngIf="filtered.length > 1">Reset</a>
        </div>
        <div class="d-flex align-items-center">
            <div class="bullet bg-secondary h-35px w-1px mx-6"></div>
            <!-- Download -->
            <button type="button" class="btn btn-sm btn-icon btn-light btn-active-color-primary px-3 w-auto"
                    [disabled]="downloading || working || gridWorking || !interventions?.length" (click)="onDownload()">
                <ng-container *ngIf="!downloading; else btnDSpinner">
                    <i class="fas fa-download me-2"></i> Download
                </ng-container>
                <ng-template #btnDSpinner>
                    <span class="indicator-progress" style="display: block">
                        <span class="spinner-border spinner-border-sm align-middle me-1"></span>
                        Downloading...
                    </span>
                </ng-template>
            </button>
        </div>
    </div>
</div>

<div class="card card-custom blockui">
    <aims-loading class="py-3 px-6" *ngIf="gridWorking"></aims-loading>
    <div class="card-header" *ngIf="!gridWorking && interventions?.length">
        <div class="card-toolbar d-flex flex-stack flex-md-nowrap w-100 m-0">
            <!--begin::Tab nav-->
            <div class="d-flex mw-85">
                <ul class="nav nav-tabs nav-line-tabs d-flex fs-6 border-0" role="tablist">
                    <li class="nav-item" role="presentation" *ngFor="let prof of interventions; let ind = index" [ngbTooltip]="htmlTooltip">
                        <a role="tab" class="nav-link justify-content-center text-active-gray-800 text-hover-gray-800"
                           [ngClass]="{ 'active': selTabId === prof.id }" (click)="getInterventionData(prof.id)"
                           [tabindex]="ind === 0 ? -1 : null">{{ prof.name }}</a>
                        <ng-template #htmlTooltip>
                            <div class="text-start" style="line-height: normal">
                                <p class="fs-8 fw-semibold text-gray-600 mb-1">{{ prof.category.code }} {{ prof.category.name }}</p>
                                <p class="fs-7 fw-bold text-gray-800">{{ prof.name }} ({{ prof.abbreviation }})</p>
                                <p class="fs-9 text-gray-700 mt-2 mb-0">{{ prof.description }}</p>
                            </div>
                        </ng-template>
                    </li>
                </ul>
            </div>
            <div class="flex-end">
                <!-- PivotPanel / Group -->
                <button type="button" class="btn btn-sm btn-icon px-3 h-30px w-auto" [ngbTooltip]="groupTooltip"
                        [ngClass]="{'btn-light': !isPivotEnabled, 'btn-light-primary border border-primary': isPivotEnabled}"
                        (click)="enableGridGroupAndPivot()" *ngIf="!filters.approvalMode">
                    <i class="fas fa-bezier-curve me-2"></i>Pivot
                </button>
                <ng-template #groupTooltip>
                    <span class="fw-semibold" *ngIf="!isPivotEnabled">Toggle Grouping and Pivot</span>
                    <div class="text-start" *ngIf="isPivotEnabled">
                        <p class="p-0 fw-semibold">Pivot and Grouping enabled</p>
                    </div>
                </ng-template> 
                <!-- Sort -->
                <button type="button" class="btn btn-sm btn-icon px-3 h-30px w-auto ms-2" [ngbTooltip]="sortTooltip"
                        [ngClass]="{'btn-light': gridSortedBy < 0, 'btn-light-primary border border-primary': gridSortedBy >= 0}"
                        (click)="enableGridSort()">
                    <i class="fas fa-sort me-2"></i>
                    <ng-container *ngIf="gridSortedBy <= 0">Sort</ng-container>
                    <ng-container *ngIf="gridSortedBy > 0">Sorted by {{gridSortedBy}} field{{gridSortedBy > 1 ? 's' : ''}}</ng-container>
                </button>
                <ng-template #sortTooltip>
                    <ng-container *ngIf="gridSortedBy < 0"><span class="fw-semibold">Enable sorting</span> on each column.</ng-container>
                    <div class="text-start" *ngIf="gridSortedBy >= 0">
                        <p class="p-0 fw-semibold">Sorting enabled</p>
                        <p>Click on a column header to sort by that column. Use <code>Shift</code> key to sort by multiple columns.</p>
                    </div>
                </ng-template>
            </div>
        </div>
    </div>
    <div class="card-body p-0 blockui">
        <aims-working *ngIf="working && !interventions?.length"></aims-working>
        <div class="d-flex justify-content-center" *ngIf="!working && !gridWorking && !interventions?.length">
            <div class="notice bg-light rounded border-secondary border border-dashed text-center py-2 px-5 my-10 fs-6">
                No intervention is selected or no intervention found.
            </div>
        </div>

        <!-- AIMS Readonly Grid -->
        <aims-readonly-grid [ngClass]="{ 'd-none': gridWorking || !interventions?.length }"
                            [hideNAPeriods]="hideNAPeriods"
                            (filterApplied)="gridFiltered($event)" (sortApplied)="gridSortedBy=$event"
                            (approve)="onApproveData($event)" (approveAll)="onApproveAll($event)" >
        </aims-readonly-grid>
    </div>
</div>