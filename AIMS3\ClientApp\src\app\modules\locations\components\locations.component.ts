import { ChangeDetectorRef, Component, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { forkJoin, lastValueFrom, Subject, Subscription } from 'rxjs';
import { MessageService } from '../../../shared/services/message.service';
import { SharedService } from '../../../shared/services/shared.service';
import { AppUtilities } from '../../../shared/utilities';
import { MenuComponent } from '../../../_theme/core/components';
import { CommunityList, LocFilter } from '../models/community.model';
import { LocationService } from '../services/location.service';
import { LocationFormComponent } from './location-form/location-form.component';

@Component({
    selector: 'locations',
    templateUrl: './locations.component.html',
    styleUrls: ['./locations.component.scss']
})
export class LocationsComponent implements OnInit, OnDestroy {
    working: boolean = false;

    locations: CommunityList[] = [];
    provinces = [];
    allProvinces = [];

    filters: LocFilter;
    lblFiltered?: string = 'Status: Pending';
    chkAll: boolean = false;
    selLocs: number[] = [];

    @ViewChild(LocationFormComponent, { static: false })
    private locFormComponent: LocationFormComponent;

    private subscriptions: Subscription[] = [];
    constructor(
        private locationsService: LocationService,
        private sharedService: SharedService,
        private messageService: MessageService,
        private cdr: ChangeDetectorRef
    ) {
        this.filters = new LocFilter();
    }

    ngOnInit(): void {
        this.getProvincesDistricts();
        this.getLocations();
    }

    // get provinces and districts
    getProvincesDistricts(): void {
        this.subscriptions.push(
            forkJoin({
                provs: this.sharedService.getProvinces(),
                dists: this.sharedService.getDistricts()
            }).subscribe({
                next: ({ provs, dists }) => {
                    this.provinces = provs;
                    this.provinces.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));
                    
                    // put provinces in the modal component one time
                    this.locFormComponent.provinces = Object.create(this.provinces);

                    this.provinces.forEach(p => {
                        p.districts = dists.filter(d => d.provinceId === p.id);
                    });

                    this.allProvinces = Object.create(this.provinces);
                },
                error: (err) => { console.log(err); },
                complete: () => {
                    this.cdr.detectChanges();

                    AppUtilities().initSelect2();

                    $('#filterProvinces').on('change', (e) => {
                        const selVal = $(e.target).val() || '*';
                        if (selVal === '*') {
                            this.filters.provId = null;
                            this.provinces = this.allProvinces.filter(p => p.id > 0);

                            if (!this.filters.isApproved)
                                $('#filterStatus').val('0').trigger('change');
                        } else {
                            this.filters.provId = +selVal;
                            this.provinces = this.allProvinces.filter(p => p.id === this.filters.provId);
                        }

                        // reset districts filter
                        $('#filterDistricts').val('*').trigger('change');
                    });

                    $('#filterDistricts').on('change', (e) => {
                        const selVal = $(e.target).val() || '*';

                        if (selVal === '*') {
                            this.filters.distId = null;
                        } else {
                            this.filters.provId = null;
                            this.filters.distId = +selVal;
                        }
                    });

                    $('#filterStatus').on('change', (e) => {
                        const selVal = $(e.target).val() || '*';
                        if (selVal === '*') {
                            this.filters.isApproved = null;
                        } else {
                            this.filters.isApproved = +selVal;
                        }
                    });
                }
            })
        );
    }

    getLocations(): void {
        this.working = true;
        $('#table_locations').DataTable({ retrieve: true }).destroy();

        this.subscriptions.push(
            this.locationsService.getFilteredLocations(this.filters).subscribe({
            next: (locs: CommunityList[]) => {
                this.locations = locs;
            },
            error: (e) => {
                console.log(e);
                this.working = false;
            },
            complete: () => {
                // build status
                this.locations.forEach((loc) => {
                    if (!loc.districtId)
                        loc.distName = `<span class="text-muted">(${loc.distName})</span>`;

                    if (loc.isApproved || loc.isVerified) {
                        loc.status = 'Approved';
                        loc.color = 'success';
                    } else if (loc.locationId && !loc.isApproved) {
                        this.chkAll = true;
                        loc.status = 'Pending approval';
                        loc.color = 'danger';
                    }
                });

                this.cdr.detectChanges();
                this.selLocs = [];

                $('#table_locations').DataTable({
                    retrieve: false,
                    pageLength: 25, columnDefs: [{orderable:false, targets: 0}], order:[1],
                    rowCallback: (row: Node, data: any, index: number, indexFull: number) => {
                        row.childNodes[1].textContent = `${indexFull + 1}`;
                    }
                } as DataTables.Settings).on('draw.dt', () => MenuComponent.reinitialization());
                $('#table_locations th').off('click', this.onSort).on('click', (e) => this.onSort(e));

                MenuComponent.reinitialization();
                this.working = false;
            }
        }));
    }

    onSearch(e: any): void {
        $('#table_locations').DataTable({ retrieve: true })
            .search(e.target.value || '', false, true).draw();
    }

    onSort(e: any): void {
        const target = e.target as HTMLElement;
        if (target.hasAttribute('data-orderable'))
            return;

        if (target.classList.contains('sorting_desc') && !target.classList.contains('noSort')) {
            target.classList.add('noSort');
            return;
        }

        if (target.classList.contains('noSort')) {console.log('here')
            setTimeout(() => {
                $('#table_locations').DataTable({
                    retrieve: true
                }).order([2, 'asc']).draw();
                    target.classList.remove('noSort');
            }, this.locations.length * 2);
        }
    }

    onFilter(): void {
        if ((this.filters.isApproved || this.filters.isApproved === null) &&
            !this.filters.provId && !this.filters.distId) {
            this.messageService.info('Please select a province or district.');
            return;
        }

        let filtersApplied = [];
        if (this.filters.provId)
            filtersApplied.push('Province: ' + this.provinces.find(p => p.id === this.filters.provId)?.name);
        else if (this.filters.distId) {
            let selDist;
            this.provinces.forEach(p => {
                selDist = p.districts.find(d => d.id === this.filters.distId);
                if (selDist)
                    return;
            });

            if (selDist)
                filtersApplied.push(`Disrict: ${selDist.name}, ${selDist.province.name}`);
        }

        if (this.filters.isApproved === 0 || this.filters.isApproved === 1)
            filtersApplied.push('Status: ' + (this.filters.isApproved ? 'Approved' : 'Pending'));

        if (this.lblFiltered.length)
            this.lblFiltered = filtersApplied.join(', ');
        else
            this.lblFiltered = '';

        this.getLocations();
    }

    private allChecked: boolean = false;
    onSelLoc(locId: number): void {
        if (locId === -1 && !this.allChecked) {     // select all
            this.selLocs = this.locations.filter(l => l.locationId && !l.isApproved)
                .map(l => l.locationId);
            this.allChecked = true;
            return;
        } else if (locId === -1) {
            this.allChecked = false;
            this.selLocs = [];
            return;
        }

        if (this.selLocs.includes(locId))
            this.selLocs = this.selLocs.filter(l => l !== locId);
        else
            this.selLocs.push(locId);
    }

    async manageLocation(loc?: CommunityList, btn: string = 'add') {
        if (!loc)
            this.locFormComponent.location = new CommunityList(0, 0, '', '');
        else {
            let _loc = Object.assign({}, loc);
            _loc.distName = _loc.distName.replace(/(<([^>]+)>)/gi, '')
                .replace('(', '').replace(')', '');
            this.locFormComponent.location = Object.create(_loc);
        }

        this.locFormComponent.editMode = true;

        if (btn === 'view')
            this.locFormComponent.editMode = false;
        else if (btn === 'edit') {
            this.locFormComponent.modalConfig.modalTitle = 'Edit location';
            this.locFormComponent.modalConfig.doneButtonLabel = 'Save changes';
        } else if (btn === 'approve') {
            this.locFormComponent.modalConfig.modalTitle = 'Edit location';
            this.locFormComponent.modalConfig.doneButtonLabel = 'Approve';
            this.locFormComponent.location.isVerified = true;
        } else {
            this.locFormComponent.modalConfig.modalTitle = 'Add new location';
            this.locFormComponent.modalConfig.doneButtonLabel = 'Add';
        }

        return await this.locFormComponent.onOpen();
    }

    async approveLocs(): Promise<boolean> {
        const result = new Subject<boolean>();

        this.messageService.confirmMessage('Approve', `Please make sure locations are verified and not redundent.`,
            () => {
                this.working = true;
                this.subscriptions.push(this.locationsService.approveLocations(this.selLocs).subscribe({
                    error: (err) => {
                        this.working = false;
                        console.log(err);

                        result.next(false);
                        result.complete();
                    },
                    complete: () => {
                        let successMsg = 'Selected locations have been approved successfully.';
                        this.selLocs = [];
                        this.messageService.success(successMsg);

                        this.working = false;

                        this.getLocations();
                        result.next(true);
                        result.complete();
                    }
                }));
            }, false, 'Approve selected');

        return await lastValueFrom(result.asObservable());
    }

    deleteLocation(locName: string[], id?: number, locId?: number): void {
        locName[0] = `<span class="text-primary">${ locName.join(', ') }</span>`;
        
        this.messageService.confirmMessage('Confirm Delete', `Are you sure you want to delete '${locName[0]}'?`,
            () => {
                this.working = true;
                $('#table_locations').DataTable({ retrieve: true }).destroy();

                let operation = id && id > 0 ?
                    this.locationsService.deleteCommunity(id) :
                    this.locationsService.deleteLocation(locId || 0);

                this.subscriptions.push(operation.subscribe({
                    next: () => {
                        if (id && id > 0)
                            this.locations = this.locations.filter(l => l.id !== id);
                        else
                            this.locations = this.locations.filter(l => l.locationId !== locId);
                    },
                    error: (err) => {
                        this.working = false;
                        console.error(err);
                    },
                    complete: () => {
                        this.cdr.detectChanges();

                        $('#table_locations').DataTable({
                            retrieve: false,
                            pageLength: 25, columnDefs: [{ orderable: false, targets: 0 }], order: [1],
                            rowCallback: (row: Node, data: any, index: number, indexFull: number) => {
                                row.childNodes[1].textContent = `${indexFull + 1}`;
                            }
                        } as DataTables.Settings).on('draw.dt', () => MenuComponent.reinitialization());
                        $('#table_locations th').off('click', this.onSort).on('click', this.onSort);

                        this.messageService.success('The location has been deleted successfully.');
                        this.working = false;
                    }
                })
                );
            }, true, 'Delete');
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}