import { Component, OnInit, Output, EventEmitter, OnDestroy, Input } from '@angular/core';
import { FormBuilder, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { Subject } from 'rxjs';
import { takeUntil, debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { ReportFilter, TimePeriodType, Quarter, ProjectGroup, Project } from '../../models/reports.model';
import { CommonModule } from '@angular/common';
import { ReportsService } from '../../services/reports.service';

@Component({
  selector: 'app-report-filter',
  templateUrl: './report-filter.component.html',
  styleUrls: ['./report-filter.component.scss'],
  standalone: true,
  imports: [CommonModule, ReactiveFormsModule]
})
export class ReportFilterComponent implements OnInit, OnDestroy {
  @Input() showProjectFilters = true;
  @Output() filterChange = new EventEmitter<ReportFilter>();
  
  filterForm: FormGroup;
  currentDate = new Date();
  currentYear = this.currentDate.getFullYear();
  yearsList: number[] = [];
  monthsList = [
    { value: 1, label: 'January' },
    { value: 2, label: 'February' },
    { value: 3, label: 'March' },
    { value: 4, label: 'April' },
    { value: 5, label: 'May' },
    { value: 6, label: 'June' },
    { value: 7, label: 'July' },
    { value: 8, label: 'August' },
    { value: 9, label: 'September' },
    { value: 10, label: 'October' },
    { value: 11, label: 'November' },
    { value: 12, label: 'December' }
  ];
  quartersList = [
    { value: Quarter.Q1, label: 'Q1 (Jan-Mar)' },
    { value: Quarter.Q2, label: 'Q2 (Apr-Jun)' },
    { value: Quarter.Q3, label: 'Q3 (Jul-Sep)' },
    { value: Quarter.Q4, label: 'Q4 (Oct-Dec)' }
  ];
  
  // Project data
  projectGroups: ProjectGroup[] = [];
  projects: Project[] = [];
  filteredProjects: Project[] = [];
  
  // Project status options
  projectStatusOptions = [
    { value: 'active', label: 'Active' },
    { value: 'completed', label: 'Completed' },
    { value: 'planned', label: 'Planned' },
    { value: 'suspended', label: 'Suspended' }
  ];
  
  // Activity status options - focus on primary statuses for reporting
  activityStatusOptions = [
    { value: 'ongoing', label: 'Ongoing' },
    { value: 'completed', label: 'Completed' },
    { value: 'archived', label: 'Archived' },
    { value: 'cancelled', label: 'Cancelled' }
  ];
  
  // Expose enum to template
  timePeriodTypes = [
    { value: TimePeriodType.Monthly, label: 'Monthly' },
    { value: TimePeriodType.Quarterly, label: 'Quarterly' },
    { value: TimePeriodType.Yearly, label: 'Yearly' }
  ];

  // Add after projects
  categories: any[] = [];
  filteredCategories: any[] = [];
  
  // Interventions data
  interventions: any[] = [];
  filteredInterventions: any[] = [];

  private destroy$ = new Subject<void>();

  constructor(
    private fb: FormBuilder,
    private reportsService: ReportsService
  ) {
    // Create years list for dropdown (10 years back and 5 years forward)
    const startYear = this.currentYear - 10;
    const endYear = this.currentYear + 5;
    for (let year = startYear; year <= endYear; year++) {
      this.yearsList.push(year);
    }

    // Create the filter form
    this.filterForm = this.fb.group({
      projectGroups: [[]],
      projects: [[]],
      projectStatus: [[]],
      activityStatus: [['ongoing', 'completed', 'archived', 'cancelled']],
      outputs: [[]],
      categories: [[]],
      interventions: [[]],
      organizations: [[]],
      regions: [[]],
      provinces: [[]],
      districts: [[]],
      communities: [[]],
      timePeriodType: [null],
      year: [this.currentYear],
      quarter: [{value: null, disabled: true}],
      month: [{value: null, disabled: true}],
      startDate: [null],
      endDate: [null],
      gender: [''],
      ageGroup: [''],
      vulnerabilityGroup: ['']
    });
  }

  ngOnInit(): void {
    // Load project groups and projects
    if (this.showProjectFilters) {
      this.loadProjectData();
    }
    
    // Subscribe to form changes
    this.filterForm.valueChanges
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(400),
        distinctUntilChanged((prev, curr) => JSON.stringify(prev) === JSON.stringify(curr))
      )
      .subscribe(() => {
        this.emitFilterChange();
      });

    // Don't emit initial filter change to prevent duplicate API calls
    // The parent dashboard component will handle initial data loading
    
    // Handle time period type changes to show/hide relevant form controls
    this.filterForm.get('timePeriodType')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(type => {
        this.handleTimePeriodTypeChange(type);
      });
    
    // Handle project group changes to filter projects
    this.filterForm.get('projectGroups')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(groups => {
        this.filterProjectsByGroups(groups);
      });
    
    // Initialize time period fields
    this.handleTimePeriodTypeChange(this.filterForm.get('timePeriodType')?.value);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  resetFilter(): void {
    this.filterForm.reset({
      regions: [],
      provinces: [],
      districts: [],
      outputs: [],
      categories: [],
      interventions: [],
      activityStatus: ['ongoing', 'completed', 'archived', 'cancelled'],
      startDate: null,
      endDate: null,
      timePeriodType: TimePeriodType.Yearly,
      year: this.currentYear,
      quarter: null,
      month: null,
      gender: 'all',
      ageGroup: 'all',
      vulnerabilityGroup: 'all',
      searchTerm: '',
      projectGroups: [],
      projects: [],
      projectStatus: []
    });
    
    this.handleTimePeriodTypeChange(TimePeriodType.Yearly);
    this.emitFilterChange();
  }

  /**
   * Load project groups and projects data
   */
  private loadProjectData(): void {
    // Load project groups
    this.reportsService.getProjectGroups()
      .pipe(takeUntil(this.destroy$))
      .subscribe(groups => {
        this.projectGroups = groups
          .filter(group => group !== null)
          .map(group => ({
            id: group, // Use group name as ID
            name: group,
            projects: []
          }));
        console.log('Loaded project groups:', this.projectGroups);
      });
    
    // Load all projects
    this.reportsService.getProjects()
      .pipe(takeUntil(this.destroy$))
      .subscribe(projects => {
        this.projects = projects;
        this.filteredProjects = projects;
        console.log('Loaded projects:', this.projects);
        // Log project group IDs for debugging
        this.projects.forEach(project => {
          console.log(`Project ${project.name} has groupId:`, project.groupId);
        });
      });
      
    // Load interventions
    this.loadInterventions();
  }
  
  /**
   * Load interventions data
   */
  private loadInterventions(): void {
    // First, load all interventions from the shared service - this matches how data/view component works
    this.reportsService.sharedService.getInterventionProfiles()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (interventionProfiles) => {
          console.log('Loaded all intervention profiles:', interventionProfiles);
          
          // Store all interventions
          this.interventions = interventionProfiles.map(profile => ({
            id: profile.id,
            name: profile.name || `Intervention ${profile.id}`,
            categoryId: profile.categoryId,
            description: profile.description || ''
          }));
          
          // Initially filter interventions based on any pre-selected categories
          const selectedCategories = this.filterForm.get('categories')?.value || [];
          this.filterInterventionsByCategories(selectedCategories);
          
          console.log('Initial interventions loaded:', this.interventions.length);
        },
        error: (error) => {
          console.error('Error loading interventions:', error);
          // Fallback to mock data
          this.interventions = [
            { id: 'int1', name: 'Public Infrastructure', categoryId: 'cat1' },
            { id: 'int2', name: 'Healthcare Facilities', categoryId: 'cat2' },
            { id: 'int3', name: 'Education Infrastructure', categoryId: 'cat3' },
            { id: 'int4', name: 'Cash Assistance', categoryId: 'cat4' },
            { id: 'int5', name: 'Livelihoods Support', categoryId: 'cat5' },
            { id: 'int6', name: 'Agricultural Development', categoryId: 'cat6' }
          ];
          this.filteredInterventions = [...this.interventions];
        }
      });
    
    // Listen for category changes to filter interventions
    this.filterForm.get('categories')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(selectedCategories => {
        this.filterInterventionsByCategories(selectedCategories);
      });
    
    // Listen for project changes to potentially filter interventions 
    // if interventions are project-specific
    this.filterForm.get('projects')?.valueChanges
      .pipe(takeUntil(this.destroy$))
      .subscribe(selectedProjects => {
        // First filter categories, then filter interventions based on categories
        this.filterCategoriesByProjects();
        const selectedCategories = this.filterForm.get('categories')?.value || [];
        this.filterInterventionsByCategories(selectedCategories);
      });
  }
  
  /**
   * Filter interventions based on selected categories
   */
  private filterInterventionsByCategories(categoryIds: string[]): void {
    // Clear interventions selection when categories change
    this.filterForm.get('interventions')?.setValue([]);
    
    // If no categories selected, show all interventions
    if (!categoryIds || categoryIds.length === 0) {
      this.filteredInterventions = [...this.interventions];
      return;
    }
    
    // Filter interventions by selected categories
    this.filteredInterventions = this.interventions.filter(
      intervention => categoryIds.includes(intervention.categoryId)
    );
    
    console.log('Filtered interventions by categories:', 
      categoryIds, 
      'Result:', 
      this.filteredInterventions);
  }

  /**
   * Filter projects based on selected project groups
   */
  private filterProjectsByGroups(groupIds: string[]): void {
    console.log('Filtering projects for groups:', groupIds);
    
    if (!groupIds || groupIds.length === 0) {
      // If no groups selected, show all projects
      this.filteredProjects = this.projects;
      console.log('Showing all projects:', this.filteredProjects);
    } else {
      // Get the selected group name
      const selectedGroup = this.projectGroups.find(group => groupIds.includes(group.id));
      if (selectedGroup) {
        console.log('Selected group:', selectedGroup);
        // Filter projects by the selected group name
        this.filteredProjects = this.projects.filter(project => {
          console.log('Project:', project);
          console.log('Project groupId:', project.groupId);
          console.log('Selected group name:', selectedGroup.name);
          return project.groupId === selectedGroup.name;
        });
        console.log('Filtered projects:', this.filteredProjects);
      }
    }
    
    // Reset project selection if the selected project is not in the filtered list
    const selectedProjects = this.filterForm.get('projects')?.value || [];
    const validProjects = selectedProjects.filter(projectId => 
      this.filteredProjects.some(p => p.id === projectId)
    );
    
    if (validProjects.length !== selectedProjects.length) {
      this.filterForm.get('projects')?.setValue(validProjects);
    }

    // After filtering projects, filter categories as well
    this.filterCategoriesByProjects();
  }

  private filterCategoriesByProjects(): void {
    // Get selected projects
    const selectedProjectIds = this.filteredProjects.map(p => p.id);
    if (!selectedProjectIds.length) {
      this.filteredCategories = [...this.categories];
    } else {
      // Only show categories that have at least one project in the selected projects
      this.filteredCategories = this.categories.filter(cat =>
        cat.projectIds && cat.projectIds.some(pid => selectedProjectIds.includes(pid))
      );
    }
    // Reset category selection if needed
    const selectedCategories = this.filterForm.get('categories')?.value || [];
    const validCategories = selectedCategories.filter(catId =>
      this.filteredCategories.some(c => c.id === catId)
    );
    if (validCategories.length !== selectedCategories.length) {
      this.filterForm.get('categories')?.setValue(validCategories);
    }
  }

  private emitFilterChange(): void {
    // Get the current filter values
    const filter: ReportFilter = this.filterForm.value;
    
    // Create a clean filter object removing null and empty arrays
    const cleanFilter: ReportFilter = {};
    
    // Process each filter property
    Object.entries(filter).forEach(([key, value]) => {
      // Skip null, undefined, empty string, or empty arrays
      if (value === null || value === undefined || value === '') {
        return;
      }
      
      // Skip empty arrays
      if (Array.isArray(value) && value.length === 0) {
        return;
      }
      
      // Add to clean filter
      cleanFilter[key] = value;
    });
    
    // For backend compatibility, also set profIds when interventions are selected
    if (cleanFilter.interventions && cleanFilter.interventions.length > 0) {
      cleanFilter.profIds = cleanFilter.interventions;
    }
    
    // Handle the time period settings
    if (cleanFilter.timePeriodType) {
      if (!cleanFilter.timePeriod) {
        cleanFilter.timePeriod = {
          type: cleanFilter.timePeriodType,
          year: cleanFilter.year || this.currentYear
        };
      }
      
      // Add quarter or month to the time period if applicable
      if (cleanFilter.timePeriodType === TimePeriodType.Quarterly && cleanFilter.quarter) {
        cleanFilter.timePeriod.quarter = cleanFilter.quarter;
      } else if (cleanFilter.timePeriodType === TimePeriodType.Monthly && cleanFilter.month) {
        cleanFilter.timePeriod.month = cleanFilter.month;
      }
    }
    
    // Emit the filter change event
    this.filterChange.emit(cleanFilter);
  }
  
  private handleTimePeriodTypeChange(periodType: TimePeriodType): void {
    // Reset quarter and month
    this.filterForm.get('quarter')?.setValue(null);
    this.filterForm.get('month')?.setValue(null);
    
    // Enable/disable fields based on period type
    if (periodType === TimePeriodType.Quarterly) {
      this.filterForm.get('quarter')?.enable();
      this.filterForm.get('month')?.disable();
    } else if (periodType === TimePeriodType.Monthly) {
      this.filterForm.get('quarter')?.disable();
      this.filterForm.get('month')?.enable();
    } else {
      // Yearly
      this.filterForm.get('quarter')?.disable();
      this.filterForm.get('month')?.disable();
    }
  }

  /**
   * Refresh interventions list from the API
   * This can be called manually when the user wants to refresh the list
   */
  refreshInterventions(): void {
    console.log('Manually refreshing interventions list');
    // Clear current selection
    this.filterForm.get('interventions')?.setValue([]);
    
    // Show loading indicator if needed
    // this.loading = true;
    
    // Get fresh interventions data directly from the source API
    this.reportsService.sharedService.getInterventionProfiles()
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (interventionProfiles) => {
          console.log('Refreshed intervention profiles:', interventionProfiles);
          
          // Update interventions list
          this.interventions = interventionProfiles.map(profile => ({
            id: profile.id,
            name: profile.name || `Intervention ${profile.id}`,
            categoryId: profile.categoryId,
            description: profile.description || ''
          }));
          
          // Apply any current category filters
          const selectedCategories = this.filterForm.get('categories')?.value || [];
          this.filterInterventionsByCategories(selectedCategories);
          
          // Hide loading indicator if needed
          // this.loading = false;
        },
        error: (error) => {
          console.error('Error refreshing interventions:', error);
          // Hide loading indicator if needed
          // this.loading = false;
        }
      });
  }
} 