.region-dashboard {
  .stats-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    .card-body {
      padding: 1.25rem;
    }
    
    h2 {
      font-weight: 600;
      font-size: 1.75rem;
    }
    
    .icon-bg {
      font-size: 1.5rem;
      opacity: 0.8;
      padding: 10px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    .progress-sm {
      height: 6px;
      border-radius: 3px;
    }
  }
  
  .card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    
    .card-body {
      padding: 1.5rem;
    }
  }
  
  .map-container {
    height: 400px;
    width: 100%;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .map-legend {
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    
    .legend-item {
      display: inline-flex;
      align-items: center;
      margin-right: 15px;
      font-size: 0.85rem;
      
      .legend-marker {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 5px;
      }
    }
  }
  
  .map-popup {
    h5 {
      margin-bottom: 8px;
      color: #333;
      font-size: 1rem;
    }
    
    p {
      margin-bottom: 5px;
      font-size: 0.9rem;
    }
  }
}

// Responsive adjustments
@media (max-width: 767.98px) {
  .region-dashboard {
    .stats-card {
      h2 {
        font-size: 1.5rem;
      }
    }
    
    .map-container {
      height: 300px;
    }
  }
} 