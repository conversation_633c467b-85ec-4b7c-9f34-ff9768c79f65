import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { ChangePasswordComponent } from './change-password/change-password.component';
import { ProfileComponent } from './profile.component';
import { UserInfoComponent } from './user-info/user-info.component';

const routes: Routes = [
  {
    path: '',
    component: ProfileComponent,
    children: [
      {
        path: 'info',
            component: UserInfoComponent,
            //canActivate: [AuthGuard]
      },
      {
        path: 'change-password',
        component: ChangePasswordComponent
      },
      { path: '', redirectTo: 'info', pathMatch: 'full' },
      { path: '**', redirectTo: 'info', pathMatch: 'full' },
    ],
  },
];

@NgModule({
    imports: [RouterModule.forChild(routes)],
    exports: [RouterModule]
})
export class ProfileRoutingModule { }