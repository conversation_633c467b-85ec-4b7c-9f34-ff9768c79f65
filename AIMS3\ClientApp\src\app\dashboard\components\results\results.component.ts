import { ChangeDetectorRef, Component, HostBinding, ViewChild } from '@angular/core';
import { Subscription, forkJoin } from 'rxjs';
import { Category } from '../../../modules/admin/models/category.model';
import { IProjectIntervention } from '../../../modules/admin/models/project.model';
import { AuthService } from '../../../modules/auth';
import { ProfileInfo } from '../../../modules/data-entry/models/profile.model';
import { ProfileService } from '../../../modules/data-entry/services/profile.service';
import { IDataFilter } from '../../../modules/data/models/data.model';
import { Pivot } from '../../../modules/pivot/models/pivot.model';
import { FilterDropdownList } from '../../../shared/components/filter-dropdown/filter-ddl.control';
import { Region } from '../../../shared/enums';
import { MessageService } from '../../../shared/services/message.service';
import { SharedService } from '../../../shared/services/shared.service';
import { compareSort } from '../../../shared/utilities';
import { IResultsData } from '../../models/result-data.model';
import { DashboardResultService } from '../../services/result.service';
import { ResultGridComponent } from './grid/result-grid.component';

@Component({
    selector: 'aims-results',
    templateUrl: './results.component.html',
    styleUrls: ['./results.component.scss'],
})
export class ResultsComponent {
    @HostBinding('attr.data-qs-app-toolbar-fixed') pgToolbar = 'true';
    @HostBinding('attr.data-qs-app-header-fixed') pgHeader = 'true';
    @HostBinding('attr.data-qs-app-sidebar-push-toolbar') pushToolbar = 'true';

    working: boolean = false; loading: boolean = false;
    isAdmin: boolean = false; isGlobalUser: boolean = false;
    downloading: boolean = false;

    projects: IProjectIntervention[] = [];
    outputs = [];
    categories: Category[] = [];
    profiles: any = [];
    orgs: any[] = [];
    submitStatus = [{ id: 0, name: 'Draft' }, { id: 1, name: 'Submitted' }, { id: 2, name: 'Approved' }];
    regions = [];

    interventions: ProfileInfo[] = [];
    allInterventions: ProfileInfo[] = [];
    selTabId: number = 0;

    filters: IDataFilter = {
        isTarget: false,
        approvalMode: false,
        dataStatus: [1,2]
    };
    filtered: string[] = [''];

    @ViewChild('output')
    private outputFilterCtrl: FilterDropdownList;
    @ViewChild('category')
    private catFilterCtrl: FilterDropdownList;
    @ViewChild('profile')
    private profFilterCtrl: FilterDropdownList;

    @ViewChild('project')
    private projFilterCtrl: FilterDropdownList;
    @ViewChild('partner')
    private partnerFilterCtrl: FilterDropdownList;
    @ViewChild('status')
    private dStatusFilterCtrl: FilterDropdownList;
    @ViewChild('region')
    private regionFilterCtrl: FilterDropdownList;


    private subscriptions: Subscription[] = [];
    constructor(
        private authService: AuthService,
        private profService: ProfileService,
        private dbResultService: DashboardResultService,
        private sharedService: SharedService,
        private messageService: MessageService,
        private cdr: ChangeDetectorRef
    ) {
        const roles = this.authService.currentUserValue.roles;
        this.isGlobalUser = roles.includes('Admin') || roles.includes('Approver') || roles.includes('Viewer');

        this.filters.dataStatus = [1,2];

        Object.values(Region)
            .forEach((v, i) => {
                if ((typeof v === 'string' && v !== 'National'))
                    this.regions.push({ id: i, name: v });
            });
        this.regions.sort((x, y) => x.name < y.name ? -1 : 1);
    }

    ngOnInit(): void {
        this.getDropdownItems();
    }

    // get categories and profiles, then pivots
    private getDropdownItems(): void {
        this.loading = true;

        let endPoints: any = {
            projs: this.sharedService.getProjectsFiltered(),        // filtered
            cats: this.sharedService.getCategories(),               // all categories
            profs: this.profService.getProfiles()                   // this could be filtered
        };

        // get organizations
        if (this.isGlobalUser)
            endPoints = { ...endPoints, 'orgs': this.sharedService.getOrgsList() };

        this.subscriptions.push(
            forkJoin(endPoints).subscribe({
                next: ({ projs, cats, profs, orgs }) => {
                    this.projects = projs;
                    this.allInterventions = [...profs];

                    this.categories = cats;
                    this.categories.forEach(cat => {
                        cat.name = cat.code + ' ' + cat.name;

                        const outputExists = this.outputs.findIndex(o => o.name === cat.output) > -1;
                        if (!outputExists) {
                            this.outputs.push({
                                id: cat.output,
                                name: cat.output,
                                categories: this.categories.filter(c => c.output === cat.output)
                            });
                        }
                    });

                    // orgs
                    if (this.isGlobalUser) {
                        orgs.forEach(org => {
                            this.orgs.push({
                                id: org.id,
                                name: org.shortName
                            });
                        });
                    }
                },
                error: (err) => {
                    console.log(err);
                    this.loading = false;
                },
                complete: () => {
                    this.createProfileFilters();
                    //this.cdr.detectChanges();
                    this.loading = false;
                    this.getPivotTables();
                }
            })
        );
    }

    private createProfileFilters(): void {
        this.profiles = [];
        this.allInterventions.forEach(prof => {
            this.profiles.push({
                id: prof.id,
                catId: prof.categoryId,
                name: prof.name
            });
        });

        // sort it alphabetically
        this.profiles.sort((x, y) => x.name > y.name ? 1 : -1);
    }

    onFilterChange(ctrl): void {
        if (ctrl.id === 'outputs') {
            this.catFilterCtrl.clearSelection();
            this.profFilterCtrl.clearSelection();

            if (!ctrl.selVals.length) {
                this.catFilterCtrl.options = [...this.categories];
                this.profFilterCtrl.options = [...this.profiles];
            } else {
                this.catFilterCtrl.options = this.categories.filter(c => ctrl.selVals.includes(c.output));
                this.catFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));
                this.profFilterCtrl.options = this.profiles.filter(p => this.catFilterCtrl.options.findIndex(c => c.id === p.catId) > -1);
                this.profFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));
            }

            this.catFilterCtrl.items = Object.create(this.catFilterCtrl.options);
            this.profFilterCtrl.items = Object.create(this.profFilterCtrl.options);
        } else if (ctrl.id === 'catIds') {
            this.profFilterCtrl.clearSelection();

            if (!ctrl.selVals.length) {
                this.profFilterCtrl.options = [...this.profiles];
            } else {
                this.profFilterCtrl.options = this.profiles.filter(p => ctrl.selVals.includes(p.catId));
                this.profFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));
            }
            this.profFilterCtrl.items = Object.create(this.profFilterCtrl.options);
        }

        if (ctrl.id === 'period' || ctrl.id === 'year')
            this.filters[ctrl.id] = ctrl.selVals[0];
        else
            this.filters[ctrl.id] = ctrl.selVals;
    }

    onPeriodChange(e: any): void {
        const target = e.target as HTMLInputElement;
        const date = target.value?.split('-');

        if (target.id === 'periodFrom') {
            if (!date) {
                this.filters.period = 0;
                this.filters.year = 0;
            } else {
                this.filters.period = +date[1];
                this.filters.year = +date[0];
            }
        } else {
            if (!date) {
                this.filters.periodEnd = 0;
                this.filters.yearEnd = 0;
            } else {
                this.filters.periodEnd = +date[1];
                this.filters.yearEnd = +date[0];
            }
        }
    }

    resetFilters(): void {
        this.outputFilterCtrl.clearSelection();
        this.outputFilterCtrl.options = this.outputs;
        this.outputFilterCtrl.items = [...this.outputs];

        this.catFilterCtrl.clearSelection();
        this.catFilterCtrl.options = this.categories;
        this.catFilterCtrl.items = [...this.categories];

        this.profFilterCtrl.clearSelection();
        this.profFilterCtrl.options = this.profiles;
        this.profFilterCtrl.items = [...this.profiles];

        this.projFilterCtrl.clearSelection();
        this.partnerFilterCtrl.clearSelection();
        this.dStatusFilterCtrl.setSelectedValues([2]);
        this.regionFilterCtrl.clearSelection();

        const pFrom = document.querySelector('#periodFrom') as HTMLInputElement;
        if (pFrom)
            pFrom.value = '';
        const pTo = document.querySelector('#periodTo') as HTMLInputElement;
        if (pTo)
            pTo.value = '';

        this.interventions = this.allInterventions.filter(i => i['pivots'].length);
        this.interventions.sort((x, y) => compareSort(x.category.code, y.category.code) || compareSort(x.name, y.name));

      

        this.filters = {
            isTarget: false,
            approvalMode: false,
            dataStatus: [1,2]
        };

        // reapply filter
        this.getData();
        this.filtered = [''];


    }

    onFilterData(): void {
        if (!this.filters.approvalMode) {
            if (this.filters.period > 0 && this.filters.year > 0 &&
                this.filters.periodEnd > 0 && this.filters.yearEnd > 0) {
                const date = new Date(this.filters.year, this.filters.period - 1, 1);
                const dateEnd = new Date(this.filters.yearEnd, this.filters.periodEnd - 1, 1);

                if (dateEnd < date) {
                    this.messageService.error('<span class="fw-semibold">Period (To)</span> should be greater ' +
                        'than <span class="fw-semibold">Period (From)</span>.', 'Invalid period', { enableHtml: true });
                    return;
                }
            }
        }

        // filtered profs
        const filteredProfIds = this.profFilterCtrl.selectedValues.length
            ? this.profFilterCtrl.selectedValues
            : this.profFilterCtrl.options.map(o => o.id);

        this.interventions = this.allInterventions.filter(i => i['pivots'].length && filteredProfIds.includes(i.id));
        //this.interventions.sort((x, y) => compareSort(x.category.code, y.category.code) || compareSort(x.name, y.name));


        // reapply filter on data
        this.getData();
        this.updateFilterDisplay();
        // create filters
        this.filtered = [''];
        if (this.filters.projIds?.length)
            this.filtered.push('Project');
        if (this.filters.profIds?.length)
            this.filtered.push('Intervention');
        else if (this.filters.catIds?.length)
            this.filtered.push('Category');
        else if (this.filters.outputs?.length)
            this.filtered.push('Output');
        if (this.filters.orgIds?.length)
            this.filtered.push('Partner');
        if (this.filters.regions?.length)
            this.filtered.push('Region');
        if (this.filters.period > 0 || this.filters.periodEnd > 0)
            this.filtered.push('Period');

        this.filtered[0] = this.filtered.filter((f, i) => i > 0).join(', ');
    }
    private updateFilterDisplay(): void {
        this.filtered = [''];
        if (this.filters.projIds?.length)
            this.filtered.push('Project');
        if (this.filters.profIds?.length)
            this.filtered.push('Intervention');
        else if (this.filters.catIds?.length)
            this.filtered.push('Category');
        else if (this.filters.outputs?.length)
            this.filtered.push('Output');
        if (this.filters.orgIds?.length)
            this.filtered.push('Partner');
        if (this.filters.regions?.length)
            this.filtered.push('Region');
        if (this.filters.period > 0 || this.filters.periodEnd > 0)
            this.filtered.push('Period');
    
        this.filtered[0] = this.filtered.filter((f, i) => i > 0).join(', ');
    }
    /** Pivot tables */
    pivots: Pivot[] = [];
    private getPivotTables(): void {
        this.working = true;

        this.subscriptions.push(
            this.dbResultService.getPivotTables().subscribe({
                next: (pivots: Pivot[]) => {
                    this.pivots = pivots;
                },
                error: (e) => {
                    console.log(e);
                    this.working = false;
                }, complete: () => {
                    this.allInterventions.forEach(prof => {
                        prof['pivots'] = this.pivots.filter(p => p.profId === prof.id);
                    });

                    this.interventions = this.allInterventions.filter(i => i['pivots'].length);
                    this.getData();
                    this.cdr.detectChanges();
                    this.working = false;
                }
            })
        );
    }

    private data: IResultsData[] = [];
    private getData(): void {
        this.working = true;

        this.subscriptions.push(
            this.dbResultService.getData(this.filters).subscribe({
                next: (res) => {
                    this.data = res;
            
                const interventionsWithData: ProfileInfo[] = [];
                
                this.interventions = this.interventions.filter(prof => {
                    const ds = this.data.find(d => d.interventionId === prof.id);
                    prof['targetData'] = ds?.targetData || [];
                    prof['progressData'] = ds?.progressData || [];
                    
                    // Check if this intervention has any data in either target or progress
                    const hasData = (prof['targetData']?.length > 0) || 
                                   (prof['progressData']?.length > 0);
                   /*  if (hasData) {
                        interventionsWithData.push(prof);
                    } */
                   if(!hasData) {
                    console.log('Filtering out intervention:', prof.id, prof.name);
                   }
                   return hasData;
                });
                console.log('After filtering:', this.interventions.length, 'interventions remain');
                // Update the interventions array
                // this.interventions = interventionsWithData;
                // this.interventions.sort((x, y) => compareSort(x.category.code, y.category.code) || compareSort(x.name, y.name));
                this.interventions.sort((x, y) => 
                    compareSort(x.category.code, y.category.code) || 
                    compareSort(x.name, y.name));
                },
                error: (err) => {
                    this.working = false;
                    console.log(err);
                },
                complete: () => {
                    this.working = false;
                this.cdr.detectChanges(); // Trigger change detection
                }
            }));
    }

    /** Download */
    private resultGrids: ResultGridComponent[] = [];
    onGridInitd(grid: ResultGridComponent): void {
        this.resultGrids.push(grid);
    }

    onDownload(): void {
        this.downloading = true;

        let sheets = [];
        this.resultGrids.forEach((grid, i) => {
            sheets.push(grid.gridApi.getSheetDataForExcel({
                sheetName: grid.pivot.name.replace(/[\s<>:"\/\\|?*]+/g, '_')
                    .substring(0, 31),
            }));

            // download
            if (i === this.resultGrids.length-1) {
                grid.gridApi.exportMultipleSheetsAsExcel({
                    author: 'AIMS 3.0',
                    fileName: 'AIMS 3.0 Results.xlsx',
                    data: sheets
                });
            }
        });

        this.downloading = false;
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}