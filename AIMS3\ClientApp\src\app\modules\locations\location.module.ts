import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DataTablesModule } from 'angular-datatables';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { AuthorizationPipe } from '../../shared/pipes/auth.pipe';
import { SharedModule } from '../../shared/shared.module';
import { ModalsModule } from '../../_theme/partials';
import { LocationFormComponent } from './components/location-form/location-form.component';
import { LocationMapComponent } from './components/location-map/location-map.component';
import { LocationsComponent } from './components/locations.component';
import { LocationRoutingModule } from './location-routing.module';
import { LocationService } from './services/location.service';

@NgModule({
    declarations: [
        LocationsComponent,
        LocationFormComponent,
        LocationMapComponent
    ],
    imports: [
        FormsModule,
        ReactiveFormsModule,
        SharedModule,
        LocationRoutingModule,
        InlineSVGModule,
        DataTablesModule,
        ModalsModule
    ],
    providers: [
        LocationService,
        AuthorizationPipe
    ]
})
export class LocationModule { }