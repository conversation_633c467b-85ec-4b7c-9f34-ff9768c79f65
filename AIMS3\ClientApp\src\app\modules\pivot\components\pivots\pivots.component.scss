label.btn {
    color: var(--qs-gray-600);
    &:hover {
        color: var(--qs-primary);
    }
}
.pivot-tables {
  height: calc(100vh - 250px);
  overflow: hidden;

  &:hover {
    overflow-y: scroll;
  }
}
.pivot-card {
  border-color: var(--qs-text-gray-400) !important;
  user-select: none;

  span, span.pivot-name {
    cursor: pointer;
    max-width: 65%;
  }

  &:hover:has(.bg-light-primary) {
    border-color: var(--qs-primary) !important;
  }

  &:hover:has(.bg-light-info) {
    border-color: var(--qs-info) !important;
  }

  &.over:has(.bg-light-primary) {
    border: 2px dashed var(--qs-primary) !important;
  }

  &.over:has(.bg-light-info) {
    border: 2px dashed var(--qs-info) !important;
  }

  .border-bottom {
    border-top-left-radius: 0.42rem;
    border-top-right-radius: 0.42rem;
  }

  .pivot-table {
    display: block;
    cursor: pointer;
    border-bottom-left-radius: 0.42rem;
    border-bottom-right-radius: 0.42rem;
    height: 150px;
  }

  .menu-sub {
    width: 120px;
  }

  .cursor-move {
    cursor: move;
  }
}