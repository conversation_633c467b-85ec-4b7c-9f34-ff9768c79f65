<div class="container-fluid p-3">
  <div class="row" *ngIf="!selectedOutput">
    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header">
          <h5 class="card-title">Project Outputs</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-12">
              <table class="table table-hover">
                <thead>
                  <tr>
                    <th>Output Name</th>
                    <th>Budget</th>
                    <th>Expenditure</th>
                    <th>Beneficiaries</th>
                    <th>Progress</th>
                    <th>Action</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let output of outputs">
                    <td><strong>{{ output.name }}</strong></td>
                    <td>{{ formatCurrency(output.totalBudget) }}</td>
                    <td>{{ formatCurrency(output.totalCashDistributed) }}</td>
                    <td>{{ formatNumber(output.totalBeneficiaries) }}</td>
                    <td>
                      <div class="progress">
                        <div class="progress-bar" [style.width.%]="output.progress || 0">
                          {{ output.progress || 0 }}%
                        </div>
                      </div>
                    </td>
                    <td>
                      <button class="btn btn-sm btn-primary" (click)="selectOutput(output)">
                        <i class="fas fa-chart-bar"></i> Details
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <div class="row" *ngIf="selectedOutput">
    <div class="col-12 mb-3">
      <button class="btn btn-secondary" (click)="backToList()">
        <i class="fas fa-arrow-left"></i> Back to Outputs List
      </button>
    </div>

    <div class="col-12">
      <div class="card mb-4">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="card-title mb-0">{{ selectedOutput.name }}</h5>
        </div>
        <div class="card-body">
          <div class="row mb-4">
            <div class="col-md-3">
              <div class="detail-card">
                <span class="detail-label">Budget</span>
                <span class="detail-value">{{ formatCurrency(selectedOutput.totalBudget) }}</span>
              </div>
            </div>
            <div class="col-md-3">
              <div class="detail-card">
                <span class="detail-label">Expenditure</span>
                <span class="detail-value">{{ formatCurrency(selectedOutput.totalCashDistributed) }}</span>
              </div>
            </div>
            <div class="col-md-3">
              <div class="detail-card">
                <span class="detail-label">Beneficiaries</span>
                <span class="detail-value">{{ formatNumber(selectedOutput.totalBeneficiaries) }}</span>
              </div>
            </div>
            <div class="col-md-3">
              <div class="detail-card">
                <span class="detail-label">Progress</span>
                <span class="detail-value">{{ selectedOutput.progress || 0 }}%</span>
              </div>
            </div>
          </div>

          <div class="row">
            <div class="col-12">
              <div class="table-responsive">
                <table class="table table-hover">
                  <thead>
                    <tr>
                      <th>Category Name</th>
                      <th>Budget</th>
                      <th>Expenditure</th>
                      <th>Beneficiaries</th>
                      <th>Progress</th>
                      <th>Action</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr *ngFor="let category of selectedOutputCategories">
                      <td><strong>{{ category.name }}</strong></td>
                      <td>{{ formatCurrency(category.totalBudget) }}</td>
                      <td>{{ formatCurrency(category.totalCashDistributed) }}</td>
                      <td>{{ formatNumber(category.totalBeneficiaries) }}</td>
                      <td>
                        <div class="progress">
                          <div class="progress-bar" [style.width.%]="category.progress || 0">
                            {{ category.progress || 0 }}%
                          </div>
                        </div>
                      </td>
                      <td>
                        <button class="btn btn-sm btn-primary" (click)="showCategoryDetails(category)">
                          <i class="fas fa-chart-bar"></i> Details
                        </button>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div> 