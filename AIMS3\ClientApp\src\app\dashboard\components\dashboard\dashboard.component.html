<div class="top-toolbar">
    <div class="d-flex">
        <div class="notice bg-light-primary rounded border-primary border border-dashed text-truncate p-2 ms-5 fs-8" *ngIf="filtered.length > 1">
            <span class="fw-semibold">Filtered by:</span> {{ filtered[0] }}
        </div>
    </div>
</div>
<div class="app-toolbar">
    <aims-loading class="w-100" [showTable]="false" *ngIf="loading"></aims-loading>
    <div class="filter-toolbar d-flex flex-stack flex-wrap flex-md-nowrap" *ngIf="!loading">
        <!-- Filters -->
        <div class="d-flex align-items-center flex-wrap flex-md-nowrap">
            <filter-ddl [id]="'projIds'" #project class="filter-container" [placeholders]="['Project']" [minWidth]="130"
                        [options]="projects" (change)="onFilterChange($event)" [multiple]="true">
            </filter-ddl>
            <filter-ddl [id]="'outputs'" #output class="filter-container ms-2" [placeholders]="['Output']" [minWidth]="100"
                        [options]="outputs" (change)="onFilterChange($event)" [multiple]="true">
            </filter-ddl>
            <filter-ddl [id]="'catIds'" #category class="filter-container ms-2" [placeholders]="['Category','Categories']" [minWidth]="140"
                        [options]="categories" (change)="onFilterChange($event)" [multiple]="true">
            </filter-ddl>
            <filter-ddl [id]="'profIds'" #profile class="filter-container ms-2" [placeholders]="['Intervention']" [minWidth]="120"
                        [options]="profiles" (change)="onFilterChange($event)" [multiple]="true">
            </filter-ddl>
            <div class="bullet bg-secondary h-35px w-1px mx-4"></div>
            <filter-ddl [id]="'orgIds'" #partner class="filter-container me-2" [placeholders]="['Partner']" [minWidth]="130"
                        [options]="orgs" (change)="onFilterChange($event)" [multiple]="true" *ngIf="isGlobalUser">
            </filter-ddl>
            <filter-ddl [id]="'dataStatus'" #status class="filter-container" [placeholders]="['Data type']" [minWidth]="130"
                        [options]="submitStatus" [showSearch]="false" [multiple]="true" [selectedValues]="[1,2]" (change)="onFilterChange($event)">
            </filter-ddl>
            <filter-ddl [id]="'regions'" #region class="filter-container ms-2" [placeholders]="['Region']" [minWidth]="130"
                        [options]="regions" [multiple]="true" (change)="onFilterChange($event)">
            </filter-ddl>
            <div class="bullet bg-secondary h-35px w-1px mx-4"></div>
            <div class="d-flex flex-stack gap-2">
                <ng-container *ngIf="!filters.approvalMode">
                    <span class="fs-7 text-gray-800">From</span>
                    <input id="periodFrom" type="month" class="form-control form-control-sm" (change)="onPeriodChange($event)"
                           ngbTooltip="Period: From date" placement="left" min="2020-01" />
                </ng-container>
                <span class="fs-7 text-gray-800">To</span>
                <input id="periodTo" type="month" class="form-control form-control-sm" (change)="onPeriodChange($event)"
                       ngbTooltip="Period: To date" placement="left" min="2020-01" />
            </div>

            <button class="btn btn-sm btn-light-primary border border-primary text-truncate py-2 px-3 ms-2"
                    role="button" (click)="onFilterData()">
                Apply filter
            </button>
            <a class="cursor-pointer fs-8 ms-3" (click)="resetFilters()" *ngIf="filtered.length > 1">Reset</a>
        </div>
    </div>
</div>

<aims-map class="blockui" (initialized)="onMapComponentInit($event)"></aims-map>

<div class="blockui" style="position: initial" *ngIf="working">
    <aims-working></aims-working>
</div>