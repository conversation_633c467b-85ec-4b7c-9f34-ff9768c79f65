import { Component, EventEmitter, HostBinding, Input, On<PERSON><PERSON>roy, OnInit, Output } from '@angular/core';

@Component({
    selector: 'filter-ddl',
    templateUrl: 'filter-ddl.control.html',
    styleUrls: ['filter-ddl.control.scss']
})
export class FilterDropdownList implements OnInit, OnDestroy {
    @HostBinding('role') role = 'combobox';

    @Input() id: string;
    @Input() minWidth: number;
    @Input() showSearch: boolean = true;
    @Input() showAll: boolean = true;
    @Input() multiple: boolean = false;
    @Input() options: any = [];
    @Input() selectedValues = [];
    @Input() placeholders: string[];
    @Input() highlightSelected: boolean = true;

    items: any = [];
    selItem: string;
    show: boolean = false;
    dismissEvent: any;

    @Output() change = new EventEmitter();

    ngOnInit(): void {
        this.generateSelectionLabel();
    }

    onInit(e: any): void {
        if (!this.items.length)
            this.items = [...this.options];

        this.show = !this.show;
       
        const target = e.target.closest('filter-ddl') as HTMLDivElement;
        const targetRect = target.getBoundingClientRect();

        let x = window.innerWidth - targetRect.right - 8;
        if (targetRect.right < target.offsetWidth)
            x = window.innerWidth - (target.offsetWidth + targetRect.left + 8);

        // add event to dismiss outside the modal
        this.dismissEvent = (e) => {
            if (target.contains(e.target))
                return;
            this.close();
        }
        document.addEventListener('click', this.dismissEvent, true);

        if (!this.showSearch)
            return;
        
        const searchBox = e.target.parentElement.querySelector('.menu-item-search input');
        if (searchBox) {
            searchBox.value = '';
            this.items = [...this.options];
            searchBox.focus();
        }
    }

    onSearch(e): void {
        if (!e.target.value)
            this.items = [...this.options];
        else
            this.items = this.options.filter(i => i.name.toLowerCase().indexOf(e.target.value) > -1);
    }

    setSelectedValues(values: (number | string)[]): void {
        this.selectedValues = values;
        this.generateSelectionLabel();
        this.change.emit({ id: this.id, selectedValues: this.selectedValues });
    }

    onSelectOption(optId?: any, emitChanges: boolean = true): void {
        if (optId === undefined || optId === null)
            this.selectedValues = [];
        else if (this.selectedValues.includes(optId))
            this.selectedValues = this.selectedValues.filter(v => v !== optId);
        else if (this.multiple)
            this.selectedValues.push(optId);
        else
            this.selectedValues = [optId];

        this.generateSelectionLabel();

        if (!this.multiple)
            this.show = false;

        if (emitChanges)
            this.change.emit({ id: this.id, selectedValues: this.selectedValues });
    }

    selectAll(): void {
        if (this.selectedValues.length === this.items.length)
            this.setSelectedValues([]);
        else
            this.setSelectedValues(this.items.map(i => i.id));
    }

    private generateSelectionLabel(): void {
        if (!this.selectedValues.length)
            this.selItem = '';
        else if (this.selectedValues.length === 1)
            this.selItem = this.options.find(o => o.id === this.selectedValues[0])?.name;
        else {
            if (!this.placeholders[1])
                this.placeholders.push(this.placeholders[0] + 's');
            this.selItem = `${this.selectedValues.length} ${this.placeholders[1]}`;
        }
    }

    clearSelection(emitChanges: boolean = false): void {
        this.onSelectOption(null, emitChanges);
        this.close();
    }

    close(): void {
        this.show = false;

        // remove any dismiss click event
        if (this.dismissEvent) {
            document.removeEventListener('click', this.dismissEvent, true);
            this.dismissEvent = null;
        }
    }

    ngOnDestroy() {
        document.removeEventListener('click', this.dismissEvent, true);
    }
}