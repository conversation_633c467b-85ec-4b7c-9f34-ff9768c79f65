export interface ICommunity {
    id: number;
    districtId: number;
    name: string;
}

export interface ICommunityInfo {
    id: number;
    region: number,
    provinceId: number;
    districtId: number;
    name: string;
    group: string;
}

export class Community {
    constructor(
        public id: number,
        public locationId: number,
        public provinceId: number,
        public name: string,
        public gpsLat: number,
        public gpsLon: number,
        public isVerified: boolean = false,
        public isApproved: boolean = false,
        public districtId?: number,
        public distName?: string,
        /*public remarks?: string*/
    ) { }
}

export class CommunityList {
    constructor(
        public id: number,
        public provinceId: number,
        public provName: string,
        public name: string,
        public locationId?: number,
        public orgId?: number,
        public orgName?: string,
        public districtId?: number,
        public distName?: string,
        public gpsLat?: number,
        public gpsLon?: number,
        public isVerified?: boolean,
        public isApproved?: boolean,
        //public remarks?: string,
        public distance?: number,
        public status?: string,
        public color?: string,
        public dateCreated?: Date
    ) { }
}

export class LocFilter {
    public provId?: number;
    public distId?: number;
    public isApproved?: number = 0;
}