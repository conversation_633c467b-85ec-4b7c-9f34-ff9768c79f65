<div class="hierarchical-report-container">
  <!-- Debug Panel has been removed as it's not needed in production -->
  
  <!-- Loading & Error States -->
  <div *ngIf="loading" class="text-center p-5">
    <div class="spinner-border text-primary" role="status">
      <span class="visually-hidden">Loading...</span>
    </div>
    <p class="mt-3">Loading report data...</p>
  </div>
  
  <div *ngIf="error" class="alert alert-danger mt-3" role="alert">
    {{ error }}
    <button type="button" class="btn btn-sm btn-outline-danger ms-3" (click)="loadReport()">
      Try Again
    </button>
  </div>
  
  <!-- Report Header with Export Options -->
  <div class="d-flex justify-content-between align-items-center mb-4" *ngIf="!loading && !error">
    <h3 class="m-0">Hierarchical Project Report</h3>
    <div class="export-buttons">
      <button type="button" class="btn btn-sm btn-light-danger me-2" (click)="loadReport()">
        <i class="bi bi-arrow-clockwise me-1"></i> Reload Data
      </button>
      <button type="button" class="btn btn-sm btn-light-warning me-2" (click)="toggleDebugInfo()">
        <i class="bi bi-bug-fill me-1"></i> {{ showDebugInfo ? 'Hide' : 'Show' }} Debug Info
      </button>
      <button type="button" class="btn btn-sm btn-light-primary me-2" (click)="exportToExcel()">
        <i class="bi bi-file-earmark-excel me-1"></i> Export to Excel
      </button>
      <button type="button" class="btn btn-sm btn-light-primary" (click)="exportToPdf()">
        <i class="bi bi-file-earmark-pdf me-1"></i> Export to PDF
      </button>
    </div>
  </div>
  
  <!-- No Data State -->
  <div *ngIf="!loading && !error && (!hierarchicalData || hierarchicalData.length === 0)" class="alert alert-info">
    No data found with the current filters. Try adjusting your filters or expanding your search criteria.
  </div>
  
  <!-- Dynamic column loading button -->
  <div *ngIf="hierarchicalData?.length && needsDynamicColumns()" class="alert alert-info mt-3 d-flex justify-content-between align-items-center">
    <div>
      <strong>Dynamic Indicators:</strong> Dynamic indicators data is not loaded to improve performance. 
      Click the button to load indicator data for all activities.
    </div>
    <button class="btn btn-primary btn-sm" (click)="loadDynamicColumns()" [disabled]="loading">
      <span *ngIf="loading" class="spinner-border spinner-border-sm me-1" role="status" aria-hidden="true"></span>
      Load Indicators
    </button>
  </div>
  
  <!-- Main Report Content -->
  <div *ngIf="!loading && !error && hierarchicalData && hierarchicalData.length > 0" class="report-content">
    <!-- Project Groups -->
    <div *ngFor="let group of hierarchicalData" class="card mb-4">
      <!-- Group Header -->
      <div class="card-header bg-light d-flex justify-content-between align-items-center cursor-pointer" 
           (click)="toggleGroup(group.id)">
        <div class="d-flex align-items-center">
          <i class="bi" 
             [ngClass]="isGroupExpanded(group.id) ? 'bi-dash-square' : 'bi-plus-square'"></i>
          <h4 class="ms-2 mb-0">{{ group.name }}</h4>
        </div>
        <div class="badge bg-primary rounded-pill">{{ group.summary.totalProjects }} Projects</div>
      </div>
      
      <!-- Group Summary -->
      <div class="card-body bg-light border-bottom" *ngIf="isGroupExpanded(group.id)">
        <div class="row">
          <div class="col-md-3">
            <div class="fw-bold text-muted">Total Budget</div>
            <div class="fs-5">{{ formatCurrency(group.summary.totalBudget) }}</div>
          </div>
          <div class="col-md-3">
            <div class="fw-bold text-muted">Cash Distributed</div>
            <div class="fs-5">{{ formatCurrency(group.summary.totalCashDistributed) }}</div>
          </div>
          <div class="col-md-2">
            <div class="fw-bold text-muted">Progress</div>
            <div class="progress mt-2" style="height: 10px;">
              <div class="progress-bar bg-success" 
                   [style.width]="group.summary.progress + '%'" 
                   [attr.aria-valuenow]="group.summary.progress" 
                   aria-valuemin="0" 
                   aria-valuemax="100">
              </div>
            </div>
            <div class="small text-end mt-1">{{ formatPercentage(group.summary.progress) }}</div>
          </div>
          <div class="col-md-2">
            <div class="fw-bold text-muted">Beneficiaries</div>
            <div class="fs-5">{{ formatNumber(group.summary.beneficiaries) }}</div>
          </div>
          <div class="col-md-2">
            <div class="fw-bold text-muted">Activities</div>
            <div class="fs-5">{{ formatNumber(group.summary.totalActivities) }}</div>
          </div>
        </div>
      </div>
      
      <!-- Projects List -->
      <div class="projects-list" *ngIf="isGroupExpanded(group.id)">
        <div *ngFor="let project of group.projects" class="project-item border-bottom p-0">
          <!-- Project Header -->
          <div class="d-flex justify-content-between align-items-center p-3 cursor-pointer"
               [ngClass]="isProjectExpanded(project.id) ? 'bg-light-primary' : ''"
               (click)="toggleProject(project.id)">
            <div class="d-flex align-items-center">
              <i class="bi" 
                 [ngClass]="isProjectExpanded(project.id) ? 'bi-dash-circle' : 'bi-plus-circle'"></i>
              <div class="ms-2">
                <h5 class="mb-0">{{ project.code }}: {{ project.name }}</h5>
                <div class="small text-muted">{{ project.startDate | date:'mediumDate' }} - {{ project.endDate | date:'mediumDate' }}</div>
              </div>
            </div>
            <div class="badge" 
                 [ngClass]="{
                   'bg-success': project.status === 'completed',
                   'bg-primary': project.status === 'active',
                   'bg-warning': project.status === 'planned',
                   'bg-danger': project.status === 'suspended'
                 }">
              {{ project.status | titlecase }}
            </div>
          </div>
          
          <!-- Project Summary -->
          <div class="project-summary p-3 border-top border-bottom bg-light" *ngIf="isProjectExpanded(project.id)">
            <div class="row">
              <div class="col-md-3">
                <div class="fw-bold text-muted">Budget</div>
                <div class="fs-5">{{ formatCurrency(project.budget) }}</div>
              </div>
              <div class="col-md-3">
                <div class="fw-bold text-muted">Cash Distributed</div>
                <div class="fs-5">{{ formatCurrency(project.cashDistributed) }}</div>
              </div>
              <div class="col-md-2">
                <div class="fw-bold text-muted">Progress</div>
                <div class="progress mt-2" style="height: 8px;">
                  <div class="progress-bar bg-success" 
                       [style.width]="project.progress + '%'" 
                       [attr.aria-valuenow]="project.progress" 
                       aria-valuemin="0" 
                       aria-valuemax="100">
                  </div>
                </div>
                <div class="small text-end mt-1">{{ formatPercentage(project.progress) }}</div>
              </div>
              <div class="col-md-2">
                <div class="fw-bold text-muted">Beneficiaries</div>
                <div class="fs-5">{{ formatNumber(project.summary.beneficiaries) }}</div>
              </div>
              <div class="col-md-2">
                <div class="fw-bold text-muted">Interventions</div>
                <div class="fs-5">{{ project.summary.totalInterventions }}</div>
              </div>
            </div>
          </div>
          
          <!-- Interventions List -->
          <div class="interventions-list ps-4" *ngIf="isProjectExpanded(project.id)">
            <div *ngFor="let intervention of project.interventions" class="intervention-item border-bottom">
              <!-- Intervention Header -->
              <div class="d-flex justify-content-between align-items-center p-3 cursor-pointer"
                   [ngClass]="isInterventionExpanded(intervention.id) ? 'bg-light-success' : ''"
                   (click)="toggleIntervention(intervention.id)">
                <div class="d-flex align-items-center">
                  <i class="bi" 
                     [ngClass]="isInterventionExpanded(intervention.id) ? 'bi-dash-circle-fill' : 'bi-plus-circle-fill'"></i>
                  <div class="ms-2">
                    <h6 class="mb-0">{{ intervention.name }}</h6>
                    <div class="small text-muted">Category: {{ intervention.categoryName }}</div>
                  </div>
                </div>
                <div class="badge bg-info rounded-pill">{{ intervention.summary.totalActivities }} Activities</div>
              </div>
              
              <!-- Intervention Summary -->
              <div class="intervention-summary p-3 border-top border-bottom bg-light-subtle" *ngIf="isInterventionExpanded(intervention.id)">
                <div class="row">
                  <div class="col-md-3">
                    <div class="fw-bold text-muted">Budget</div>
                    <div>{{ formatCurrency(intervention.summary.totalBudget) }}</div>
                  </div>
                  <div class="col-md-3">
                    <div class="fw-bold text-muted">Cash Distributed</div>
                    <div>{{ formatCurrency(intervention.summary.totalCashDistributed) }}</div>
                  </div>
                  <div class="col-md-2">
                    <div class="fw-bold text-muted">Progress</div>
                    <div class="progress mt-2" style="height: 6px;">
                      <div class="progress-bar bg-success" 
                           [style.width]="intervention.summary.progress + '%'" 
                           [attr.aria-valuenow]="intervention.summary.progress" 
                           aria-valuemin="0" 
                           aria-valuemax="100">
                      </div>
                    </div>
                    <div class="small text-end mt-1">{{ formatPercentage(intervention.summary.progress) }}</div>
                  </div>
                  <div class="col-md-2">
                    <div class="fw-bold text-muted">Beneficiaries</div>
                    <div>{{ formatNumber(intervention.summary.beneficiaries) }}</div>
                  </div>
                  <div class="col-md-2">
                    <div class="fw-bold text-muted">Activities</div>
                    <div>{{ intervention.summary.totalActivities }}</div>
                  </div>
                </div>
                
                <!-- Intervention Description -->
                <div class="mt-3" *ngIf="intervention.description">
                  <div class="fw-bold text-muted">Description</div>
                  <p class="mb-0">{{ intervention.description }}</p>
                </div>
                
                <!-- Dynamic Indicators Button -->
                <div class="mt-3" *ngIf="hasDynamicColumns(intervention.activities)">
                  <button type="button" class="btn btn-sm btn-outline-primary" 
                          (click)="openCategoryModal(intervention.categoryId, intervention.categoryName)">
                    <i class="bi bi-graph-up me-1"></i>
                    View Dynamic Indicators
                  </button>
                </div>
              </div>
              
              <!-- Activities List -->
              <div class="activities-table p-3" *ngIf="isInterventionExpanded(intervention.id) && intervention.activities.length > 0">
                <h6 class="mb-3">Activities</h6>
                <div class="table-responsive">
                  <table class="table table-bordered table-hover">
                    <thead class="table-light">
                      <tr>
                        <th>ID</th>
                        <th>Status</th>
                        <th>Timeline</th>
                        <th>Location</th>
                        <th>Progress</th>
                        <th>Budget</th>
                        <th>Cash Distributed</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr *ngFor="let activity of intervention.activities">
                        <td>{{ activity.uniqueId }}</td>
                        <td>
                          <span class="badge" 
                                [ngClass]="{
                                  'bg-success': activity.status === 'completed',
                                  'bg-primary': activity.status === 'ongoing',
                                  'bg-warning': activity.status === 'planned',
                                  'bg-secondary': activity.status === 'archived',
                                  'bg-danger': activity.status === 'cancelled' || activity.status === 'suspended'
                                }">
                            {{ activity.status | titlecase }}
                          </span>
                        </td>
                        <td>{{ activity.startDate | date:'shortDate' }} - {{ activity.endDate | date:'shortDate' }}</td>
                        <td>{{ activity.location.provinceName }}, {{ activity.location.districtName }}</td>
                        <td>
                          <div class="progress" style="height: 6px;">
                            <div class="progress-bar bg-success" 
                                 [style.width]="activity.progress + '%'" 
                                 [attr.aria-valuenow]="activity.progress" 
                                 aria-valuemin="0" 
                                 aria-valuemax="100">
                            </div>
                          </div>
                          <div class="small text-end mt-1">{{ formatPercentage(activity.progress) }}</div>
                        </td>
                        <td>{{ formatCurrency(activity.budget) }}</td>
                        <td>{{ formatCurrency(activity.cashDistributed) }}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
                
                <!-- Dynamic Columns Section - Show if any activity has dynamic columns -->
                <div class="dynamic-indicators-section" *ngIf="hasDynamicColumns(intervention.activities)">
                  <h6 class="mb-3">Dynamic Indicators</h6>
                  <div class="table-responsive">
                    <table class="table table-bordered table-hover">
                      <thead class="table-light">
                        <tr>
                          <th>Activity</th>
                          <th *ngFor="let column of getDynamicColumnNames(intervention.activities)">{{ column }}</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr *ngFor="let activity of intervention.activities">
                          <td>{{ activity.uniqueId }}</td>
                          <!-- Add a cell for each possible dynamic column -->
                          <td *ngFor="let columnName of getDynamicColumnNames(intervention.activities)">
                            {{ getActivityDynamicColumnValue(activity, columnName) }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  
                  <!-- Structured Activity Data -->
                  <div class="structured-data mt-4">
                    <div class="row">
                      <div class="col-md-12">
                        <h6 class="text-primary">Structured Activity Data</h6>
                        <div class="accordion" id="structuredDataAccordion">
                          <!-- Loop through each activity -->
                          <div class="accordion-item" *ngFor="let activity of intervention.activities; let actIndex = index">
                            <h2 class="accordion-header">
                              <button class="accordion-button collapsed" type="button" 
                                      data-bs-toggle="collapse" 
                                      [attr.data-bs-target]="'#activity' + activity.id" 
                                      aria-expanded="false" 
                                      [attr.aria-controls]="'activity' + activity.id">
                                {{ activity.uniqueId }} - {{ activity.status | titlecase }}
                              </button>
                            </h2>
                            <div [id]="'activity' + activity.id" class="accordion-collapse collapse">
                              <div class="accordion-body">
                                <!-- New Dynamic Columns Display Component -->
                                <div *ngIf="activity.processedDynamicData" class="mb-4">
                                  <app-dynamic-columns-display 
                                    [processedData]="activity.processedDynamicData"
                                    [showRawData]="showDebugInfo"
                                    layout="cards">
                                  </app-dynamic-columns-display>
                                </div>
                                
                                <div class="row">
                                  <!-- Info Data -->
                                  <div class="col-md-6">
                                    <div class="card border-light mb-3">
                                      <div class="card-header bg-light-info">
                                        <h6 class="mb-0">General Information</h6>
                                      </div>
                                      <div class="card-body">
                                        <div *ngIf="!activity.info || !hasProperties(activity.info)" class="text-muted">
                                          No additional information available
                                        </div>
                                        
                                        <!-- Beneficiary Details Section -->
                                        <div *ngIf="activity.info?.beneficiaryDetails && hasProperties(activity.info.beneficiaryDetails)" class="mb-3">
                                          <h6 class="text-muted">Beneficiary Details</h6>
                                          <div class="table-responsive">
                                            <table class="table table-sm">
                                              <tbody>
                                                <tr *ngFor="let prop of getObjectProperties(activity.info.beneficiaryDetails)">
                                                  <th>{{ formatPropertyName(prop.key) }}</th>
                                                  <td>{{ prop.value }} {{ getBeneficiaryUnit(prop.key) }}</td>
                                                </tr>
                                              </tbody>
                                            </table>
                                          </div>
                                        </div>
                                        
                                        <!-- Other Info Properties -->
                                        <div *ngIf="getInfoProperties(activity).length > 0">
                                          <div class="table-responsive">
                                            <table class="table table-sm">
                                              <tbody>
                                                <tr *ngFor="let prop of getInfoProperties(activity)">
                                                  <th>{{ formatPropertyName(prop.key) }}</th>
                                                  <td>
                                                    {{ prop.value.value }} 
                                                    <small *ngIf="prop.value.unit" class="text-muted">{{ prop.value.unit }}</small>
                                                  </td>
                                                </tr>
                                              </tbody>
                                            </table>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                  
                                  <!-- Cumulative Progress Data -->
                                  <div class="col-md-6">
                                    <div class="card border-light mb-3">
                                      <div class="card-header bg-light-success">
                                        <h6 class="mb-0">Progress Metrics</h6>
                                      </div>
                                      <div class="card-body">
                                        <div *ngIf="!activity.cumulativeProgress || !hasProperties(activity.cumulativeProgress)" class="text-muted">
                                          No progress metrics available
                                        </div>
                                        
                                        <!-- Target & Actual Values -->
                                        <div *ngIf="activity.cumulativeProgress?.target || activity.cumulativeProgress?.actual" class="mb-3">
                                          <div class="d-flex justify-content-between mb-2">
                                            <div>
                                              <span class="text-muted">Target:</span> 
                                              <span class="fw-bold">{{ activity.cumulativeProgress?.target || 'N/A' }}</span>
                                            </div>
                                            <div>
                                              <span class="text-muted">Actual:</span> 
                                              <span class="fw-bold">{{ activity.cumulativeProgress?.actual || 'N/A' }}</span>
                                            </div>
                                            <div>
                                              <span class="text-muted">Completion:</span> 
                                              <span class="fw-bold">{{ activity.cumulativeProgress?.percentComplete || activity.progress }}%</span>
                                            </div>
                                          </div>
                                          <div class="progress" style="height: 8px;">
                                            <div class="progress-bar bg-success" 
                                                [style.width]="(activity.cumulativeProgress?.percentComplete || activity.progress) + '%'" 
                                                [attr.aria-valuenow]="activity.cumulativeProgress?.percentComplete || activity.progress" 
                                                aria-valuemin="0" 
                                                aria-valuemax="100">
                                            </div>
                                          </div>
                                        </div>
                                        
                                        <!-- Indicators Table -->
                                        <div *ngIf="activity.cumulativeProgress?.indicators && activity.cumulativeProgress.indicators.length > 0" class="mb-3">
                                          <h6 class="text-muted">Progress Indicators</h6>
                                          <div class="table-responsive">
                                            <table class="table table-sm">
                                              <thead>
                                                <tr>
                                                  <th>Indicator</th>
                                                  <th>Value</th>
                                                </tr>
                                              </thead>
                                              <tbody>
                                                <tr *ngFor="let indicator of activity.cumulativeProgress.indicators">
                                                  <td>{{ indicator.name }}</td>
                                                  <td>{{ indicator.value }} <small *ngIf="indicator.unit" class="text-muted">{{ indicator.unit }}</small></td>
                                                </tr>
                                              </tbody>
                                            </table>
                                          </div>
                                        </div>
                                        
                                        <!-- Other Progress Properties -->
                                        <div *ngIf="getProgressProperties(activity).length > 0">
                                          <div class="table-responsive">
                                            <table class="table table-sm">
                                              <tbody>
                                                <tr *ngFor="let prop of getProgressProperties(activity)">
                                                  <th>{{ formatPropertyName(prop.key) }}</th>
                                                  <td>
                                                    {{ prop.value.value }} 
                                                    <small *ngIf="prop.value.unit" class="text-muted">{{ prop.value.unit }}</small>
                                                  </td>
                                                </tr>
                                              </tbody>
                                            </table>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <!-- Debug information section -->
                  <div class="debug-info mt-4" *ngIf="showDebugInfo">
                    <h6 class="text-danger">Debug Information</h6>
                    <div class="small bg-light p-3 rounded">
                      <div *ngFor="let activity of intervention.activities">
                        <strong>Activity {{ activity.uniqueId }}</strong>
                        <div *ngIf="activity.dynamicColumns?.length">
                          <span class="badge bg-success me-2">{{ activity.dynamicColumns.length }} processed dynamic columns</span>
                        </div>
                        <div *ngIf="activity.DynamicColumnsValues?.length">
                          <span class="badge bg-primary me-2">{{ activity.DynamicColumnsValues.length }} raw DynamicColumnsValues</span>
                        </div>
                        <div *ngIf="activity.DynamicColactprogvalues?.length">
                          <span class="badge bg-info me-2">{{ activity.DynamicColactprogvalues.length }} raw DynamicColactprogvalues</span>
                        </div>
                        
                        <!-- Show raw dynamic columns data -->
                        <div *ngIf="activity.dynamicColumns?.length" class="mt-2 mb-3">
                          <div *ngFor="let col of activity.dynamicColumns" class="ms-3 mb-1">
                            <strong>{{ col.columnName }}:</strong> {{ col.value }} 
                            <small class="text-muted">{{ col.unit }}</small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Category Modal for Dynamic Indicators -->
<app-category-modal
  [isVisible]="categoryModalVisible"
  [categoryId]="selectedCategoryId"
  [dynamicColumns]="categoryDynamicColumns"
  [activities]="categoryActivities"
  (closeModal)="closeCategoryModal()">
</app-category-modal> 