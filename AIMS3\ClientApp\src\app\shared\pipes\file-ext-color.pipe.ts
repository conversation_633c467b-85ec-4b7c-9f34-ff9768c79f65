import { Pipe, PipeTransform } from '@angular/core';

@Pipe({ name: 'fileExtColor' })
export class FileExtColorPipe implements PipeTransform {
    transform(fn: string): string {
        let c = /(?:\.([^.]+))?$/.exec(fn)[1];
        if (c) {
            c = c.toLowerCase();
            if (c.indexOf('pdf') > -1)
                return 'red';
            else if (c.indexOf('xls') > -1)
                return 'green-haze';
            else if (c.indexOf('doc') > -1)
                return 'blue';
            else if (c.indexOf('ppt') > -1 || c.indexOf('pps') > -1)
                return 'red-haze';
            else if (c.indexOf('jpg') > -1 || c.indexOf('jpeg') > -1 || c.indexOf('png') > -1)
                return 'purple-sharp'
            else if (c.indexOf('zip') > -1)
                return 'yellow'
            else
                return 'btn-default';
        }
    }
}

//export const getExtAndColor = (fn: string): string => {
//    return this.getExt(fn) + ' font-' + this.getExtColor(fn.replace('?', '')).replace('btn-default', '');
//}