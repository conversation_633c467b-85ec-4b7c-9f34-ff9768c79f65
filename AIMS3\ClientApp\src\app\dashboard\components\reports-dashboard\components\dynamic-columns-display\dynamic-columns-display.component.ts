import { Component, Input, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ProcessedDynamicData, DynamicColumnData } from '../../models/reports.model';

@Component({
  selector: 'app-dynamic-columns-display',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="dynamic-columns-container" *ngIf="processedData">
      <!-- Info Section -->
      <div class="row mb-3" *ngIf="hasInfoColumns()">
        <div class="col-12">
          <h6 class="text-muted mb-2">Activity Information</h6>
          <div class="row">
            <div class="col-md-6 col-lg-4 mb-2" *ngFor="let item of getInfoColumns()">
              <div class="card border-0 bg-light-info">
                <div class="card-body p-3">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 class="card-title mb-1 fs-7">{{ item.displayName }}</h6>
                      <div class="fw-bold text-dark">{{ formatValue(item.value, item.dataType, item.unit) }}</div>
                    </div>
                    <i class="fas fa-info-circle text-info"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Targets Section -->
      <div class="row mb-3" *ngIf="hasTargetColumns()">
        <div class="col-12">
          <h6 class="text-muted mb-2">Targets</h6>
          <div class="row">
            <div class="col-md-6 col-lg-4 mb-2" *ngFor="let item of getTargetColumns()">
              <div class="card border-0 bg-light-warning">
                <div class="card-body p-3">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 class="card-title mb-1 fs-7">{{ item.displayName }}</h6>
                      <div class="fw-bold text-dark">{{ formatValue(item.value, item.dataType, item.unit) }}</div>
                    </div>
                    <i class="fas fa-bullseye text-warning"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Progress Section -->
      <div class="row mb-3" *ngIf="hasProgressColumns()">
        <div class="col-12">
          <h6 class="text-muted mb-2">Progress Metrics</h6>
          <div class="row">
            <div class="col-md-6 col-lg-4 mb-2" *ngFor="let item of getProgressColumns()">
              <div class="card border-0 bg-light-success">
                <div class="card-body p-3">
                  <div class="d-flex justify-content-between align-items-center">
                    <div>
                      <h6 class="card-title mb-1 fs-7">{{ item.displayName }}</h6>
                      <div class="fw-bold text-dark">{{ formatValue(item.value, item.dataType, item.unit) }}</div>
                      <div class="progress progress-sm mt-2" *ngIf="item.dataType === 'percentage'">
                        <div class="progress-bar bg-success" 
                             [style.width]="getPercentageWidth(item.value)" 
                             role="progressbar"></div>
                      </div>
                    </div>
                    <i class="fas fa-chart-line text-success"></i>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Raw Data Table (Optional) -->
      <div class="row" *ngIf="showRawData && processedData.raw.length > 0">
        <div class="col-12">
          <h6 class="text-muted mb-2">All Dynamic Data</h6>
          <div class="table-responsive">
            <table class="table table-sm table-striped">
              <thead>
                <tr>
                  <th>Field</th>
                  <th>Value</th>
                  <th>Type</th>
                  <th>Category</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngFor="let column of processedData.raw">
                  <td>{{ column.displayName || column.columnName }}</td>
                  <td>{{ formatValue(column.value, column.dataType, column.unit) }}</td>
                  <td><span class="badge badge-light-primary">{{ column.dataType }}</span></td>
                  <td><span class="badge badge-light-secondary">{{ column.columnType }}</span></td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .dynamic-columns-container {
      margin: 1rem 0;
    }
    
    .card {
      transition: all 0.2s ease;
    }
    
    .card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    
    .progress-sm {
      height: 4px;
    }
    
    .fs-7 {
      font-size: 0.85rem;
    }
    
    .bg-light-info {
      background-color: rgba(54, 162, 235, 0.1);
    }
    
    .bg-light-warning {
      background-color: rgba(255, 193, 7, 0.1);
    }
    
    .bg-light-success {
      background-color: rgba(40, 167, 69, 0.1);
    }
  `]
})
export class DynamicColumnsDisplayComponent implements OnInit {
  @Input() processedData: ProcessedDynamicData | null = null;
  @Input() showRawData: boolean = false;
  @Input() layout: 'cards' | 'table' | 'compact' = 'cards';

  ngOnInit(): void {
    // Component initialization
  }

  hasInfoColumns(): boolean {
    return this.processedData ? Object.keys(this.processedData.info).length > 0 : false;
  }

  hasTargetColumns(): boolean {
    return this.processedData ? Object.keys(this.processedData.targets).length > 0 : false;
  }

  hasProgressColumns(): boolean {
    return this.processedData ? Object.keys(this.processedData.progress).length > 0 : false;
  }

  getInfoColumns(): any[] {
    return this.processedData ? Object.values(this.processedData.info) : [];
  }

  getTargetColumns(): any[] {
    return this.processedData ? Object.values(this.processedData.targets) : [];
  }

  getProgressColumns(): any[] {
    return this.processedData ? Object.values(this.processedData.progress) : [];
  }

  formatValue(value: any, dataType: string, unit?: string): string {
    if (value === null || value === undefined) {
      return 'N/A';
    }

    switch (dataType?.toLowerCase()) {
      case 'currency':
        return new Intl.NumberFormat('en-US', { 
          style: 'currency', 
          currency: 'USD' 
        }).format(Number(value));
        
      case 'percentage':
        return `${Number(value).toFixed(1)}%`;
        
      case 'number':
        const num = Number(value);
        return isNaN(num) ? value.toString() : num.toLocaleString();
        
      case 'checkbox':
        return value ? 'Yes' : 'No';
        
      case 'date':
        try {
          return new Date(value).toLocaleDateString();
        } catch {
          return value.toString();
        }
        
      default:
        const result = value.toString();
        return unit ? `${result} ${unit}` : result;
    }
  }

  getPercentageWidth(value: any): string {
    const num = Number(value);
    if (isNaN(num)) return '0%';
    return `${Math.min(Math.max(num, 0), 100)}%`;
  }
} 