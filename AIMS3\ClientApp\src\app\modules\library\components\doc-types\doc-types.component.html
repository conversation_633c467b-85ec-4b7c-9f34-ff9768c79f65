<app-modal #modal [modalConfig]="modalConfig" *ngIf="(['admin'] | isAuth)">
    <!-- Toolbar -->
    <div class="d-flex flex-stack align-items-center">
        <h5 class="flex-start fw-semibold text-primary m-0" *ngIf="!editMode">Document file types</h5>
        <h5 class="flex-start fw-semibold text-primary m-0" *ngIf="editMode">{{ docType.id > 0 ? 'Edit document file type' : 'Add new document file type' }}</h5>
        <button class="btn btn-sm btn-icon btn-light btn-active-color-primary flex-end w-auto px-3" *ngIf="editMode"
                [disabled]="modalConfig.working" (click)="editMode = false">
            <i class="fas fa-chevron-left me-3"></i> Cancel
        </button>
        <button type="button" class="btn btn-sm btn-icon btn-light-primary px-3 w-auto flex-end"
                [disabled]="working" (click)="onAddEdit()" *ngIf="!editMode">
            <i class="fas fa-plus me-2"></i> Add new
        </button>
    </div>
    <div class="separator py-1"></div>
    <!-- Document Types -->
    <div class="mt-5 blockui" [ngClass]="{'d-none': editMode}">
        <aims-working *ngIf="working"></aims-working>
        <table id="table_docTypes" class="table table-sm table-hover fs-8 table-row-dashed align-middle">
            <thead>
                <tr class="text-start text-gray-900 fw-bold gs-0">
                    <th class="w-50px" data-orderable="false"></th>
                    <th class="d-none"></th>
                    <th width="20%">File type name</th>
                    <th width="20%">Upload location</th>
                    <th data-orderable="false">Fields</th>
                    <th class="w-50px text-end" data-orderable="false"></th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let docType of documentTypes; let ind = index;">
                    <td class="text-center"></td>
                    <td class="d-none">{{ ind+1 }}</td>
                    <td>{{ docType.typeName }}</td>
                    <td>{{ uploadLocs[docType.uploadLocation] }}</td>
                    <td>
                        <ul class="list-unstyled fields-list">
                            <li *ngFor="let f of docType.fields">
                                <span class="badge badge-light me-1" [title]="f.fTypeInfo[1]">{{ f.fTypeInfo[0] }}</span>
                                <span class="text-gray-900" [ngClass]="{'required': f.isRequired}">{{ f.fieldName }}</span>
                            </li>
                        </ul>
                    </td>
                    <td class="text-end fs-6">
                        <button class="btn btn-sm btn-icon w-30px h-30px btn-bg-light btn-active-color-primary ms-2" ngbTooltip="Actions"
                                data-qs-menu-trigger="click" data-qs-menu-placement="bottom-end" data-qs-menu-flip="top-end">
                            <i class="bi bi-three-dots fs-5"></i>
                        </button>
                        <!-- Dropdown menu -->
                        <div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown py-lg-1 w-lg-225px" data-qs-menu="true">
                            <div class="menu-item menu-lg-down-accordion">
                                <a class="menu-link py-3" (click)="onAddEdit(docType.id)">
                                    <span class="menu-icon">
                                        <span [inlineSVG]="'./assets/media/icons/duotune/general/gen055.svg'" class="svg-icon svg-icon-3"></span>
                                    </span><span class="menu-title">Edit</span>
                                </a>
                            </div>
                            <div class="menu-item separator"></div>
                            <div class="menu-item menu-lg-down-accordion">
                                <a class="menu-link py-3" (click)="onDelete(docType.id, docType.typeName)">
                                    <span class="menu-icon">
                                        <span [inlineSVG]="'./assets/media/icons/duotune/general/trash.svg'" class="svg-icon svg-icon-3 text-danger"></span>
                                    </span><span class="menu-title text-danger">Delete</span>
                                </a>
                            </div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <!-- DocType Edit Mode -->
    <ng-container *ngIf="editMode">
        <form id="docTypeForm" class="form my-5" [formGroup]="form">
            <div class="row">
                <div class="col-5 form-group">
                    <label class="required">Type name</label>
                    <input name="typeName" class="form-control" type="text" placeholder="File type name" formControlName="name"
                           [ngClass]="{ 'is-invalid': form.controls['name'].dirty && form.controls['name'].invalid }" />
                    <div class="fv-plugins-message-container" *ngIf="exists">
                        <div class="fv-help-block">
                            <span role="alert">A file type with this name already exists.</span>
                        </div>
                    </div>
                    <ng-container [ngTemplateOutlet]="formError"
                                  [ngTemplateOutletContext]="{
                            validation: 'required',
                            message: 'Type name is required.',
                            control: form.controls['name']
                            }"></ng-container>
                </div>
                <div class="col-4 form-group">
                    <label class="required">Allow upload from</label>
                    <select class="form-select" placeholder="Upload location" formControlName="upLoc">
                        <option selected></option>
                        <option [value]="i" *ngFor="let loc of uploadLocs; let i = index;">{{ loc }}</option>
                    </select>
                </div>
                <div class="col-3 form-group">
                    <label class="required">Activity ID required?</label>
                    <div class="form-check form-check-custom form-check-solid">
                        <input type="checkbox" class="form-check-input" formControlName="isActReq" />
                    </div>
                </div>
            </div>
        </form>
        <ng-template #formError let-control="control" let-message="message" let-validation="validation">
            <ng-container *ngIf="control?.hasError(validation) && control.dirty">
                <div class="fv-plugins-message-container">
                    <div class="fv-help-block">
                        <span role="alert">{{ message }}</span>
                    </div>
                </div>
            </ng-container>
        </ng-template>

        <!-- Fields -->
        <h6 class="fw-semibold">File information fields</h6>
        <div class="separator mb-3"></div>
        <table class="table table-sm table-hover fs-7 table-row-dashed align-middle">
            <thead>
                <tr class="text-start text-gray-900 fw-bold">
                    <th class="w-50px"></th>
                    <th>Field name <span class="required"></span></th>
                    <th width="20%">Type <span class="required"></span></th>
                    <th>Field values / Unit</th>
                    <th width="15%">Is required? <span class="required"></span></th>
                    <th></th>
                </tr>
            </thead>
            <tbody>
                <tr *ngIf="!docType.fields?.length"><td class="text-center text-muted py-3" colspan="6">No fields</td></tr>
                <tr *ngFor="let df of docType.fields; let ind = index;">
                    <td class="text-center">{{ ind+1 }}</td>
                    <td>
                        <input class="form-control form-control-sm" type="text" placeholder="Field name" [(ngModel)]="df.fieldName"
                               [ngClass]="{ 'is-invalid': fieldsNotFilled[ind+1] }" (keyup)="fieldsNotFilled[ind+1]=false" />
                    </td>
                    <td>
                        <select class="form-select form-select-sm" placeholder="Field type" [(ngModel)]="df.fieldType">
                            <option *ngFor="let colType of fieldTypes" [value]="colType.id">{{ colType.name }}</option>
                        </select>
                    </td>
                    <td>
                        <textarea class="form-control form-control-sm" type="text" [(ngModel)]="df.fieldTypeValues"
                                  rows="1" [disabled]="+df.fieldType == 0 || +df.fieldType == 5 || +df.fieldType == 6">
                        </textarea>
                    </td>
                    <td>
                        <div class="form-check form-check-custom form-check-sm form-check-solid">
                            <input type="checkbox" class="form-check-input" [(ngModel)]="df.isRequired" />
                        </div>
                    </td>
                    <td class="text-center">
                        <button class="btn btn-sm btn-icon w-30px h-30px btn-bg-light btn-active-color-danger"
                                ngbTooltip="Delete field" (click)="onDeleteField(ind)">
                            <span [inlineSVG]="'./assets/media/icons/duotune/general/trash.svg'" class="svg-icon svg-icon-3 text-danger"></span>
                        </button>
                    </td>
                </tr>
                <!-- New field -->
                <tr class="border-top border-primary">
                    <td class="text-center">(New)</td>
                    <td>
                        <input class="form-control form-control-sm" type="text" placeholder="Field name" [(ngModel)]="field.fieldName"
                               [ngClass]="{ 'is-invalid': fieldsNotFilled[0] }" (keyup)="onAddField($event)" />
                        <div class="fv-plugins-message-container" *ngIf="fieldsNotFilled[0]">
                            <div class="fv-help-block">
                                <span role="alert">Field name is required.</span>
                            </div>
                        </div>
                    </td>
                    <td>
                        <select class="form-select form-select-sm" placeholder="Field type" [(ngModel)]="field.fieldType">
                            <option *ngFor="let colType of fieldTypes" [value]="colType.id">{{ colType.name }}</option>
                        </select>
                    </td>
                    <td>
                        <textarea class="form-control form-control-sm" type="text" [(ngModel)]="field.fieldTypeValues" rows="1"
                                  [disabled]="+field.fieldType == 0 || +field.fieldType == 5 || +field.fieldType == 6">
                        </textarea>
                    </td>
                    <td>
                        <div class="form-check form-check-custom form-check-sm form-check-solid">
                            <input type="checkbox" class="form-check-input" [(ngModel)]="field.isRequired" />
                        </div>
                    </td>
                    <td class="text-center">
                        <button class="btn btn-sm btn-icon w-30px h-30px btn-bg-light btn-active-color-primary"
                                ngbTooltip="Add new" (click)="onAddField()">
                            <span [inlineSVG]="'./assets/media/icons/duotune/arrows/arr075.svg'" class="svg-icon svg-icon-3"></span>
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="modal-footer pb-0 px-0">
            <button type="button" class="btn btn-sm btn-primary" [disabled]="form?.invalid || modalConfig.working" (click)="save()">
                <span class="indicator-label" *ngIf="!modalConfig.working; else btnSpinner">{{ docType.id > 0 ? 'Save' : 'Add' }}</span>
                <ng-template #btnSpinner>
                    <span class="indicator-progress" style="display: block">
                        Please wait...
                        <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                    </span>
                </ng-template>
            </button>
        </div>
    </ng-container>
</app-modal>