﻿using AIMS3.Business.Models;
using AIMS3.Data.Models;
using AIMS3.Repository.Repositories;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using AIMS3.Business.Services.Interfaces;
using AIMS3.Business.Services;
using AIMS3.Business.Models.DashboardModels;

namespace AIMS3.Business.Services.Implementations;

public class DashboardService : IDashboardService, Interfaces.IDashboardService
{
    private readonly IDapperRepository _dapperRepository;
    private readonly ILogger<DashboardService> _logger;

    private List<InterventionDataResultModel> profilesActivities;

    public DashboardService(IDapperRepository dapperRepository, ILogger<DashboardService> logger)
    {
        _dapperRepository = dapperRepository;
        _logger = logger;
    }

    /// <summary>
    /// Gets GPS Points of the Progress Data for the main map in the Dashboard.
    /// At least one Community's GPS is selected as its corresponding ditrict, if no community is selected.
    /// </summary>
    /// <param name="filters">Filteres applied to to get progress data</param>
    /// <returns></returns>
    public async Task<IEnumerable<GpsPointModel>> GetActivityDataPoints(DataFilterModel filters)
    {
        var query = @"SELECT DISTINCT actLoc.DistrictId, actLoc.DistName, actLoc.ProvName,
                      MAX(actLoc.GpsLat) AS GpsLat, MAX(actLoc.GpsLon) AS GpsLon
                      FROM vActivitiesWithProgressDetails act INNER JOIN
                      vActivitiesWithLocations0 actLoc ON actLoc.Id = act.Id";

        var conditions = GetConditions(filters);

        // 1. If both start and end periods are selected
        if (filters.Period > 0 && filters.PeriodEnd > 0)
        {
            int periodEnd = (int)filters.PeriodEnd + 1;
            int yearEnd = (int)filters.YearEnd;
            if (periodEnd > 12)
            {
                periodEnd = 1;
                yearEnd++;
            }
            var periodDate = new DateTime((int)filters.Year, (int)filters.Period, 1);
            var periodEndDate = new DateTime(yearEnd, periodEnd, 1).AddDays(-1);

            conditions.Add($"((SDate >= '{periodDate}' AND SDate <= '{periodEndDate}') OR " +
                               $"(EDate >= '{periodDate}' AND EDate <= '{periodEndDate}') OR " +
                               $"(AsOfDate >= '{periodDate}' AND AsOfDate <= '{periodEndDate}'))");
        }
        // 2. If start or end period is selected, take it as of that period,year
        else if (filters.Period > 0 && (filters.PeriodEnd == null || filters.PeriodEnd == 0))
        {
            var periodDate = new DateTime((int)filters.Year, (int)filters.Period, 1);
            conditions.Add($"SDate >= '{periodDate}' OR EDate >= '{periodDate}' OR AsOfDate >= '{periodDate}'");
        }
        else if (filters.PeriodEnd > 0 && (filters.Period == null || filters.Period == 0))
        {
            int periodEnd = (int)filters.PeriodEnd + 1;
            int yearEnd = (int)filters.YearEnd;
            {
                periodEnd = 1;
                yearEnd++;
            }
            var periodEndDate = new DateTime(yearEnd, periodEnd, 1).AddDays(-1);
            conditions.Add($"SDate <= '{periodEndDate}' OR EDate <= '{periodEndDate}' OR AsOfDate <= '{periodEndDate}'");
        }

        if (conditions?.Count > 0) 
            query += " WHERE act." + string.Join(" AND act.", conditions);

        query = query.Replace("act.(", "(");
        query += " GROUP BY DistrictId, DistName, ProvName";

        return await _dapperRepository.ExecuteRawAsync<GpsPointModel>(query);
    }

    /// <summary>
    /// Gets activity progress by activity status for the district.
    /// </summary>
    /// <param name="filters">Filteres applied to to get activity progress data</param>
    /// <returns></returns>
    public async Task<IEnumerable<ActivityStatusModel>> GetActivitiesForDistrict(int distId, DataFilterModel filters)
    {
        var query = @"SELECT InterventionId, SUM(IIF(act.Status=0, 1, 0)) AS Ongoing,
                      SUM(IIF(act.Status IN (1,2),1,0)) AS Completed
                      FROM vActivitiesWithProgressDetails act
				      WHERE act.Status IN (0,1,2) AND EXISTS(
                            SELECT Id FROM vActivitiesWithLocations0
                            WHERE DistrictId = @DistId AND Id = act.Id)";

        var conditions = GetConditions(filters);

        // 1. If both start and end periods are selected
        if (filters.Period > 0 && filters.PeriodEnd > 0)
        {
            int periodEnd = (int)filters.PeriodEnd + 1;
            int yearEnd = (int)filters.YearEnd;
            if (periodEnd > 12)
            {
                periodEnd = 1;
                yearEnd++;
            }
            var periodDate = new DateTime((int)filters.Year, (int)filters.Period, 1);
            var periodEndDate = new DateTime(yearEnd, periodEnd, 1).AddDays(-1);

            conditions.Add($"((SDate >= '{periodDate}' AND SDate <= '{periodEndDate}') OR " +
                               $"(EDate >= '{periodDate}' AND EDate <= '{periodEndDate}') OR " +
                               $"(AsOfDate >= '{periodDate}' AND AsOfDate <= '{periodEndDate}'))");
        }
        // 2. If start or end period is selected, take it as of that period,year
        else if (filters.Period > 0 && (filters.PeriodEnd == null || filters.PeriodEnd == 0))
        {
            var periodDate = new DateTime((int)filters.Year, (int)filters.Period, 1);
            conditions.Add($"SDate >= '{periodDate}' OR EDate >= '{periodDate}' OR AsOfDate >= '{periodDate}'");
        }
        else if (filters.PeriodEnd > 0 && (filters.Period == null || filters.Period == 0))
        {
            int periodEnd = (int)filters.PeriodEnd + 1;
            int yearEnd = (int)filters.YearEnd;
            {
                periodEnd = 1;
                yearEnd++;
            }
            var periodEndDate = new DateTime(yearEnd, periodEnd, 1).AddDays(-1);
            conditions.Add($"SDate <= '{periodEndDate}' OR EDate <= '{periodEndDate}' OR AsOfDate <= '{periodEndDate}'");
        }

        if (conditions?.Count > 0)
            query += " AND " + string.Join(" AND ", conditions);

        query += " GROUP BY InterventionId";

        var results = await _dapperRepository.ExecuteRawAsync<IntActivityStatusModel>(query, new { DistId = distId });
        return results.Select(r => new ActivityStatusModel
        {
            Id = r.InterventionId,
            Name = r.InterventionId.ToString(),
            Status = r.Ongoing > 0 ? AIMS3.Data.ActivityStatus.Ongoing : AIMS3.Data.ActivityStatus.Completed,
            Progress = 0,
            StartDate = null,
            EndDate = null
        });
    }

    /// <summary>
    /// Gets pivot tables for all the interventions for the 'Results' page.
    /// </summary>
    /// <param name="orgId"></param>
    /// <returns></returns>
    public async Task<IEnumerable<PivotTableModel>> GetPivotTables(int? orgId = null)
    {
        var query = $@"SELECT Id,[Name],IsTarget,InterventionProfileId AS ProfId,PivotColumns,FiltersApplied
                       FROM PivotTables WHERE DateDeleted IS NULL";

        if(orgId > 0)
        {
            query += @$" AND InterventionProfileId IN (SELECT InterventionProfileId
                         FROM [InterventionsInventory] WHERE OrganizationId = {orgId} AND DateDeleted IS NULL)";
        }

        query += " ORDER BY ProfId, [Order]";
        return await _dapperRepository.ExecuteRawAsync<PivotTableModel>(query);
    }

    /// <summary>
    /// Gets progress and target data for the pivot tables in the 'Rresults' page.
    /// </summary>
    /// <param name="filters"></param>
    /// <returns></returns>
    public async Task<IEnumerable<ResultsDataModel>> GetData(DataFilterModel filters)
    {
        //filters.ProfIds = new int[] { 44 }; // TEMP
        var conditions = GetConditions(filters);
        var tResults = await GetTargetData(filters, conditions);
        var results = await GetProgressData(filters, conditions);

        if (results.Count > tResults.Count)
        {
            foreach (var result in results)
            {
                result.TargetData = tResults.FirstOrDefault(t =>
                    t.InterventionId == result.InterventionId)?.TargetData;
            }

            return results;
        }
        else
        {
            foreach (var tResult in tResults)
            {
                tResult.ProgressData = results.FirstOrDefault(p =>
                    p.InterventionId == tResult.InterventionId)?.ProgressData;
            }

            return tResults;
        }
    }

    private async Task<List<ResultsDataModel>> GetTargetData(DataFilterModel filters, List<string> conditions)
    {
        if (filters.Period > 0)
            conditions.Add($"CAST(CAST(Period as nvarchar(2)) + CAST(Year as nvarchar(4)) as int) >= {filters.Period}{filters.Year}");
        if (filters.PeriodEnd > 0)
            conditions.Add($"CAST(CAST(Period as nvarchar(2)) + CAST(Year as nvarchar(4)) as int) <= {filters.PeriodEnd}{filters.YearEnd}");
        //Partner,OrganizationId,Project,ProjectId,
        var query = @"SELECT t.Id,InterventionId,UniqueId,Region,Province,District,Period,Year,
                      dcv.DynamicColumnId, dcv.Value AS ColumnValue
                      FROM vTargetsWithDetails t INNER JOIN DynamicColumnsValues dcv ON dcv.TargetId = t.Id";

        if (conditions?.Count > 0)
            query += " WHERE " + string.Join(" AND ", conditions);

        query += " ORDER BY Partner,Project,Region";

        var targets = await _dapperRepository.ExecuteRawAsync<DataResultModel>(query);

        var results = targets.GroupBy(profTargets => new
        {
            profTargets.InterventionId
        }).Select(g => new ResultsDataModel()
        {
            InterventionId = g.Key.InterventionId,
            TargetData = g.GroupBy(t => t.Id).Select(target =>
            {
                var t = target.First();
                return new TargetDataResultModel()
                {
                    Id = t.Id,
                    UniqueId = t.UniqueId,
                    Region = t.Region,
                    Province = t.Province,
                    District = t.District,
                    Year = t.Year,
                    Qtr = t.Period,
                    ColVals = ActivityService.GetDynamicColumnsAsObject(
                    target.Select(t =>
                    {
                        if (t.DynamicColumnId != null)
                        {
                            return new DynamicColumnValue()
                            {
                                DynamicColumnId = (int)t.DynamicColumnId,
                                Value = t.ColumnValue
                            };
                        }
                        return null;
                    }).Where(c => c != null).ToList())
                };
            })
        });

        return results.ToList();
    }

    private async Task<List<ResultsDataModel>> GetProgressData(DataFilterModel filters, List<string> conditions)
    { //Partner, OrganizationId, Project, ProjectId, // MarkedSubmitted, DateSubmitted, DateApproved
        var query = @"SELECT act.Id, ProgressId, UniqueId, InterventionId, Project, Partner, Status,
                      SDate, EDate, Region, Province, District, Community, AsOfDate, Period, Year
                      FROM vActivitiesWithProgressDetails act";
        
        // 1. If both start and end periods are selected
        if (filters.Period > 0 && filters.PeriodEnd > 0)
        {
            // go one month backwards to include the progress of the current month
            filters.Period--;
            if (filters.Period == 0)
            {
                filters.Period = 12;
                filters.Year--;
            }

            int periodEnd = (int)filters.PeriodEnd + 1;
            int yearEnd = (int)filters.YearEnd;
            if (periodEnd > 12)
            {
                periodEnd = 1;
                yearEnd++;
            }
            var periodDate = new DateTime((int)filters.Year, (int)filters.Period, 1);
            var periodEndDate = new DateTime(yearEnd, periodEnd, 1).AddDays(-1);

            conditions.Add($"((SDate >= '{periodDate}' AND SDate <= '{periodEndDate}') OR " +
                           $"(EDate >= '{periodDate}' AND EDate <= '{periodEndDate}') OR " +
                           $"(AsOfDate >= '{periodDate}' AND AsOfDate <= '{periodEndDate}'))");
        }
        // 2. If start or end period is selected, take it as of that period,year
        else if (filters.Period > 0 && (filters.PeriodEnd == null || filters.PeriodEnd == 0))
        {
            var periodDate = new DateTime((int)filters.Year, (int)filters.Period, 1);
            conditions.Add($"(SDate >= '{periodDate}' OR EDate >= '{periodDate}' OR AsOfDate >= '{periodDate}')");
        }
        else if (filters.PeriodEnd > 0 && (filters.Period == null || filters.Period == 0))
        {
            int periodEnd = (int)filters.PeriodEnd + 1;
            int yearEnd = (int)filters.YearEnd;
            {
                periodEnd = 1;
                yearEnd++;
            }
            var periodEndDate = new DateTime(yearEnd, periodEnd, 1).AddDays(-1);
            conditions.Add($"(SDate <= '{periodEndDate}' OR EDate <= '{periodEndDate}' OR AsOfDate <= '{periodEndDate}')");
        }

        if (conditions?.Count > 0)
            query += " WHERE " + string.Join(" AND ", conditions);

        query += " ORDER BY Partner,Project,Region";

        var activities = await _dapperRepository.ExecuteRawAsync<DataResultModel>(query);

        var dynCols = new List<DynColValueModel>();
        //var actIds = new List<int>();

        profilesActivities = new List<InterventionDataResultModel>();

        // 2. if no period (start and end is sel), group and take max of period,year
        if ((filters.Period == null || filters.Period == 0) &&
            (filters.PeriodEnd == null || filters.PeriodEnd == 0))
        {
            profilesActivities = activities.GroupBy(prog => new
            {
                prog.InterventionId
                //prog.Project,
                //prog.Partner
            }).Select(g =>
            {
                var maxRows = g.GroupBy(a => new { a.Id, a.Partner, a.Project })
                               .Select(a => a.MaxBy(r => r.AsOfDate));
                
                return new InterventionDataResultModel()
                {
                    InterventionId = g.Key.InterventionId,
                    Data = maxRows,
                    ActIds = maxRows.Select(a => a.Id).Distinct().ToList(),
                    //DynColIds = maxRows.Select(a => a.DynamicColumnId)?.Distinct().ToList(),
                    ProgIds = maxRows.Select(a => a.ProgressId).Distinct().ToList()
                };
            }).ToList();

            // get dyn col values for all interventions in one go
            dynCols = await GetDynamicColumnValuesPrivate();
        }
        // 3. If both start and end periods are selected, subtract To from From
        else
        {
            if (filters.Period > 0)
            {
                // get activities with min period
                var initialProfilesProgIds = activities.Where(a => a.Period == filters.Period &&
                                                              a.Year == filters.Year)
                                                       .GroupBy(prog => new
                                                       {
                                                           prog.InterventionId
                                                       }).SelectMany(g =>
                                                       {
                                                           var minRows = g.GroupBy(a => new { a.Id, a.Partner, a.Project })
                                                                      .Select(a => a.MinBy(r => r.AsOfDate));

                                                           return minRows.Select(r => r.ProgressId);
                                                       }).Distinct().ToList();

                // get only progress columns values for initial progress
                var initialProgress = new List<DynColValueModel>();
                if (initialProfilesProgIds.Count > 0)
                {
                    query = @$"SELECT ActivityId, ProgressId, DynamicColumnId, Value
                            FROM vDynColsWithValues
                            WHERE ProgressId IN ({string.Join(",", initialProfilesProgIds)})
                            AND FieldType IN (1,2,3);";

                    initialProgress = (await _dapperRepository.ExecuteRawAsync<DynColValueModel>(query))
                                                              .ToList();
                }

                // if start is selected, but not end
                if (filters.PeriodEnd == 0 || filters.PeriodEnd == null)
                {
                    profilesActivities = activities.GroupBy(prog => new
                    {
                        prog.Id,
                        prog.InterventionId,
                        prog.Project,
                        prog.Partner
                    }).Select(g =>
                    {
                        var maxRows = g.GroupBy(a => new { a.Id, a.Partner, a.Project })
                                       .Select(a => a.MaxBy(r => r.AsOfDate));

                        return new InterventionDataResultModel()
                        {
                            InterventionId = g.Key.InterventionId,
                            Data = maxRows,
                            ActIds = maxRows.Select(a => a.Id).Distinct().ToList(),
                            //DynColIds = maxRows.Select(a => a.DynamicColumnId)?.Distinct().ToList(),
                            ProgIds = maxRows.Select(a => a.ProgressId).Distinct().ToList()
                        };
                    }).ToList();
                }
                else if (filters.PeriodEnd > 0)
                {
                    int periodEnd = (int)filters.PeriodEnd + 1;
                    int yearEnd = (int)filters.YearEnd;
                    if (periodEnd > 12)
                    {
                        periodEnd = 1;
                        yearEnd++;
                    }
                    var periodEndDate = new DateTime(yearEnd, periodEnd, 1).AddDays(-1);

                    activities = activities.Where(a => a.AsOfDate <= periodEndDate);
                    profilesActivities = activities.GroupBy(prog => new
                    {
                        prog.InterventionId
                    }).Select(g =>
                    {
                        var rows = g.GroupBy(a => new { a.Id, a.Partner, a.Project })
                                    .Select(a => a.First());

                        return new InterventionDataResultModel()
                        {
                            InterventionId = g.Key.InterventionId,
                            Data = rows,
                            ActIds = rows.Select(a => a.Id).Distinct().ToList(),
                            //DynColIds = rows.Select(a => a.DynamicColumnId)?.Distinct().ToList(),
                            ProgIds = rows.Select(a => a.ProgressId).Distinct().ToList()
                        };
                    }).ToList();
                }

                // get info and progress columns values for recent/Period (to) activities
                //actIds = activities.Select(a => a.Id).Distinct().ToList();
                //var progIds = activities.Select(a => a.ProgressId).Distinct().ToList();
                dynCols = await GetDynamicColumnValuesPrivate();

                foreach (var col in dynCols)
                {
                    foreach (var initProg in initialProgress)
                    {
                        if (col.ProgressId == initProg.ProgressId)
                            continue;

                        if (col.ActivityId == initProg.ActivityId &&
                            col.DynamicColumnId == initProg.DynamicColumnId)
                        {
                            double.TryParse(col.Value, out var fromVal);
                            double.TryParse(initProg.Value, out var toVal);
                            col.Value = (fromVal - toVal).ToString();
                        }
                    }
                }
            }
            else
            {
                profilesActivities = activities.GroupBy(prog => new
                {
                    //prog.Id,
                    prog.InterventionId,
                    //prog.Project,
                    //prog.Partner
                }).Select(g =>
                {
                    var maxRows = g.GroupBy(a => new { a.Id, a.Partner, a.Project })
                                   .Select(a => a.MaxBy(r => r.AsOfDate));

                    return new InterventionDataResultModel()
                    {
                        InterventionId = g.Key.InterventionId,
                        Data = maxRows,
                        ActIds = maxRows.Select(a => a.Id).Distinct().ToList(),
                        //DynColIds = maxRows.Select(a => a.DynamicColumnId)?.Distinct().ToList(),
                        ProgIds = maxRows.Select(a => a.ProgressId).Distinct().ToList()
                    };
                }).ToList();

                dynCols = await GetDynamicColumnValuesPrivate();
            }
        }
        //else
        //{
        //    // get columns values
        //    //actIds = activities.Select(a => a.Id).Distinct().ToList();
        //    //var progIds = activities.Select(a => a.ProgressId).Distinct().ToList();
        //    dynCols = await GetDynamicColumnValues();
        //}

        CultureInfo culture = new("en-GB");

        // attach documents
        query = @$"SELECT ActivityId, COUNT(DocumentId) AS DocumentId
                   FROM ActivitiesDocuments
                   WHERE ActivityId IN (${string.Join(",", profilesActivities.SelectMany(p => p.ActIds))})
                   GROUP BY ActivityId;";
        var docs = await _dapperRepository.ExecuteRawAsync<ActivityDocument>(query);

        var profilesActsWithProgress = profilesActivities.Select(pa =>
        {
            var data = pa.Data.Where(d => pa.InterventionId == d.InterventionId).Select(a =>
            {
                var colVals = dynCols.Where(c => c.ActivityId == a.Id || c.ProgressId == a.ProgressId)
                    .Select(c => new DynamicColumnValue()
                    {
                        DynamicColumnId = c.DynamicColumnId,
                        Value = c.Value
                    }).ToList();

                return new ProgressDataResultModel()
                {
                    Id = a.Id,
                    ProgressId = a.ProgressId,
                    ActivityId = a.UniqueId,
                    File = docs.SingleOrDefault(d => d.ActivityId == a.Id)?.DocumentId,
                    Status = a.Status,
                    SMonth = a.SDate?.ToString("dd/MM/yyyy", culture),
                    EMonth = a.EDate?.ToString("dd/MM/yyyy", culture),
                    Region = a.Region,
                    Province = a.Province,
                    District = a.District,
                    Community = a.Community,
                    Month = a.Period,
                    Year = a.Year,
                    AsOf = a.AsOfDate?.ToString("dd/MM/yyyy", culture),
                    ColVals = ActivityService.GetDynamicColumnsAsObject(colVals)
                };
            });

            return new ResultsDataModel
            {
                InterventionId = pa.InterventionId,
                ProgressData = data
            };
        });

        return profilesActsWithProgress.ToList();
    }

    /// <summary>
    /// Creates list of string array for each condition set in the filters.
    /// This used for the raw queries called in GetDataPoints and GetData methods.
    /// </summary>
    /// <param name="filters"></param>
    /// <returns></returns>
    private static List<string> GetConditions(DataFilterModel filters)
    {
        var conditions = new List<string>();

        if (filters.OrgIds?.Length > 0)
            conditions.Add($"OrganizationId IN ({string.Join(",", filters.OrgIds)})");

        if (filters.ProjIds?.Length > 0)
            conditions.Add($"ProjectId IN ({string.Join(",", filters.ProjIds)})");

        if (filters.ProfIds?.Length > 0)
            conditions.Add($"InterventionId IN ({string.Join(",", filters.ProfIds)})");
        else if (filters.CatIds?.Length > 0)
            conditions.Add($"CategoryId IN ({string.Join(",", filters.CatIds)})");
        if (filters.Outputs?.Length > 0)
            conditions.Add($"Output IN (N'{string.Join("',N'", filters.Outputs)}')");

        if (filters.Regions?.Length > 0)
            conditions.Add($"Region IN ({string.Join(",", filters.Regions)})");

        if (filters.DataStatus?.Length > 0 && filters.DataStatus.Length < 3)
        {
            if (filters.DataStatus.Contains(0) && filters.DataStatus.Contains(1))           // Draft or submitted
                conditions.Add("(MarkedSubmitted<>1 OR (DateSubmitted IS NOT NULL AND DateApproved IS NULL))");
            else if (filters.DataStatus.Contains(0) && filters.DataStatus.Contains(2))      // Draft or approved
                conditions.Add("(MarkedSubmitted<>1 OR DateApproved IS NOT NULL)");
            else if (filters.DataStatus.Contains(1) && filters.DataStatus.Contains(2))      // Submitted or approved
                conditions.Add("((MarkedSubmitted=1 AND DateSubmitted IS NOT NULL) OR DateApproved IS NOT NULL)");
            else if (filters.DataStatus.Contains(0))
                conditions.Add("MarkedSubmitted<>1");  // Only truly draft items (not marked for submission)
            else if (filters.DataStatus.Contains(1))
                conditions.Add("MarkedSubmitted=1 AND DateSubmitted IS NOT NULL AND DateApproved IS NULL");
            else if (filters.DataStatus.Contains(2))
                conditions.Add("DateApproved IS NOT NULL");
        }

        // Activity status filtering: 0=Ongoing, 1=Completed, 2=Archived, 3=Cancelled
        if (filters.ActivityStatus?.Length > 0)
        {
            conditions.Add($"Status IN ({string.Join(",", filters.ActivityStatus)})");
        }

        return conditions;
    }

    /// <summary>
    /// Gets the actual data values for the dynamic columns in the filtered activities or targets.
    /// </summary>
    /// <returns></returns>
    private async Task<List<DynColValueModel>> GetDynamicColumnValuesPrivate()
    {
        var result = await _dapperRepository.ExecuteRawAsync<DynColValueModel>(
            @"select Id, [Name], [GroupId]
              from DynamicColumns 
              where DateDeleted is null
              order by 3,2");
        return result.ToList();
    }

    // Reports Dashboard API methods
    public async Task<object> GetCategoryDashboardData(DataFilterModel filters)
    {
        try
        {
            // Implementation for category dashboard
            var dashboardData = new AIMS3.Business.Models.CategoryDashboardModel();

            return dashboardData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category dashboard data");
            throw;
        }
    }
    
    public async Task<object> GetReportsDashboardData(DataFilterModel filters)
    {
        try
        {
            // Create a new dashboard model
            var dashboardData = new AIMS3.Business.Models.ReportsDashboardModel();
            
            // In a real implementation, you would populate the model with data from the database
            // For now, we're returning an empty model
            
            return dashboardData;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving reports dashboard data");
            throw;
        }
    }
    
    public async Task<object> GetReportsSummaryStats(DataFilterModel filters)
    {
        try
        {
            // Create a summary model
            var summaryStats = new AIMS3.Business.Models.ReportsSummaryModel();
            
            // In a real implementation, you would populate the model with data from the database
            // For now, we're returning an empty model
            
            return summaryStats;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving reports summary statistics");
            throw;
        }
    }
    
    public async Task<object> GetReportsTimeSeriesData(DataFilterModel filters)
    {
        try
        {
            var timeSeriesModel = new AIMS3.Business.Models.ReportsTimeSeriesModel();
            
            // Sample implementation - we can't use the database directly yet
            // In a real implementation, you'd query the database for time series data
            
            // Generate sample data
            return timeSeriesModel;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving reports time series data");
            throw;
        }
    }
    
    public async Task<CategoryStatisticsModel> GetCategoryStatistics(DataFilterModel filters)
    {
        try
        {
            var result = new CategoryStatisticsModel();
            
            // SQL to get categories with their activities data
            string sql = @"
                SELECT 
                    c.Id AS CategoryId,
                    c.Code AS CategoryCode,
                    c.Name AS CategoryName,
                    c.Output AS OutputName,
                    COUNT(a.Id) AS ActivityCount,
                    SUM(ISNULL(a.Budget, 0)) AS Budget,
                    SUM(ISNULL(a.CashDistributed, 0)) AS CashDistributed,
                    SUM(ISNULL(a.TotalBeneficiaries, 0)) AS Beneficiaries,
                    AVG(ISNULL(a.Progress, 0)) AS Progress
                FROM Categories c
                LEFT JOIN InterventionProfiles ip ON ip.CategoryId = c.Id
                LEFT JOIN Activities a ON a.InterventionProfileId = ip.Id
                WHERE c.DateDeleted IS NULL
                AND (ip.DateDeleted IS NULL OR ip.DateDeleted IS NOT NULL)
                AND (a.DateDeleted IS NULL OR a.DateDeleted IS NOT NULL)";

            // Add filter conditions if any
            List<string> conditions = new List<string>();
            
            if (filters != null)
            {
                if (filters.OrgIds?.Any() == true)
                {
                    // Filter by organizations
                    sql += $" AND a.OrganizationId IN ({string.Join(",", filters.OrgIds)})";
                }
                
                if (filters.ProjIds?.Any() == true)
                {
                    // Filter by projects
                    sql += $" AND a.ProjectId IN ({string.Join(",", filters.ProjIds)})";
                }

                if (filters.ProfIds?.Any() == true)
                {
                    // Filter by profiles
                    sql += $" AND ip.Id IN ({string.Join(",", filters.ProfIds)})";
                }
                
                if (filters.CatIds?.Any() == true)
                {
                    // Filter by categories
                    sql += $" AND c.Id IN ({string.Join(",", filters.CatIds)})";
                }
                
                if (filters.Period > 0 && filters.Year > 0)
                {
                    // Apply date filters if present
                    var periodDate = new DateTime((int)filters.Year, (int)filters.Period, 1);
                    sql += $" AND (a.StartDate <= '{periodDate:yyyy-MM-dd}' OR a.EndDate >= '{periodDate:yyyy-MM-dd}')";
                }
            }

            sql += @" GROUP BY c.Id, c.Code, c.Name, c.Output
                    ORDER BY c.Code";

            // Get categories with basic statistics
            var categories = await _dapperRepository.ExecuteRawAsync<dynamic>(sql);
            
            // Map to our model
            foreach (var cat in categories)
            {
                var categoryDetail = new CategoryDetailModel
                {
                    Id = cat.CategoryId,
                    Code = cat.CategoryCode,
                    Name = cat.CategoryName,
                    Output = cat.OutputName,
                    Budget = cat.Budget,
                    CashDistributed = cat.CashDistributed,
                    Beneficiaries = cat.Beneficiaries,
                    Progress = cat.Progress,
                    ActivityCount = cat.ActivityCount
                };
                
                // Add specific indicators based on category code/type
                await AddCategorySpecificIndicators(categoryDetail);
                
                result.Categories.Add(categoryDetail);
            }
            
            // Calculate overall statistics
            result.TotalBudget = result.Categories.Sum(c => c.Budget);
            result.TotalSpent = result.Categories.Sum(c => c.CashDistributed);
            result.TotalBeneficiaries = result.Categories.Sum(c => c.Beneficiaries);
            result.AverageProgress = result.Categories.Any() 
                ? result.Categories.Average(c => c.Progress) 
                : 0;
                
            // Get activities by status
            var statusCounts = await _dapperRepository.ExecuteRawAsync<ActivityStatusCount>(
                @"SELECT 
                    CASE 
                        WHEN Status = 0 THEN 'Ongoing' 
                        WHEN Status = 1 THEN 'Completed' 
                        WHEN Status = 2 THEN 'Approved' 
                        ELSE 'Other' 
                    END AS Status,
                    COUNT(*) AS Count
                  FROM Activities 
                  WHERE DateDeleted IS NULL
                  GROUP BY Status");
                  
            result.ActivitiesByStatus = statusCounts.ToDictionary(
                x => x.Status,
                x => x.Count
            );
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving category statistics");
            throw;
        }
    }
    
    // Helper method to add category-specific indicators
    private async Task AddCategorySpecificIndicators(CategoryDetailModel category)
    {
        try {
            // Different indicators for different category types
            switch (category.Code?.Substring(0, 2))
            {
                case "1.": // Infrastructure categories
                    // Get count of structures
                    string structuresQuery = $@"
                        SELECT COUNT(*) 
                        FROM Activities a
                        JOIN InterventionProfiles ip ON a.InterventionProfileId = ip.Id
                        WHERE ip.CategoryId = {category.Id}
                        AND a.DateDeleted IS NULL
                        AND EXISTS (
                            SELECT 1 FROM DynamicColumnsValues dcv
                            JOIN DynamicColumns dc ON dcv.DynamicColumnId = dc.Id
                            WHERE dcv.ActivityId = a.Id
                            AND dc.Name LIKE '%structure%'
                        )";
                    
                    var structures = await _dapperRepository.ExecuteScalarAsync(structuresQuery);
                    category.Indicators["structures"] = structures;
                    
                    // Get count of households
                    string householdsQuery = $@"
                        SELECT ISNULL(SUM(CAST(dcv.Value AS decimal)), 0)
                        FROM Activities a
                        JOIN InterventionProfiles ip ON a.InterventionProfileId = ip.Id
                        JOIN DynamicColumnsValues dcv ON dcv.ActivityId = a.Id
                        JOIN DynamicColumns dc ON dcv.DynamicColumnId = dc.Id
                        WHERE ip.CategoryId = {category.Id}
                        AND a.DateDeleted IS NULL
                        AND dc.Name LIKE '%household%'";
                    
                    var households = await _dapperRepository.ExecuteScalarAsync(householdsQuery);
                    category.Indicators["households"] = households;
                    break;
                    
                case "2.": // Health categories
                    // Get count of health facilities
                    string facilitiesQuery = $@"
                        SELECT COUNT(*) 
                        FROM Activities a
                        JOIN InterventionProfiles ip ON a.InterventionProfileId = ip.Id
                        WHERE ip.CategoryId = {category.Id}
                        AND a.DateDeleted IS NULL
                        AND EXISTS (
                            SELECT 1 FROM DynamicColumnsValues dcv
                            JOIN DynamicColumns dc ON dcv.DynamicColumnId = dc.Id
                            WHERE dcv.ActivityId = a.Id
                            AND dc.Name LIKE '%facility%'
                        )";
                    
                    var facilities = await _dapperRepository.ExecuteScalarAsync(facilitiesQuery);
                    category.Indicators["facilities"] = facilities;
                    
                    // Get count of patients served
                    string patientsQuery = $@"
                        SELECT ISNULL(SUM(TotalBeneficiaries), 0)
                        FROM Activities a
                        JOIN InterventionProfiles ip ON a.InterventionProfileId = ip.Id
                        WHERE ip.CategoryId = {category.Id}
                        AND a.DateDeleted IS NULL";
                    
                    var patients = await _dapperRepository.ExecuteScalarAsync(patientsQuery);
                    category.Indicators["patients"] = patients;
                    break;
                    
                case "3.": // UCT (Cash transfer) categories
                    // Get total cash distributed
                    string cashQuery = $@"
                        SELECT ISNULL(SUM(CashDistributed), 0)
                        FROM Activities a
                        JOIN InterventionProfiles ip ON a.InterventionProfileId = ip.Id
                        WHERE ip.CategoryId = {category.Id}
                        AND a.DateDeleted IS NULL";
                    
                    var cashDistributed = await _dapperRepository.ExecuteScalarAsync(cashQuery);
                    category.Indicators["cashTransfers"] = cashDistributed;
                    
                    // Get count of recipients
                    string recipientsQuery = $@"
                        SELECT ISNULL(SUM(TotalBeneficiaries), 0)
                        FROM Activities a
                        JOIN InterventionProfiles ip ON a.InterventionProfileId = ip.Id
                        WHERE ip.CategoryId = {category.Id}
                        AND a.DateDeleted IS NULL";
                    
                    var recipients = await _dapperRepository.ExecuteScalarAsync(recipientsQuery);
                    category.Indicators["recipients"] = recipients;
                    break;
                    
                default:
                    // Add default indicators
                    category.Indicators["activities"] = category.ActivityCount;
                    break;
            }
        } catch (Exception ex) {
            _logger.LogError(ex, $"Error adding specific indicators for category {category.Id}");
        }
    }

    // Helper class for activity status counts
    private class ActivityStatusCount
    {
        public string Status { get; set; }
        public int Count { get; set; }
    }

    public async Task<List<AIMS3.Business.Models.DashboardModels.DynamicColumnValuesModel>> GetDynamicColumnValues()
    {
        try
    {
            // Get all dynamic columns with their metadata
            var query = @"
                SELECT 
                    dc.Id,
                    dc.Name,
                    dc.DisplayName,
                    dc.Description,
                    dc.Type as ColumnVarType,
                    dc.FieldType as ColDataType,
                    dc.InterventionProfileId,
                    dc.FieldTypeValues,
                    COUNT(dcv.Id) as ValueCount
                FROM DynamicColumns dc
                LEFT JOIN DynamicColumnValues dcv ON dc.Id = dcv.DynamicColumnId
                WHERE dc.DateDeleted IS NULL
                GROUP BY dc.Id, dc.Name, dc.DisplayName, dc.Description, dc.Type, dc.FieldType, dc.InterventionProfileId, dc.FieldTypeValues
                ORDER BY dc.Name";

            var dynamicColumns = await _dapperRepository.ExecuteRawAsync<dynamic>(query);
            
            // Convert to the expected model format
            var result = new List<AIMS3.Business.Models.DashboardModels.DynamicColumnValuesModel>();
            
            foreach (var column in dynamicColumns)
            {
                var columnModel = new AIMS3.Business.Models.DashboardModels.DynamicColumnValuesModel
                {
                    ColumnName = column.Name,
                    DisplayName = column.DisplayName ?? column.Name,
                    Values = new List<string>()
                };
                
                // Get sample values for this column
                var valuesQuery = @"
                    SELECT TOP 10 DISTINCT Value 
                    FROM DynamicColumnValues 
                    WHERE DynamicColumnId = @ColumnId 
                    AND Value IS NOT NULL 
                    AND Value != ''
                    ORDER BY Value";
                
                var values = await _dapperRepository.ExecuteRawAsync<string>(valuesQuery, new { ColumnId = column.Id });
                columnModel.Values = values.ToList();
                
                result.Add(columnModel);
            }
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving dynamic column values");
            return new List<AIMS3.Business.Models.DashboardModels.DynamicColumnValuesModel>();
        }
    }

    // Explicit implementations for Interfaces.IDashboardService
    Task<AIMS3.Business.Models.CategoryDashboardModel> AIMS3.Business.Services.Interfaces.IDashboardService.GetCategoryDashboardData(DataFilterModel filters)
    {
        // Convert the object return type to specific type
        return Task.FromResult((AIMS3.Business.Models.CategoryDashboardModel)GetCategoryDashboardData(filters).Result);
    }

    Task<AIMS3.Business.Models.ReportsDashboardModel> AIMS3.Business.Services.Interfaces.IDashboardService.GetReportsDashboardData(DataFilterModel filters)
    {
        // Convert the object return type to specific type
        return Task.FromResult((AIMS3.Business.Models.ReportsDashboardModel)GetReportsDashboardData(filters).Result);
    }

    Task<AIMS3.Business.Models.ReportsSummaryModel> AIMS3.Business.Services.Interfaces.IDashboardService.GetReportsSummaryStats(DataFilterModel filters)
    {
        // Convert the object return type to specific type
        return Task.FromResult((AIMS3.Business.Models.ReportsSummaryModel)GetReportsSummaryStats(filters).Result);
    }

    Task<AIMS3.Business.Models.ReportsTimeSeriesModel> AIMS3.Business.Services.Interfaces.IDashboardService.GetReportsTimeSeriesData(DataFilterModel filters)
    {
        // Convert the object return type to specific type
        return Task.FromResult((AIMS3.Business.Models.ReportsTimeSeriesModel)GetReportsTimeSeriesData(filters).Result);
    }

    public async Task<object> GetTestDatabaseStats()
    {
        try
        {
            // Get counts directly from database tables
            int outputsCount = await _dapperRepository.ExecuteScalarAsync(
                "SELECT COUNT(*) FROM InterventionProfiles WHERE DateDeleted IS NULL");
            
            int categoriesCount = await _dapperRepository.ExecuteScalarAsync(
                "SELECT COUNT(*) FROM Categories WHERE DateDeleted IS NULL");
            
            int activitiesCount = await _dapperRepository.ExecuteScalarAsync(
                "SELECT COUNT(*) FROM Activities WHERE DateDeleted IS NULL");
            
            int completedActivitiesCount = await _dapperRepository.ExecuteScalarAsync(
                "SELECT COUNT(*) FROM Activities WHERE DateDeleted IS NULL AND Status = 2"); // 2 = Completed status
            
            int beneficiariesCount = await _dapperRepository.ExecuteScalarAsync(
                "SELECT ISNULL(SUM(TotalBeneficiaries), 0) FROM Activities WHERE DateDeleted IS NULL");
            
            // Return counts
            return new
            {
                OutputsCount = outputsCount,
                CategoriesCount = categoriesCount,
                ActivitiesCount = activitiesCount,
                CompletedActivitiesCount = completedActivitiesCount,
                BeneficiariesCount = beneficiariesCount,
                TablesExist = true
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving test database stats");
            return new { ErrorMessage = ex.Message, TablesExist = false };
        }
    }
}