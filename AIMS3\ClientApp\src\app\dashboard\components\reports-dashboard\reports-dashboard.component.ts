import { Component, OnInit, <PERSON><PERSON>iew<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, HostBinding, ChangeDetector<PERSON>ef, NO_ERRORS_SCHEMA } from '@angular/core';
import { Subject, BehaviorSubject, Observable, of, EMPTY } from 'rxjs';
import { takeUntil, debounceTime, distinctUntilChanged, map, switchMap, shareReplay, finalize } from 'rxjs/operators';
import { ReportsService } from './services/reports.service';
import {
  ReportData,
  Output,
  Category,
  Intervention,
  Activity,
  SummaryStats,
  TimeSeriesData,
  TimePeriodType,
  Quarter,
  ProjectGroup,
  Project,
  ReportFilter
} from './models/reports.model';
import { FormsModule } from '@angular/forms';
import { DatePipe, CommonModule } from '@angular/common';
import { MatTabsModule } from '@angular/material/tabs';
import { ReportFilterComponent } from './components/report-filter/report-filter.component';
import { OverviewDashboardComponent } from './components/overview-dashboard/overview-dashboard.component';
import { CategoryDashboardComponent } from './components/category-dashboard/category-dashboard.component';
import { RegionDashboardComponent } from './components/region-dashboard/region-dashboard.component';
import { ProjectDashboardComponent } from './components/project-dashboard/project-dashboard.component';

// Declare the bootstrap global variable for TypeScript
declare global {
  interface Window {
    bootstrap: any;
  }
}

// Make bootstrap globally accessible
declare const bootstrap: any;

@Component({
  selector: 'app-reports-dashboard',
  standalone: true,
  imports: [
    CommonModule, 
    FormsModule,
    MatTabsModule,
    ReportFilterComponent, 
    OverviewDashboardComponent,
    CategoryDashboardComponent,
    RegionDashboardComponent,
    ProjectDashboardComponent
  ],
  schemas: [NO_ERRORS_SCHEMA],
  templateUrl: './reports-dashboard.component.html',
  styleUrl: './reports-dashboard.component.scss'
})
export class ReportsDashboardComponent implements OnInit, AfterViewInit, OnDestroy {
  @HostBinding('attr.data-qs-app-toolbar-fixed') pgToolbar = 'true';
  @HostBinding('attr.data-qs-app-header-fixed') pgHeader = 'true';
  @HostBinding('attr.data-qs-app-sidebar-push-toolbar') pushToolbar = 'true';

  // Material Tab properties
  selectedTabIndex = 0;
  tabMapping = {
    0: 'categories'
  };

  // Performance optimization properties
  private filterChangeSubject = new BehaviorSubject<ReportFilter>({});
  private dataCache = new Map<string, any>();
  private lastApiCall: number = 0;
  private readonly API_DEBOUNCE_TIME = 300; // 300ms debounce (reduced from 800ms)
  private readonly CACHE_DURATION = 5 * 60 * 1000; // 5 minutes cache
  private isLoadingData = false;
  private pendingFilterChanges = false;

  // Data properties
  reportData: ReportData;
  summaryStats: SummaryStats;
  timeSeriesData: TimeSeriesData[] = [
    {
      date: '2023-Q1',
      budget: 12000000,
      cashDistributed: 7200000,
      beneficiaries: 35000,
      progress: 28
    },
    {
      date: '2023-Q2',
      budget: 25000000,
      cashDistributed: 15000000,
      beneficiaries: 65000,
      progress: 42
    },
    {
      date: '2023-Q3',
      budget: 37500000,
      cashDistributed: 18750000,
      beneficiaries: 125000,
      progress: 58
    }
  ];
  
  // Project data
  projectGroups: ProjectGroup[] = [];
  projects: Project[] = [];
  
  // For template use
  Math = Math;
  
  // Selection states
  selectedOutput: Output | null = null;
  selectedCategory: Category | null = null;
  selectedIntervention: Intervention | null = null;
  selectedProjectGroup: ProjectGroup | null = null;
  selectedProject: Project | null = null;
  
  // UI states
  loading: boolean = true;
  error: string | null = null;
  activeTab: 'overview' | 'outputs' | 'categories' | 'interventions' | 'geographic' | 'project-groups' | 'projects' = 'categories';
  displayMode: string = 'cards'; // 'cards', 'table', 'chart'
  
  // Filter state
  filters: ReportFilter = {};
  filterVisible: boolean = false;
  private isInitializing: boolean = true; // Flag to prevent duplicate initial calls
  
  // Chart data
  outputChartData: any[] = [];
  categoryChartData: any[] = [];
  statusChartData: any[] = [];
  trendChartData: any = {};
  geographicChartData: any[] = [];
  projectChartData: any[] = [];
  projectOutputChartData: any = {};
  
  // Time tracking
  lastUpdated: Date = new Date();
  
  // Enhanced functionality properties
  startDate: Date | null = null;
  endDate: Date | null = null;
  autoRefreshEnabled: boolean = false;
  refreshInterval: number = 30; // seconds
  private refreshTimer: any;
  
  // Quarterly filtering properties
  selectedYear: number = new Date().getFullYear();
  selectedQuarter: 'Q1' | 'Q2' | 'Q3' | 'Q4' | null = null;
  availableYears: number[] = [];
  enableQuarterComparison: boolean = false;
  compareWithPreviousQuarter: boolean = false;
  compareWithSameQuarterLastYear: boolean = false;
  quarterlyStats: any = {};
  quarterlyKPIs: any = null;
  selectedMetric: string = 'budget';
  
  // Subject for unsubscribing
  private destroy$ = new Subject<void>();

  currentFilters: ReportFilter = {}; // Current filters applied to the reports
  fullReportData: any = null; // Full report data from API
  filteredData: any = null; // Filtered data for components
  hierarchicalData: any[] = []; // Hierarchical report data

  // Master lists - always contain all available options for dropdowns
  private allProjectGroups: ProjectGroup[] = [];
  private allProjects: Project[] = [];

  constructor(
    private reportsService: ReportsService,
    private cdr: ChangeDetectorRef
  ) { }

  ngOnInit(): void {
    // Set default filters if needed
    this.currentFilters = {
      // Add any default filters here
    };
    
    console.log('🏁 ReportsDashboardComponent initialized with performance optimizations');
    
    // Initialize quarterly filtering
    this.initializeAvailableYears();
    
    // Create mock data first to ensure UI has something to display
    this.createMockData();
    
    // Set up optimized filter change handling
    this.setupOptimizedFilterHandling();
    
    // Load initial data
    this.loadReportData();
    
    // Set the default tab to Categories (now index 0)
    this.selectedTabIndex = 0;
    this.activeTab = 'categories';
    
    // Mark initialization as complete after a brief delay to ensure filter component is ready
    setTimeout(() => {
      this.isInitializing = false;
      console.log('✅ Dashboard initialization complete, optimized filter changes now enabled');
    }, 1000);
    
    // 🐛 DEBUG: Expose debug methods to window for testing
    (window as any).debugDashboard = {
      testProjectGroupFiltering: () => this.debugProjectGroupFiltering(),
      makeImmediateApiCall: (filters: any) => this.makeImmediateApiCall(filters),
      getCurrentFilters: () => this.currentFilters,
      getProjectGroups: () => this.projectGroups,
      clearCache: () => this.clearCache(),
      forceRefresh: () => this.forceRefresh()
    };
    console.log('🐛 DEBUG: Window.debugDashboard methods available:', Object.keys((window as any).debugDashboard));
  }

  ngAfterViewInit(): void {
    // Initialize charts after view is initialized
    setTimeout(() => {
      this.initializeCharts();
    }, 0);
  }

  ngOnDestroy(): void {
    this.stopAutoRefresh();
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load the dashboard data - UPDATED to use hierarchical endpoint exclusively
   */
  loadDashboardData(): void {
    console.log('🔄 loadDashboardData called - redirecting to optimized hierarchical flow');
    console.log('🔄 Using current filters:', this.currentFilters);
    
    // Instead of calling getFullDashboardReport, use the optimized hierarchical flow
    this.loadReportData();
  }

  /**
   * Set active tab
   */
  setActiveTab(tab: 'overview' | 'outputs' | 'categories' | 'interventions' | 'geographic' | 'project-groups' | 'projects'): void {
    this.activeTab = tab;
    
    // When changing tabs, maintain project group and project selections 
    // but reset other tab-specific selections
    if (tab !== 'outputs') {
      this.selectedOutput = null;
    }
    
    if (tab !== 'categories') {
      this.selectedCategory = null;
    }
    
    if (tab !== 'interventions') {
      this.selectedIntervention = null;
    }
    
    // Re-initialize charts when tab changes
    setTimeout(() => {
      this.initializeCharts();
    }, 0);
  }

  /**
   * Toggle filter panel visibility
   */
  toggleFilters(): void {
    this.filterVisible = !this.filterVisible;
  }

  /**
   * Apply filters and reload data - UPDATED to use hierarchical endpoint
   */
  applyFilters(): void {
    console.log('🔄 applyFilters called - using optimized hierarchical flow');
    this.loadReportData(); // Use hierarchical endpoint
    this.filterVisible = false;
  }

  /**
   * Reset all filters - UPDATED to use hierarchical endpoint
   */
  resetFilters(): void {
    console.log('🔄 resetFilters called - clearing all filters and using hierarchical flow');
    this.filters = {};
    this.currentFilters = {}; // Also clear current filters
    this.selectedProjectGroup = null;
    this.selectedProject = null;
    this.selectedQuarter = null; // Reset quarterly filters too
    this.startDate = null;
    this.endDate = null;
    
    this.loadReportData(); // Use hierarchical endpoint
    this.filterVisible = false;
  }

  /**
   * Select a project group to display detailed information
   */
  selectProjectGroup(group: ProjectGroup): void {
    console.log('🎯 Project group selected:', group.name);
    console.log('🔍 Current initialization status:', this.isInitializing);
    console.log('🔍 Current loading status:', this.isLoadingData);
    
    this.selectedProjectGroup = group;
    this.selectedProject = null; // Clear individual project selection
    
    // Add project group to filters
    this.currentFilters = {
      ...this.currentFilters,
      projectGroups: [group.id],
      projects: [] // Clear individual project filter
    };
    
    console.log('✅ Project group filter applied - triggering hierarchical API call:', {
      groupId: group.id,
      groupName: group.name,
      currentFilters: this.currentFilters
    });
    
    // Force immediate API call for project group selection (bypass debouncing)
    this.makeImmediateApiCall(this.currentFilters);
    
    // Also trigger the standard optimized flow
    this.loadReportData();
    
    // Apply client-side filtering for immediate UI feedback
    this.applyProjectFiltering();
    this.forceUpdateAllTabs();
  }

  /**
   * Select a project to display detailed information
   */
  selectProject(project: Project): void {
    console.log('🎯 Project selected:', project.name);
    console.log('🔍 Current initialization status:', this.isInitializing);
    console.log('🔍 Current loading status:', this.isLoadingData);
    
    this.selectedProject = project;
    this.selectedProjectGroup = null; // Clear group selection
    
    // Add project to filters
    this.currentFilters = {
      ...this.currentFilters,
      projects: [project.id],
      projectGroups: [] // Clear group filter
    };
    
    console.log('✅ Project filter applied - triggering hierarchical API call:', {
      projectId: project.id,
      projectName: project.name,
      currentFilters: this.currentFilters
    });
    
    // Force immediate API call for project selection (bypass debouncing)
    this.makeImmediateApiCall(this.currentFilters);
    
    // Also trigger the standard optimized flow
    this.loadReportData();
    
    // Apply client-side filtering for immediate UI feedback
    this.applyProjectFiltering();
    this.forceUpdateAllTabs();
  }
  
  /**
   * Load detailed project group data
   */
  private loadProjectGroupDetails(groupId: string): void {
    // This method is no longer used as getProjectGroupData has been removed
  }

  /**
   * Load detailed project data
   */
  private loadProjectDetails(projectId: string): void {
    // This method is no longer used as getProjectData has been removed
  }

  /**
   * Select an output to display detailed information
   */
  selectOutput(output: Output): void {
    this.selectedOutput = output;
    this.selectedCategory = null;
    this.selectedIntervention = null;
    this.activeTab = 'outputs';
    
    // Load detailed output data if needed
    if (this.shouldLoadDetailedData(output)) {
      this.loadOutputDetails(output.id);
    }
  }

  /**
   * Select a category to display detailed information
   */
  selectCategory(category: Category): void {
    console.log('Selecting category:', category);
    console.log('Category has indicators:', category.indicators != null && category.indicators.length > 0);
    console.log('Category structures property:', category.structures);
    console.log('Category households property:', category.households);
    
    this.selectedCategory = category;
    this.selectedIntervention = null;
    this.activeTab = 'categories';
    
    // Load detailed category data if needed
    if (this.shouldLoadDetailedData(category)) {
      this.loadCategoryDetails(category.id);
    } else {
      console.log('Using existing category data without loading details');
    }
  }

  /**
   * Select an intervention to display detailed information
   */
  selectIntervention(intervention: Intervention): void {
    this.selectedIntervention = intervention;
    this.activeTab = 'interventions';
    
    // Load detailed intervention data if needed
    if (!intervention.activities || intervention.activities.length === 0) {
      this.loadInterventionDetails(intervention.id);
    }
  }

  /**
   * Check if we should load detailed data
   */
  private shouldLoadDetailedData(item: Output | Category | Intervention): boolean {
    // If it's an output, check if categories are loaded
    if ('categories' in item) {
      return item.categories.length === 0;
    }
    
    // If it's a category, check if interventions are loaded
    if ('interventions' in item) {
      return item.interventions.length === 0;
    }
    
    // If it's an intervention, check if activities are loaded
    return !item.activities || item.activities.length === 0;
  }

  /**
   * Load detailed output data
   */
  private loadOutputDetails(outputId: string): void {
    // This method is no longer used as getOutputData has been removed
  }

  /**
   * Load detailed category data
   */
  private loadCategoryDetails(categoryId: string): void {
    console.log('Loading category details for ID:', categoryId);
    
    // This method is no longer used as getCategoryData has been removed
  }

  /**
   * Load detailed intervention data
   */
  private loadInterventionDetails(interventionId: string): void {
    // This method is no longer used as getInterventionData has been removed
  }

  /**
   * Prepare data for charts
   */
  private prepareChartData(): void {
    if (!this.reportData) return;
    
    // Prepare output chart data
    this.outputChartData = this.reportData.outputs.map(output => ({
      name: output.name,
      budget: output.totalBudget,
      expenditure: output.totalCashDistributed,
      beneficiaries: output.totalBeneficiaries,
      progress: output.progress
    }));
    
    // Prepare status chart data
    this.statusChartData = [
      { name: 'Ongoing', value: this.reportData.activitiesByStatus.ongoing },
      { name: 'Completed', value: this.reportData.activitiesByStatus.completed },
      { name: 'Cancelled', value: this.reportData.activitiesByStatus.cancelled }
    ];
    
    // Prepare category chart data if we have a selected output
    if (this.selectedOutput) {
      this.categoryChartData = this.selectedOutput.categories.map(category => ({
        name: category.name,
        budget: category.totalBudget,
        expenditure: category.totalCashDistributed,
        beneficiaries: category.totalBeneficiaries,
        progress: category.progress
      }));
    }
  }

  /**
   * Prepare trend chart data
   */
  private prepareTrendChartData(): void {
    if (!this.timeSeriesData) return;
    
    this.trendChartData = {
      periods: this.timeSeriesData.map(d => d.date),
      activitiesStarted: this.timeSeriesData.map(d => d.cashDistributed),
      activitiesCompleted: this.timeSeriesData.map(d => d.beneficiaries),
      beneficiariesReached: this.timeSeriesData.map(d => d.beneficiaries),
      expenditure: this.timeSeriesData.map(d => d.cashDistributed)
    };
  }

  /**
   * Prepare project chart data
   */
  private prepareProjectChartData(): void {
    if (!this.projectGroups) return;
    
    // Prepare project group chart data
    this.projectChartData = this.projectGroups.map(group => ({
      name: group.name,
      budget: group.totalBudget,
      expenditure: group.totalCashDistributed,
      beneficiaries: group.totalBeneficiaries,
      progress: group.progress
    }));
  }

  /**
   * Prepare project-output relationship data for visualization
   */
  private prepareProjectOutputRelationshipData(): void {
    if (!this.projects || !this.reportData) return;
    
    // Create a data structure for visualizing the relationship between projects and outputs
    // This could be used for a Sankey diagram or other visualization
    
    // Map to store output name by id for easier reference
    const outputsMap = new Map<string, string>();
    this.reportData.outputs.forEach(output => {
      outputsMap.set(output.id, output.name);
    });
    
    // Count the number of projects per output
    const outputCounts = new Map<string, number>();
    
    // Create links data for visualization
    const links = [];
    
    // Process each project
    this.projects.forEach(project => {
      if (project.outputs && project.outputs.length > 0) {
        project.outputs.forEach(outputId => {
          // Increment the count for this output
          const currentCount = outputCounts.get(outputId) || 0;
          outputCounts.set(outputId, currentCount + 1);
          
          // Add a link for visualization
          const outputName = outputsMap.get(outputId) || `Output ${outputId}`;
          links.push({
            source: project.name,
            target: outputName,
            value: project.budget / project.outputs.length // Distribute budget proportionally
          });
        });
      }
    });
    
    // Store the data for chart rendering
    this.projectOutputChartData = {
      nodes: [
        // Project nodes
        ...this.projects.map(p => ({ id: p.name, type: 'project' })),
        // Output nodes
        ...Array.from(outputsMap.entries()).map(([id, name]) => ({ id: name, type: 'output' }))
      ],
      links: links
    };
  }

  /**
   * Initialize charts
   */
  private initializeCharts(): void {
    // This would set up the charts using a charting library
    // For now, it's a placeholder
    console.log('Initializing charts');
  }

  /**
   * Get color for progress bar based on percentage
   */
  getProgressColor(progress: number): string {
    if (progress < 25) return 'danger';
    if (progress < 50) return 'warning';
    if (progress < 75) return 'info';
    return 'success';
  }

  /**
   * Format large numbers with comma separators
   */
  formatNumber(num: number): string {
    return num ? num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',') : '0';
  }

  /**
   * Format currency values
   */
  formatCurrency(value: number): string {
    return `$${this.formatNumber(value)}`;
  }

  /**
   * Calculate percentage
   */
  calculatePercentage(value: number, total: number): number {
    return total > 0 ? Math.round((value / total) * 100) : 0;
  }

  /**
   * Helper method to safely find an output by ID
   */
  getOutputById(outputId: string): Output | null {
    if (!this.reportData || !this.reportData.outputs) return null;
    return this.reportData.outputs.find(output => output.id === outputId) || null;
  }

  /**
   * Export the current view as PDF
   */
  exportAsPdf(): void {
    console.log('Exporting as PDF');
    // This would be implemented with a PDF generation library
  }

  /**
   * Export the current view as Excel
   */
  exportAsExcel(): void {
    console.log('Exporting as Excel');
    // This would be implemented with an Excel generation library
  }

  /**
   * Select a category by its code (used by the categories grid)
   */
  selectCategoryByCode(code: string): void {
    // Format is like "1.1", "2.3", etc.
    // First part (before dot) is the output number, second part is the category within that output
    const parts = code.split('.');
    if (parts.length !== 2) return;
    
    const outputNumber = parts[0];
    const categoryNumber = parts[1];
    
    // Find the matching output and category
    for (const output of this.reportData.outputs) {
      if (output.code === outputNumber) {
        for (const category of output.categories) {
          if (category.code.endsWith(`.${categoryNumber}`)) {
            this.selectCategory(category);
            return;
          }
        }
      }
    }
    
    // If no match found, we could add code to create a dummy category or show error message
    console.log(`No category found with code ${code}`);
  }

  /**
   * Get total structures for the selected category
   */
  getCategoryStructuresTotal(): number {
    // Temporarily return a fixed value for testing
    if (this.isInfrastructureCategory()) {
      return 845;
    }
    
    // Original code
    if (!this.selectedCategory) return 0;
    return this.selectedCategory.structures || 0;
  }
  
  /**
   * Get total households for the selected category
   */
  getCategoryHouseholdsTotal(): number {
    // Temporarily return a fixed value for testing
    if (this.isInfrastructureCategory()) {
      return 3200;
    }
    
    // Original code
    if (!this.selectedCategory) return 0;
    return this.selectedCategory.households || 0;
  }
  
  /**
   * Check if a category has specific indicators
   */
  hasCategoryIndicators(): boolean {
    return this.selectedCategory?.indicators != null && this.selectedCategory.indicators.length > 0;
  }
  
  /**
   * Get indicators for the selected category
   */
  getCategoryIndicators(): any[] {
    if (!this.selectedCategory || !this.selectedCategory.indicators) return [];
    return this.selectedCategory.indicators;
  }
  
  /**
   * Get indicator by name for a specific category
   */
  getCategoryIndicatorByName(name: string): number {
    if (!this.selectedCategory || !this.selectedCategory.indicators) return 0;
    const indicator = this.selectedCategory.indicators.find(i => i.name === name);
    return indicator ? indicator.value : 0;
  }
  
  /**
   * Check if category is Infrastructure type (has structures and households)
   */
  isInfrastructureCategory(): boolean {
    console.log('Checking if infrastructure category. Selected category:', this.selectedCategory?.code);
    
    // Make sure we always identify category 1.1 as Infrastructure
    if (this.selectedCategory && this.selectedCategory.code === '1.1') {
      console.log('This is an infrastructure category');
      return true;
    }
    
    return false;
  }
  
  /**
   * Check if category is Health type
   */
  isHealthCategory(): boolean {
    if (!this.selectedCategory) return false;
    return this.selectedCategory.code === '1.2';
  }
  
  /**
   * Check if category is UCT type
   */
  isUCTCategory(): boolean {
    if (!this.selectedCategory) return false;
    return this.selectedCategory.code === '2.1';
  }
  
  /**
   * Check if category is Livelihoods type
   */
  isLivelihoodsCategory(): boolean {
    if (!this.selectedCategory) return false;
    return this.selectedCategory.code === '2.3';
  }

  /**
   * Get projects filtered by the selected project group
   */
  getFilteredProjects(): Project[] {
    if (!this.projects || !Array.isArray(this.projects)) {
      return [];
    }
    
    if (this.selectedProjectGroup) {
      return this.projects.filter(p => p.groupId === this.selectedProjectGroup.id);
    }
    
    return this.projects;
  }
  
  /**
   * Select a project group by its ID - UPDATED to use hierarchical endpoint
   */
  selectProjectGroupById(groupId: string): void {
    console.log('🔄 selectProjectGroupById called with:', groupId);
    
    if (!groupId) {
      this.selectedProjectGroup = null;
      this.currentFilters = { ...this.currentFilters, projectGroups: [] };
    } else {
      const group = this.projectGroups.find(g => g.id === groupId);
      this.selectedProjectGroup = group || null;
      this.currentFilters = { 
        ...this.currentFilters, 
        projectGroups: group && group.name ? [group.name] : [], 
        projects: [] // Clear individual project filter
      };
    }
    
    console.log('🔄 Updated filters, triggering hierarchical endpoint');
    this.loadReportData(); // Use hierarchical endpoint instead of loadDashboardData
  }
  
  /**
   * Check if there are filtered projects to show in the dropdown
   */
  hasFilteredProjects(): boolean {
    return this.getFilteredProjects().length > 0;
  }
  
  /**
   * Clear the project group filter
   */
  clearProjectGroupFilter(): void {
    console.log('🔄 Clearing project group filter');
    
    this.selectedProjectGroup = null;
    
    // Remove project group filter
    this.currentFilters = {
      ...this.currentFilters,
      projectGroups: []
    };
    
    // Refresh data without project group filter
    this.loadReportData();
    
    console.log('✅ Project group filter cleared');
  }
  
  /**
   * Check if a tab is active
   */
  isTabActive(tab: 'overview' | 'outputs' | 'categories' | 'interventions' | 'geographic' | 'project-groups' | 'projects'): boolean {
    return this.activeTab === tab;
  }

  /**
   * Select a project by its ID - UPDATED to use hierarchical endpoint
   */
  selectProjectById(projectId: string): void {
    console.log('🔄 selectProjectById called with:', projectId);
    
    if (!projectId) {
      this.selectedProject = null;
      this.currentFilters = { ...this.currentFilters, projects: [] };
    } else {
      const project = this.projects.find(p => p.id === projectId);
      this.selectedProject = project || null;
      this.currentFilters = { 
        ...this.currentFilters, 
        projects: [projectId],
        projectGroups: [] // Clear group filter
      };
    }
    
    console.log('🔄 Updated filters, triggering hierarchical endpoint');
    this.loadReportData(); // Use hierarchical endpoint instead of loadDashboardData
  }

  clearProjectFilter(): void {
    console.log('🔄 Clearing project filter');
    
    this.selectedProject = null;
    
    // Remove project filter
    this.currentFilters = {
      ...this.currentFilters,
      projects: []
    };
    
    // Refresh data without project filter
    this.loadReportData();
    
    console.log('✅ Project filter cleared');
  }

  // Add methods to extract and compute data from fullReport
  private computeReportData(fullReport: any): ReportData {
    // Example: just return the fullReport as ReportData if it matches the shape
    // You may want to adapt this to match your ReportData interface
    return fullReport as ReportData;
  }

  private computeSummaryStats(fullReport: any): SummaryStats {
    // Compute summary stats from fullReport.activities, outputs, etc.
    // Example: count activities, outputs, indicators, etc.
    return {
      totalProjects: Array.isArray(fullReport.projects) ? fullReport.projects.length : 0,
      totalBudget: fullReport.projects?.reduce((sum: number, p: any) => sum + (p.budget || 0), 0) || 0,
      totalCashDistributed: fullReport.projects?.reduce((sum: number, p: any) => sum + (p.cashDistributed || 0), 0) || 0,
      totalBeneficiaries: fullReport.projects?.reduce((sum: number, p: any) => sum + (p.beneficiaries || 0), 0) || 0,
      avgProgress: fullReport.projects?.length ? Math.round(fullReport.projects.reduce((sum: number, p: any) => sum + (p.progress || 0), 0) / fullReport.projects.length) : 0,
      countByStatus: {},
      countByLocation: {},
      topCategories: []
    };
  }

  private computeTimeSeriesData(fullReport: any): TimeSeriesData[] {
    // Example: return an empty array or compute from activities if available
    return [];
  }

  private computeProjectGroups(fullReport: any): ProjectGroup[] {
    // Extract unique groupings from projects
    if (!Array.isArray(fullReport.projects)) return [];
    const groupNames = Array.from(new Set(fullReport.projects.map((p: any) => p.grouping).filter((g: any) => !!g)));
    return groupNames.map((name, idx) => ({ id: idx.toString(), name: String(name), projects: fullReport.projects.filter((p: any) => p.grouping === name) }));
  }

  /**
   * Load all report data based on current filters
   */
  private loadReportData(): void {
    console.log('🔄 Triggering optimized data load with current filters:', this.currentFilters);
    
    // Use the optimized filter subject instead of direct API call
    this.filterChangeSubject.next(this.currentFilters);
  }

  /**
   * Process project groups data from API
  // Removed obsolete processProjectGroupsData and processProjectsData methods
  // Now using extractProjectGroupsFromHierarchy and extractProjectsFromHierarchy

      cashDistributed: project.cashDistributed || 0,
      progress: project.progress || 0,
      beneficiaries: project.beneficiaries || 0,
      outputs: project.outputs || []
    }));
  }

  /**
   * Apply project-based filtering to all data
   */
  private applyProjectFiltering(): void {
    if (!this.filteredData) return;
    
    let projectFilterApplied = false;
    
    // Apply project group filter
    if (this.selectedProjectGroup) {
      console.log('🎯 Applying project group filter:', this.selectedProjectGroup.name);
      
      // Get all projects in the selected group
      const groupProjects = this.projects.filter(p => 
        p.groupId === this.selectedProjectGroup.id || 
        p.grouping === this.selectedProjectGroup.name
      );
      const groupProjectIds = groupProjects.map(p => p.id);
      
      console.log('📊 Projects in selected group:', {
        groupName: this.selectedProjectGroup.name,
        projectCount: groupProjects.length,
        projectIds: groupProjectIds
      });
      
      // Filter all data types by projects in the group
      this.filteredData = {
        ...this.filteredData,
        projects: this.filteredData.projects?.filter(p => 
          groupProjectIds.includes(p.id) || groupProjectIds.includes(p.id?.toString())
        ) || [],
        activities: this.filteredData.activities?.filter(a => 
          groupProjectIds.includes(a.projectId) || groupProjectIds.includes(a.projectId?.toString())
        ) || []
      };
      
      projectFilterApplied = true;
    }
    
    // Apply specific project filter (overrides group filter)
    if (this.selectedProject) {
      console.log('🎯 Applying specific project filter:', this.selectedProject.name);
      
      const projectId = this.selectedProject.id;
      
      // Filter all data types by the specific project
      this.filteredData = {
        ...this.filteredData,
        projects: this.filteredData.projects?.filter(p => 
          p.id === projectId || p.id?.toString() === projectId
        ) || [],
        activities: this.filteredData.activities?.filter(a => 
          a.projectId === projectId || a.projectId?.toString() === projectId
        ) || []
      };
      
      projectFilterApplied = true;
    }
    
    if (projectFilterApplied) {
      // Recalculate category totals based on filtered projects/activities
      this.recalculateCategoryTotalsFromFilteredData();
      
      // Recalculate overall totals
      this.recalculateOverallTotals();
      
      console.log('✅ Project filtering applied. Filtered data:', {
        projects: this.filteredData.projects?.length || 0,
        activities: this.filteredData.activities?.length || 0,
        categories: this.filteredData.categories?.length || 0
      });
    }
  }

  /**
   * Recalculate category totals from filtered projects and activities
   */
  private recalculateCategoryTotalsFromFilteredData(): void {
    if (!this.filteredData?.categories) return;
    
    this.filteredData.categories = this.filteredData.categories.map(category => {
      // Get activities for this category from filtered data
      const categoryActivities = this.filteredData.activities?.filter(activity => {
        // Find intervention profile for this activity
        const interventionProfile = this.fullReportData?.interventionProfiles?.find(ip => 
          ip.id === activity.interventionProfileId
        );
        return interventionProfile?.categoryId === category.id;
      }) || [];
      
      // Recalculate totals from filtered activities
      const totalBudget = categoryActivities.reduce((sum, a) => sum + (a.budget || 0), 0);
      const totalCashDistributed = categoryActivities.reduce((sum, a) => sum + (a.cashDistributed || 0), 0);
      const totalBeneficiaries = categoryActivities.reduce((sum, a) => sum + (a.beneficiaries || 0), 0);
      const totalActivities = categoryActivities.length;
      const progress = totalActivities > 0 ? 
        Math.round(categoryActivities.reduce((sum, a) => sum + (a.progress || 0), 0) / totalActivities) : 0;
      
      return {
        ...category,
        totalBudget,
        totalCashDistributed,
        totalBeneficiaries,
        totalActivities,
        progress
      };
    });
  }

  /**
   * Recalculate overall totals from filtered data
   */
  private recalculateOverallTotals(): void {
    if (!this.filteredData) return;
    
    const projects = this.filteredData.projects || [];
    const activities = this.filteredData.activities || [];
    
    this.filteredData.totalBudget = projects.reduce((sum, p) => sum + (p.budget || 0), 0);
    this.filteredData.totalCashDistributed = projects.reduce((sum, p) => sum + (p.cashDistributed || 0), 0);
    this.filteredData.totalBeneficiaries = projects.reduce((sum, p) => sum + (p.beneficiaries || 0), 0);
    this.filteredData.overallProgress = projects.length > 0 ? 
      Math.round(projects.reduce((sum, p) => sum + (p.progress || 0), 0) / projects.length) : 0;
    
    // Update geographic coverage from filtered activities
    this.filteredData.geographicCoverage = this.calculateGeographicCoverage(activities);
    this.filteredData.activitiesByStatus = this.calculateActivitiesByStatus(activities);
    
    console.log('📊 Overall totals recalculated:', {
      totalBudget: this.filteredData.totalBudget,
      totalProjects: projects.length,
      totalActivities: activities.length
      });
  }
  
  /**
   * Process the full report data into filtered data for components
   */
  private processReportData(data: any): void {
    console.log('Processing report data with quarterly filters:', data);
    
    // Create default data structure if data is missing or invalid
    if (!data) {
      console.warn('⚠️ No data to process, using defaults');
      this.filteredData = {
        projects: [],
        categories: [],
        interventionProfiles: [],
        activities: []
      };
      return;
    }
    
    try {
      // Apply quarterly filtering to all data types
      const filteredProjects = this.applyQuarterlyFilterToProjects(data.projects || []);
      const filteredActivities = this.applyQuarterlyFilterToActivities(data.activities || []);
      const filteredCategories = this.applyQuarterlyFilterToCategories(data.categories || []);
      const filteredInterventions = this.applyQuarterlyFilterToInterventions(data.interventionProfiles || []);
      
      // Create a filtered version of the data based on current filters
      this.filteredData = {
        // Apply quarterly filters to all data
        projects: filteredProjects,
        categories: filteredCategories,
        interventionProfiles: filteredInterventions,
        activities: filteredActivities,
        
        // Recalculate totals based on filtered data
        totalBudget: this.calculateFilteredTotal(filteredProjects, 'budget'),
        totalCashDistributed: this.calculateFilteredTotal(filteredProjects, 'cashDistributed'),
        totalBeneficiaries: this.calculateFilteredTotal(filteredProjects, 'beneficiaries'),
        overallProgress: this.calculateFilteredProgress(filteredProjects),
        
        // Add quarterly-specific data
        quarterlyPeriod: this.selectedQuarter ? `${this.selectedQuarter} ${this.selectedYear}` : null,
        quarterlyFiltersApplied: !!this.selectedQuarter,
        
        // Geographic and status breakdowns
        activitiesByStatus: this.calculateActivitiesByStatus(filteredActivities),
        geographicCoverage: this.calculateGeographicCoverage(filteredActivities)
      };
      
      // Update quarterly KPIs based on filtered data
      this.updateQuarterlyKPIsFromFilteredData(this.filteredData);
      
      console.log('✅ Report data processed successfully with quarterly filtering');
      console.log('📊 Filtered data summary:', {
        projects: filteredProjects.length,
        activities: filteredActivities.length,
        categories: filteredCategories.length,
        quarterlyPeriod: this.filteredData.quarterlyPeriod,
        totalBudget: this.filteredData.totalBudget
      });
      
    } catch (error) {
      console.error('❌ Error processing report data:', error);
      
      // Provide default structure on error
      this.filteredData = {
        projects: [],
        categories: [],
        interventionProfiles: [],
        activities: [],
        totalBudget: 0,
        totalCashDistributed: 0,
        totalBeneficiaries: 0,
        overallProgress: 0
      };
    }
  }

  /**
   * Apply quarterly filtering to projects
   */
  private applyQuarterlyFilterToProjects(projects: any[]): any[] {
    if (!this.selectedQuarter || !this.selectedYear || !projects?.length) {
      return projects || [];
    }
    
    const quarterStartMonth = (parseInt(this.selectedQuarter.substring(1)) - 1) * 3;
    const quarterStart = new Date(this.selectedYear, quarterStartMonth, 1);
    const quarterEnd = new Date(this.selectedYear, quarterStartMonth + 3, 0);
    
    return projects.filter(project => {
      const projectStart = new Date(project.startDate);
      const projectEnd = project.endDate ? new Date(project.endDate) : new Date();
      
      // Include project if it overlaps with the selected quarter
      return (projectStart <= quarterEnd && projectEnd >= quarterStart);
    });
  }

  /**
   * Apply quarterly filtering to activities
   */
  private applyQuarterlyFilterToActivities(activities: any[]): any[] {
    if (!this.selectedQuarter || !this.selectedYear || !activities?.length) {
      return activities || [];
    }
    
    const quarterStartMonth = (parseInt(this.selectedQuarter.substring(1)) - 1) * 3;
    const quarterStart = new Date(this.selectedYear, quarterStartMonth, 1);
    const quarterEnd = new Date(this.selectedYear, quarterStartMonth + 3, 0);
    
    return activities.filter(activity => {
      const activityStart = new Date(activity.startDate || activity.plannedStartDate);
      const activityEnd = activity.endDate ? new Date(activity.endDate) : new Date();
      
      // Include activity if it overlaps with the selected quarter
      return (activityStart <= quarterEnd && activityEnd >= quarterStart);
    });
  }

  /**
   * Apply quarterly filtering to categories
   */
  private applyQuarterlyFilterToCategories(categories: any[]): any[] {
    if (!categories?.length) return categories || [];
    
    // Categories are filtered based on their associated activities/projects
    // We'll keep all categories but recalculate their totals based on filtered data
    return categories.map(category => ({
      ...category,
      quarterlyFiltered: true,
      lastUpdated: new Date().toISOString()
    }));
  }

  /**
   * Apply quarterly filtering to intervention profiles
   */
  private applyQuarterlyFilterToInterventions(interventions: any[]): any[] {
    if (!interventions?.length) return interventions || [];
    
    // Similar to categories, keep all interventions but mark as filtered
    return interventions.map(intervention => ({
      ...intervention,
      quarterlyFiltered: true,
      lastUpdated: new Date().toISOString()
    }));
  }

  /**
   * Calculate filtered total for a specific field
   */
  private calculateFilteredTotal(items: any[], field: string): number {
    if (!items?.length) return 0;
    return items.reduce((sum, item) => sum + (item[field] || 0), 0);
  }

  /**
   * Calculate filtered progress average
   */
  private calculateFilteredProgress(projects: any[]): number {
    if (!projects?.length) return 0;
    const totalProgress = projects.reduce((sum, project) => sum + (project.progress || 0), 0);
    return Math.round(totalProgress / projects.length);
  }

  /**
   * Calculate activities by status from filtered data
   */
  private calculateActivitiesByStatus(activities: any[]): any {
    if (!activities?.length) {
      return { ongoing: 0, completed: 0, cancelled: 0 };
    }
    
    return activities.reduce((acc, activity) => {
      const status = this.mapActivityStatusNumberToString(activity.status);
      acc[status] = (acc[status] || 0) + 1;
      return acc;
    }, { ongoing: 0, completed: 0, cancelled: 0 });
  }

  /**
   * Calculate geographic coverage from filtered data
   */
  private calculateGeographicCoverage(activities: any[]): any {
    if (!activities?.length) {
      return { provinces: 0, districts: 0 };
    }
    
    const provinces = new Set();
    const districts = new Set();
    
    activities.forEach(activity => {
      if (activity.provinceIds) {
        activity.provinceIds.forEach((id: any) => provinces.add(id));
      }
      if (activity.districtIds) {
        activity.districtIds.forEach((id: any) => districts.add(id));
      }
    });
    
    return {
      provinces: provinces.size,
      districts: districts.size
    };
  }

  /**
   * Map activity status number to string
   */
  private mapActivityStatusNumberToString(status: number): string {
    switch (status) {
      case 0: return 'ongoing';
      case 1: return 'completed';
      case 2: return 'archived';
      case 3: return 'cancelled';
      default: return 'ongoing';
    }
  }

  /**
   * Update quarterly KPIs based on filtered data
   */
  private updateQuarterlyKPIsFromFilteredData(filteredData: any): void {
    if (!this.selectedQuarter) {
      this.quarterlyKPIs = null;
      return;
    }
    
    const projects = filteredData.projects || [];
    const activities = filteredData.activities || [];
    
    this.quarterlyKPIs = {
      totalProjects: projects.length,
      totalBudget: filteredData.totalBudget || 0,
      totalBeneficiaries: filteredData.totalBeneficiaries || 0,
      totalActivities: activities.length,
      overallProgress: filteredData.overallProgress || 0,
      
      // Calculate progress percentages
      projectProgress: projects.length > 0 ? 
        Math.round(projects.filter((p: any) => p.progress > 80).length / projects.length * 100) : 0,
      budgetUtilization: filteredData.totalBudget > 0 ? 
        Math.round((filteredData.totalCashDistributed || 0) / filteredData.totalBudget * 100) : 0,
      beneficiaryProgress: 91, // This would be calculated against targets
      
      // Additional quarterly metrics
      quarterlyPeriod: `${this.selectedQuarter} ${this.selectedYear}`,
      lastUpdated: new Date().toISOString()
    };
    
    console.log('📊 Updated quarterly KPIs:', this.quarterlyKPIs);
  }

  /**
   * Force refresh all data - can be called from UI button
   */
  forceRefresh(): void {
    console.log('🔄 Forcing data refresh...');
    this.lastUpdated = new Date();
    
    // Clear any cached data
    this.fullReportData = null;
    this.filteredData = null;
    
    // Show loading indicator
    this.loading = true;
    this.error = null;
    
    // Create fresh mock data to ensure UI doesn't break
    this.createMockData();
    
    // Load fresh data
    this.loadReportData();
  }

  /**
   * Get the base API URL from the reports service
   */
  getApiBaseUrl(): string {
    return this.reportsService['apiUrl'] || 'Not available';
  }

  /**
   * Handle tab change from Material tabs
   */
  onTabChange(tabIndex: number): void {
    this.selectedTabIndex = tabIndex;
    
    // Map the tab index to the tab name
    const tabName = this.tabMapping[tabIndex as keyof typeof this.tabMapping];
    
    // Set the active tab
    this.setActiveTab(tabName as any);
    
    console.log(`Tab changed to ${tabName} (index: ${tabIndex})`);
    
    // Ensure filtered data is propagated to the new tab
    this.propagateFilteredDataToActiveTab(tabName);
  }

  /**
   * Propagate filtered data to the active tab
   */
  private propagateFilteredDataToActiveTab(tabName: string): void {
    if (!this.filteredData) {
      console.warn('No filtered data available for tab:', tabName);
      return;
    }
    
    console.log(`📊 Propagating filtered data to ${tabName} tab:`, {
      projects: this.filteredData.projects?.length || 0,
      activities: this.filteredData.activities?.length || 0,
      categories: this.filteredData.categories?.length || 0,
      quarterlyPeriod: this.filteredData.quarterlyPeriod || 'All Time',
      totalBudget: this.filteredData.totalBudget || 0
    });
    
    // Force update of charts and visualizations for the active tab
    setTimeout(() => {
      this.updateTabSpecificData(tabName);
    }, 100);
  }

  /**
   * Update tab-specific data and visualizations
   */
  private updateTabSpecificData(tabName: string): void {
    switch (tabName) {
      case 'overview':
        this.updateOverviewData();
        break;
      case 'projects':
        this.updateProjectsData();
        break;
      case 'categories':
        this.updateCategoriesData();
        break;
      case 'regions':
        this.updateRegionsData();
        break;
    }
  }

  /**
   * Update overview tab data
   */
  private updateOverviewData(): void {
    // Recalculate overview statistics based on filtered data
    if (this.filteredData) {
      this.summaryStats = {
        totalProjects: this.filteredData.projects?.length || 0,
        totalBudget: this.filteredData.totalBudget || 0,
        totalCashDistributed: this.filteredData.totalCashDistributed || 0,
        totalBeneficiaries: this.filteredData.totalBeneficiaries || 0,
        avgProgress: this.filteredData.overallProgress || 0,
        countByStatus: this.filteredData.activitiesByStatus || {},
        countByLocation: this.filteredData.geographicCoverage || {},
        topCategories: this.getTopCategoriesFromFilteredData()
      };
      
      console.log('📈 Updated overview data for quarterly filter');
    }
  }

  /**
   * Update projects tab data
   */
  private updateProjectsData(): void {
    // Update project-specific visualizations
    this.prepareProjectChartData();
    console.log('📊 Updated projects data for quarterly filter');
  }

  /**
   * Update categories tab data
   */
  private updateCategoriesData(): void {
    // Update category-specific data
    if (this.filteredData?.categories) {
      console.log('🏷️ Updated categories data for quarterly filter');
    }
  }

  /**
   * Update regions tab data
   */
  private updateRegionsData(): void {
    // Update geographic data
    if (this.filteredData?.geographicCoverage) {
      console.log('🗺️ Updated regions data for quarterly filter');
    }
  }

  /**
   * Get top categories from filtered data
   */
  private getTopCategoriesFromFilteredData(): any[] {
    if (!this.filteredData?.categories?.length) return [];
    
    return this.filteredData.categories
      .sort((a: any, b: any) => (b.totalBudget || 0) - (a.totalBudget || 0))
      .slice(0, 5)
      .map((category: any) => ({
        name: category.name,
        budget: category.totalBudget || 0,
        progress: category.progress || 0
      }));
  }

  /**
   * Create mock data if needed for testing UI
   */
  private createMockData(): void {
    console.log('📊 Creating mock data for UI testing');
    
    // Create mock project data
    const mockProjects = [
      {
        id: 'p1',
        code: 'ECO-001',
        name: 'Market Access Program',
        groupId: 'g1',
        grouping: 'Economic Development',
        status: 'active',
        startDate: '2023-01-15',
        endDate: '2024-12-31',
        budget: 3500000,
        cashDistributed: 2100000,
        progress: 60,
        beneficiaries: 15000
      },
      {
        id: 'p2',
        code: 'HEA-001',
        name: 'Primary Healthcare Strengthening',
        groupId: 'g2',
        grouping: 'Health',
        status: 'active',
        startDate: '2023-03-01',
        endDate: '2025-02-28',
        budget: 4800000,
        cashDistributed: 2400000,
        progress: 50,
        beneficiaries: 20000
      }
    ];
    
    // Create mock categories
    const mockCategories = [
      {
        id: 'c1',
        code: '1.1',
        name: 'Infrastructure Development',
        budget: 2500000,
        cashDistributed: 1500000,
        progress: 60,
        beneficiaries: 12500
      },
      {
        id: 'c2',
        code: '1.2',
        name: 'Health Services',
        budget: 3200000,
        cashDistributed: 1920000,
        progress: 60,
        beneficiaries: 16000
      }
    ];
    
    // Create mock intervention profiles
    const mockInterventions = [
      {
        id: 'i1',
        name: 'Market Infrastructure',
        categoryId: 'c1',
        budget: 1800000,
        cashDistributed: 1080000,
        progress: 60,
        beneficiaries: 9000
      },
      {
        id: 'i2',
        name: 'Health Facilities',
        categoryId: 'c2',
        budget: 2500000,
        cashDistributed: 1500000,
        progress: 60,
        beneficiaries: 12500
      }
    ];
    
    // Check if we already have data
    if (!this.fullReportData || !this.fullReportData.projects || this.fullReportData.projects.length === 0) {
      console.log('Using mock data as primary data source');
      this.fullReportData = {
        projects: mockProjects,
        categories: mockCategories,
        interventionProfiles: mockInterventions,
        activities: [],
        totalBudget: 8300000,
        totalCashDistributed: 4500000,
        totalBeneficiaries: 35000,
        overallProgress: 55
      };
    }
    
    // Always update filtered data to ensure components have something to display
    this.filteredData = {
      projects: mockProjects,
      categories: mockCategories,
      interventionProfiles: mockInterventions,
      activities: [],
      totalBudget: 8300000,
      totalCashDistributed: 4500000,
      totalBeneficiaries: 35000,
      overallProgress: 55
    };
    
    // Update project groups too
    this.projectGroups = this.computeProjectGroups(this.fullReportData);
    
    console.log('📊 Mock data created successfully:', this.filteredData);
  }

  /**
   * Extract full report data from hierarchical structure for compatibility
   */
  private extractFullReportFromHierarchical(hierarchicalData: any[]): any {
    console.log('🔍 extractFullReportFromHierarchical called with data:', hierarchicalData);
    
    if (!hierarchicalData || hierarchicalData.length === 0) {
      console.log('❌ No hierarchical data provided');
      return this.createDefaultFullReportData();
    }
    
    const projects: any[] = [];
    const categoriesMap = new Map(); // Use Map to accumulate category totals
    const interventionProfiles: any[] = [];
    const activities: any[] = [];
    
    let totalBudget = 0;
    let totalCashDistributed = 0;
    let totalBeneficiaries = 0;
    let projectCount = 0;
    let totalProgress = 0;
    
    // Extract data from hierarchical structure
    hierarchicalData.forEach((group, groupIndex) => {
      console.log(`🔍 Processing group ${groupIndex}:`, {
        groupId: group.id,
        groupName: group.name,
        projectsCount: group.projects?.length || 0
      });
      
      if (group.projects && Array.isArray(group.projects)) {
        group.projects.forEach((project, projectIndex) => {
          console.log(`🔍 Processing project ${projectIndex} in group ${groupIndex}:`, {
            projectId: project.id,
            projectName: project.name,
            interventionsCount: project.interventions?.length || 0
          });
          
          // Add project to flat list
          projects.push({
            id: project.id,
            code: project.code,
            name: project.name,
            groupId: group.id,
            grouping: group.name,
            status: project.status,
            startDate: project.startDate,
            endDate: project.endDate,
            budget: project.budget || 0,
            cashDistributed: project.cashDistributed || 0,
            progress: project.progress || 0,
            beneficiaries: project.summary?.beneficiaries || 0
          });
          
          // Accumulate totals
          totalBudget += project.budget || 0;
          totalCashDistributed += project.cashDistributed || 0;
          totalBeneficiaries += project.summary?.beneficiaries || 0;
          totalProgress += project.progress || 0;
          projectCount++;
          
          // Extract interventions and activities
          if (project.interventions && Array.isArray(project.interventions)) {
            project.interventions.forEach((intervention, interventionIndex) => {
              console.log(`🔍 Processing intervention ${interventionIndex}:`, {
                interventionId: intervention.id,
                categoryId: intervention.categoryId,
                categoryName: intervention.categoryName,
                hasActivities: intervention.activities?.length || 0,
                hasSummary: !!intervention.summary
              });
              
              // Initialize or get existing category data
              let categoryData = categoriesMap.get(intervention.categoryId);
              if (!categoryData) {
                categoryData = {
                  id: intervention.categoryId,
                  name: intervention.categoryName,
                  code: intervention.categoryCode || intervention.categoryId, // Try categoryCode first, fallback to categoryId
                  output: intervention.output || '',
                  description: intervention.description || '',
                  totalBudget: 0,
                  totalCashDistributed: 0,
                  totalBeneficiaries: 0,
                  progress: 0,
                  interventionCount: 0,
                  totalActivities: 0
                };
                categoriesMap.set(intervention.categoryId, categoryData);
                console.log(`✅ Created new category:`, categoryData);
              }
              
              // Accumulate category totals from intervention summary
              if (intervention.summary) {
                categoryData.totalBudget += intervention.summary.totalBudget || 0;
                categoryData.totalCashDistributed += intervention.summary.totalCashDistributed || 0;
                categoryData.totalBeneficiaries += intervention.summary.beneficiaries || 0;
                categoryData.totalActivities += intervention.summary.totalActivities || 0;
                
                // Calculate average progress
                const newCount = categoryData.interventionCount + 1;
                categoryData.progress = ((categoryData.progress * categoryData.interventionCount) + (intervention.summary.progress || 0)) / newCount;
                categoryData.interventionCount = newCount;
              }
              
              // Add intervention profiles
              if (!interventionProfiles.find(ip => ip.id === intervention.id)) {
                interventionProfiles.push({
                  id: intervention.id,
                  categoryId: intervention.categoryId,
                  name: intervention.name,
                  abbreviation: intervention.abbreviation || '',
                  description: intervention.description || '',
                  totalBudget: intervention.summary?.totalBudget || 0,
                  totalCashDistributed: intervention.summary?.totalCashDistributed || 0,
                  totalBeneficiaries: intervention.summary?.beneficiaries || 0,
                  progress: intervention.summary?.progress || 0,
                  totalActivities: intervention.summary?.totalActivities || (intervention.activities ? intervention.activities.length : 0)
                });
              }
              
              // Extract activities
              if (intervention.activities && Array.isArray(intervention.activities)) {
                intervention.activities.forEach(activity => {
                  activities.push({
                    id: activity.id,
                    uniqueId: activity.uniqueId,
                    organizationId: activity.organizationId || 0,
                    projectId: project.id,
                    interventionProfileId: intervention.id,
                    status: this.mapActivityStatusToNumber(activity.status),
                    startDate: activity.startDate,
                    endDate: activity.endDate,
                    regions: activity.location?.regions || '',
                    provinceIds: activity.location?.provinceId ? [activity.location.provinceId] : [],
                    districtIds: activity.location?.districtId ? [activity.location.districtId] : [],
                    communityIds: activity.location?.communityIds || [],
                    budget: activity.budget || 0,
                    cashDistributed: activity.cashDistributed || 0,
                    dynamicColumns: activity.dynamicColumns || []
                  });
                });
              }
            });
          }
        });
      }
    });
    
    // Convert categories map to array and clean up temporary fields
    const categories = Array.from(categoriesMap.values()).map(cat => {
      const { interventionCount, ...cleanCategory } = cat;
      return {
        ...cleanCategory,
        progress: Math.round(cleanCategory.progress) // Round progress to whole number
      };
    });
    
    console.log('🔍 Final extraction results:', {
      categoriesCount: categories.length,
      categories: categories,
      projectsCount: projects.length,
      interventionProfilesCount: interventionProfiles.length,
      activitiesCount: activities.length
    });
    
    return {
      projects,
      categories,
      interventionProfiles,
      activities,
      totalBudget,
      totalCashDistributed,
      totalBeneficiaries,
      overallProgress: projectCount > 0 ? Math.round(totalProgress / projectCount) : 0
    };
  }
  
  /**
   * Extract project groups from hierarchical data
   */
  private extractProjectGroupsFromHierarchy(hierarchicalData: any[]): ProjectGroup[] {
    if (!hierarchicalData || hierarchicalData.length === 0) {
      return [];
    }
    
    return hierarchicalData.map(group => ({
      id: group.id?.toString() || '',
      name: group.name || `Group ${group.id}`,
      description: group.description || '',
      totalBudget: group.totalBudget || 0,
      totalCashDistributed: group.totalCashDistributed || 0,
      totalBeneficiaries: group.totalBeneficiaries || 0,
      progress: group.progress || 0,
      projectCount: group.projects?.length || 0,
      projects: group.projects || []
    }));
  }

  /**
   * Extract projects from hierarchical data
   */
  private extractProjectsFromHierarchy(hierarchicalData: any[]): Project[] {
    if (!hierarchicalData || hierarchicalData.length === 0) {
      return [];
    }
    
    const projects: Project[] = [];
    
    hierarchicalData.forEach(group => {
      if (group.projects && Array.isArray(group.projects)) {
        group.projects.forEach(project => {
          projects.push({
            id: project.id?.toString() || '',
            code: project.code || `PROJ-${project.id}`,
            name: project.name || `Project ${project.id}`,
            groupId: group.id?.toString() || '',
            grouping: group.name || '',
            status: project.status || 'active',
            startDate: project.startDate,
            endDate: project.endDate,
            budget: project.budget || 0,
            cashDistributed: project.cashDistributed || 0,
            progress: project.progress || 0,
            beneficiaries: project.summary?.beneficiaries || 0,
            outputs: project.outputs || []
          });
        });
      }
    });
    
    return projects;
  }
  
  /**
   * Map activity status from string to number for compatibility
   */
  private mapActivityStatusToNumber(status: string): number {
    switch (status?.toLowerCase()) {
      case 'ongoing': return 0;
      case 'completed': return 1;
      case 'archived': return 2;
      case 'cancelled': return 3;
      default: return 0;
    }
  }
  
  /**
   * Create default filtered data structure
   */
  private createDefaultFilteredData(): any {
    return {
      projects: [],
      categories: [],
      interventionProfiles: [],
      activities: [],
      totalBudget: 0,
      totalCashDistributed: 0,
      totalBeneficiaries: 0,
      overallProgress: 0
    };
  }
  
  /**
   * Create default full report data structure
   */
  private createDefaultFullReportData(): any {
    return {
      projects: [],
      activities: [],
      categories: [],
      interventionProfiles: [],
      targets: [],
      activityProgress: [],
      dynamicColumns: []
    };
  }

  /**
   * Enhanced Dashboard Functionality Methods
   */

  /**
   * Handle date range changes
   */
  onDateRangeChange(): void {
    if (this.startDate && this.endDate) {
      this.currentFilters = {
        ...this.currentFilters,
        startDate: this.startDate,
        endDate: this.endDate
      };
      this.loadReportData();
    }
  }

  /**
   * Toggle auto-refresh functionality
   */
  toggleAutoRefresh(): void {
    this.autoRefreshEnabled = !this.autoRefreshEnabled;
    
    if (this.autoRefreshEnabled) {
      this.startAutoRefresh();
    } else {
      this.stopAutoRefresh();
    }
  }

  /**
   * Start auto-refresh timer
   */
  private startAutoRefresh(): void {
    this.stopAutoRefresh(); // Clear any existing timer
    this.refreshTimer = setInterval(() => {
      this.loadReportData();
    }, this.refreshInterval * 1000);
  }

  /**
   * Stop auto-refresh timer
   */
  private stopAutoRefresh(): void {
    if (this.refreshTimer) {
      clearInterval(this.refreshTimer);
      this.refreshTimer = null;
    }
  }

  /**
   * Get count of active filters
   */
  getActiveFiltersCount(): number {
    let count = 0;
    if (this.currentFilters.startDate || this.currentFilters.endDate) count++;
    if (this.currentFilters.organizations?.length) count++;
    if (this.currentFilters.projects?.length) count++;
    if (this.currentFilters.categories?.length) count++;
    if (this.currentFilters.provinces?.length) count++;
    if (this.currentFilters.districts?.length) count++;
    if (this.selectedProjectGroup) count++;
    if (this.selectedProject) count++;
    return count;
  }

  /**
   * Export data as JSON
   */
  exportAsJson(): void {
    const dataToExport = {
      reportData: this.filteredData,
      summaryStats: this.summaryStats,
      timeSeriesData: this.timeSeriesData,
      filters: this.currentFilters,
      exportDate: new Date().toISOString(),
      reportTitle: 'UNDP Afghanistan Dashboard Report'
    };

    const dataStr = JSON.stringify(dataToExport, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `undp-dashboard-report-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
  }

  /**
   * Schedule report functionality
   */
  scheduleReport(): void {
    // This would open a modal for scheduling reports
    console.log('Schedule report functionality - to be implemented');
    
    // For now, show an alert
    alert('Report scheduling feature will be available soon. You will be able to schedule automated PDF/Excel reports via email.');
  }

  /**
   * Enhanced filter reset with confirmation
   */
  resetFiltersConfirm(): void {
    if (this.getActiveFiltersCount() > 0) {
      if (confirm('Are you sure you want to reset all filters? This will reload the dashboard with default settings.')) {
        this.resetFilters();
      }
    } else {
      this.resetFilters();
    }
  }

  /**
   * Quick filter presets
   */
  applyQuickFilter(filterType: string): void {
    const now = new Date();
    const currentYear = now.getFullYear();
    
    switch (filterType) {
      case 'thisMonth':
        this.startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        this.endDate = now;
        break;
      case 'thisQuarter':
        const currentQuarter = Math.floor(now.getMonth() / 3);
        this.startDate = new Date(currentYear, currentQuarter * 3, 1);
        this.endDate = now;
        break;
      case 'thisYear':
        this.startDate = new Date(currentYear, 0, 1);
        this.endDate = now;
        break;
      case 'lastYear':
        this.startDate = new Date(currentYear - 1, 0, 1);
        this.endDate = new Date(currentYear - 1, 11, 31);
        break;
    }
    
    this.onDateRangeChange();
  }

  /**
   * Enhanced Quarterly Filtering Methods
   */

  /**
   * Initialize available years for quarterly filtering
   */
  private initializeAvailableYears(): void {
    const currentYear = new Date().getFullYear();
    this.availableYears = [];
    
    // Add years from 2020 to current year + 1
    for (let year = 2020; year <= currentYear + 1; year++) {
      this.availableYears.push(year);
    }
    
    // Set PREVIOUS quarter as default (one quarter back)
    const currentMonth = new Date().getMonth();
    const currentQuarterNumber = Math.floor(currentMonth / 3) + 1;
    
    // Calculate previous quarter
    let previousQuarterNumber: number;
    let defaultYear: number;
    
    if (currentQuarterNumber === 1) {
      // If current is Q1, previous quarter is Q4 of last year
      previousQuarterNumber = 4;
      defaultYear = currentYear - 1;
    } else {
      // Otherwise, just subtract 1 from current quarter
      previousQuarterNumber = currentQuarterNumber - 1;
      defaultYear = currentYear;
    }
    
    this.selectedQuarter = `Q${previousQuarterNumber}` as 'Q1' | 'Q2' | 'Q3' | 'Q4';
    this.selectedYear = defaultYear;
    
    console.log('🗓️ Quarterly filter initialized to PREVIOUS quarter:', {
      currentQuarter: `Q${currentQuarterNumber} ${currentYear}`,
      selectedQuarter: `${this.selectedQuarter} ${this.selectedYear}`,
      defaultPeriod: 'Previous Quarter'
    });
    
    // Initialize quarterly stats
    this.initializeQuarterlyStats();
  }

  /**
   * Initialize quarterly statistics
   */
  private initializeQuarterlyStats(): void {
    this.quarterlyStats = {
      Q1: { activities: 45, budget: 2500000, beneficiaries: 12000 },
      Q2: { activities: 52, budget: 3200000, beneficiaries: 15500 },
      Q3: { activities: 38, budget: 2800000, beneficiaries: 11200 },
      Q4: { activities: 41, budget: 3100000, beneficiaries: 13800 }
    };
    
    this.updateQuarterlyKPIs();
  }

  /**
   * Update quarterly KPIs based on selected quarter
   */
  private updateQuarterlyKPIs(): void {
    if (!this.selectedQuarter) return;
    
    const quarterData = this.quarterlyStats[this.selectedQuarter];
    this.quarterlyKPIs = {
      totalProjects: 12,
      totalBudget: quarterData?.budget || 0,
      totalBeneficiaries: quarterData?.beneficiaries || 0,
      overallProgress: 68,
      projectProgress: 75,
      budgetUtilization: 82,
      beneficiaryProgress: 91
    };
  }

  /**
   * Handle year change
   */
  onYearChange(): void {
    console.log('Year changed to:', this.selectedYear);
    
    // Update quarterly stats
    this.updateQuarterlyKPIs();
    
    // Apply filters and update all tabs
    this.applyQuarterlyFilters();
    
    // Update the header display
    this.updateHeaderDisplay();
  }

  /**
   * Select a quarter
   */
  selectQuarter(quarter: 'Q1' | 'Q2' | 'Q3' | 'Q4'): void {
    const previousQuarter = this.selectedQuarter;
    this.selectedQuarter = quarter;
    
    console.log('Quarter selected:', quarter, this.selectedYear);
    console.log('Previous quarter:', previousQuarter);
    
    // Update quarterly stats immediately
    this.updateQuarterlyKPIs();
    
    // Apply filters and update all tabs
    this.applyQuarterlyFilters();
    
    // Update the header display
    this.updateHeaderDisplay();
  }

  /**
   * Toggle quarter comparison
   */
  onQuarterComparisonToggle(): void {
    if (!this.enableQuarterComparison) {
      this.compareWithPreviousQuarter = false;
      this.compareWithSameQuarterLastYear = false;
    }
  }

  /**
   * Apply quick period shortcuts
   */
  applyQuickPeriod(periodType: string): void {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentMonth = now.getMonth();
    
    console.log('🚀 Applying quick period:', periodType);
    
    switch (periodType) {
      case 'currentQuarter':
        // Select the ACTUAL current quarter (not the default previous quarter)
        const currentQuarterNum = Math.floor(currentMonth / 3) + 1;
        this.selectedYear = currentYear;
        this.selectedQuarter = `Q${currentQuarterNum}` as 'Q1' | 'Q2' | 'Q3' | 'Q4';
        break;
        
      case 'lastQuarter':
        // This now selects the quarter before the previous quarter (2 quarters back)
        if (currentMonth < 3) {
          // If current is Q1, two quarters back is Q3 of previous year
          this.selectedYear = currentYear - 1;
          this.selectedQuarter = 'Q3';
        } else if (currentMonth < 6) {
          // If current is Q2, two quarters back is Q4 of previous year
          this.selectedYear = currentYear - 1;
          this.selectedQuarter = 'Q4';
        } else {
          // For Q3 and Q4, just subtract 2 quarters
          this.selectedYear = currentYear;
          const lastQuarterNum = Math.floor(currentMonth / 3) - 1;
          this.selectedQuarter = `Q${lastQuarterNum}` as 'Q1' | 'Q2' | 'Q3' | 'Q4';
        }
        break;
        
      case 'defaultQuarter':
        // Reset to the default (previous quarter)
        this.initializeAvailableYears();
        return; // Exit early since initializeAvailableYears sets the values
        
      case 'ytd':
        this.selectedYear = currentYear;
        this.selectedQuarter = null; // Show all quarters of current year
        this.startDate = new Date(currentYear, 0, 1);
        this.endDate = now;
        break;
        
      case 'lastYear':
        this.selectedYear = currentYear - 1;
        this.selectedQuarter = null; // Show all quarters of last year
        this.startDate = new Date(currentYear - 1, 0, 1);
        this.endDate = new Date(currentYear - 1, 11, 31);
        break;
        
      case 'allTime':
        this.selectedYear = currentYear;
        this.selectedQuarter = null;
        this.startDate = null;
        this.endDate = null;
        break;
    }
    
    // Update KPIs and apply filters
    this.updateQuarterlyKPIs();
    this.applyQuarterlyFilters();
    
    console.log('✅ Quick period applied:', {
      periodType,
      quarter: this.selectedQuarter,
      year: this.selectedYear,
      dateRange: this.startDate && this.endDate ? 
        `${this.startDate.toDateString()} - ${this.endDate.toDateString()}` : 'Not set'
    });
  }

  /**
   * Navigate between quarters
   */
  navigateQuarter(direction: 'prev' | 'next'): void {
    if (!this.selectedQuarter) return;
    
    const quarters: Array<'Q1' | 'Q2' | 'Q3' | 'Q4'> = ['Q1', 'Q2', 'Q3', 'Q4'];
    const currentIndex = quarters.indexOf(this.selectedQuarter);
    
    if (direction === 'prev') {
      if (currentIndex === 0) {
        this.selectedQuarter = 'Q4';
        this.selectedYear--;
      } else {
        this.selectedQuarter = quarters[currentIndex - 1];
      }
    } else {
      if (currentIndex === 3) {
        this.selectedQuarter = 'Q1';
        this.selectedYear++;
      } else {
        this.selectedQuarter = quarters[currentIndex + 1];
      }
    }
    
    this.updateQuarterlyKPIs();
    this.applyQuarterlyFilters();
  }

  /**
   * Check if can navigate to previous quarter
   */
  canNavigateToPrevQuarter(): boolean {
    return this.selectedYear > 2020 || (this.selectedYear === 2020 && this.selectedQuarter !== 'Q1');
  }

  /**
   * Check if can navigate to next quarter
   */
  canNavigateToNextQuarter(): boolean {
    const now = new Date();
    const currentYear = now.getFullYear();
    const currentQuarter = `Q${Math.floor(now.getMonth() / 3) + 1}`;
    
    return this.selectedYear < currentYear || 
           (this.selectedYear === currentYear && this.selectedQuarter < currentQuarter);
  }

  /**
   * Apply quarterly filters to data
   */
  applyQuarterlyFilters(): void {
    console.log('🔄 Applying quarterly filters:', {
      quarter: this.selectedQuarter,
      year: this.selectedYear,
      comparison: this.enableQuarterComparison
    });
    
    if (this.selectedQuarter && this.selectedYear) {
      // Convert quarter to date range
      const quarterStartMonth = (parseInt(this.selectedQuarter.substring(1)) - 1) * 3;
      this.startDate = new Date(this.selectedYear, quarterStartMonth, 1);
      this.endDate = new Date(this.selectedYear, quarterStartMonth + 3, 0);
      
      // Update filters
      this.currentFilters = {
        ...this.currentFilters,
        startDate: this.startDate,
        endDate: this.endDate,
        timePeriodType: 'quarterly' as any,
        year: this.selectedYear,
        quarter: this.selectedQuarter as any
      };
    } else {
      // Clear date filters if no quarter selected
      this.startDate = null;
      this.endDate = null;
      
      // Remove quarterly filters but keep others
      const { startDate, endDate, timePeriodType, year, quarter, ...otherFilters } = this.currentFilters;
      this.currentFilters = otherFilters;
    }
    
    // Show loading state
    this.loading = true;
    
    // Load data with quarterly filters
    this.loadReportData();
    
    // Force update all tabs after data loads
    setTimeout(() => {
      this.forceUpdateAllTabs();
    }, 1500);
  }

  /**
   * Force update all tabs with filtered data
   */
  private forceUpdateAllTabs(): void {
    console.log('🔄 Force updating all tabs with quarterly filtered data');
    
    // Update each tab's data
    Object.values(this.tabMapping).forEach(tabName => {
      this.updateTabSpecificData(tabName);
    });
    
    // Update chart data
    this.prepareChartData();
    this.prepareTrendChartData();
    this.prepareProjectChartData();
    
    // Trigger change detection for components
    this.triggerComponentUpdates();
    
    console.log('✅ All tabs updated with quarterly filtered data');
  }

  /**
   * Trigger component updates
   */
  private triggerComponentUpdates(): void {
    // This would trigger change detection in child components
    // In a real implementation, you might use EventEmitter or Subject
    
    // Update last updated time to force template updates
    this.lastUpdated = new Date();
    
    // Log the current state for debugging
    console.log('🔄 Component updates triggered:', {
      filteredProjects: this.filteredData?.projects?.length || 0,
      filteredActivities: this.filteredData?.activities?.length || 0,
      quarterlyPeriod: this.filteredData?.quarterlyPeriod || 'All Time',
      currentTab: this.tabMapping[this.selectedTabIndex as keyof typeof this.tabMapping]
    });
  }

  /**
   * Update header display with current filter status
   */
  private updateHeaderDisplay(): void {
    // This method can be used to update any header-specific displays
    console.log('📅 Header updated with:', {
      quarter: this.selectedQuarter,
      year: this.selectedYear,
      comparison: this.enableQuarterComparison
    });
  }

  /**
   * Update performance chart based on selected metric
   */
  updatePerformanceChart(): void {
    console.log('Updating performance chart for metric:', this.selectedMetric);
    // This would update the chart with the selected metric data
  }

  /**
   * Reset all filters including quarterly
   */
  resetAllFilters(): void {
    console.log('🔄 Resetting all filters');
    
    this.selectedQuarter = null;
    this.selectedYear = new Date().getFullYear();
    this.enableQuarterComparison = false;
    this.compareWithPreviousQuarter = false;
    this.compareWithSameQuarterLastYear = false;
    this.startDate = null;
    this.endDate = null;
    this.currentFilters = {};
    this.selectedProjectGroup = null;
    this.selectedProject = null;
    
    // Clear quarterly KPIs
    this.quarterlyKPIs = null;
    
    // Reload data without filters
    this.loadReportData();
    
    // Force update all tabs
    setTimeout(() => {
      this.forceUpdateAllTabs();
    }, 1000);
    
    console.log('✅ All filters reset');
  }

  /**
   * Export quarterly report
   */
  exportQuarterlyReport(): void {
    if (!this.selectedQuarter || !this.selectedYear) {
      alert('Please select a quarter to export quarterly report');
      return;
    }
    
    const quarterlyData = {
      period: `${this.selectedQuarter} ${this.selectedYear}`,
      kpis: this.quarterlyKPIs,
      reportData: this.filteredData,
      filters: this.currentFilters,
      comparison: {
        enabled: this.enableQuarterComparison,
        previousQuarter: this.compareWithPreviousQuarter,
        sameQuarterLastYear: this.compareWithSameQuarterLastYear
      },
      summary: {
        totalProjects: this.filteredData?.projects?.length || 0,
        totalActivities: this.filteredData?.activities?.length || 0,
        totalBudget: this.filteredData?.totalBudget || 0,
        quarterlyPeriod: this.filteredData?.quarterlyPeriod || 'Unknown'
      },
      exportDate: new Date().toISOString(),
      reportTitle: `UNDP Afghanistan Quarterly Report - ${this.selectedQuarter} ${this.selectedYear}`
    };

    const dataStr = JSON.stringify(quarterlyData, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `undp-quarterly-report-${this.selectedQuarter}-${this.selectedYear}.json`;
    link.click();
    
    URL.revokeObjectURL(url);
    
    console.log('📊 Quarterly report exported:', quarterlyData);
  }

  /**
   * Set up optimized filter change handling with debouncing and caching
   */
  private setupOptimizedFilterHandling(): void {
    console.log('🚀 Setting up optimized filter handling');
    
    // Set up debounced filter changes
    this.filterChangeSubject
      .pipe(
        takeUntil(this.destroy$),
        debounceTime(this.API_DEBOUNCE_TIME),
        distinctUntilChanged((prev, curr) => {
          // Deep comparison to prevent unnecessary API calls
          return JSON.stringify(prev) === JSON.stringify(curr);
        }),
        switchMap(filters => {
          // Prevent multiple simultaneous API calls
          if (this.isLoadingData) {
            console.log('⏳ API call already in progress, skipping duplicate request');
            return EMPTY;
          }
          
          // Check cache first
          const cacheKey = this.generateCacheKey(filters);
          const cachedData = this.getCachedData(cacheKey);
          
          if (cachedData) {
            console.log('💾 Using cached data for filters:', filters);
            return of(cachedData);
          }
          
          // Make optimized API call based on filter complexity
          console.log('🌐 Making optimized API call with filters:', filters);
          this.isLoadingData = true;
          this.lastApiCall = Date.now();
          
          // Choose the most efficient API endpoint based on current filters
          const apiCall = this.selectOptimalApiEndpoint(filters);
          
          return apiCall.pipe(
            map(data => ({ data, cacheKey, fromCache: false })),
            finalize(() => {
              this.isLoadingData = false;
            })
          );
        })
      )
      .subscribe({
        next: (result) => {
          if (result.fromCache) {
            this.processApiResponse(result.data, true);
          } else {
            // Cache the new data
            this.setCachedData(result.cacheKey, result.data);
            this.processApiResponse(result.data, false);
          }
        },
        error: (err) => {
          console.error('❌ Optimized filter API error:', err);
          this.handleApiError(err);
          this.isLoadingData = false;
        }
      });
  }

  /**
   * Transform frontend filters to backend DataFilterModel format
   */
  private transformFiltersForBackend(filters: ReportFilter): any {
    const backendFilters: any = { ...filters };
    
    // Map frontend filter names to backend field names
    if (filters.projects?.length) {
      backendFilters.ProjIds = filters.projects.map(p => parseInt(p, 10)).filter(id => !isNaN(id));
      delete backendFilters.projects; // Remove frontend field
    }
    
    if (filters.projectGroups?.length) {
      backendFilters.ProjectGroups = filters.projectGroups;
      delete backendFilters.projectGroups; // Remove frontend field
    }
    
    if (filters.categories?.length) {
      backendFilters.CatIds = filters.categories.map(c => parseInt(c, 10)).filter(id => !isNaN(id));
      delete backendFilters.categories; // Remove frontend field
    }
    
    if (filters.interventions?.length) {
      backendFilters.ProfIds = filters.interventions.map(i => parseInt(i, 10)).filter(id => !isNaN(id));
      delete backendFilters.interventions; // Remove frontend field
    }
    
    if (filters.organizations?.length) {
      backendFilters.OrgIds = filters.organizations.map(o => parseInt(o, 10)).filter(id => !isNaN(id));
      delete backendFilters.organizations; // Remove frontend field
    }
    
    if (filters.provinces?.length) {
      backendFilters.ProvinceIds = filters.provinces.map(p => parseInt(p, 10)).filter(id => !isNaN(id));
      delete backendFilters.provinces; // Remove frontend field
    }
    
    if (filters.districts?.length) {
      backendFilters.DistrictIds = filters.districts.map(d => parseInt(d, 10)).filter(id => !isNaN(id));
      delete backendFilters.districts; // Remove frontend field
    }
    
    // Map activity status to backend format
    if (filters.activityStatus?.length) {
      backendFilters.ActivityStatus = filters.activityStatus.map(status => {
        switch (status) {
          case 'ongoing': return 0;
          case 'completed': return 1;
          case 'archived': return 2;
          case 'cancelled': return 3;
          default: return 0;
        }
      });
      delete backendFilters.activityStatus; // Remove frontend field
    }
    
    // Handle date filters
    if (filters.startDate) {
      // Extract year and period from date if needed
      const startYear = filters.startDate.getFullYear();
      const startMonth = filters.startDate.getMonth() + 1;
      const startQuarter = Math.ceil(startMonth / 3);
      
      if (!backendFilters.Year) {
        backendFilters.Year = startYear;
      }
      if (!backendFilters.Period) {
        backendFilters.Period = startQuarter;
      }
    }
    
    if (filters.endDate) {
      const endYear = filters.endDate.getFullYear();
      const endMonth = filters.endDate.getMonth() + 1;
      const endQuarter = Math.ceil(endMonth / 3);
      
      backendFilters.YearEnd = endYear;
      backendFilters.PeriodEnd = endQuarter;
    }
    
    // Remove frontend-specific fields that backend doesn't understand
    const frontendOnlyFields = [
      'startDate', 'endDate', 'gender', 'ageGroup', 'vulnerabilityGroup',
      'searchTerm', 'timePeriodType', 'year', 'quarter', 'month',
      'status', 'minBudget', 'maxBudget', 'minProgress', 'maxProgress',
      'projectStatus', 'outputs', 'regions', 'communities', 'timePeriod'
    ];
    
    frontendOnlyFields.forEach(field => {
      delete backendFilters[field];
    });
    
    console.log('🔄 Transformed filters for backend:', {
      original: filters,
      transformed: backendFilters
    });
    
    return backendFilters;
  }

  /**
   * Select the most optimal API endpoint based on current filters
   * 
   * SIMPLIFIED APPROACH: Using only hierarchical endpoint for all reports
   * - Consistent data structure across all scenarios
   * - Single endpoint to maintain and optimize
   * - Server-side filtering handled by hierarchical endpoint
   * 
   * Benefits:
   * - Simplified caching (single data format)
   * - Consistent data processing pipeline
   * - Easier to maintain and debug
   * - Single source of truth for all dashboard data
   */
  private selectOptimalApiEndpoint(filters: ReportFilter): Observable<any> {
    console.log('🎯 Using hierarchical endpoint for all reports:', {
      filters,
      strategy: 'Single endpoint approach',
      endpoint: 'getHierarchicalReport'
    });

    // Transform filters to backend format
    const backendFilters = this.transformFiltersForBackend(filters);

    // Always use hierarchical report - handles all filtering server-side
    return this.reportsService.getHierarchicalReport(backendFilters);
  }

  /**
   * Generate cache key for filters
   */
  private generateCacheKey(filters: ReportFilter): string {
    // Create a stable cache key from filters
    const sortedFilters = Object.keys(filters)
      .sort()
      .reduce((result, key) => {
        result[key] = filters[key];
        return result;
      }, {} as any);
    
    return btoa(JSON.stringify(sortedFilters));
  }

  /**
   * Get cached data if available and not expired
   */
  private getCachedData(cacheKey: string): any {
    const cached = this.dataCache.get(cacheKey);
    if (!cached) return null;
    
    const isExpired = Date.now() - cached.timestamp > this.CACHE_DURATION;
    if (isExpired) {
      this.dataCache.delete(cacheKey);
      return null;
    }
    
    return { data: cached.data, fromCache: true };
  }

  /**
   * Set cached data with timestamp
   */
  private setCachedData(cacheKey: string, data: any): void {
    // Limit cache size to prevent memory issues
    if (this.dataCache.size > 10) {
      const firstKey = this.dataCache.keys().next().value;
      this.dataCache.delete(firstKey);
    }
    
    this.dataCache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Process API response and update component data
   */
  private processApiResponse(hierarchicalData: any[], fromCache: boolean): void {
    console.log('🔄 processApiResponse called:', {
      dataLength: hierarchicalData.length,
      fromCache,
      hasData: hierarchicalData && hierarchicalData.length > 0,
      hasFilters: Object.keys(this.currentFilters).some(key => {
        const value = this.currentFilters[key];
        return value && (Array.isArray(value) ? value.length > 0 : true);
      })
    });
    
    if (hierarchicalData && hierarchicalData.length > 0) {
      // Store hierarchical data
      this.hierarchicalData = hierarchicalData;
      
      // Extract full report data from hierarchical structure
      console.log('🔄 Extracting full report from hierarchical data...');
      this.fullReportData = this.extractFullReportFromHierarchical(hierarchicalData);
      
      // Process the data into the required format
      console.log('🔄 Processing report data...');
      this.processReportData(this.fullReportData);
      
      // Extract project groups and projects for current filtered data
      const currentProjectGroups = this.extractProjectGroupsFromHierarchy(hierarchicalData);
      const currentProjects = this.extractProjectsFromHierarchy(hierarchicalData);
      
      // Check if this is an unfiltered response (no active filters)
      const hasActiveFilters = this.currentFilters.projectGroups?.length > 0 || 
                               this.currentFilters.projects?.length > 0 ||
                               this.currentFilters.categories?.length > 0 ||
                               this.currentFilters.organizations?.length > 0;
      
      if (!hasActiveFilters) {
        // No filters active - this is the master data, update master lists
        console.log('🔄 No active filters - updating master lists');
        this.allProjectGroups = [...currentProjectGroups];
        this.allProjects = [...currentProjects];
        this.projectGroups = [...currentProjectGroups];
        this.projects = [...currentProjects];
      } else {
        // Filters are active - keep master lists unchanged, but update current working data
        console.log('🔄 Active filters detected - preserving master lists for dropdowns');
        console.log('🔄 Master project groups count:', this.allProjectGroups.length);
        console.log('🔄 Filtered project groups count:', currentProjectGroups.length);
        
        // Use master lists for dropdowns (so all options remain available)
        this.projectGroups = [...this.allProjectGroups];
        this.projects = [...this.allProjects];
        
        // Log for debugging
        console.log('✅ Dropdown options preserved:', {
          availableProjectGroups: this.projectGroups.map(pg => pg.name),
          selectedProjectGroup: this.selectedProjectGroup?.name,
          filteredDataProjectGroups: currentProjectGroups.map(pg => pg.name)
        });
      }
      
      console.log('✅ processApiResponse completed:', {
        fullReportData: !!this.fullReportData,
        fullReportCategories: this.fullReportData?.categories?.length || 0,
        filteredData: !!this.filteredData,
        filteredDataCategories: this.filteredData?.categories?.length || 0,
        projectGroupsInDropdown: this.projectGroups.length,
        projectsInDropdown: this.projects.length,
        masterProjectGroups: this.allProjectGroups.length,
        masterProjects: this.allProjects.length,
        hasActiveFilters
      });
      
      // Update UI
      this.lastUpdated = new Date();
      this.loading = false;
      this.error = null;
      
      // Trigger change detection
      this.cdr.detectChanges();
      
      // Update component-specific data based on active tab
      const activeTabName = Object.keys(this.tabMapping)[this.selectedTabIndex] || 'overview';
      this.updateTabSpecificData(activeTabName);
      
      // Trigger component updates for all tabs
      this.triggerComponentUpdates();
    } else {
      console.log('❌ No data received from API');
      this.loading = false;
      this.error = 'No data available';
      this.cdr.detectChanges();
    }
  }

  /**
   * Handle API errors with fallback
   */
  private handleApiError(err: any): void {
    this.error = `Failed to load dashboard data: ${err.message || 'Unknown error'}`;
    
    // Fallback to empty data structures
    this.fullReportData = this.createDefaultFullReportData();
    this.filteredData = this.createDefaultFilteredData();
    this.hierarchicalData = [];
    this.projectGroups = [];
    this.projects = [];
    
    this.loading = false;
    this.cdr.detectChanges();
  }

  /**
   * Clear cache when needed
   */
  private clearCache(): void {
    console.log('🗑️ Clearing data cache');
    this.dataCache.clear();
  }

  /**
   * Handle filter changes from the filter component (OPTIMIZED)
   */
  onFilterChange(filters: ReportFilter): void {
    if (this.isInitializing) {
      console.log('⏸️ Skipping filter change during initialization');
      return;
    }
    
    console.log('🔍 Optimized filter change received:', filters);
    
    // Update current filters
    this.currentFilters = { ...this.currentFilters, ...filters };
    
    // Use optimized filter handling instead of immediate API call
    this.filterChangeSubject.next(this.currentFilters);
    
    // Apply local filtering immediately for better UX
    this.applyProjectFiltering();
  }

  /**
   * Handle filter changes from category dashboard (OPTIMIZED)
   */
  onCategoryFilterChange(newFilters: ReportFilter): void {
    if (this.isInitializing) {
      console.log('⏸️ Skipping category filter change during initialization');
      return;
    }
    
    console.log('🔄 Optimized category filter change received:', newFilters);
    
    // Update current filters with the new filters
    this.currentFilters = {
      ...this.currentFilters,
      ...newFilters
    };
    
    console.log('🔄 Updated current filters:', this.currentFilters);
    
    // Use optimized filter handling
    this.filterChangeSubject.next(this.currentFilters);
  }

  /**
   * Make immediate API call bypassing debouncing and caching for debugging
   */
  private makeImmediateApiCall(filters: ReportFilter): void {
    console.log('🚀 Making IMMEDIATE API call with filters:', filters);
    
    // Transform filters to backend format
    const backendFilters = this.transformFiltersForBackend(filters);
    console.log('🔄 Transformed backend filters:', backendFilters);
    
    // Clear any cache for this request
    const cacheKey = this.generateCacheKey(filters);
    this.dataCache.delete(cacheKey);
    
    // Make direct API call without debouncing
    this.loading = true;
    this.reportsService.getHierarchicalReport(backendFilters)
      .pipe(
        takeUntil(this.destroy$),
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe({
        next: (data) => {
          console.log('✅ IMMEDIATE API call successful, data received:', data?.length || 0, 'groups');
          this.processApiResponse(data || [], false);
        },
        error: (err) => {
          console.error('❌ IMMEDIATE API call failed:', err);
          this.handleApiError(err);
        }
      });
  }

  /**
   * Debug method to test filtering
   */
  debugProjectGroupFiltering(): void {
    console.log('🐛 DEBUG: Testing project group filtering');
    console.log('🐛 Available project groups:', this.projectGroups);
    console.log('🐛 Current filters:', this.currentFilters);
    console.log('🐛 Is initializing:', this.isInitializing);
    console.log('🐛 Is loading data:', this.isLoadingData);
    
    // Test with first available project group
    if (this.projectGroups.length > 0) {
      const testGroup = this.projectGroups[0];
      console.log('🐛 Testing with group:', testGroup);
      this.selectProjectGroup(testGroup);
    } else {
      console.log('🐛 No project groups available for testing');
    }
  }
}

