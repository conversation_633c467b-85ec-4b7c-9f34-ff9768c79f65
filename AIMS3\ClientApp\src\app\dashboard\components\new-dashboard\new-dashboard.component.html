﻿<!-- Dashboard Header -->
<div class="card mb-5">
    <div class="card-body py-3">
        <div class="d-flex flex-column flex-md-row align-items-md-center justify-content-md-between">
            <h1 class="fw-bold text-dark mb-0">Intervention Dashboard</h1>
            <div class="d-flex align-items-center">
                <div *ngIf="projectStatusFilter" class="badge badge-light-primary me-2">
                    {{projectStatusFilter === 'ongoing' ? 'Ongoing Projects' : 'Completed Projects'}}
                </div>
                <button *ngIf="selectedIntervention" 
                        class="btn btn-sm btn-light-primary me-3"
                        (click)="resetDashboard()">
                    <i class="bi bi-arrow-counterclockwise me-1"></i>
                    Reset Dashboard
                </button>
                <div class="fs-7 fw-semibold text-gray-500 me-2">Last updated:</div>
                <div class="fs-7 fw-semibold text-dark">April 14, 2025</div>
            </div>
        </div>
    </div>
</div>

<!-- Filters Bar -->
<div class="card mb-5">
    <div class="card-body py-4">
        <div class="d-flex flex-wrap gap-3">
            <!-- Project Group Filter -->
            <div class="w-225px">
                <label class="form-label fw-bold fs-7 text-gray-700">Project Group</label>
                <select class="form-select form-select-sm form-select-solid">
                    <option value="">All Project Groups</option>
                    <option value="1">Rural Development</option>
                    <option value="2">Healthcare Initiatives</option>
                    <option value="3">Education Programs</option>
                    <option value="4">Infrastructure Projects</option>
                </select>
            </div>
            
            <!-- Project Status Filter -->
            <div class="w-225px">
                <label class="form-label fw-bold fs-7 text-gray-700">Project Status</label>
                <select class="form-select form-select-sm form-select-solid" 
                        [(ngModel)]="projectStatusFilter" 
                        (change)="filterInterventions()">
                    <option value="">All Projects</option>
                    <option value="ongoing">Ongoing Projects</option>
                    <option value="completed">Completed Projects</option>
                </select>
            </div>
            
            <!-- Category Filter -->
            <div class="w-225px">
                <label class="form-label fw-bold fs-7 text-gray-700">Category</label>
                <select class="form-select form-select-sm form-select-solid">
                    <option value="">All Categories</option>
                    <!-- Output 1 -->
                    <optgroup label="Output 1">
                        <option value="1.1">1.1 Infrastructure</option>
                        <option value="1.2">1.2 Health</option>
                        <option value="1.3">1.3 Education</option>
                        <option value="1.4">1.4 Energy</option>
                    </optgroup>
                    <!-- Output 2 -->
                    <optgroup label="Output 2">
                        <option value="2.1">2.1 UCT</option>
                        <option value="2.2">2.2 CFW</option>
                        <option value="2.3">2.3 Livelihoods</option>
                        <option value="2.4">2.4 Cross-border trade</option>
                        <option value="2.5">2.5 Finance and digital solution</option>
                        <option value="2.6">2.6 ExhibitionsAndSales</option>
                    </optgroup>
                    <!-- Output 3 -->
                    <optgroup label="Output 3">
                        <option value="3.1">3.1 Agriculture</option>
                        <option value="3.2">3.2 DRR</option>
                        <option value="3.3">3.3 Climate-smart water security</option>
                        <option value="3.4">3.4 Ecosystem</option>
                    </optgroup>
                    <!-- Output 4 -->
                    <optgroup label="Output 4">
                        <option value="4.1">4.1 Social cohesion</option>
                        <option value="4.2">4.2 Gender</option>
                        <option value="4.3">4.3 Justice</option>
                        <option value="4.4">4.4 Community planning</option>
                        <option value="4.5">4.5 Regional strategy</option>
                        <option value="4.9">4.9 GRM</option>
                    </optgroup>
                </select>
            </div>
            
            <!-- Region Filter -->
            <div class="w-225px">
                <label class="form-label fw-bold fs-7 text-gray-700">Region</label>
                <select class="form-select form-select-sm form-select-solid">
                    <option value="">All Regions</option>
                    <option value="1">Northern Region</option>
                    <option value="2">Southern Region</option>
                    <option value="3">Eastern Region</option>
                    <option value="4">Western Region</option>
                    <option value="5">Central Region</option>
                </select>
            </div>
            
            <!-- Date Range -->
            <div class="w-225px">
                <label class="form-label fw-bold fs-7 text-gray-700">Time Period</label>
                <div class="d-flex gap-2">
                    <input type="month" class="form-control form-control-sm form-control-solid" value="2025-01">
                    <input type="month" class="form-control form-control-sm form-control-solid" value="2025-04">
                </div>
            </div>
            
            <!-- Apply Button -->
            <div class="align-self-end">
                <button class="btn btn-sm btn-primary px-4 py-2">
                    <i class="bi bi-funnel-fill me-1"></i>Apply Filters
                </button>
            </div>
        </div>
    </div>
</div>

<!-- KPI Cards Row -->
<div class="row g-5 g-xl-8 mb-5">
    <!-- Total Interventions -->
    <div class="col-xl-3 col-md-6">
        <div class="card card-xl-stretch mb-xl-8 bg-light-primary" 
            (click)="openInterventionsModal()" 
            style="cursor: pointer;"
            data-bs-toggle="modal" 
            data-bs-target="#interventionsModal">
            <div class="card-body p-5">
                <div class="d-flex flex-stack">
                    <div class="d-flex align-items-center">
                        <div class="symbol symbol-50px me-4">
                            <div class="symbol-label bg-white">
                                <i class="bi bi-collection-fill text-primary fs-2x"></i>
                            </div>
                        </div>
                        <div>
                            <div class="fs-4 text-dark fw-bold">{{interventions?.length || 0}}</div>
                            <div class="fs-7 text-gray-600 fw-semibold mt-1">Categories</div>
                        </div>
                    </div>
                    <div class="bg-light-success px-3 py-2 rounded-2">
                        <div class="fs-8 fw-semibold text-success">+12%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Activities Count -->
    <div class="col-xl-3 col-md-6">
        <div class="card card-xl-stretch mb-xl-8 bg-light-info">
            <div class="card-body p-5">
                <div class="d-flex flex-stack">
                    <div class="d-flex align-items-center">
                        <div class="symbol symbol-50px me-4">
                            <div class="symbol-label bg-white">
                                <i class="bi bi-list-check text-info fs-2x"></i>
                            </div>
                        </div>
                        <div>
                            <div class="fs-4 text-dark fw-bold">187</div>
                            <div class="fs-7 text-gray-600 fw-semibold mt-1">Activity Instances</div>
                        </div>
                    </div>
                    <div class="d-flex flex-column">
                        <div class="d-flex justify-content-end">
                            <span class="badge badge-primary badge-circle">78</span>
                            <span class="text-gray-600 fs-8 ms-1">Active</span>
                        </div>
                        <div class="d-flex justify-content-end mt-1">
                            <span class="badge badge-success badge-circle">109</span>
                            <span class="text-gray-600 fs-8 ms-1">Completed</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Regions Coverage -->
    <div class="col-xl-3 col-md-6">
        <div class="card card-xl-stretch mb-xl-8 bg-light-warning">
            <div class="card-body p-5">
                <div class="d-flex flex-stack">
                    <div class="d-flex align-items-center">
                        <div class="symbol symbol-50px me-4">
                            <div class="symbol-label bg-white">
                                <i class="bi bi-geo-alt-fill text-warning fs-2x"></i>
                            </div>
                        </div>
                        <div>
                            <div class="fs-4 text-dark fw-bold">5/5</div>
                            <div class="fs-7 text-gray-600 fw-semibold mt-1">Regions Covered</div>
                        </div>
                    </div>
                    <div class="bg-light-warning px-3 py-2 rounded-2">
                        <div class="fs-8 fw-semibold text-warning">100%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Beneficiaries -->
    <div class="col-xl-3 col-md-6">
        <div class="card card-xl-stretch mb-xl-8 bg-light-success">
            <div class="card-body p-5">
                <div class="d-flex flex-stack">
                    <div class="d-flex align-items-center">
                        <div class="symbol symbol-50px me-4">
                            <div class="symbol-label bg-white">
                                <i class="bi bi-people-fill text-success fs-2x"></i>
                            </div>
                        </div>
                        <div>
                            <div class="fs-4 text-dark fw-bold">125,780</div>
                            <div class="fs-7 text-gray-600 fw-semibold mt-1">Total Beneficiaries</div>
                        </div>
                    </div>
                    <div class="bg-light-success px-3 py-2 rounded-2">
                        <div class="fs-8 fw-semibold text-success">+18%</div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Interventions Tabs Section - Only shown when dashboard is loaded -->
<div *ngIf="selectedCategory" class="card mb-5">
    <div class="card-header border-0">
        <h3 class="card-title align-items-start flex-column">
            <span class="card-label fw-bold fs-3 mb-1">{{selectedCategory.name}} Interventions</span>
            <span class="text-muted fw-semibold fs-7">Showing {{categoryInterventions.length}} intervention(s)</span>
        </h3>
        <div class="card-toolbar">
            <button class="btn btn-sm btn-light-primary" [class.active]="activeInterventionTab === -1" (click)="showAllInterventionsData()">
                <i class="bi bi-grid-3x3-gap-fill me-1"></i>Show All
            </button>
        </div>
    </div>
    <div class="card-body pt-0">
        <!-- Intervention Tabs -->
        <div class="d-flex flex-nowrap overflow-auto pb-3">
            <div *ngFor="let intervention of categoryInterventions; let i = index" 
                 class="btn btn-light-primary btn-active-primary btn-md me-2 mb-2"
                 [class.active]="activeInterventionTab === i"
                 (click)="setActiveInterventionTab(i)">
                <div class="d-flex flex-column align-items-center">
                    <span class="fs-7 fw-bold">{{intervention.name}}</span>
                    <div class="d-flex align-items-center mt-2">
                        <div class="symbol symbol-20px me-1">
                            <span class="symbol-label bg-light-info rounded-circle">
                                <i class="bi bi-bar-chart-fill text-info fs-8"></i>
                            </span>
                        </div>
                        <span class="fs-8">Progress: {{intervention.progress}}%</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Intervention Details -->
        <div *ngIf="activeInterventionTab >= 0 && activeInterventionTab < categoryInterventions.length" class="pt-3 border-top">
            <div class="d-flex flex-row flex-wrap justify-content-between">
                <!-- Intervention Image -->
                <div class="d-flex flex-column align-items-center me-5 mb-3">
                    <div class="symbol symbol-100px mb-3">
                        <div class="symbol-label bg-light-primary">
                            <i class="bi bi-building-fill text-primary fs-1"></i>
                        </div>
                    </div>
                    <span class="fs-6 fw-bold text-gray-800">{{getInterventionByIndex(activeInterventionTab)?.name}}</span>
                </div>
                
                <!-- Intervention Info -->
                <div class="flex-grow-1 mb-3">
                    <h4 class="fs-4 fw-bold mb-3">{{getInterventionByIndex(activeInterventionTab)?.description}}</h4>
                    
                    <div class="d-flex flex-wrap gap-3">
                        <div class="border border-gray-300 rounded px-3 py-2">
                            <span class="text-muted fs-8">Region</span>
                            <div class="fw-semibold">{{getInterventionByIndex(activeInterventionTab)?.region}}</div>
                        </div>
                        
                        <div class="border border-gray-300 rounded px-3 py-2">
                            <span class="text-muted fs-8">Start Date</span>
                            <div class="fw-semibold">{{getInterventionByIndex(activeInterventionTab)?.startDate | date}}</div>
                        </div>
                        
                        <div class="border border-gray-300 rounded px-3 py-2">
                            <span class="text-muted fs-8">End Date</span>
                            <div class="fw-semibold">{{getInterventionByIndex(activeInterventionTab)?.endDate | date}}</div>
                        </div>
                        
                        <div class="border border-gray-300 rounded px-3 py-2">
                            <span class="text-muted fs-8">Beneficiaries</span>
                            <div class="fw-semibold">{{getInterventionByIndex(activeInterventionTab)?.beneficiaries | number}}</div>
                        </div>
                        
                        <div class="border border-gray-300 rounded px-3 py-2">
                            <span class="text-muted fs-8">Activities</span>
                            <div class="fw-semibold">{{getInterventionByIndex(activeInterventionTab)?.activities}}</div>
                        </div>
                    </div>
                </div>
                
                <!-- Progress -->
                <div class="d-flex flex-column align-items-center mb-3">
                    <div class="position-relative d-flex flex-center mb-1">
                        <input type="text" class="form-control form-control-solid border-0 h-100px w-100px" 
                               value="{{getInterventionByIndex(activeInterventionTab)?.progress}}" disabled 
                               style="font-size: 24px; text-align: center;">
                        <div class="position-absolute translate-middle-y top-50 end-0 me-4 text-muted fs-2">%</div>
                    </div>
                    <span class="fs-6 fw-semibold text-muted">Completion</span>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Geographic Distribution Row -->
<div class="row g-5 g-xl-8 mb-5">
    <!-- Afghanistan Map and Projects by Region - col-8 -->
    <div class="col-lg-8">
        <div class="card card-xl-stretch mb-xl-8" id="mapChartCard">
            <div class="card-header border-0 pt-5">
                <h3 class="card-title align-items-start flex-column">
                    <span class="card-label fw-bold fs-3 mb-1">Geographic Distribution - Afghanistan</span>
                    <span class="text-muted fw-semibold fs-7">Projects by region with provincial borders</span>
                </h3>
                <div class="card-toolbar">
                    <button class="btn btn-sm btn-light-primary me-2" id="mapToggleMode" (click)="toggleMapMode()">
                        <i class="bi bi-bullseye me-1"></i>Show Bubble View
                    </button>
                    <button class="btn btn-sm btn-light-primary me-2" id="mapExtraZoomOut" (click)="extraZoomOut()">
                        <i class="bi bi-zoom-out me-1"></i>Zoom Out
                    </button>
                    <button class="btn btn-sm btn-icon btn-light-primary" id="mapToggleFullscreen" (click)="toggleMapFullscreen()">
                        <i class="bi bi-fullscreen"></i>
                    </button>
                </div>
            </div>
            <div class="card-body">
                <!-- Map chart container -->
                <div id="mapChart" style="width: 100%; height: 500px; background-color: #f5f5f5; border: 1px solid #ddd;"></div>
            </div>
        </div>
    </div>
    
    <!-- Cash Distribution by Outputs - col-4 -->
    <div class="col-lg-4">
        <div class="card card-xl-stretch mb-xl-8">
            <div class="card-header border-0 pt-5">
                <h3 class="card-title align-items-start flex-column">
                    <span class="card-label fw-bold fs-3 mb-1">Cash Distribution</span>
                    <span class="text-muted fw-semibold fs-7">By Outputs</span>
                </h3>
            </div>
            <div class="card-body py-3">
                <!-- Output 1 Cash -->
                <div class="d-flex flex-stack mb-5">
                    <div class="d-flex align-items-center me-2">
                        <div class="symbol symbol-50px me-3">
                            <div class="symbol-label bg-light-primary">
                                <i class="bi bi-building fs-1 text-primary"></i>
                            </div>
                        </div>
                        <div class="d-flex flex-column">
                            <span class="fs-6 fw-bold">Output 1</span>
                            <span class="fs-7 text-muted">Basic Services & Infrastructure</span>
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="fs-6 fw-bolder">${{getOutputCashDistribution(1) | number:'1.0-0'}}</span>
                    </div>
                </div>
                
                <!-- Output 2 Cash -->
                <div class="d-flex flex-stack mb-5">
                    <div class="d-flex align-items-center me-2">
                        <div class="symbol symbol-50px me-3">
                            <div class="symbol-label bg-light-info">
                                <i class="bi bi-cash-coin fs-1 text-info"></i>
                            </div>
                        </div>
                        <div class="d-flex flex-column">
                            <span class="fs-6 fw-bold">Output 2</span>
                            <span class="fs-7 text-muted">Livelihoods & Economic Recovery</span>
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="fs-6 fw-bolder">${{getOutputCashDistribution(2) | number:'1.0-0'}}</span>
                    </div>
                </div>
                
                <!-- Output 3 Cash -->
                <div class="d-flex flex-stack mb-5">
                    <div class="d-flex align-items-center me-2">
                        <div class="symbol symbol-50px me-3">
                            <div class="symbol-label bg-light-warning">
                                <i class="bi bi-tree fs-1 text-warning"></i>
                            </div>
                        </div>
                        <div class="d-flex flex-column">
                            <span class="fs-6 fw-bold">Output 3</span>
                            <span class="fs-7 text-muted">Climate Resilience & Environment</span>
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="fs-6 fw-bolder">${{getOutputCashDistribution(3) | number:'1.0-0'}}</span>
                    </div>
                </div>
                
                <!-- Output 4 Cash -->
                <div class="d-flex flex-stack mb-5">
                    <div class="d-flex align-items-center me-2">
                        <div class="symbol symbol-50px me-3">
                            <div class="symbol-label bg-light-danger">
                                <i class="bi bi-people fs-1 text-danger"></i>
                            </div>
                        </div>
                        <div class="d-flex flex-column">
                            <span class="fs-6 fw-bold">Output 4</span>
                            <span class="fs-7 text-muted">Social Cohesion & Community Resilience</span>
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="fs-6 fw-bolder">${{getOutputCashDistribution(4) | number:'1.0-0'}}</span>
                    </div>
                </div>
                
                <!-- Total Cash Distribution -->
                <div class="separator my-5"></div>
                <div class="d-flex flex-stack">
                    <div class="d-flex align-items-center me-2">
                        <div class="symbol symbol-50px me-3">
                            <div class="symbol-label bg-light-success">
                                <i class="bi bi-currency-dollar fs-1 text-success"></i>
                            </div>
                        </div>
                        <div class="d-flex flex-column">
                            <span class="fs-6 fw-bold">Total Cash Distributed</span>
                            <span class="fs-7 text-muted">All Outputs Combined</span>
                        </div>
                    </div>
                    <div class="text-end">
                        <span class="fs-5 fw-bolder text-success">${{getTotalCashDistribution() | number:'1.0-0'}}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Main Charts Row -->
<div class="row g-5 g-xl-8 mb-5">
    <!-- Intervention Performance Chart -->
    <div class="col-xl-8">
        <div class="card card-xl-stretch mb-xl-8">
            <div class="card-header border-0 pt-5">
                <h3 class="card-title align-items-start flex-column">
                    <span class="card-label fw-bold fs-3 mb-1">Category Performance</span>
                    <span class="text-muted fw-semibold fs-7">Activity progress by category</span>
                </h3>
                <div class="card-toolbar">
                    <ul class="nav">
                        <li class="nav-item">
                            <a class="nav-link btn btn-sm btn-color-muted btn-active btn-active-light-primary active fw-bold px-4 me-1" data-bs-toggle="tab" href="#kt_tab_pane_1">By Category</a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link btn btn-sm btn-color-muted btn-active btn-active-light-primary fw-bold px-4 me-1" data-bs-toggle="tab" href="#kt_tab_pane_2">By Output</a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="card-body pt-3">
                <!-- Static bar chart visual representation -->
                <div class="chart-container" style="height: 320px; overflow-x: auto;">
                    <div class="d-flex flex-column">
                        <!-- Output 1 - Infrastructure -->
                        <div class="mb-5">
                            <div class="d-flex align-items-center mb-2">
                                <div class="fw-bold text-dark fs-6 me-3">1.1 Infrastructure</div>
                                <div class="d-flex align-items-center">
                                    <span class="rounded bg-light px-2 py-1 me-2 fs-7">{{getCategoryInterventions('1.1', 'Infrastructure').length}} Interventions</span>
                                    <span class="text-primary fs-7">78% Complete</span>
                                </div>
                            </div>
                            <div class="progress h-8px bg-light-primary">
                                <div class="progress-bar bg-primary" role="progressbar" style="width: 78%" aria-valuenow="78" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        
                        <!-- Output 1 - Health -->
                        <div class="mb-5">
                            <div class="d-flex align-items-center mb-2">
                                <div class="fw-bold text-dark fs-6 me-3">1.2 Health</div>
                                <div class="d-flex align-items-center">
                                    <span class="rounded bg-light px-2 py-1 me-2 fs-7">{{getCategoryInterventions('1.2', 'Health').length}} Interventions</span>
                                    <span class="text-info fs-7">65% Complete</span>
                                </div>
                            </div>
                            <div class="progress h-8px bg-light-info">
                                <div class="progress-bar bg-info" role="progressbar" style="width: 65%" aria-valuenow="65" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        
                        <!-- Output 1 - Education -->
                        <div class="mb-5">
                            <div class="d-flex align-items-center mb-2">
                                <div class="fw-bold text-dark fs-6 me-3">1.3 Education</div>
                                <div class="d-flex align-items-center">
                                    <span class="rounded bg-light px-2 py-1 me-2 fs-7">{{getCategoryInterventions('1.3', 'Education').length}} Interventions</span>
                                    <span class="text-success fs-7">92% Complete</span>
                                </div>
                            </div>
                            <div class="progress h-8px bg-light-success">
                                <div class="progress-bar bg-success" role="progressbar" style="width: 92%" aria-valuenow="92" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        
                        <!-- Output 2 - UCT -->
                        <div class="mb-5">
                            <div class="d-flex align-items-center mb-2">
                                <div class="fw-bold text-dark fs-6 me-3">2.1 UCT</div>
                                <div class="d-flex align-items-center">
                                    <span class="rounded bg-light px-2 py-1 me-2 fs-7">{{getCategoryInterventions('2.1', 'UCT').length}} Interventions</span>
                                    <span class="text-warning fs-7">45% Complete</span>
                                </div>
                            </div>
                            <div class="progress h-8px bg-light-warning">
                                <div class="progress-bar bg-warning" role="progressbar" style="width: 45%" aria-valuenow="45" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                        
                        <!-- Output 3 - Agriculture -->
                        <div>
                            <div class="d-flex align-items-center mb-2">
                                <div class="fw-bold text-dark fs-6 me-3">3.1 Agriculture</div>
                                <div class="d-flex align-items-center">
                                    <span class="rounded bg-light px-2 py-1 me-2 fs-7">{{getCategoryInterventions('3.1', 'Agriculture').length}} Interventions</span>
                                    <span class="text-danger fs-7">32% Complete</span>
                                </div>
                            </div>
                            <div class="progress h-8px bg-light-danger">
                                <div class="progress-bar bg-danger" role="progressbar" style="width: 32%" aria-valuenow="32" aria-valuemin="0" aria-valuemax="100"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Activity Status Distribution -->
    <div class="col-xl-4">
        <div class="card card-xl-stretch mb-xl-8">
            <div class="card-header border-0 pt-5">
                <h3 class="card-title align-items-start flex-column">
                    <span class="card-label fw-bold fs-3 mb-1">Intervention Distribution</span>
                    <span class="text-muted fw-semibold fs-7">Distribution by output</span>
                </h3>
            </div>
            <div class="card-body">
                <!-- Donut chart placeholder -->
                <div class="d-flex justify-content-center mb-5">
                    <div class="position-relative" style="width: 200px; height: 200px;">
                        <!-- Static donut chart representation -->
                        <div class="position-absolute" style="width: 100%; height: 100%; border-radius: 50%; background: conic-gradient(
                            #50CD89 0% 25%,
                            #009EF7 25% 50%,
                            #F1BC00 50% 75%,
                            #F1416C 75% 100%
                        );"></div>
                        <div class="position-absolute" style="width: 60%; height: 60%; top: 20%; left: 20%; border-radius: 50%; background: white;"></div>
                        <div class="position-absolute w-100 h-100 d-flex justify-content-center align-items-center">
                            <div class="text-center">
                                <div class="fs-3 fw-bold text-dark">{{interventions.length}}</div>
                                <div class="fs-7 text-gray-600">Interventions</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Output legend -->
                <div class="d-flex flex-wrap justify-content-around">
                    <div class="d-flex flex-column align-items-center mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <div class="badge badge-circle bg-success me-2"></div>
                            <span class="fs-7 text-gray-800">Output 1</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="fs-3 fw-bold text-dark">{{getOutputInterventionsCount(1)}}</span>
                            <span class="fs-7 text-gray-600 ms-2">({{getOutputInterventionsPercent(1)}}%)</span>
                        </div>
                    </div>
                    <div class="d-flex flex-column align-items-center mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <div class="badge badge-circle bg-primary me-2"></div>
                            <span class="fs-7 text-gray-800">Output 2</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="fs-3 fw-bold text-dark">{{getOutputInterventionsCount(2)}}</span>
                            <span class="fs-7 text-gray-600 ms-2">({{getOutputInterventionsPercent(2)}}%)</span>
                        </div>
                    </div>
                    <div class="d-flex flex-column align-items-center mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <div class="badge badge-circle bg-warning me-2"></div>
                            <span class="fs-7 text-gray-800">Output 3</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="fs-3 fw-bold text-dark">{{getOutputInterventionsCount(3)}}</span>
                            <span class="fs-7 text-gray-600 ms-2">({{getOutputInterventionsPercent(3)}}%)</span>
                        </div>
                    </div>
                    <div class="d-flex flex-column align-items-center mb-3">
                        <div class="d-flex align-items-center mb-2">
                            <div class="badge badge-circle bg-danger me-2"></div>
                            <span class="fs-7 text-gray-800">Output 4</span>
                        </div>
                        <div class="d-flex align-items-center">
                            <span class="fs-3 fw-bold text-dark">{{getOutputInterventionsCount(4)}}</span>
                            <span class="fs-7 text-gray-600 ms-2">({{getOutputInterventionsPercent(4)}}%)</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>



<!-- Category Dashboard Charts - Shown only when a category is selected -->
<div *ngIf="selectedCategory" class="category-dashboard">
    <!-- Structures and Households Section -->
    <div class="row g-5 g-xl-8 mb-5">
        <div class="col-12">
            <div class="card card-xl-stretch mb-xl-8">
                <div class="card-header border-0 pt-5">
                    <h3 class="card-title align-items-start flex-column">
                        <span class="card-label fw-bold fs-3 mb-1">{{selectedCategory.name}} - Households & Structures</span>
                        <span class="text-muted fw-semibold fs-7">Quarterly progress</span>
                    </h3>
                    <div class="card-toolbar">
                        <button class="btn btn-sm btn-light-primary me-2" (click)="toggleDataView()">
                            <i class="bi bi-calendar me-1"></i>Toggle Monthly/Quarterly
                        </button>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Structures Chart -->
                        <div class="col-xl-6">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h4 class="fs-5 text-gray-800 mb-0">Structures Completed</h4>
                                <div class="bg-light-primary px-4 py-2 rounded-pill">
                                    <span class="fs-3 fw-bolder text-primary">{{getCategoryStructuresTotal() | number}}</span>
                                    <span class="fs-7 text-gray-600 ms-1">Total</span>
                                </div>
                            </div>
                            <div id="structuresChart" style="height: 350px;"></div>
                        </div>
                        <!-- Households Chart -->
                        <div class="col-xl-6">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h4 class="fs-5 text-gray-800 mb-0">Households Reached</h4>
                                <div class="bg-light-success px-4 py-2 rounded-pill">
                                    <span class="fs-3 fw-bolder text-success">{{getCategoryHouseholdsTotal() | number}}</span>
                                    <span class="fs-7 text-gray-600 ms-1">Total</span>
                                </div>
                            </div>
                            <div id="householdsChart" style="height: 350px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cash Distribution & Activities Section -->
    <div class="row g-5 g-xl-8 mb-5">
        <div class="col-12">
            <div class="card card-xl-stretch mb-xl-8">
                <div class="card-header border-0 pt-5">
                    <h3 class="card-title align-items-start flex-column">
                        <span class="card-label fw-bold fs-3 mb-1">{{selectedCategory.name}} - Cash & Activities</span>
                        <span class="text-muted fw-semibold fs-7">Quarterly distribution</span>
                    </h3>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- Cash Distribution Chart -->
                        <div class="col-xl-6">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h4 class="fs-5 text-gray-800 mb-0">Cash Distributed by Quarter</h4>
                                <div class="bg-light-info px-4 py-2 rounded-pill">
                                    <span class="fs-3 fw-bolder text-info">${{getCategoryCashTotal() | number:'1.0-0'}}</span>
                                    <span class="fs-7 text-gray-600 ms-1">Total</span>
                                </div>
                            </div>
                            <div id="cashDistributionChart" style="height: 350px;"></div>
                        </div>
                        <!-- Activities Chart -->
                        <div class="col-xl-6">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h4 class="fs-5 text-gray-800 mb-0">Activities Completion</h4>
                                <div class="bg-light-warning px-4 py-2 rounded-pill">
                                    <span class="fs-3 fw-bolder text-warning">{{getCategoryActivitiesTotal() | number}}</span>
                                    <span class="fs-7 text-gray-600 ms-1">Total</span>
                                </div>
                            </div>
                            <div id="activitiesChart" style="height: 350px;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add this section where you want the Infrastructure tabs to appear -->
<div *ngIf="selectedCategory?.code === '1.1' && selectedCategory?.name === 'Infrastructure'" class="infrastructure-tabs">
    <ul class="nav nav-tabs" role="tablist">
        <li class="nav-item" *ngFor="let tab of infrastructureTabs">
            <a class="nav-link" 
               [class.active]="activeInfrastructureTab === tab.id"
               (click)="setActiveInfrastructureTab(tab.id)"
               role="tab">
                {{tab.name}}
            </a>
        </li>
    </ul>
    
    <div class="tab-content">
        <div class="tab-pane fade" 
             [class.show.active]="activeInfrastructureTab === 'public-infrastructure'"
             role="tabpanel">
            <!-- Public Infrastructure Content -->
            <div class="row">
                <div class="col-xl-3">
                    <div class="card card-custom gutter-b">
                        <div class="card-body">
                            <span class="text-muted font-weight-bold font-size-h6">Active Interventions</span>
                            <span class="text-dark-75 font-weight-bolder font-size-h2">12</span>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3">
                    <div class="card card-custom gutter-b">
                        <div class="card-body">
                            <span class="text-muted font-weight-bold font-size-h6">Total Activities</span>
                            <span class="text-dark-75 font-weight-bolder font-size-h2">45</span>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3">
                    <div class="card card-custom gutter-b">
                        <div class="card-body">
                            <span class="text-muted font-weight-bold font-size-h6">Regions</span>
                            <span class="text-dark-75 font-weight-bolder font-size-h2">5/5</span>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3">
                    <div class="card card-custom gutter-b">
                        <div class="card-body">
                            <span class="text-muted font-weight-bold font-size-h6">Beneficiaries</span>
                            <span class="text-dark-75 font-weight-bolder font-size-h2">25,780</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="row mt-5">
                <div class="col-xl-8">
                    <div class="card card-custom gutter-b">
                        <div class="card-header">
                            <div class="card-title">
                                <h3 class="card-label">Structures Completed</h3>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container" style="height: 350px;">
                                <!-- Placeholder for chart -->
                                <div class="d-flex justify-content-center align-items-center h-100">
                                    <span class="text-muted">Chart will be displayed here</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-4">
                    <div class="card card-custom gutter-b">
                        <div class="card-header">
                            <div class="card-title">
                                <h3 class="card-label">Activities Distribution</h3>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container" style="height: 350px;">
                                <!-- Placeholder for chart -->
                                <div class="d-flex justify-content-center align-items-center h-100">
                                    <span class="text-muted">Chart will be displayed here</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="tab-pane fade" 
             [class.show.active]="activeInfrastructureTab === 'productive-facility'"
             role="tabpanel">
            <!-- Productive Facility Content -->
            <div class="row">
                <div class="col-xl-3">
                    <div class="card card-custom gutter-b">
                        <div class="card-body">
                            <span class="text-muted font-weight-bold font-size-h6">Active Interventions</span>
                            <span class="text-dark-75 font-weight-bolder font-size-h2">8</span>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3">
                    <div class="card card-custom gutter-b">
                        <div class="card-body">
                            <span class="text-muted font-weight-bold font-size-h6">Total Activities</span>
                            <span class="text-dark-75 font-weight-bolder font-size-h2">32</span>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3">
                    <div class="card card-custom gutter-b">
                        <div class="card-body">
                            <span class="text-muted font-weight-bold font-size-h6">Regions</span>
                            <span class="text-dark-75 font-weight-bolder font-size-h2">4/5</span>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3">
                    <div class="card card-custom gutter-b">
                        <div class="card-body">
                            <span class="text-muted font-weight-bold font-size-h6">Beneficiaries</span>
                            <span class="text-dark-75 font-weight-bolder font-size-h2">15,420</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Charts -->
            <div class="row mt-5">
                <div class="col-xl-8">
                    <div class="card card-custom gutter-b">
                        <div class="card-header">
                            <div class="card-title">
                                <h3 class="card-label">Facilities Completed</h3>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container" style="height: 350px;">
                                <!-- Placeholder for chart -->
                                <div class="d-flex justify-content-center align-items-center h-100">
                                    <span class="text-muted">Chart will be displayed here</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-4">
                    <div class="card card-custom gutter-b">
                        <div class="card-header">
                            <div class="card-title">
                                <h3 class="card-label">Activities Distribution</h3>
                            </div>
                        </div>
                        <div class="card-body">
                            <div class="chart-container" style="height: 350px;">
                                <!-- Placeholder for chart -->
                                <div class="d-flex justify-content-center align-items-center h-100">
                                    <span class="text-muted">Chart will be displayed here</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Interventions Modal -->
<div class="modal fade" id="interventionsModal" tabindex="-1" aria-labelledby="interventionsModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="interventionsModalLabel">Active Interventions</h5>
                <button type="button" class="btn-close" (click)="closeInterventionsModal()" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- List view -->
                <div *ngIf="!modalDetailView">
                    <div class="mb-5">
                        <input type="text" class="form-control form-control-solid" 
                               placeholder="Search interventions..."
                               [(ngModel)]="interventionSearchTerm"
                               (input)="filterInterventions()">
                    </div>
                    
                    <!-- Categories Table View -->
                    <div class="table-responsive">
                        <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3">
                            <thead>
                                <tr class="fw-bold text-muted bg-light">
                                    <th class="min-w-150px">Category</th>
                                    <th>Description</th>
                                    <th class="min-w-200px text-end">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <!-- Output 1 Header -->
                                <tr class="bg-light-primary">
                                    <td colspan="3" class="fw-bold fs-6 text-dark py-3">Output 1</td>
                                </tr>
                                
                                <!-- 1.1 Infrastructure -->
                                <tr>
                                    <td class="fw-bold">1.1 Infrastructure</td>
                                    <td>Public infrastructure and productive facilities</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('1.1', 'Infrastructure')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-primary" (click)="viewCategoryDetails('1.1', 'Infrastructure')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 1.2 Health -->
                                <tr>
                                    <td class="fw-bold">1.2 Health</td>
                                    <td>Healthcare services and facilities</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('1.2', 'Health')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-primary" (click)="viewCategoryDetails('1.2', 'Health')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 1.3 Education -->
                                <tr>
                                    <td class="fw-bold">1.3 Education</td>
                                    <td>Educational services and facilities</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('1.3', 'Education')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-primary" (click)="viewCategoryDetails('1.3', 'Education')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 1.4 Energy -->
                                <tr>
                                    <td class="fw-bold">1.4 Energy</td>
                                    <td>Energy infrastructure and services</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('1.4', 'Energy')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-primary" (click)="viewCategoryDetails('1.4', 'Energy')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- Output 2 Header -->
                                <tr class="bg-light-info">
                                    <td colspan="3" class="fw-bold fs-6 text-dark py-3">Output 2</td>
                                </tr>
                                
                                <!-- 2.1 UCT -->
                                <tr>
                                    <td class="fw-bold">2.1 UCT</td>
                                    <td>Unconditional Cash Transfers</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('2.1', 'UCT')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-info" (click)="viewCategoryDetails('2.1', 'UCT')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 2.2 CFW -->
                                <tr>
                                    <td class="fw-bold">2.2 CFW</td>
                                    <td>Cash for Work programs</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('2.2', 'CFW')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-info" (click)="viewCategoryDetails('2.2', 'CFW')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 2.3 Livelihoods -->
                                <tr>
                                    <td class="fw-bold">2.3 Livelihoods</td>
                                    <td>Sustainable livelihood development</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('2.3', 'Livelihoods')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-info" (click)="viewCategoryDetails('2.3', 'Livelihoods')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 2.4 Cross-border trade -->
                                <tr>
                                    <td class="fw-bold">2.4 Cross-border trade</td>
                                    <td>Trade facilitation across borders</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('2.4', 'Cross-border trade')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-info" (click)="viewCategoryDetails('2.4', 'Cross-border trade')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 2.5 Finance and digital solution -->
                                <tr>
                                    <td class="fw-bold">2.5 Finance and digital solution</td>
                                    <td>Financial inclusion and digital solutions</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('2.5', 'Finance and digital solution')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-info" (click)="viewCategoryDetails('2.5', 'Finance and digital solution')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 2.6 ExhibitionsAndSales -->
                                <tr>
                                    <td class="fw-bold">2.6 ExhibitionsAndSales</td>
                                    <td>Exhibition and sales promotion</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('2.6', 'ExhibitionsAndSales')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-info" (click)="viewCategoryDetails('2.6', 'ExhibitionsAndSales')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- Output 3 Header -->
                                <tr class="bg-light-warning">
                                    <td colspan="3" class="fw-bold fs-6 text-dark py-3">Output 3</td>
                                </tr>
                                
                                <!-- 3.1 Agriculture -->
                                <tr>
                                    <td class="fw-bold">3.1 Agriculture</td>
                                    <td>Agricultural development and support</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('3.1', 'Agriculture')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-warning" (click)="viewCategoryDetails('3.1', 'Agriculture')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 3.2 DRR -->
                                <tr>
                                    <td class="fw-bold">3.2 DRR</td>
                                    <td>Disaster Risk Reduction</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('3.2', 'DRR')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-warning" (click)="viewCategoryDetails('3.2', 'DRR')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 3.3 Climate-smart water security -->
                                <tr>
                                    <td class="fw-bold">3.3 Climate-smart water security</td>
                                    <td>Climate-resilient water management</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('3.3', 'Climate-smart water security')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-warning" (click)="viewCategoryDetails('3.3', 'Climate-smart water security')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 3.4 Ecosystem -->
                                <tr>
                                    <td class="fw-bold">3.4 Ecosystem</td>
                                    <td>Ecosystem preservation and restoration</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('3.4', 'Ecosystem')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-warning" (click)="viewCategoryDetails('3.4', 'Ecosystem')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- Output 4 Header -->
                                <tr class="bg-light-danger">
                                    <td colspan="3" class="fw-bold fs-6 text-dark py-3">Output 4</td>
                                </tr>
                                
                                <!-- 4.1 Social cohesion -->
                                <tr>
                                    <td class="fw-bold">4.1 Social cohesion</td>
                                    <td>Community integration and social support</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('4.1', 'Social cohesion')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-danger" (click)="viewCategoryDetails('4.1', 'Social cohesion')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 4.2 Gender -->
                                <tr>
                                    <td class="fw-bold">4.2 Gender</td>
                                    <td>Gender equality and empowerment</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('4.2', 'Gender')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-danger" (click)="viewCategoryDetails('4.2', 'Gender')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 4.3 Justice -->
                                <tr>
                                    <td class="fw-bold">4.3 Justice</td>
                                    <td>Legal services and justice system reforms</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('4.3', 'Justice')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-danger" (click)="viewCategoryDetails('4.3', 'Justice')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 4.4 Community planning -->
                                <tr>
                                    <td class="fw-bold">4.4 Community planning</td>
                                    <td>Participatory community planning and development</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('4.4', 'Community planning')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-danger" (click)="viewCategoryDetails('4.4', 'Community planning')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 4.5 Regional strategy -->
                                <tr>
                                    <td class="fw-bold">4.5 Regional strategy</td>
                                    <td>Regional development and planning strategies</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('4.5', 'Regional strategy')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-danger" (click)="viewCategoryDetails('4.5', 'Regional strategy')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                                
                                <!-- 4.9 GRM -->
                                <tr>
                                    <td class="fw-bold">4.9 GRM</td>
                                    <td>Grievance Redress Mechanism</td>
                                    <td class="text-end">
                                        <button class="btn btn-sm btn-primary me-2" (click)="closeModalAndLoadDashboard('4.9', 'GRM')" data-bs-dismiss="modal">
                                            <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                                        </button>
                                        <button class="btn btn-sm btn-light-danger" (click)="viewCategoryDetails('4.9', 'GRM')">
                                            <i class="bi bi-info-circle me-1"></i>Details
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                
                <!-- Detail view -->
                <div *ngIf="modalDetailView && selectedModalIntervention">
                    <div class="d-flex justify-content-between align-items-center mb-5">
                        <button type="button" class="btn btn-sm btn-light-primary" (click)="backToInterventionsList()">
                            <i class="bi bi-arrow-left me-1"></i>Back to List
                        </button>
                        <button type="button" class="btn btn-sm btn-primary" 
                                (click)="selectIntervention(selectedModalIntervention)">
                            Load Dashboard
                        </button>
                    </div>
                    
                    <div class="card shadow-sm mb-5">
                        <div class="card-body">
                            <div class="d-flex align-items-center mb-7">
                                <div class="symbol symbol-50px me-4">
                                    <div class="symbol-label bg-light-{{selectedModalIntervention.categoryColor}}">
                                        <i class="bi bi-{{selectedModalIntervention.icon}} text-{{selectedModalIntervention.categoryColor}} fs-2x"></i>
                                    </div>
                                </div>
                                <div>
                                    <h3 class="fw-bold text-dark mb-1">{{selectedModalIntervention.name}}</h3>
                                    <div class="d-flex align-items-center">
                                        <span class="badge badge-light-{{selectedModalIntervention.categoryColor}} me-2">
                                            {{selectedModalIntervention.category}}
                                        </span>
                                        <span class="text-muted fs-7">ID: {{selectedModalIntervention.id}}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="separator separator-dashed mb-5"></div>
                            
                            <!-- Intervention Profile Details -->
                            <div class="mb-5">
                                <h4 class="fs-5 text-dark mb-4">Intervention Profile Information</h4>
                                <div class="d-flex flex-column mb-3">
                                    <div class="fw-semibold fs-6 text-gray-800 mb-1">Category Information</div>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="bg-light-secondary rounded p-3">
                                                <div class="text-muted fs-7">Category</div>
                                                <div class="fw-bold fs-6">{{selectedModalIntervention.category}}</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="bg-light-secondary rounded p-3">
                                                <div class="text-muted fs-7">Output</div>
                                                <div class="fw-bold fs-6">{{selectedModalIntervention.output || 'Infrastructure Development'}}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="d-flex flex-column mb-3">
                                    <div class="fw-semibold fs-6 text-gray-800 mb-1">Intervention Identifiers</div>
                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <div class="bg-light-secondary rounded p-3">
                                                <div class="text-muted fs-7">Abbreviation</div>
                                                <div class="fw-bold fs-6">{{getAbbreviation(selectedModalIntervention.id)}}</div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="bg-light-secondary rounded p-3">
                                                <div class="text-muted fs-7">Full ID</div>
                                                <div class="fw-bold fs-6">{{selectedModalIntervention.id}}</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="separator separator-dashed mb-5"></div>
                            
                            <!-- Variables Section -->
                            <div class="d-flex flex-column mb-3">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <div class="fw-semibold fs-6 text-gray-800">Variables</div>
                                    <a href="/data-entry/intervention-profiles" target="_blank" class="btn btn-sm btn-light-primary">
                                        <i class="bi bi-info-circle me-1"></i>
                                        Variable Explanation
                                    </a>
                                </div>
                                <div class="d-flex flex-wrap gap-2 bg-light-secondary p-4 rounded">
                                    <ng-container *ngIf="selectedModalIntervention.variables && selectedModalIntervention.variables.length > 0; else noVariables">
                                        <span *ngFor="let variable of selectedModalIntervention.variables" 
                                            class="badge badge-light-primary fw-semibold px-3 py-2"
                                            [title]="variable.description || variable.displayName">
                                            {{variable.displayName}}
                                        </span>
                                    </ng-container>
                                    <ng-template #noVariables>
                                        <span class="text-muted">No variables defined for this intervention</span>
                                    </ng-template>
                                </div>
                            </div>
                            
                            <div class="separator separator-dashed mb-5"></div>

                            <div class="mb-5">
                                <div class="fw-semibold fs-6 text-gray-800 mb-2">Description</div>
                                <div class="fw-normal fs-6 text-gray-700 bg-light-secondary p-4 rounded">{{selectedModalIntervention.description}}</div>
                            </div>
                            
                            <div class="separator separator-dashed mb-5"></div>

                            <div class="row g-5">
                                <div class="col-md-6">
                                    <div class="fw-semibold fs-6 text-gray-800 mb-2">Region</div>
                                    <div class="fw-bold fs-5 text-dark">{{selectedModalIntervention.region}}</div>
                                </div>
                                <div class="col-md-6">
                                    <div class="fw-semibold fs-6 text-gray-800 mb-2">Beneficiaries</div>
                                    <div class="fw-bold fs-5 text-dark">{{selectedModalIntervention.beneficiaries | number}}</div>
                                </div>
                                <div class="col-md-6">
                                    <div class="fw-semibold fs-6 text-gray-800 mb-2">Start Date</div>
                                    <div class="fw-bold fs-5 text-dark">{{selectedModalIntervention.startDate | date:'mediumDate'}}</div>
                                </div>
                                <div class="col-md-6">
                                    <div class="fw-semibold fs-6 text-gray-800 mb-2">End Date</div>
                                    <div class="fw-bold fs-5 text-dark">{{selectedModalIntervention.endDate | date:'mediumDate'}}</div>
                                </div>
                            </div>
                            
                            <div class="separator separator-dashed my-5"></div>
                            
                            <div class="mb-5">
                                <div class="fw-semibold fs-6 text-gray-800 mb-3">Progress</div>
                                <div class="d-flex align-items-center mb-2">
                                    <div class="fw-bold text-dark fs-6 me-3">Overall Completion</div>
                                    <div class="d-flex align-items-center ms-auto">
                                        <span class="badge badge-{{selectedModalIntervention.progressColor}} me-2">
                                            {{selectedModalIntervention.progress}}%
                                        </span>
                                    </div>
                                </div>
                                <div class="progress h-8px bg-light-{{selectedModalIntervention.categoryColor}} mb-5">
                                    <div class="progress-bar bg-{{selectedModalIntervention.categoryColor}}" 
                                         role="progressbar" 
                                         [style.width.%]="selectedModalIntervention.progress" 
                                         aria-valuenow="selectedModalIntervention.progress" 
                                         aria-valuemin="0" 
                                         aria-valuemax="100"></div>
                                </div>
                                
                                <div class="d-flex align-items-center justify-content-between">
                                    <div>
                                        <div class="fw-semibold fs-7 text-gray-600 mb-1">Total Activities</div>
                                        <div class="fw-bold fs-6 text-dark">{{selectedModalIntervention.activities}}</div>
                                    </div>
                                    <div>
                                        <div class="fw-semibold fs-7 text-gray-600 mb-1">Estimated Completion</div>
                                        <div class="fw-bold fs-6 text-dark">{{getEstimatedCompletion(selectedModalIntervention)}}</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="closeInterventionsModal()">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Category Details Modal -->
<div class="modal fade" id="categoryDetailsModal" tabindex="-1" aria-labelledby="categoryDetailsModalLabel" aria-hidden="true" data-bs-backdrop="static">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="categoryDetailsModalLabel">{{selectedCategory?.name}} Category Details</h5>
                <button type="button" class="btn-close" (click)="closeCategoryDetailsModal()" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- Category Summary -->
                <div class="card shadow-sm mb-5">
                    <div class="card-body p-5">
                        <div class="d-flex align-items-center mb-7">
                            <div class="symbol symbol-50px me-4">
                                <div class="symbol-label bg-light-primary">
                                    <i class="bi bi-collection-fill text-primary fs-2x"></i>
                                </div>
                            </div>
                            <div>
                                <h3 class="fw-bold text-dark mb-1">{{selectedCategory?.name}}</h3>
                                <div class="d-flex align-items-center">
                                    <span class="badge badge-light-primary me-2">
                                        Code: {{selectedCategory?.code}}
                                    </span>
                                    <span class="text-muted fs-7">{{selectedCategory?.interventionCount}} Interventions</span>
                                </div>
                            </div>
                        </div>
                        
                        <div class="separator separator-dashed mb-5"></div>
                        
                        <!-- Category KPIs -->
                        <div class="row g-5 mb-5">
                            <div class="col-md-4">
                                <div class="bg-light-primary rounded p-5 text-center">
                                    <div class="fs-2 fw-bold text-dark">{{selectedCategory?.interventionCount}}</div>
                                    <div class="fs-7 text-gray-600">Total Interventions</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="bg-light-success rounded p-5 text-center">
                                    <div class="fs-2 fw-bold text-dark">{{selectedCategory?.beneficiaries | number}}</div>
                                    <div class="fs-7 text-gray-600">Total Beneficiaries</div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="bg-light-info rounded p-5 text-center">
                                    <div class="fs-2 fw-bold text-dark">{{getRegionCount()}}</div>
                                    <div class="fs-7 text-gray-600">Regions Covered</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Intervention List -->
                <div class="card shadow-sm">
                    <div class="card-header">
                        <h3 class="card-title">Interventions in this Category</h3>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-row-bordered table-row-gray-100 align-middle gs-0 gy-3">
                                <thead>
                                    <tr class="fw-bold text-muted bg-light">
                                        <th>Name</th>
                                        <th>Region</th>
                                        <th>Beneficiaries</th>
                                        <th>Progress</th>
                                        <th class="text-end">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr *ngFor="let intervention of selectedModalInterventions">
                                        <td>
                                            <div class="d-flex align-items-center">
                                                <div class="symbol symbol-40px me-3">
                                                    <div class="symbol-label bg-light-{{intervention.categoryColor}}">
                                                        <i class="bi bi-{{intervention.icon}} text-{{intervention.categoryColor}} fs-2x"></i>
                                                    </div>
                                                </div>
                                                <div class="d-flex justify-content-start flex-column">
                                                    <a class="text-dark fw-bold text-hover-primary fs-6">{{intervention.name}}</a>
                                                    <span class="text-muted fw-semibold text-muted d-block fs-7">ID: {{intervention.id}}</span>
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge badge-light-{{intervention.categoryColor}}">{{intervention.region}}</span>
                                        </td>
                                        <td>
                                            <span class="text-muted fw-semibold">{{intervention.beneficiaries | number:'1.0-0'}}</span>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column w-100 me-2">
                                                <div class="d-flex flex-stack mb-2">
                                                    <span class="text-muted me-2 fs-7 fw-semibold">75%</span>
                                                </div>
                                                <div class="progress h-6px w-100">
                                                    <div class="progress-bar bg-primary" role="progressbar" 
                                                        style="width: 75%"></div>
                                                </div>
                                            </div>
                                        </td>
                                        <td class="text-end">
                                            <button type="button" class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1">
                                                <i class="bi bi-eye fs-3"></i>
                                            </button>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" (click)="closeCategoryDetailsModal()">Close</button>
                <button type="button" class="btn btn-primary" (click)="closeModalAndLoadDashboard(selectedCategory?.code, selectedCategory?.name)" data-bs-dismiss="modal">
                    <i class="bi bi-bar-chart-fill me-1"></i>Load Dashboard
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Region Details Modal -->
<div class="modal fade" id="regionDetailsModal" tabindex="-1" aria-labelledby="regionDetailsModalLabel" aria-hidden="true" data-bs-backdrop="static">
  <div class="modal-dialog modal-xl">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="regionDetailsModalLabel">{{selectedRegion?.name}} Region Details</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row g-5 g-xl-8 mb-5">
          <div class="col-xl-4">
            <div class="card card-xl-stretch mb-xl-8">
              <div class="card-body d-flex flex-column p-7">
                <div class="d-flex align-items-center">
                  <div class="flex-grow-1">
                    <div class="text-muted fw-bold fs-6 mb-1">Total Interventions</div>
                    <div class="fs-2 fw-bold text-dark">{{selectedRegion?.interventionCount || 0}}</div>
                  </div>
                  <div class="symbol symbol-50px">
                    <div class="symbol-label fs-2 fw-bold bg-primary text-white">
                      <i class="bi bi-geo-alt"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-4">
            <div class="card card-xl-stretch mb-xl-8">
              <div class="card-body d-flex flex-column p-7">
                <div class="d-flex align-items-center">
                  <div class="flex-grow-1">
                    <div class="text-muted fw-bold fs-6 mb-1">Total Beneficiaries</div>
                    <div class="fs-2 fw-bold text-dark">{{selectedRegion?.beneficiaries | number}}</div>
                  </div>
                  <div class="symbol symbol-50px">
                    <div class="symbol-label fs-2 fw-bold bg-success text-white">
                      <i class="bi bi-people"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col-xl-4">
            <div class="card card-xl-stretch mb-xl-8">
              <div class="card-body d-flex flex-column p-7">
                <div class="d-flex align-items-center">
                  <div class="flex-grow-1">
                    <div class="text-muted fw-bold fs-6 mb-1">Region Code</div>
                    <div class="fs-2 fw-bold text-dark">{{selectedRegion?.code}}</div>
                  </div>
                  <div class="symbol symbol-50px">
                    <div class="symbol-label fs-2 fw-bold bg-info text-white">
                      <i class="bi bi-code-square"></i>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="card mb-5 mb-xl-8">
          <div class="card-header border-0 pt-5">
            <h3 class="card-title align-items-start flex-column">
              <span class="card-label fw-bold fs-3 mb-1">Interventions in {{selectedRegion?.name}}</span>
              <span class="text-muted mt-1 fw-semibold fs-7">List of all interventions in this region</span>
            </h3>
          </div>
          <div class="card-body pt-3">
            <div class="table-responsive">
              <table class="table table-row-dashed table-row-gray-300 align-middle gs-0 gy-4">
                <thead>
                  <tr class="fw-bold text-muted">
                    <th class="min-w-150px">Name</th>
                    <th class="min-w-100px">Category</th>
                    <th class="min-w-100px">Output</th>
                    <th class="min-w-100px">Beneficiaries</th>
                    <th class="min-w-100px">Progress</th>
                    <th class="min-w-100px text-end">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  <tr *ngFor="let intervention of selectedModalInterventions">
                    <td>
                      <div class="d-flex align-items-center">
                        <div class="symbol symbol-45px me-5">
                          <div class="symbol-label bg-light-{{intervention.categoryColor}}">
                            <i class="bi bi-{{intervention.icon}} text-{{intervention.categoryColor}} fs-2x"></i>
                          </div>
                        </div>
                        <div class="d-flex justify-content-start flex-column">
                          <a class="text-dark fw-bold text-hover-primary fs-6">{{intervention.name}}</a>
                          <span class="text-muted fw-semibold text-muted d-block fs-7">ID: {{intervention.id}}</span>
                        </div>
                      </div>
                    </td>
                    <td>
                      <span class="badge badge-light-{{intervention.categoryColor}}">{{intervention.category}}</span>
                    </td>
                    <td>
                      <span class="text-muted fw-semibold">{{intervention.output || 'N/A'}}</span>
                    </td>
                    <td>
                      <span class="text-muted fw-semibold">25,780</span>
                    </td>
                    <td>
                      <div class="d-flex flex-column w-100 me-2">
                        <div class="d-flex flex-stack mb-2">
                          <span class="text-muted me-2 fs-7 fw-semibold">75%</span>
                        </div>
                        <div class="progress h-6px w-100">
                          <div class="progress-bar bg-primary" role="progressbar" 
                              style="width: 75%"></div>
                        </div>
                      </div>
                    </td>
                    <td class="text-end">
                      <button type="button" class="btn btn-icon btn-bg-light btn-active-color-primary btn-sm me-1">
                        <i class="bi bi-eye fs-3"></i>
                      </button>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
      </div>
    </div>
  </div>
</div> 