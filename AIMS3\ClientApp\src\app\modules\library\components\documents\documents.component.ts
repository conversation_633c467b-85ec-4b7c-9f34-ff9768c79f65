import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { Subscription, forkJoin } from 'rxjs';
import { MenuComponent } from '../../../../_theme/core/components';
import { FilterDropdownList } from '../../../../shared/components/filter-dropdown/filter-ddl.control';
import { UploadLocation } from '../../../../shared/enums';
import { MessageService } from '../../../../shared/services/message.service';
import { SharedService } from '../../../../shared/services/shared.service';
import { AppUtilities } from '../../../../shared/utilities';
import { AuthService } from '../../../auth';
import { InvFilter } from '../../../data-entry/models/inventory.model';
import { DocType } from '../../models/doc-type.model';
import { <PERSON>, FieldValue, IDoc } from '../../models/document.model';
import { DocumentService } from '../../services/document.service';
import { DocumentTypesComponent } from '../doc-types/doc-types.component';
import { UploadFormComponent } from '../upload/upload-form.component';
import { SafeUrl } from '@angular/platform-browser';


@Component({
    selector: 'documents-modal',
    templateUrl: './documents.component.html',
    styleUrls: ['./documents.component.scss']
})
export class DocumentsComponent implements OnInit, OnDestroy {
    working: boolean = false; isGlobalUser: boolean = false;

    showPreviewModal: boolean = false;
    previewFileBlobUrl: SafeUrl;
    previewFileName: string;

    documents: IDoc[] = [];
    fileTypeId: number = 0;
    fileTypes: DocType[] = [];
    fields: any = {};
    statuses = [{ id: true, name: 'Approved' }, { id: false, name: 'Unapproved' }];

   

    orgs: any[] = [];
    filters: InvFilter;
    lblFiltered: string;
    @ViewChild('partner')
    private partnerFilterCtrl: FilterDropdownList;

    @ViewChild('status')
    private statusFilterCtrl: FilterDropdownList;

    @ViewChild(UploadFormComponent, { static: true })
    private uploadFormComponent: UploadFormComponent;

    @ViewChild(DocumentTypesComponent, { static: true })
    private docTypesComponent: DocumentTypesComponent;

    subscriptions: Subscription[] = [];
    constructor(
        private authService: AuthService,
        private docService: DocumentService,
        private sharedService: SharedService,
        private cdr: ChangeDetectorRef,
        private messageService: MessageService
    ) {
        const roles = this.authService.currentUserValue.roles;
        this.isGlobalUser = roles.includes('Admin') || roles.includes('Approver') || roles.includes('Viewer');
    }

    ngOnInit() {
        this.filters = new InvFilter();
        this.filters.status = null;

        this.getDocTypes();
    }

    getDocTypes(): void {
        this.working = true;

        let endpoints: any = {
            docTypes: this.docService.getDocumentTypes(true)
        };

        if (this.isGlobalUser) {
            endpoints = {
                docTypes: this.docService.getDocumentTypes(true),
                orgs: this.sharedService.getOrgsList()
            };
        }

        this.subscriptions.push(
            forkJoin(endpoints).subscribe({
                next: ({ docTypes, orgs }) => {
                    this.fileTypes = docTypes || [];

                    this.orgs = [];
                    orgs.forEach(o => {
                        this.orgs.push({ id: o.id, name: o.shortName, tooltip: o.fullName });
                    });
                },
                error: (e) => {
                    console.log(e);
                    this.working = false;
                },
                complete: () => {
                    AppUtilities().initSelect2();
                    
                    this.fileTypes.forEach(ft => {
                        if (ft.fields?.length)
                            this.fields['t' + ft.id] = ft.fields;
                    });
                    
                    $('#fileType').on('change', (e) => {
                        const selVal = $(e.target).val();
                        this.fileTypeId = +selVal;

                        if (this.fileTypeId === -1) {
                            this.filters.status = null;
                            this.statusFilterCtrl.clearSelection();
                        }

                        this.getDocuments();
                    });

                    this.getDocuments();
                }
            }));
    }

    /** Partner search */
    onFilterChange(ctrl): void {
        if (ctrl.id === 'partner')
            this.filters.orgIds = ctrl.selVals;
        else {
            if (ctrl.selVals && ctrl.selVals[0] === false)
                this.filters.status = false;
            else if (ctrl.selVals && ctrl.selVals[0])
                this.filters.status = true;
            else
                this.filters.status = null;
        }
    }

    resetFilters(): void {
        this.filters.orgIds = null;
        this.filters.status = null;
        this.partnerFilterCtrl.clearSelection();
        this.statusFilterCtrl.clearSelection();
        this.getDocuments();
    }
    /** */

    getDocuments(all: boolean = false): void {
        this.working = true;
        this.documents = [];
        this.cdr.detectChanges();

        const operation = this.filters.orgIds?.length > 0 || this.filters.status !== null
            ? this.docService.getDocumentsFiltered(this.filters, this.fileTypeId, all)
            : this.docService.getAllDocuments(this.fileTypeId, all);

        this.subscriptions.push(
            operation.subscribe({
                next: (docs) => {
                    docs?.forEach(d => {
                        if (d.docDesc)
                            d.docDesc = d.docDesc.replace(/\n/g, '<br/>');
                        d.docTypeName = d.docTypeId > 0
                            ? this.fileTypes.find(t => t.id === d.docTypeId)?.typeName
                            : 'General';
                    });

                    this.documents = docs || [];
                },
                error: (e) => {
                    console.log(e);
                    this.working = false;
                },
                complete: () => {
                    if ($.fn.dataTable.isDataTable('#table_documents'))
                        $('#table_documents').DataTable({ retrieve: true }).destroy();

                    let appliedFilters: string[] = [];
                    if (this.filters.orgIds?.length)
                        appliedFilters.push('Partner');
                    if (this.filters.status !== null)
                        appliedFilters.push('Status');

                    if (appliedFilters.length)
                        this.lblFiltered = 'By ' + appliedFilters.join(', ');
                    else
                        this.lblFiltered = '';

                    this.cdr.detectChanges();
                    this.bindDataTable();
                    this.working = false;
                }
            }));
    }

    private bindDataTable(): void {
        $('#table_documents').DataTable({
            retrieve: false,
            responsive: this.fileTypeId < 1,
            dom: '<f<t>p>',
            pageLength: 25,
            autoWidth: true,
            rowCallback: (row: Node, data: any, index: number, indexFull?: number) => {
                row.childNodes[0].textContent = `${indexFull + 1}`;
            }
        } as DataTables.Settings).on('draw.dt', () => MenuComponent.reinitialization());
        $('#table_documents th').on('click', (e) => this.onSort(e));

        MenuComponent.reinitialization();
    }

    onSearch(e: any): void {
        $('#table_documents').DataTable({ retrieve: true })
            .search(e.target.value || '').draw();
    }

    onSort(e: any): void {
        const target = e.target as HTMLElement;
        if (target.hasAttribute('data-orderable'))
            return;

        if (target.classList.contains('sorting_desc') && !target.classList.contains('noSort')) {
            target.classList.add('noSort');
            return;
        }

        if (target.classList.contains('noSort')) {
            setTimeout(() => {
                $('#table_documents').DataTable({
                    retrieve: true
                }).order([1, 'asc']).draw();
                target.classList.remove('noSort');
            }, this.documents.length * 2);
        }
    }

    getFieldValue(docValues: FieldValue[], fId: number): string {
        if (!docValues?.length)
            return '';

        return docValues.find(fv => fv.fieldId === fId)?.value;
    }

    onUpload(): void {
        this.uploadFormComponent.fileTypes = this.fileTypes
            .filter(ft => ft.uploadLocation <= UploadLocation.Library);

        this.uploadFormComponent.document = new Doc(0, '', '');
        this.uploadFormComponent.document.fieldsWithValues = [];
        this.uploadFormComponent.document.docTypeId = null;

        if (this.fileTypeId === -1 || this.uploadFormComponent.fileTypes
            .findIndex(ft => ft.id === this.fileTypeId) > -1) {
            this.uploadFormComponent.document.docTypeId = this.fileTypeId;
        }

        this.uploadFormComponent.ngOnInit();
    }

    onCreateLink(): void {
        this.uploadFormComponent.document = new Doc(0, '', '');
        this.uploadFormComponent.document.fieldsWithValues = [];
        this.uploadFormComponent.document.docTypeId = -1;
        this.uploadFormComponent.document.isLink = true;
        this.uploadFormComponent.ngOnInit();
    }

    onEdit(docId: number): void {
        const libraryDocTypes = this.fileTypes.filter(ft =>
            ft.uploadLocation <= UploadLocation.Library);
        
        this.uploadFormComponent.fileTypes = libraryDocTypes;

        const doc = this.documents.find(d => d.id === docId);
        if (doc.docTypeId && libraryDocTypes.findIndex(dt => dt.id === doc.docTypeId) === -1) {
            this.messageService.warning("The file information for this file can only be edited from the 'Progress & Target' section.");
            return;
        }

        let _doc = new Doc(doc.id, doc.docName, doc.fileName, doc.docDesc, doc.activityIds, doc.targetId, doc.columnId);
        _doc.docTypeId = doc.docTypeId || -1;
        _doc.fieldsWithValues = doc.fieldsWithValues;
        _doc.organizationId = doc.orgId;
        _doc.isLink = doc.isLink;

        this.uploadFormComponent.document = _doc;
        this.uploadFormComponent.ngOnInit();
    }

    onSaved(doc: Doc): void {
        if (this.fileTypeId && doc.docTypeId && this.fileTypeId !== doc.docTypeId)
            return;

        let _doc = this.documents.find(d => d.id === doc.id);

        // update
        if (_doc) {
            _doc.docName = doc.docName;
            _doc.activityIds = doc.activityIds;
            _doc.docDesc = doc.docDesc;
            _doc.docTypeId = doc.docTypeId;
            _doc.fieldsWithValues = doc.fieldsWithValues;
            _doc.orgId = doc.organizationId;

            if (_doc.isLink)
                _doc.fileName = doc.fileName;

            if (doc.docTypeId)
                _doc.docTypeName = this.fileTypes.find(ft => ft.id === doc.docTypeId)?.typeName;

            if ($.fn.dataTable.isDataTable('#table_documents'))
                $('#table_documents').DataTable({ retrieve: true }).destroy();
            this.cdr.detectChanges();
            this.bindDataTable();
        } else
            this.getDocuments();
    }

    /** Approve status */
    toggleStatus(docId: number, isApproved: Date): void {
        this.messageService.confirmMessage('Change Status', `Status for this document will be changed.`,
            () => {
                this.working = true;
                this.subscriptions.push(this.docService.changeStatus(docId).subscribe({
                    error: (e) => {
                        console.log(e);
                        this.working = false;
                    },
                    complete: () => {
                        let doc = this.documents.find(d => d.id === docId);
                        if (doc)
                            doc.dateApproved = doc.dateApproved ? null : new Date();

                        this.messageService.success('The status for the document has been changed.');
                        this.working = false;
                    }
                }));
            }, false, isApproved ? 'Retreat Approval' : 'Approve');
    }

    onDelete(docId: number, docName: string): void {
        this.messageService.confirmMessage('Confirm Delete',
            `Are you sure you want to delete this document: <span class="text-primary">'${docName}'</span>?`,
            () => {
                this.working = true;
                if ($.fn.dataTable.isDataTable('#table_documents'))
                    $('#table_documents').DataTable({ retrieve: true }).destroy();

                let operation = this.docService.deleteDocument(docId);

                //if (!this.colId && !this.isTarget)
                //    operation = this.docService.deleteDocument(docId, this.recordId);

                this.subscriptions.push(operation.subscribe({
                    next: () => {
                        this.documents = this.documents.filter(d => d.id !== docId);
                    },
                    error: (err) => {
                        this.working = false;
                        console.error(err);
                    },
                    complete: () => {
                        this.cdr.detectChanges();
                        this.bindDataTable();
                        //this.done.emit({
                        //    id: this.recordId,
                        //    colId: this.colId,
                        //    emitVal: -1
                        //});
                        this.messageService.success('The document has been deleted successfully.');
                        this.working = false;
                    }
                })
                );
            }, true, 'Delete');
    }

    onCopyFileLink(docId: number, fileName: string, isLink: boolean): void {
        if (isLink)
            navigator.clipboard.writeText(fileName);
        else
            navigator.clipboard.writeText(`${location.origin}/library/download/${docId}?fileName=${fileName}`);
    }

    downloadFile(fileName: string): void {
        this.sharedService.downloadFile(fileName).subscribe({
            next: (fileBlob) => {
                this.messageService.info('The file is being downloaded.');

                let file = document.createElement("a");
                file.href = URL.createObjectURL(fileBlob);
                file.download = fileName;
                file.click(); // start download
            }, error: (err) => {
                this.messageService.error('Sorry, there was an error downloading the file.');
                console.log(err);
            }
        });
    }
    openPreview(fileName: string): void {
        this.previewFileName = fileName;
        this.sharedService.downloadFile(fileName).subscribe({
            next: (fileBlob) => {
                this.previewFileBlobUrl = URL.createObjectURL(fileBlob);
                this.showPreviewModal = true;
                // off the scrolling when modal is open
                document.body.style.overflow = 'hidden';
            }, error: (err) => {
                this.messageService.error('Sorry, there was an error previewing the file.');
                console.log(err);
            }
        });
    }
    closePreview(): void {
        this.showPreviewModal = false;
        // restore body scrolling and close modal
        document.body.style.overflow = 'auto';
    }

    isImageFile(filename: string): boolean {
        if (!filename) return false;
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
        return imageExtensions.some(ext => filename.toLowerCase().endsWith(ext));
    }
    /** Settings --------------------------------- */
    onSettings(): void {
        this.docTypesComponent.ngOnInit(true);
    }

    onSettingSaved(docTypes: DocType[]): void {
        this.fileTypes = [...docTypes];
    }
    // --------------------------------------------
    
    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}