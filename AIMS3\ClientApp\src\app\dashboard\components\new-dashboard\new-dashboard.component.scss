/* Custom styles for new dashboard */
:host {
    display: block;
    padding: 2rem;
    height: 100%;
}

/* Custom chart container styling */
.bar-chart-placeholder {
    .text-gray-600 {
        font-size: 0.75rem;
    }
}

/* Map placeholder */
.map-placeholder {
    transition: all 0.3s ease;
    
    &:hover {
        background-color: #eef3f7 !important;
    }
}

/* Map fullscreen mode */
.fullscreen-map {
    position: fixed !important;
    top: 50px;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw !important;
    height: calc(100vh - 50px) !important;
    z-index: 1050;
    margin: 0 !important;
    padding: 0 !important;
    border-radius: 0 !important;
    overflow: hidden;
    
    .card-body {
        padding: 0;
    }
    
    #mapChart {
        width: 100% !important;
        height: calc(100vh - 110px) !important;
    }
}

/* Card hover effects */
.card {
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    
    &:hover {
        transform: translateY(-3px);
        box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
    }
    
    &.fullscreen-map {
        transform: none !important;
    }
}

/* Table row hover effects */
.table tr {
    transition: background-color 0.2s ease;
    
    &:hover {
        background-color: #f8f9fa;
    }
}

/* Badge styling */
.badge {
    padding: 0.35em 0.65em;
    font-weight: 500;
} 