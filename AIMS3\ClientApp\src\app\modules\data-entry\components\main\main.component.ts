import { ChangeDetector<PERSON><PERSON>, <PERSON>mponent, Host<PERSON>inding, HostListener, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { IRowNode } from 'ag-grid-community';
import { Observable, Subject, Subscription, forkJoin, lastValueFrom } from 'rxjs';
import { MenuComponent } from '../../../../_theme/core/components';
import { FilterDropdownList } from '../../../../shared/components/filter-dropdown/filter-ddl.control';
import { ActivityStatus, ColDataType, OperationResult } from '../../../../shared/enums';
import { OperationDataResult } from '../../../../shared/models/operation-result.model';
import { AuthorizationPipe } from '../../../../shared/pipes/auth.pipe';
import { MessageService } from '../../../../shared/services/message.service';
import { SharedService } from '../../../../shared/services/shared.service';
import { AppUtilities, compareSort } from '../../../../shared/utilities';
import { Category } from '../../../admin/models/category.model';
import { IProjectIntervention } from '../../../admin/models/project.model';
import { GridComponent } from '../../../aims-grid/components/grid/grid.component';
import { DynamicColumn } from '../../../aims-grid/models/dynamic-column.model';
import { AuthService } from '../../../auth';
import { PivotsComponent } from '../../../pivot/components/pivots/pivots.component';
import { IActivityAdded, IProgressData, ITargetAdded, ITargetData } from '../../models/data.model';
import { Profile, ProfileInfo } from '../../models/profile.model';
import { DataService } from '../../services/data.service';
import { ProfileService } from '../../services/profile.service';
import { InterventionProfileFormComponent } from './intervention-form/profile-form.component';
import { SubmitDataComponent } from 'src/app/modules/data/components/submit/submit-data.component';
import { SubmissionLogComponent } from 'src/app/modules/data/components/submission-log/submission-log.component';
import { MatDialog } from '@angular/material/dialog';



@Component({
    selector: 'aims-data-entry-main',
    templateUrl: './main.component.html',
    styleUrls: ['./main.component.scss'],
    encapsulation: ViewEncapsulation.None
})
export class DataEntryMainComponent implements OnInit, OnDestroy {
    @HostBinding('attr.data-qs-app-toolbar-fixed') pgToolbar = 'true';
    @HostBinding('attr.data-qs-app-header-fixed') pgHeader = 'true';
    @HostBinding('attr.data-qs-app-sidebar-push-toolbar') pushToolbar = 'true';
    appHeader: HTMLElement; pivotPanel: string;

    // **** Profiles ----------------------------------------------------------
    working: boolean = false; gridWorking: boolean = false;
    isAdmin: boolean = false; isViewer: boolean = false;
    saving: boolean = false;
    downloading: boolean = false;

    projects: IProjectIntervention[] = [];
    outputs = [];
    categories: Category[] = [];
    profiles: any = [];
    interventions: ProfileInfo[] = [];
    allInterventions: ProfileInfo[] = [];
    profTabs: ProfileInfo[] = [];
    btnAllTabs: boolean = false;
    selTabId: number = 0;

    @ViewChild(InterventionProfileFormComponent, { static: false })
    private profFormComponent: InterventionProfileFormComponent;
    toolbarBgColor: string = '';

    filtered: boolean = false;

    @ViewChild('output')
    private outputFilterCtrl: FilterDropdownList;
    @ViewChild('category')
    private catFilterCtrl: FilterDropdownList;
    @ViewChild('profile')
    private profFilterCtrl: FilterDropdownList;

    // **** Grid --------------------------------------------------------------
    @ViewChild(GridComponent, { static: true })
    private gridComponent: GridComponent;
    public gridFilters = {
        type: 'Progress',
        profId: 0,
        project: null,
        orgId: 0
    };

    orgs: any[] = []; allOrgs = []; userOrgId: number = 0;

    private subscriptions: Subscription[] = [];
    constructor(
        private authService: AuthService,
        private authPipe: AuthorizationPipe,
        private profService: ProfileService,
        private dataService: DataService,
        private sharedService: SharedService,
        private messageService: MessageService,
        private cdr: ChangeDetectorRef,
        private dialog:MatDialog,
    ) {
        this.isAdmin = this.authPipe.transform(['admin']);
        this.isViewer = !this.isAdmin && this.authPipe.transform(['gv', 'lv']);
        this.appHeader = document.querySelector('app-header');
    }

    private routeFragment = { valid: false, orgId: 0, profId: 0, projId: 0, actId: 0, tId: 0, y: 0, m: 0, colId: '' };
    ngOnInit(): void {
        this.gridComponent.isAdmin = this.isAdmin;
        if (this.isViewer)
            this.gridComponent.defaultColDefs.suppressFillHandle = true;

        this.gridComponent.selectedVals.orgId = this.authService.currentUserValue.org.id;
        this.gridComponent.selectedVals.org = this.authService.currentUserValue.org.shortName;
        this.getDropdownItems();

        // get link fragment if there
        let fragment: any = this.authService.getActivatedRoute().fragment;
        try {
            if (fragment) { // format: {OrgId}S{ProfId}a{ProjId}b{ActOrTarId}a{Month-Year}n{ColId}
                fragment = fragment.split('=')[1];
                fragment = fragment.split('S');
                this.routeFragment.orgId = +fragment[0];
                fragment = fragment[1].split('a');
                this.routeFragment.profId = +fragment[0];
                this.routeFragment.projId = +fragment[1].split('b')[0];
                this.routeFragment.actId = +fragment[1].split('b')[1]; // tId
                fragment = fragment[2].split('n');

                if (fragment[0] === '0') {
                    this.routeFragment.tId = this.routeFragment.actId;
                    this.routeFragment.actId = null;
                } else if (fragment[0] !== 'MY') {
                    this.routeFragment.m = +fragment[0].split('-')[0];
                    this.routeFragment.y = +fragment[0].split('-')[1];
                }

                this.routeFragment.colId = fragment[1];
                if (!this.routeFragment.orgId || !this.routeFragment.profId || !this.routeFragment.projId || !this.routeFragment.colId)
                    this.routeFragment.valid = false;
                else
                    this.routeFragment.valid = this.routeFragment.actId > 0 || this.routeFragment.tId > 0;
            }

            if (this.routeFragment.valid)
                this.messageService.info('Getting to the cell, please wait...', 'Loading', { timeOut: 2000 });
        } catch (e) {
            this.messageService.error('The link is not valid.', 'Invalid Link');
        }
    }

    // get organizations
    private getOrganizations(): void {
        this.orgs = [];
        this.subscriptions.push(
            this.sharedService.getOrgsList().subscribe({
                next: (orgs) => {
                    orgs.forEach(o => {
                        this.orgs.push({
                            id: o.id, name: o.shortName, tooltip: o.fullName,
                            selected: o.id === this.authService.currentUserValue.org.id
                        });
                    });
                }, error: (e) => { }, complete: () => {
                    this.userOrgId = this.authService.currentUserValue.org.id;
                    this.gridComponent.selectedVals.orgId = this.userOrgId;
                    this.gridComponent.selectedVals.org = this.authService.currentUserValue.org.shortName;
                    this.allOrgs = [...this.orgs];

                    if (this.gridFilters.project?.organizations) {
                        this.orgs = this.allOrgs.filter(o => o.id === this.userOrgId ||
                            this.gridFilters.project.organizations.includes(o.id));
                    }
                    
                    AppUtilities().initSelect2();
                    $('#partner').on('change', (e) => {
                        const selVal = +$(e.target).val();
                        this.onOrgSelect(selVal);
                    });

                    //if (this.routeFragment.valid) {
                    //    this.gridComponent.selectedVals.orgId = this.routeFragment.orgId;
                    //    this.gridComponent.selectedVals.org = this.orgs.find(o => o.id === this.routeFragment.orgId)?.name;
                    //    if (!this.gridComponent.selectedVals.org) {
                    //        this.messageService.error('Link is not valid or permission denied.');
                    //        this.routeFragment.valid = false;
                    //    }
                    //}

                    if (this.pivotPanel)
                        this.pivotsComponent.orgs = this.allOrgs;
                }
            })
        );
    }

    private onOrgSelect(orgId: number): void {
        this.messageService.confirmActionWithCondition(this.pendingSave && this.gridFilters.project, 'Save changes',
            `There are unsaved changes that require saving. Do you want to save the changes?`,
            () => { return this.onSave(); }, () => {
                this.gridFilters.orgId = orgId;
                this.gridComponent.selectedVals.orgId = orgId;
                this.gridComponent.selectedVals.org = this.orgs.find(o => o.id === orgId)?.name;
                this.pendingSave = false;
                this.getData();
        }, () => { $('#partner').val(this.gridComponent.selectedVals.orgId).trigger('change.select2'); });
    }

    // get categories and profiles
    private getDropdownItems(): void {
        this.working = true;
        this.subscriptions.push(
            forkJoin({
                projs: this.sharedService.getProjectsFiltered(true),    // filtered
                cats: this.sharedService.getCategories(),               // all categories
                profs: this.profService.getProfiles()                   // this could be filtered
            }).subscribe({
                next: ({ projs, cats, profs }) => {
                    this.projects = projs;
                    this.interventions = [...profs];
                    this.interventions.sort((x, y) => compareSort(x.category.code, y.category.code) || compareSort(x.name, y.name));
                    this.allInterventions = [...profs];
                    this.categories = cats;

                    this.categories.forEach(cat => {
                        cat.name = cat.code + ' ' + cat.name;

                        const outputExists = this.outputs.findIndex(o => o.name === cat.output) > -1;
                        if (!outputExists) {
                            this.outputs.push({
                                id: cat.output,
                                name: cat.output,
                                categories: this.categories.filter(c => c.output === cat.output)
                            });
                        }

                        // profiles for pivot
                        cat['profiles'] = this.allInterventions.filter(p => p.categoryId === cat.id);
                    });
                },
                error: (err) => {
                    console.log(err);
                    this.working = false;
                },
                complete: () => {
                    this.createProfileFilters();
                    this.cdr.detectChanges();

                    // bind dropdowns
                    AppUtilities().initSelect2();
                    $('#projFilter').on('change', (e) => {
                        const selVal = +$(e.target).val();
                        this.onProjectSelect(selVal);
                    });

                    if (this.routeFragment.valid)
                        $('#projFilter').val(this.routeFragment.projId).trigger('change');
                    else if (this.isAdmin)
                        this.createProfileTabs();

                    this.working = false;
                }
            })
        );
    }

    private onProjectSelect(projId: number): void {
        this.messageService.confirmActionWithCondition(this.pendingSave && this.gridFilters.project, 'Save changes',
            `There are unsaved changes that require saving. Do you want to save the changes?`,
            () => { return this.onSave(); }, () => {
                try {
                    if (!projId) {
                        if (this.toolbarBgColor) {
                            this.appHeader.classList.remove(this.toolbarBgColor);
                            this.toolbarBgColor = '';
                        }

                        $('#projFilter').val('').trigger('change.select2');
                        this.gridFilters.project = null;
                        this.data = [];
                        this.gridComponent.refreshGridRows(this.data, true);

                        this.interventions = [...this.allInterventions];
                        this.interventions.sort((x, y) => x.name > y.name ? 1 : -1);
                        this.createProfileFilters();
                        this.createProfileTabs();
                        if (this.saveBound) {
                            document.removeEventListener('keydown', this.saveBound, true);
                            this.saveBound = null;
                        }
                        this.orgs = [...this.allOrgs];

                        if (this.pivotPanel)
                            this.pivotsComponent.selProjId = 0;
                        return;
                    }

                    this.pendingSave = false;

                    if (this.toolbarBgColor)
                        this.appHeader.classList.remove(this.toolbarBgColor);
                    this.toolbarBgColor = 'bg-color-' + (projId > 10 ? projId % 10 : projId);
                    this.appHeader.classList.add(this.toolbarBgColor);

                    // this.gridFilters.type = 'Progress', // do not update this
                    this.gridFilters.project = this.projects.find(p => p.id === projId);
                    this.gridComponent.selectedVals.proj = this.gridFilters.project.abbreviation;
                    this.gridComponent.selectedVals.projId = projId;

                    // filter if region is restricted
                    let regions = this.gridFilters.project.regions;
                    if (regions) {
                        if (!Array.isArray(regions))
                            regions = this.gridFilters.project.regions.split(',');

                        this.gridFilters.project.regions = [];
                        if (regions.includes('0'))
                            this.gridComponent.regionsFilter = []; // all allowed
                        else {
                            for (let i = 0; i < regions.length; i++)
                                this.gridFilters.project.regions.push(parseInt(regions[i]));
                            this.gridComponent.regionsFilter = this.gridFilters.project.regions;
                        }
                    } else if (!this.isAdmin) this.gridComponent.regionsFilter = [9]; // dummy value: none allowed

                    // filter interventions as per project
                    this.interventions = this.allInterventions.filter(i =>
                         this.gridFilters.project.interventions?.includes(i.id));
                    this.interventions.sort((x, y) => x.name > y.name ? 1 : -1);

                    // filter organizations based on project selected
                    if (this.isAdmin || this.authPipe.transform(['ga', 'gv'])) {
                        if (!this.allOrgs.length)
                            this.getOrganizations();
                        else { // refresh orgs list
                            if (this.gridFilters.project?.organizations) {
                                this.orgs = this.allOrgs.filter(o => o.id === this.userOrgId ||
                                    this.gridFilters.project.organizations.includes(o.id));

                                //if (this.routeFragment.valid) {
                                //    const org = this.orgs.find(o => o.id === this.routeFragment.orgId);
                                //    if (!org) {
                                //        this.messageService.error('Link is not valid or permission denied.');
                                //        this.routeFragment.valid = false;
                                //    } else {
                                //        org.selected = true;
                                //        this.gridFilters.orgId = this.routeFragment.orgId;
                                //        this.gridComponent.selectedVals.orgId = this.routeFragment.orgId;
                                //        this.gridComponent.selectedVals.org = org.name;
                                //    }
                                //} else {
                                    this.orgs.find(o => o.id === this.userOrgId).selected = true;
                                    this.gridFilters.orgId = this.userOrgId;
                                    this.gridComponent.selectedVals.orgId = this.userOrgId;
                                    this.gridComponent.selectedVals.org = this.authService.currentUserValue.org.shortName;
                                //}
                                this.cdr.detectChanges();
                                AppUtilities().initSelect2('#partner', true);
                            }
                        }
                    }

                    if (this.pivotPanel)
                        this.pivotsComponent.selProjId = projId;

                    this.cdr.detectChanges();
                    this.createProfileFilters();
                    this.createProfileTabs();

                    if (!this.saveBound)
                        this.bindSave();
                } catch (ex) {
                    console.error(ex);
                    this.messageService.error(`You don't have access to this intervention for this project. Or, something other went wrong.'`);
                }
            }, () => { $('#projFilter').val(this.gridFilters.project.id).trigger('change.select2'); });
    }

    private createProfileFilters(): void {
        this.profiles = [];
        this.interventions.forEach(prof => {
            this.profiles.push({
                id: prof.id,
                catId: prof.categoryId,
                name: prof.name
            });
        });

        // sort it alphabetically
        this.profiles.sort((x, y) => x.name > y.name ? 1 : -1);
    }

    onFilterChange(ctrl): void {
        this.messageService.confirmActionWithCondition(this.pendingSave && this.gridFilters.project, 'Save changes',
            `There are unsaved changes that require saving. Do you want to save the changes?`,
            () => { return this.onSave(); }, () => {
                this.pendingSave = false;

                if (ctrl.id === 'output') {
                    this.catFilterCtrl.clearSelection();
                    this.profFilterCtrl.clearSelection();

                    if (!ctrl.selVals.length) {
                        this.catFilterCtrl.options = this.categories;
                        this.profFilterCtrl.options = this.profiles;
                    } else {
                        this.catFilterCtrl.options = this.categories.filter(c => ctrl.selVals.includes(c.output));
                        this.catFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));
                        this.profFilterCtrl.options = this.profiles.filter(p => this.catFilterCtrl.options.findIndex(c => c.id === p.catId) > -1);
                        this.profFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));
                    }

                    this.catFilterCtrl.items = Object.create(this.catFilterCtrl.options);
                    this.profFilterCtrl.items = Object.create(this.profFilterCtrl.options);
                } else if (ctrl.id === 'category') {
                    this.profFilterCtrl.clearSelection();

                    if (!ctrl.selVals.length) {
                        this.profFilterCtrl.options = this.profiles;
                    } else {
                        this.profFilterCtrl.options = this.profiles.filter(p => ctrl.selVals.includes(p.catId));
                        this.profFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));
                    }
                    this.profFilterCtrl.items = Object.create(this.profFilterCtrl.options);
                }

                // filter interventions
                if (!this.profFilterCtrl.selectedValues.length) {
                    this.interventions = this.allInterventions.filter(i =>
                        this.profFilterCtrl.options.findIndex(o => o.id === i.id) > -1
                    );
                } else {
                    this.interventions = this.allInterventions.filter(i =>
                        this.profFilterCtrl.selectedValues.includes(i.id)
                    );
                }

                this.filtered = this.interventions.length !== this.allInterventions.length;
                this.createProfileTabs();
            });
    }

    resetFilters(): void {
        this.messageService.confirmActionWithCondition(this.pendingSave && this.gridFilters.project, 'Save changes',
            `There are unsaved changes that require saving. Do you want to save the changes?`,
            () => { return this.onSave(); }, () => {
                this.pendingSave = false;
                this.outputFilterCtrl.clearSelection();
                this.outputFilterCtrl.options = this.outputs;
                this.outputFilterCtrl.items = [...this.outputs];

                this.catFilterCtrl.clearSelection();
                this.catFilterCtrl.options = this.categories;
                this.catFilterCtrl.items = [...this.categories];

                this.profFilterCtrl.clearSelection();
                this.profFilterCtrl.options = this.profiles;
                this.profFilterCtrl.items = [...this.profiles];

                if (this.gridFilters.project) {
                    this.interventions = this.allInterventions.filter(i =>
                        this.gridFilters.project.interventions?.includes(i.id));
                } else
                    this.interventions = this.allInterventions.filter(i => i.id > 0);
                this.filtered = false;
                this.createProfileTabs();
            });
    }

    // triggerred first time and after top filters are applied
    private createProfileTabs(profId?: number): void {
        if (!this.interventions.length) {
            this.profTabs = [];
            this.selTabId = 0;
            return;
        }

        // init defaults
        this.btnAllTabs = false;
        
        // sort it by category code, then by profile name
        this.interventions.sort((x, y) => compareSort(x.category.code, y.category.code) || compareSort(x.name, y.name));
        this.profTabs = [...this.interventions];

        // select first tab by default, or if provided, that one
        this.selTabId = this.profTabs[0].id;
        this.gridComponent.selectedVals.prof = this.profTabs[0].abbreviation;
        this.gridComponent.selectedVals.profId = this.profTabs[0].id;

        // adjust using container's width
        const container = document.querySelector('#interventions');
        this.cdr.detectChanges();

        // get all tabs size
        setTimeout(() => {
            container.setAttribute('style', 'overflow: initial');
            const tabItems = document.querySelectorAll('#interventions li');

            let totalTabsSize = 0;
            for (let i = 0; i < tabItems.length; i++) {
                totalTabsSize += tabItems[i].clientWidth;
                if (totalTabsSize > (container.clientWidth - 80)) {         // -80px for more info and add buttons
                    this.profTabs = this.profTabs.slice(0, i - 1);          // this.profTabs.splice(i, this.profTabs.length - 1);
                    this.btnAllTabs = true;
                    break;
                }
            }

            // init dropdown form modals
            MenuComponent.reinitialization();

            if (profId > 0) {
                this.onSelectProfileTab(profId);
                return;
            }
        }, 300); // wait until tabs render

        if (profId > 0) return;

        // create grid columns for the selected profile
        this.gridFilters.type = 'Progress';
        this.gridFilters.profId = this.profTabs[0].id;
        this.gridComponent.dynamicColumns = this.profTabs[0].variables
            ?.filter(v => [2, 4, 5].includes(v.type)); // ColumnVarType.Progress, Info, Static
        this.gridComponent.selectedVals.dataType = 'Progress';
        this.gridComponent.initGrid(this.gridFilters);
        //console.log('createProfTab', this.routeFragment.profId) // TEMP
        if (this.routeFragment.valid) {
            this.selTabId = 0;
            this.onSelectProfileTab(this.routeFragment.profId);
            return;
        }

        // get data
        if (this.gridFilters.project)
            this.getData();
        else {
            this.data = [];
            this.gridComponent.refreshGridRows(this.data, !this.isViewer);
        }
    }

    // triggered on profile selection from tabs or menu
    onSelectProfileTab(profId: number): void {
        if (profId === this.selTabId) return;
        this.messageService.confirmActionWithCondition(this.pendingSave && this.gridFilters.project, 'Save changes',
            `There are unsaved changes that require saving. Do you want to save the changes?`,
            () => { return this.onSave(); }, () => {
                this.pendingSave = false;
                
                // push tab to first position if it's not there
                if (this.btnAllTabs) {
                    let selItem = this.profTabs.find(p => p.id === profId);
                    let lastItemOrder = 0;
                    if (!selItem) {
                        selItem = this.interventions.find(i => i.id === profId);
                        const lastTabItem = this.profTabs[this.profTabs.length-1];
                        lastItemOrder = compareSort(selItem.category.code, lastTabItem.category.code) || compareSort(selItem.name, lastTabItem.name);
                        if (lastItemOrder > 0)
                            this.profTabs.shift();
                        else
                            this.profTabs.pop();
                        this.profTabs = [...this.profTabs, selItem];
                        this.profTabs.sort((x, y) => compareSort(x.category.code, y.category.code) || compareSort(x.name, y.name));
                    }

                    setTimeout(() => {
                        const containerWidth = document.querySelector('#interventions')?.clientWidth;
                        const tabItems = document.querySelectorAll('#interventions li');

                        let totalTabsSize = 0;
                        for (let i = 0; i < tabItems.length; i++) {
                            totalTabsSize += tabItems[i].clientWidth;
                            if (totalTabsSize > (containerWidth - 60)) {
                                if (selItem.id === this.profTabs[this.profTabs.length-1].id)        // if sel tab is now the last item
                                    this.profTabs.shift();
                                else
                                    this.profTabs.pop();
                                totalTabsSize - tabItems[i].clientWidth;
                            }
                        }
                    }, 200);
                }
                this.selTabId = profId;

                // create grid columns for the selected profile
                // always switch to Progress by default when profile is changed
                this.gridFilters.type = 'Progress';
                this.gridFilters.profId = profId;
                const selProfile = this.interventions.find(i => i.id === profId);
                this.gridComponent.dynamicColumns = selProfile?.variables.filter(v => v.type > 1); // ColumnType.Progress and ProgressInfo
                this.gridComponent.selectedVals.dataType = 'Progress';
                this.gridComponent.selectedVals.prof = selProfile.abbreviation;
                this.gridComponent.selectedVals.profId = profId;
                //console.log('onSelProfTab', this.routeFragment.valid) // TEMP
                if (this.routeFragment.valid) {
                    this.gridFilters.orgId = this.routeFragment.orgId;
                    this.gridComponent.selectedVals.orgId = this.routeFragment.orgId;
                    //console.log('here at prof tab')
                    if (this.routeFragment.actId > 0) {
                        this.month = this.routeFragment.m;
                        this.year = this.routeFragment.y;
                        this.onTypeChange('Progress');
                    } else
                        this.onTypeChange('Targets');
                    return;
                }

                this.gridComponent.initGrid(this.gridFilters);

                // get data
                if (this.gridFilters.project)
                    this.getData();
                else
                {
                    this.data = [];
                    this.gridComponent.refreshGridRows(this.data, !this.isViewer);
                }
            });
    }

    onManageProfile(e: any, profId?: number): void {
        this.profFormComponent.profile = new Profile(0, 0, '', '');

        if (profId > 0) {
            const prof = this.profTabs.find(p => p.id === profId);
            if (prof) {
                this.profFormComponent.profile =
                    new Profile(prof.id, prof.categoryId, prof.name, prof.abbreviation, prof.description);
            }
        }

        this.profFormComponent.open(e);
    }

    onProfileDone(prof: ProfileInfo): void {
        // if deleted
        if (prof.id < 0) {
            prof.id = prof.id * -1;

            this.allInterventions = this.allInterventions.filter(i => i.id !== prof.id);
            this.allInterventions.sort((x, y) => x.name > y.name ? 1 : -1);
            this.interventions = this.interventions.filter(i => i.id !== prof.id);
            this.interventions.sort((x, y) => x.name > y.name ? 1 : -1);

            this.profTabs = this.profTabs.filter(i => i.id !== prof.id);
            this.profiles.forEach(p => {
                if (p.id === prof.id && p.profiles)
                    p.profiles = p.profiles.filter(p => p.id !== prof.id);
            });

            // if is selected
            if (this.selTabId === prof.id)
                this.selTabId = this.profTabs.length ? this.profTabs[0].id : 0;

            // reset filters
            this.profFilterCtrl.clearSelection(true);
            this.profFilterCtrl.options = this.profiles;
            this.profFilterCtrl.items = [...this.profiles];
        } else {
            let profInd = this.interventions.findIndex(i => i.id === prof.id);
            if (profInd > -1) {     // is updated
                this.interventions[profInd].categoryId = prof.categoryId;
                this.interventions[profInd].category = prof.category;
                this.interventions[profInd].name = prof.name;
                this.interventions[profInd].abbreviation = prof.abbreviation;
                this.interventions[profInd].description = prof.description;
                // variables are not updated here
                this.interventions.sort((x, y) => x.name > y.name ? 1 : -1);

                // update all interventions array as well
                profInd = this.allInterventions.findIndex(i => i.id === prof.id);
                if (profInd > -1) {     // is updated
                    this.allInterventions[profInd].categoryId = prof.categoryId;
                    this.allInterventions[profInd].category = prof.category;
                    this.allInterventions[profInd].name = prof.name;
                    this.allInterventions[profInd].abbreviation = prof.abbreviation;
                    this.allInterventions[profInd].description = prof.description;
                    this.allInterventions[profInd].variables = prof.variables;
                }
            } else {                // is added or duplicated
                // static variables are added and is included in prof.variables
                this.interventions = [...this.interventions, prof];
                //this.interventions.sort((x, y) => compareSort(x.category.code, y.category.code) || compareSort(x.name, y.name));
                this.allInterventions = [...this.allInterventions, prof];
                this.allInterventions.sort((x, y) => compareSort(x.category.code, y.category.code) || compareSort(x.name, y.name));
                //this.profTabs.push(prof);
                this.cdr.detectChanges();
                //this.onSelectProfileTab(prof.id);
                this.createProfileTabs(prof.id);
                this.resetFilters();
            }
        }

        // update profile filter
        this.createProfileFilters();
    }

    onTypeChange(val: string) {
        this.messageService.confirmActionWithCondition(this.pendingSave && this.gridFilters.project, 'Save changes',
            `There are unsaved changes that require saving. Do you want to save the changes?`,
            () => { return this.onSave(); }, () => {
                this.pendingSave = false;
            this.gridFilters.type = val;
            this.gridFilters.profId = this.selTabId;
            this.gridComponent.selectedVals.dataType = val;

            const selProfile = this.interventions.find(i => i.id === this.selTabId);
            if (selProfile) {
                this.gridComponent.selectedVals.prof = selProfile.abbreviation;

                if (val === 'Progress') {
                    this.gridComponent.dynamicColumns = selProfile.variables
                        .filter(v => [2, 4, 5].includes(v.type)); // ColumnVarType.Progress, Info, Static
                } else {
                    this.gridComponent.dynamicColumns = selProfile.variables
                        .filter(v => [0, 1, 3].includes(v.type)); // ColumnVarType.Target, Info, Static
                }

                this.gridComponent.initGrid(this.gridFilters);
                if (this.gridFilters.project)
                    this.getData();
                else {
                    this.data = [];
                    this.gridComponent.refreshGridRows(this.data, !this.isViewer);
                }
            }
        });
    }

    // *** DynamicColumn from child grid
    onColumnDone(cols: DynamicColumn[]): void {
        try {
            // if deleted or updated
            if (cols.length === 1) {
                if (cols[0].id < 0) {   // col deleted
                    cols[0].id *= -1;

                    // remove from variables of the profile
                    let profInd = this.interventions.findIndex(i => i.id === cols[0].interventionProfileId);
                    this.interventions[profInd].variables = this.interventions[profInd].variables.filter(v => v.id !== cols[0].id);
                    // as all as from the all array
                    profInd = this.allInterventions.findIndex(i => i.id === cols[0].interventionProfileId);
                    this.allInterventions[profInd].variables = this.allInterventions[profInd].variables.filter(v => v.id !== cols[0].id);
                    // ignore updating var arrays of the profiles tabs and filters
                } else {    // col updated
                    // get variable reference of the column
                    let colVar = this.interventions.find(i => i.id === cols[0].interventionProfileId)?.variables
                        ?.find(v => v.id === cols[0].id);
                    if (!colVar) return;

                    // update changed props only
                    colVar.name = cols[0].name;
                    colVar.displayName = cols[0].displayName;
                    colVar.description = cols[0].description;
                    colVar.fieldType = cols[0].fieldType;
                    colVar.fieldTypeValues = cols[0].fieldTypeValues;
                    colVar.isRequired = cols[0].isRequired;
                    colVar.conditionsApplied = cols[0].conditionsApplied;

                    // ger variable reference of the column for allInterventions array now
                    colVar = this.allInterventions.find(i => i.id === cols[0].interventionProfileId)?.variables
                        .find(v => v.id === cols[0].id);
                    if (!colVar) return;

                    // update changed props only
                    colVar.name = cols[0].name;
                    colVar.displayName = cols[0].displayName;
                    colVar.description = cols[0].description;
                    colVar.fieldType = cols[0].fieldType;
                    colVar.fieldTypeValues = cols[0].fieldTypeValues;
                    colVar.isRequired = cols[0].isRequired;
                    colVar.conditionsApplied = cols[0].conditionsApplied;
                }
            } else {        // new col is added or duplicated
                // find the profile
                let profInd = this.interventions.findIndex(i => i.id === cols[0].interventionProfileId);
                if (profInd === -1) return;

                // update all variables (same type) of the profile since it's emitted with addition and sorted order
                // first remove vars of the sent type, then add the emitted ones
                this.interventions[profInd].variables = this.interventions[profInd].variables.filter(v => v.type !== cols[0].type);
                this.interventions[profInd].variables.push(...cols);

                // repeat above for allInterventions array as well
                profInd = this.allInterventions.findIndex(i => i.id === cols[0].interventionProfileId);
                if (profInd === -1) return;
                this.allInterventions[profInd].variables = this.allInterventions[profInd].variables.filter(v => v.type !== cols[0].type);
                this.allInterventions[profInd].variables.push(...cols);
            }
        } catch (ex) { }
    }

    onCellValueChanged(rowInd: number): void {
        this.pendingSave = rowInd > -1;
        // insert new rows if any cell in the last row is changed
        if ((this.gridComponent.rowData[rowInd]['activityId'] || this.gridComponent.rowData[rowInd]['uniqueId']) &&
            rowInd === this.gridComponent.rowData.length - 1) {
            const rows = this.gridComponent.rowData.slice();

            for (let i = 1; i <= 10; i++)       // append extra rows
                rows.push({ id: 0, colVals: {}, dirty: false } as IProgressData);

            this.gridComponent.rowData = rows;
            this.gridComponent.gridApi.setRowData(this.gridComponent.rowData);
            setTimeout(() => this.gridComponent.gridApi.ensureIndexVisible(rowInd, 'middle'), 100);
        }
    }

    onMoreTabsMenu(): void {
        const btn = document.querySelector('#moreProfileTabs') as HTMLLinkElement;
        setTimeout(() => btn?.click(), 100);
    }

    // *** DATA -------------------------------------------------
    private month: number; year: number;
    onMonthChange(e: any, prev?: boolean): void {
        if (e.target.id !== 'progressMonth')
            e = document.querySelector('#progressMonth');

        this.messageService.confirmActionWithCondition(this.pendingSave, 'Save changes',
            `There are unsaved changes that require saving. Do you want to save the changes?`,
            () => { return this.onSave(); }, () => {
                this.pendingSave = false;
                if (e.target) {
                    if (e.target.value) {
                        const my = e.target.value.split('-');
                        this.month = +my[1];
                        this.year = +my[0];
                    } else
                        e.target.value = `${this.year}-${this.month.toString().padStart(2, '0')}`;
                } else {
                    // nav on buttons
                    this.month = prev ? this.month - 1 : this.month + 1;
                    if (this.month > 12) {
                        this.month = 1;
                        this.year = this.year + 1;
                    } else if (this.month < 1) {
                        this.month = 12;
                        this.year = this.year - 1;
                    }
                    e.value = `${this.year}-${this.month.toString().padStart(2, '0')}`;
                }
                this.getData();
        }, () => { e.value = `${this.year}-${this.month.toString().padStart(2, '0')}`; return false; });
    }

    private data: (ITargetData | IProgressData)[] = [];
    private getData(): void {
        this.gridWorking = true;
        
        let operation: Observable<IProgressData[] | ITargetData[]>;
        if (this.gridFilters.type === 'Progress') {
            if (!this.month || !this.year) { // get current month
                this.month = new Date().getMonth();
                if (this.month === 0) this.month++;
                this.year = new Date().getFullYear();
            }
            
            this.gridComponent.selectedVals.month = this.month;
            this.gridComponent.selectedVals.year = this.year;

            if (this.gridFilters.orgId > 0) {
                operation = this.dataService.getActivities(this.gridFilters.profId,
                    this.gridFilters.project.id, this.month, this.year, this.gridFilters.orgId);
            } else {
                operation = this.dataService.getActivities(this.gridFilters.profId,
                    this.gridFilters.project.id, this.month, this.year);
            }
        } else {
            if (this.gridFilters.orgId > 0)
                operation = this.dataService.getTargets(this.gridFilters.profId, this.gridFilters.project.id, this.gridFilters.orgId);
            else
                operation = this.dataService.getTargets(this.gridFilters.profId, this.gridFilters.project.id);
        }

        this.subscriptions.push(operation.subscribe({
            next: (res) => { 
                this.data = [...res]; 
            },
            error: (err) => {
                this.gridWorking = false;
            },
            complete: () => {
                this.resetSortAndGroup();
                // Don't reset submitStatus if we're currently saving (it will be set correctly below)
                if (!this.saving) {
                this.submitStatus = null;
                }
                this.checkEditPermissions();
                this.gridComponent.refreshGridRows(this.data, !this.isViewer);
                
                if (this.gridFilters.type === 'Progress') {
                    // disable asOf column
                    if (this.isAdmin) {
                        const asOfCol = this.gridComponent.gridColumnApi.getColumn('asOf');
                        let colDef = asOfCol.getColDef();
                        colDef.editable = this.gridFilters.project ? false : true;
                        asOfCol.setColDef(colDef, asOfCol.getColDef());
                    }
                    const inputMonth = document.querySelector('#progressMonth') as HTMLInputElement;
                    if (inputMonth)
                        inputMonth.value = `${this.year}-${this.month.toString().padStart(2, '0')}`;

                    // if draft data is there, return
                    if (this.data?.findIndex((r: IProgressData) => r.draft) > -1) {
                        this.submitStatus = null;
                        this.gridWorking = false;

                        // Goto the cell based on routeFragment set
                        if (this.routeFragment.valid)
                            this.goToCell();
                        return;
                    }
                }

                // set submitted if all data is marked for submission
                if (this.data?.length) {
                    let allSubmitted = false;
                    
                                        if (this.gridFilters.type === 'Targets') {
                        // For targets: check if all targets with uniqueId are actually submitted (have dateSubmitted)
                        const targets = this.data.filter(r => r.id && (r as ITargetData).uniqueId);
                        allSubmitted = targets.length > 0 && targets.findIndex(r => !r.dateSubmitted) === -1;
                    } else {
                        // For progress: check if all activities are actually submitted (have dateSubmitted) in two-step process
                        const activities = this.data.filter(r => r.id && r['activityId'] && r['status'] != ActivityStatus.Archived);
                        allSubmitted = activities.length > 0 && activities.findIndex(r => !r.dateSubmitted) === -1;
                    }
                    
                    if (allSubmitted) {
                    this.submitStatus = 'submitted';
                    
                    // set to approved, if approved
                        if (this.data.findIndex(r => r.id && !r.dateApproved && 
                            (this.gridFilters.type === 'Targets' ? (r as ITargetData).uniqueId : r['activityId'])) === -1) {
                        this.submitStatus = 'approved';
                        }
                    }
                }

                // Goto the cell based on routeFragment set
                if (this.routeFragment.valid)
                    this.goToCell();

                if (this.pivotPanel)
                    this.pivotsComponent.onProfileSelect(this.selTabId);

                this.gridWorking = false;
            }
        }));
    }

    private checkEditPermissions(): void {
        if (!this.isAdmin && !this.isViewer && this.authPipe.transform(['gv','lv'])) {
            this.isViewer = true;
            this.gridComponent.defaultColDefs.suppressFillHandle = true;
        }

        if (this.isViewer) {
            if (this.authPipe.transform(['la', 'lde']) &&
                this.gridComponent.selectedVals.orgId === this.userOrgId) {
                this.isViewer = false;
                this.gridComponent.defaultColDefs.suppressFillHandle = false;
            }
        }
    }

    private goToCell(): void {
        this.gridComponent.gridApi.clearRangeSelection();

        let rowNode: IRowNode;
        let rowId = this.routeFragment.actId;

        if (this.routeFragment.tId > 0)
            rowId = this.routeFragment.tId;
        
        setTimeout(() => {
            this.gridComponent.gridApi.forEachNode(node => {
                if (node.data.id === rowId) {
                    rowNode = node;
                    return;
                }
            });

            if (rowNode) {
                const colMap = this.gridComponent.commentModalComponent.staticColMap
                    .find(c => c.dColId === +this.routeFragment.colId);
                if (colMap)
                    this.routeFragment.colId = colMap.colId;
                
                // fix colid due to grid's behavior
                const cols = this.gridComponent.gridColumnApi.getColumns();
                for (let i = 0; i < cols.length; i++) {
                    const colId = cols[i].getColId();

                    if (colId === this.routeFragment.colId ||
                        colId.startsWith(`${this.routeFragment.colId}_`)) {
                        this.routeFragment.colId = colId;
                        break;
                    }
                }

                // select cell
                this.gridComponent.gridApi.addCellRange({
                    columns: [this.routeFragment.colId],
                    rowStartIndex: rowNode.rowIndex,
                    rowEndIndex: rowNode.rowIndex
                });
                
                // make it visible on the screen, and flash
                setTimeout(() => {
                    this.gridComponent.gridApi.ensureColumnVisible(this.routeFragment.colId, 'middle');
                    this.gridComponent.gridApi.ensureIndexVisible(rowNode.rowIndex, 'middle');
                    this.gridComponent.gridApi.flashCells({
                        columns: [this.routeFragment.colId],
                        rowNodes: [rowNode], fadeDelay: 2000
                    });
                    
                    // if cell is commented, open comment
                    if (location.hash.startsWith('#comment')) {
                        const cellElem = document.querySelector(`[col-id="${this.routeFragment.colId}"][aria-selected="true"]`);
                        if (cellElem) {
                            const colDef = this.gridComponent.gridApi.getColumnDef(this.routeFragment.colId);
                            const cellRect = cellElem.getBoundingClientRect();
                            this.gridComponent.commentModalComponent.open(colDef, rowNode, cellRect, false, true, cellElem as HTMLElement);
                        }
                    }
                }, 100);
            }
        }, this.data.length * 2);

        this.routeFragment.valid = false;
        $('#partner').val(this.gridFilters.orgId).trigger('change.select2');
    }

    // bind save button with ctrl+s
    saveBound: any;
    private bindSave(): void {
        if (this.saveBound) return;

        this.saveBound = (e: KeyboardEvent) => {
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.onSave();
            }
        };

        document.addEventListener('keydown', this.saveBound, true);
    }

    pendingSave: boolean = false;
    async onSave(): Promise<boolean> {
        if (!this.pendingSave || this.saving) return;

        // Validate required fields before saving - only for targets, not for progress
        if (this.gridFilters.type === 'Targets' && !this.validateGridData()) {
            this.messageService.warning('Please fill out all the mandatory fields before saving.', 'Required fields');
            this.saving = false;
            return false;
        }

        const result = new Subject<boolean>();
        this.saving = true;

        // save only data marked changed
        const _data = this.gridComponent.rowData.filter(d => d.dirty);
        // track deleted rows by comparing the initial fetched data with the data already on grid having id > 0
        // i.e. grid rows won't include deleted rows but initial data would
        let deletedIds: number[] = [];
        for (let i = 0; i < this.data.length; i++) {
            for (let j = 0; j < this.gridComponent.rowData.length; j++) {
                if (this.gridComponent.rowData[j].id === this.data[i].id) {
                    if (!this.gridComponent.rowData[j]['activityId'] && !this.gridComponent.rowData[j]['uniqueId'])
                        deletedIds.push(this.data[i].id);
                }
            }
        }

        if (!_data.length && !deletedIds.length) {
            this.saving = false;
            this.pendingSave = false;
            return;
        }

        // save records
        let operation: Observable<OperationDataResult>;
        if (this.gridFilters.type === 'Progress') {
            operation = this.dataService.saveActivities(this.gridFilters.profId, this.gridFilters.project.id,
                this.month, this.year, _data as IProgressData[], deletedIds, this.gridFilters.orgId);
        } else {
            _data.forEach((r: ITargetData) => {
                r.qtr = +r.qtr;
                if (!r.qtr) r.qtr = null;
                r.year = r.year !== 0 && !r.year ? null : +r.year;
            });
            operation = this.dataService.saveTargets(this.gridFilters.profId,
                this.gridFilters.project.id, _data as ITargetData[], deletedIds, this.gridFilters.orgId);
        }

        let addedIds: (ITargetAdded | IActivityAdded)[] = [];
        this.subscriptions.push(operation.subscribe({
            next: (res) => {
                if (res && res.result === OperationResult.Succeeded)
                    addedIds = res.data;
            },
            error: (err) => {
                this.saving = false;
                console.log(err);
                result.next(false);
                result.complete();
            },
            complete: async () => {
                try {
                    // update ids from the server and set to clean
                    if (addedIds.length) {
                        if (this.gridFilters.type === 'Progress') {
                            const idsMapping = addedIds as IActivityAdded[];
                            this.gridComponent.rowData.forEach((row: IProgressData) => {
                                const added = idsMapping.find(i => i.activityId === row.activityId);
                                if (added)
                                    row.id = added.id;
                                row.dirty = false;
                            });
                        } else {
                            const idsMapping = addedIds as ITargetAdded[];
                            this.gridComponent.rowData.forEach((row: ITargetData) => {
                                const added = idsMapping.find(i => i.uniqueId === row.uniqueId);
                                if (added)
                                    row.id = added.id;
                                row.dirty = false;
                            });
                        }
                        this.submitStatus = null;
                    }

                    // delete from initial fetched data if data is deleted from the grid and server
                    if (deletedIds.length)
                        this.data = this.data.filter(d => !deletedIds.includes(d.id));

                    // For targets: Backend automatically marks new/updated targets for submission
                    if (this.gridFilters.type === 'Targets') {
                        // Update the local grid row data immediately to reflect auto-submission
                        this.gridComponent.rowData.forEach((row) => {
                            const targetRow = row as ITargetData;
                            const savedRow = (_data as ITargetData[]).find(d => d.uniqueId === targetRow.uniqueId);
                            if (savedRow && targetRow.uniqueId) { // This row was saved
                                targetRow.markedSubmitted = true;
                            }
                        });
                        
                        // Force grid refresh to show updated status
                        this.gridComponent.gridApi.refreshCells({ force: true });
                        
                        this.messageService.success('Targets have been saved and automatically submitted.', 'Targets Auto-Submitted', { newestOnTop: true });
                    } else if (this.gridFilters.type === 'Progress') {
                        // For progress: Backend marks for submission but doesn't auto-submit
                        this.gridComponent.rowData.forEach((row) => {
                            const progressRow = row as IProgressData;
                            const savedRow = (_data as IProgressData[]).find(d => d.activityId === progressRow.activityId);
                            if (savedRow && progressRow.activityId) { // This row was saved
                                progressRow.markedSubmitted = true;
                            }
                        });
                        
                        // Force grid refresh to show updated status
                        this.gridComponent.gridApi.refreshCells({ force: true });
                        
                        this.messageService.success('Progress has been saved and marked for submission.', 'Progress Saved', { newestOnTop: true });
                    } else {
                    this.messageService.success('The data have been saved successfully.', 'Data saved', { newestOnTop: true });
                    }
                    
                    // Refresh data from backend to ensure consistency
                    this.getData();

                    // REMOVED: No longer unmark submission on save - data stays submitted
                    // Previous logic that unmarked submission on save has been removed
                    // to maintain submitted status while allowing updates
                    this.gridComponent.invalidCells = [];
                    this.saving = false;
                    this.pendingSave = false;
                    result.next(true);
                    result.complete();
                } catch (ex) {
                    this.saving = false;
                    this.pendingSave = false;
                    result.next(false);
                    result.complete();
                    this.messageService.error('Something went wrong.');
                }
            }
        }));

        return await lastValueFrom(result.asObservable());
    }

    gridFilteredBy: string[] = ['-'];
    gridFiltered(col: string): void {
        if (col.startsWith('-')) {
            col = col.replace('-', '');
            this.gridFilteredBy = this.gridFilteredBy.filter(c => c !== col);
        } else if(!this.gridFilteredBy.includes(col))
            this.gridFilteredBy.push(col);

        this.gridFilteredBy[0] = this.gridFilteredBy
            .slice(1, this.gridFilteredBy.length).join(', ');
    }

    gridSortedBy: number = -1;
    enableGridSort(): void {
        let isSortable = false;
        if (this.gridSortedBy === -1) {
            isSortable = true;
            this.gridSortedBy = 0;
        } else {
            isSortable = false;
            this.gridSortedBy = -1;
        }
        this.gridComponent.gridColumnApi.getColumns().forEach(col => {
            const colId = col.getColId();
            if (!colId.startsWith('new') && !colId.startsWith('cols-hidden')) {
                let colDef = col.getColDef();
                colDef.sortable = isSortable;
                col.setColDef(colDef, col.getColDef());
            }
        });
        
        this.gridComponent.gridApi.refreshHeader();
        this.gridComponent.gridColumnApi.autoSizeAllColumns();
    }

    gridGroupedBy: number = -1;
    enableGridGroup(): void {
        let enableRowGroup = false;
        if (this.gridGroupedBy === -1) {
            enableRowGroup = true;
            this.gridComponent.gridApi.setRowGroupPanelShow('always');
            this.gridGroupedBy = 0;
        } else {
            enableRowGroup = false;
            this.gridComponent.gridApi.setRowGroupPanelShow('never');
            this.gridComponent.gridColumnApi.getRowGroupColumns().forEach(col => {
                this.gridComponent.gridColumnApi.removeRowGroupColumn(col.getColId());
            });
            this.gridGroupedBy = -1;
        }
        this.gridComponent.gridColumnApi.getColumns().forEach(col => {
            const colId = col.getColId();
            if (!colId.startsWith('new') && !colId.startsWith('cols-hidden')) {
                let colDef = col.getColDef();
                colDef.enableRowGroup = enableRowGroup;
                col.setColDef(colDef, col.getColDef());
            }
        });
    }

    private resetSortAndGroup(): void {
        if (this.gridSortedBy > -1) {
            this.gridComponent.defaultColDefs.sortable = false;
            this.gridSortedBy = -1;
        }

        if (this.gridGroupedBy > -1) {
            this.gridComponent.defaultColDefs.enableRowGroup = false;
            this.gridComponent.gridApi.setRowGroupPanelShow('never');
            this.gridComponent.gridColumnApi.getRowGroupColumns().forEach(col => {      // remove all group cols except status
                //if (col.getColDef().field !== 'status')
                this.gridComponent.gridColumnApi.removeRowGroupColumn(col.getColId());
            });
            this.gridGroupedBy = -1;
        }
    }

    public submitting: boolean = false;
    public submitStatus?: 'submitted' | 'approved';
    onSubmit(): void {  // Removed voidSubmission parameter
        this.submitting = true;
        let dataIds = [];

        // Only handle submission, no void submission logic
            dataIds = this.gridComponent.rowData.filter(r => !r.dateApproved && !r.markedSubmitted &&
                (r['uniqueId'] || (r['activityId'] && r['status'] != ActivityStatus.Archived)))        // Archived doesn't get submitted
                .map(r => r.id);

            if (!dataIds.length) {
                this.messageService.info('Nothing to submit.');
                this.submitting = false;
                return;
            }

            if (!this.validateGridData()) {
                this.messageService.warning('Please fill out all the mandatory fields.', 'Required fields');
                this.submitting = false;
                return;
        }

        let operation: Observable<any>;
        let month = this.month; let year = this.year;

         // Prepare data for the modal
        const modalData: any = {
            projId: this.gridFilters.project.id,
            dataType: this.gridFilters.type,
            period: month,
            year: year,
            
        };

      
        if (this.gridFilters.type === 'Progress') {
            // check if asOf date is the same for all records
            let sameDate: boolean = false; let draftPresent: boolean = false;
            const data = this.gridComponent.rowData.filter((r: IProgressData) => r.activityId &&
                r.status <= ActivityStatus.Completed && !r.dateApproved && !r.markedSubmitted) as IProgressData[];
            
            for (let i = 0; i < data.length; i++) {
                const asOfDate = data[i].asOf?.split('/');
                if (asOfDate && asOfDate[1] && asOfDate[2]) {
                    if (month !== +asOfDate[1] || year !== +asOfDate[2]) {
                        sameDate = false;
                        break;
                    } else {
                        month = +asOfDate[1];
                        year = +asOfDate[2];
                        sameDate = true;
                    }
                }
                // check if any draft is there
                if (data[i].draft)
                    draftPresent = true;
            }
            
            /*if (data.length && (!sameDate || draftPresent)) {
                this.messageService.warning("'As of' month should be same for all the activities. Please update the " +
                     "cummulative progress of all your activities for the selected month.", 'As of mismatch');
                this.submitting = false;
                return;
            }*/
            operation = this.dataService.markAtivities(dataIds, month, year, false, this.gridFilters.orgId);  // Always false for voidSubmission
        } else {
            operation = this.dataService.markTargets(dataIds, false, this.gridFilters.orgId);  // Always false for voidSubmission
             
            // Get all quarters and years from grid data
            const targetData = this.gridComponent.rowData
                .filter((r: ITargetData) => r.qtr && !r.dateApproved && !r.markedSubmitted)
                .map((r: ITargetData) => ({ qtr: r.qtr, year: r.year }));

            // Get the most frequent combination
            modalData.quarters = [...new Set(targetData.map(item => item.qtr))];
            modalData.years = [...new Set(targetData.map(item => item.year))];
            modalData.qtr = modalData.quarters[0];
        }
            
        let targetsNotExist = [];
        this.subscriptions.push(operation.subscribe({
            next: (res) => {
                if (res && res.length)
                    targetsNotExist = res;
            },
            error: (err) => {
                this.submitting = false;
            },
            complete: async () => {
                // Only handle submission, no void submission logic
                if (!targetsNotExist?.length) {
                    // DON'T mark as submitted yet - wait for actual submission in modal
                    // this.gridComponent.rowData.forEach(row => {
                    //     if (!row.dateApproved && !row.markedSubmitted && (row['uniqueId'] ||
                    //         (row['activityId'] && row['status'] != ActivityStatus.Archived)))
                    //         row.markedSubmitted = true;
                    // });
                    // this.submitStatus = 'submitted';
                    // this.messageService.success('The data have been marked for submission successfully.', 'Marked Submitted');

                    // Open SubmitDataComponent as a modal
            const screenWidth = window.innerWidth * 0.9; // 90% of screen width
            const screenHeight = window.innerHeight * 0.9; // 90% of screen height

            const dialogRef = this.dialog.open(SubmitDataComponent, {
                width: `${screenWidth}px`, // 90% of screen width
                height: `${screenHeight}px`, // 90% of screen height
                position: {
                   left: '10%' 
                },
                        panelClass: 'custom-modal',
                        data: modalData
                    });

                    dialogRef.afterClosed().subscribe(result => {
                        // If user actually submitted through the modal, result will contain submission data
                        if (result && result.submitted) {
                            // Now mark as submitted and update UI
                            this.gridComponent.rowData.forEach(row => {
                                if (!row.dateApproved && !row.markedSubmitted && (row['uniqueId'] ||
                                    (row['activityId'] && row['status'] != ActivityStatus.Archived)))
                                    row.markedSubmitted = true;
                            });
                            this.submitStatus = 'submitted';
                            this.messageService.success('The data have been marked for submission successfully.', 'Marked Submitted');
                        }
                        // If user closed without submitting, nothing changes - button stays as "Submit Progress"
                    });

                } else {
                    this.messageService.error('To mark for submission, you must first submit targets for the corresponding time periods and corresponding regions.', 'No Targets',
                        { closeButton: true, enableHtml: true, newestOnTop: false, disableTimeOut: true, tapToDismiss: true });
                }
                this.submitting = false;
            }
        }));
    }

    private validateGridData(): boolean {
        this.gridComponent.invalidCells = [];

        // validate required fields
        let allCols = this.gridComponent.gridColumnApi.getColumns();
        let colsRequired = [];

        for (let i = 0; i < allCols.length; i ++) {
            const colDef = allCols[i].getColDef();
            const colId = allCols[i].getColId();
            if (!colDef.field || colId === 'rowNum' || colId.startsWith('new') ||
                colDef.headerComponentParams?.colType == ColDataType.Checkbox// ||
                //colDef.headerComponentParams?.colType == ColDataType.Attachment
            ) continue;

            const isRequired = colDef.headerComponentParams?.isRequired;
            colsRequired.push({ id: colId, field: colDef.field, isRequired: isRequired });
        }

        if (this.gridFilters.type === 'Progress') {
            this.gridComponent.rowData.forEach((row: IProgressData) => {
                // for Progress, check only rows with OnGoing and Completed rows
                if (row.activityId && row.status <= ActivityStatus.Completed) { // row.id is also there by this time
                    colsRequired.forEach(col => {
                        // ensure endMonth is required for any Completed row
                        if (col.field === 'eMonth')
                            col.isRequired = row.status == ActivityStatus.Completed;

                        // col is required but data in the row is not entered
                        let rowField = row[col.field];
                        if (col.field.indexOf('.') > -1)
                            rowField = row.colVals[col.field.split('.')[1]];
                        if (col.isRequired && (rowField === undefined || rowField === '' || rowField === null)) {
                            // check if cell is disabled
                            this.gridComponent.gridApi.forEachNode(node => {
                                if (node.data.id === row.id) {
                                    const cell = this.gridComponent.gridColumnApi.getColumn(col.id);
                                    if (cell.isCellEditable(node)) {
                                        this.gridComponent.invalidCells.push({ colId: col.id, field: col.field, rowDataId: row.id });
                                        return;
                                    }
                                }
                            });
                        }
                    });
                }
            });
        } else {
            this.gridComponent.rowData.forEach((row: ITargetData) => {
                // Validate targets that have uniqueId OR have any data entered (new targets)
                if (row.uniqueId || row.dirty || row.region || row.province || row.district || row.year || row.qtr || 
                    (row.colVals && Object.keys(row.colVals).some(key => row.colVals[key] !== null && row.colVals[key] !== ''))) {
                    colsRequired.forEach(col => {
                        // col is required but data in the row is not entered
                        let rowField = row[col.field];
                        if (col.field.indexOf('.') > -1)
                            rowField = row.colVals[col.field.split('.')[1]];
                        if (col.isRequired && (rowField === undefined || rowField === '' || rowField === null)) {
                            // check if cell is disabled
                            this.gridComponent.gridApi.forEachNode(node => {
                                if (node.data.id === row.id) {
                                    if (this.gridComponent.gridColumnApi.getColumn(col.id).isCellEditable(node)) {
                                        this.gridComponent.invalidCells.push({ colId: col.id, field: col.field, rowDataId: row.id });
                                        return;
                                    }
                                }
                            });
                        }
                    });
                }
            });
        }

        if (this.gridComponent.invalidCells.length) {
            let nodesValidated = [];
            this.gridComponent.gridApi.forEachNode(node => {
                this.gridComponent.invalidCells.forEach(cell => {
                    if (node.data.id === cell.rowDataId)
                        nodesValidated.push(node);
                });
            });

            this.gridComponent.gridApi.refreshCells({
                columns: this.gridComponent.invalidCells.map(c => c.colId),
                rowNodes: nodesValidated, force: true, suppressFlash: true
            });

            return false;
        }
        return true;
    }

    onDownload(): void {
        //if (!this.data?.length) {
        //    this.messageService.info('Nothing to download.');
        //    return;
        //}
        this.downloading = true;
        this.gridComponent.gridApi.exportDataAsExcel();
        this.downloading = false;
    }

    /** Pivot */
    private pivotsComponent: PivotsComponent;
    onPivotsInitd(comp: PivotsComponent): void {
        this.pivotsComponent = comp;
        if (this.pivotPanel) {
            this.pivotsComponent.onProfileSelect(this.selTabId);
            this.pivotsComponent.selProjId = this.gridFilters.project?.id || 0;

            if (this.allOrgs.length)
                this.pivotsComponent.orgs = this.allOrgs;
        }
    }

    onPivot(size?: 'sm' | 'lg'): void {
        if (this.pivotPanel && !size)
            this.pivotPanel = null;
        else {
            this.pivotPanel = size || (localStorage.getItem('pivotPanelSize') ||'lg');
            localStorage.setItem('pivotPanelSize', this.pivotPanel);
        }

        if (this.pivotPanel) {
            // refresh tabs
            if (this.btnAllTabs)
                this.createProfileTabs(this.selTabId || null);
            
            if (!this.allOrgs.length)
                this.getOrganizations();
        }
    }

    onSubmissionLog(): void {
        const modalData = {
            dataType: this.gridFilters.type
        };

        const screenWidth = window.innerWidth * 0.9; // 90% of screen width
        const screenHeight = window.innerHeight * 0.9; // 90% of screen height

        const dialogRef = this.dialog.open(SubmissionLogComponent, {
            width: `${screenWidth}px`,
            height: `${screenHeight}px`,
            position: {
               left: '10%' 
            },
            panelClass: 'custom-modal',
            data: modalData
        });
    }

    @HostListener('window:beforeunload')
    canDeactivate(): Observable<boolean> | boolean {
        if (!this.pendingSave || !this.gridFilters.project) return true; 
        return confirm("There are unsaved changes that require saving. Do you want to discard the changes?");
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());

        if (this.toolbarBgColor && this.appHeader)
            this.appHeader.classList.remove(this.toolbarBgColor);
    }
}