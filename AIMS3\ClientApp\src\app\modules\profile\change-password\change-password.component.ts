import { Compo<PERSON>, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Subscription } from 'rxjs';
import { MessageService } from '../../../shared/services/message.service';
import { AuthService, User } from '../../auth';
import { ChangePassword } from '../../auth/models/password.model';
import { AuthHTTPService } from '../../auth/services/auth-http';

@Component({
    selector: 'change-password',
    templateUrl: './change-password.component.html',
    styleUrls: ['../profile.component.scss']
})
export class ChangePasswordComponent implements OnInit, OnDestroy {
    working: boolean = false;

    form: FormGroup;

    private subscriptions: Subscription[] = [];
    constructor(
        private authService: AuthService,
        private userService: AuthHTTPService,
        private messageService: MessageService
    ) { }

    ngOnInit(): void {
        this.initForm();
    }

    // convenient getter for easy access to form fields
    get f() {
        return this.form.controls;
    }

    initForm() {
        this.form = new FormGroup({
            currentPass: new FormControl('', Validators.required),
            password: new FormControl('', [Validators.required, Validators.minLength(5)]),
            cPassword: new FormControl('', Validators.required)
        });
    }

    change() {
        if (!this.authService.currentUserValue) {
            this.messageService.error('Something went wrong. Pleaser re-login and try to change your password.');
            return;
        }

        this.working = true;

        let model = new ChangePassword();
        model.userName = this.authService.currentUserValue.username;
        model.currentPassword = this.f.currentPass.value;
        model.newPassword = this.f.cPassword.value;

        // update the password
        this.subscriptions.push(
            this.userService.changePassword(model).subscribe({
                error: (err) => {
                    this.working = false;
                    console.log(err);
                },
                complete: () => {
                    this.working = false;
                    this.messageService.success('Your password has been changed. You are now logged out. Please login again.');
                    this.authService.logout();
                }
            })
        );
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}