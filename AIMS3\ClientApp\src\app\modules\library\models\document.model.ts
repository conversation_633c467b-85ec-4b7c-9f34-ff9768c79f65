import { ISubmitProfile } from "../../data/models/submit-log.model";

export interface IDoc {
    id: number;
    docName: string;
    docTypeId?: number;
    activityIds?: any[];         // activity unique ids
    profile?: ISubmitProfile;
    targetPeriod?: string;
    columnName?: string;
    docSize?: number;
    fileName?: string;
    docDesc?: string;
    fieldsWithValues?: FieldValue[];
    docTypeName?: string;
    createdBy?: string;
    dateCreated?: Date;
    modifiedBy?: string;
    dateModified?: Date;
    dateApproved?: Date;
    orgId?: number;
    orgName?: string;
    columnId?: number;
    targetId?: number;
    isLink?: boolean;
}

export class Doc implements IDoc {
    constructor(public id: number,
        public docName: string,
        public fileName: string,
        public docDesc?: string,
        public activityIds?: any[],
        public targetId?: number,
        public dynamicColumnId?: number,
        public docTypeId?: number,
        public fieldsWithValues?: FieldValue[],
        public organizationId?: number,
        public isLink?: boolean
    ) { }
}

export class FieldValue {
    constructor(
        public fieldId: number,
        public value: string
    ) { }
}