<app-modal #modal [modalConfig]="modalConfig">
    <aims-working *ngIf="working"></aims-working>
    <!-- Data filters: Project, Partner; and data type toggle -->
    <div class="modal-toolbar justify-content-end" *ngIf="!isAdmin else editPivot">
        <div class="d-flex flex-stack gap-2 me-10" style="width:35%;" *ngIf="!pivot.id">
            <span class="text-gray-600 fs-7 fw-semibold ms-4">{{ profName }}</span>
            <select id="pivotDataType" class="form-select form-select-sm" data-control="select2" data-hide-search="true"
                    data-width="140px">
                <option value="Progress" selected>Progress</option>
                <option value="Targets">Targets</option>
            </select>
        </div>
        <div class="d-flex flex-stack gap-2">
            <span class="fs-7 text-gray-800">Data</span>
            <filter-ddl [id]="'projIds'" #project class="filter-container w-150px" [placeholders]="['Project']" [minWidth]="130"
                        [options]="projects" [selectedValues]="dataFilters.projIds" (change)="onFilterChange($event)" [multiple]="true">
            </filter-ddl>
            <filter-ddl [id]="'orgIds'" #partner class="filter-container w-150px" [placeholders]="['Partner']" [minWidth]="130"
                        [options]="orgs" (change)="onFilterChange($event)" [multiple]="true" *ngIf="(['ga','gv'] | isAuth)">
            </filter-ddl>
            <button type="button" class="btn btn-sm btn-icon btn-light btn-active-color-primary px-3" ngbTooltip="Load data"
                    [disabled]="modalConfig.working" (click)="getData()">
                <i class="fa fa-search"></i>
            </button>
            <div class="bullet bg-secondary h-35px w-1px mx-4"></div>
        </div>
    </div>
    <ng-template #editPivot>
        <div class="modal-toolbar justify-content-between">
            <div class="d-flex flex-stack gap-2 me-10" style="width:40%;">
                <input name="pvtName" class="form-control form-control-sm fw-bold fs-5" type="text" placeholder="Pivot name" [(ngModel)]="pivot.name"
                       [ngClass]="{ 'is-invalid': !pivot.name || exists }" maxlength="256" (click)="exists=false" />
                <div *ngIf="!pivot.id">
                    <select id="pivotDataType" class="form-select form-select-sm" data-control="select2" data-hide-search="true"
                            data-width="140px">
                        <option value="Progress" selected>Progress</option>
                        <option value="Targets">Targets</option>
                    </select>
                </div>
                <span class="text-gray-600 fs-7 fw-semibold ms-4">{{ profName }}</span>
                <span class="text-info fs-7 fw-semibold px-2" *ngIf="pivot.id && pivot.isTarget">Targets</span>
                <span class="text-primary fs-7 fw-semibold px-2" *ngIf="pivot.id && !pivot.isTarget">Progress</span>
            </div>
            <div class="d-flex flex-stack gap-2">
                <span class="fs-7 text-gray-800">Data</span>
                <filter-ddl [id]="'projIds'" class="filter-container w-150px" [placeholders]="['Project']" [minWidth]="130"
                            [options]="projects" [selectedValues]="dataFilters.projIds" (change)="onFilterChange($event)" [multiple]="true">
                </filter-ddl>
                <filter-ddl [id]="'orgIds'" class="filter-container w-150px" [placeholders]="['Partner']" [minWidth]="130"
                            [options]="orgs" (change)="onFilterChange($event)" [multiple]="true">
                </filter-ddl>
                <button type="button" class="btn btn-sm btn-icon btn-light btn-active-color-primary px-3" ngbTooltip="Load data"
                        [disabled]="modalConfig.working" (click)="getData()">
                    <i class="fa fa-search"></i>
                </button>
                <div class="bullet bg-secondary h-35px w-1px mx-4"></div>
            </div>
        </div>
    </ng-template>

    <!-- AIMS Pivot Grid -->
    <aims-pivot-grid></aims-pivot-grid>
</app-modal>