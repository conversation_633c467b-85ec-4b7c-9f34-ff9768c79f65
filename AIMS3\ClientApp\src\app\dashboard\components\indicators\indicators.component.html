<aims-loading *ngIf="loading"></aims-loading>
<div class="card card-custom card-stretch" [ngClass]="{'d-none':loading}">
    <!-- begin::Header -->
    <div class="card-header">
        <div class="card-title" style="max-width:80%;">
            <!-- <ng-container *ngIf="isAdmin"> -->
            <ul class="nav nav-tabs nav-line-tabs d-flex flex-md-nowrap fs-6 border-0" role="tablist">
                <li class="nav-item" role="presentation" (click)="onSelTab('formula')">
                    <a role="tab" class="nav-link justify-content-center text-active-gray-800 text-hover-gray-800"
                        [ngClass]="{ 'active': selTab === 'formula' }" tabindex="-1">Formula</a>
                </li>
                <li class="nav-item" role="presentation" (click)="onSelTab('performance')">
                    <a role="tab" class="nav-link justify-content-center text-active-gray-800 text-hover-gray-800"
                        [ngClass]="{ 'active': selTab === 'performance' }">Performance</a>
                </li>
            </ul>
            <div class="bullet bg-secondary h-35px w-1px mx-4"></div>
            <!-- </ng-container> -->
            <!-- Filters -->
            <div class="d-flex scroll-x" style="overflow-y: hidden; margin-bottom: -5px;">
                <filter-ddl [id]="'groups'" #group class="filter-container" [placeholders]="['Project group']" [minWidth]="130"
                            [options]="projGroups" (change)="onFilterChange($event)" [multiple]="true" ngbTooltip="Project group">
                </filter-ddl>
                <filter-ddl [id]="'types'" #type class="filter-container ms-2" [placeholders]="['Indicator type']" [minWidth]="130"
                            [options]="types" (change)="onFilterChange($event)" [multiple]="true" ngbTooltip="Indicator type">
                </filter-ddl>
                <div class="d-flex" [ngClass]="{ 'd-none': selTab === 'formula' }">
                    <div class="bullet bg-secondary h-35px w-1px mx-4"></div>
                    <!-- <filter-ddl [id]="'projIds'" #project class="filter-container" [placeholders]="['Project']" [minWidth]="130"
                                [options]="projects" (change)="onFilterChange($event)" [multiple]="true">
                    </filter-ddl>-->
                    <filter-ddl [id]="'orgIds'" #partner class="filter-container" [placeholders]="['Partner']" [minWidth]="130"
                                [options]="orgs" (change)="onFilterChange($event)" [multiple]="true">
                    </filter-ddl>
                    <filter-ddl [id]="'dataStatus'" #status class="filter-container ms-2" [placeholders]="['Data type', 'Types']" [minWidth]="130"
                                [options]="submitStatus" [showSearch]="false" [multiple]="true" [selectedValues]="[1,2]" (change)="onFilterChange($event)">
                    </filter-ddl>
                    <filter-ddl [id]="'regions'" #region class="filter-container ms-2 me-3" [placeholders]="['Region']" [minWidth]="130"
                                [options]="regions" [multiple]="true" (change)="onFilterChange($event)">
                    </filter-ddl>
                    <div class="d-flex flex-stack gap-2">
                        <span class="fs-7 text-gray-800">From</span>
                        <input id="periodFrom" type="month" class="form-control form-control-sm w-100px ms-1" (change)="onPeriodChange($event)"
                               ngbTooltip="Period: From date" placement="left" min="2020-01" />
                        <span class="fs-7 text-gray-800">To</span>
                        <input id="periodTo" type="month" class="form-control form-control-sm w-100px ms-1" (change)="onPeriodChange($event)"
                               ngbTooltip="Period: To date" placement="left" min="2020-01" />
                    </div>
                </div>
            </div>
            <button class="btn btn-sm btn-light-primary border border-primary py-2 px-3 ms-2"
                    role="button" (click)="onFilterData()">Filter
            </button>
            <a class="cursor-pointer fs-8 ms-3" (click)="resetFilters()" *ngIf="filtered">Reset</a>
        </div>
        <div class="card-toolbar">
            <!--begin::Search-->
            <!-- <div class="d-flex align-items-center position-relative me-3" *ngIf="!isAdmin">
                <span [inlineSVG]="'./assets/media/icons/duotune/general/gen021.svg'" class="svg-icon svg-icon-2 position-absolute ms-3"></span>
                <input type="search" class="form-control form-control-sm form-control-solid w-150px ps-12"
                       placeholder="Search" (search)="onSearch($event)" (keyup)="onSearch($event)" />
            </div> -->
            <!-- Add New -->
            <button id="btnAdd" type="button" class="btn btn-sm btn-primary px-3 py-2 me-2" (click)="onAdd()" *ngIf="isAdmin">
                <span [inlineSVG]="'./assets/media/icons/duotune/arrows/arr075.svg'" class="svg-icon svg-icon-2"></span>
                Add new
            </button>
            <!-- Download -->
            <button class="btn btn-sm btn-bg-light btn-active-color-primary px-3" (click)="onDownloadTable()">
                <i class="fas fa-download"></i> Download
            </button>
        </div>
    </div>
    <!-- begin::Body -->
    <div class="card-body">
        <aims-loading *ngIf="working" [tableOnly]="true"></aims-loading>
        <div class="table-responsive" [ngClass]="{'d-none':working}">
            <table id="table_indicators" class="table table-sm table-hover fs-8 table-row-dashed align-middle">
                <thead>
                    <tr class="text-start text-gray-900 fw-bold gs-0">
                        <th data-orderable="false" width="3%"></th>
                        <th class="d-none"></th>
                        <th width="7%">Project group</th>
                        <th width="10%">Indicator type</th>
                        <th>Indicator</th>
                        <th width="7%">Target</th>
                        <th width="10%">Target Disagg.</th>
                        <th width="7%">Progress</th>
                        <th width="10%">Progress Disagg.</th>
                        <th width="7%" class="text-end" *ngIf="isAdmin"></th>
                    </tr>
                </thead>
                <tbody class="text-gray-800">
                    <tr *ngFor="let ind of indicators; let i = index;">
                        <td class="text-center"></td>
                        <td class="d-none">{{ i+1 }}</td>
                        <td class="text-gray-700" [attr.data-order]="ind.projectGroups" [attr.data-content-id]="ind.typeName+ind.projectGroups">
                            {{ ind.projectGroups }}
                        </td>
                        <td class="text-gray-700" [attr.data-order]="ind.typeName" [attr.data-content-id]="ind.typeName">
                            <p class="mb-2">{{ ind.typeName }}</p>
                            <p class="text-muted fs-9 m-0" *ngIf="ind.filtersApplied" [innerHtml]="ind.filtersApplied"></p>
                        </td>
                        <td [innerHtml]="ind.name | nlHtml"></td>
                        <ng-container *ngIf="selTab !== 'formula' else tdFormulas">
                            <td [attr.data-order]="ind.value1">
                                <div class="d-flex flex-stack">
                                    <span class="flex-start" *ngIf="ind.value1">{{ ind.f1Info }}</span>
                                    <span class="flex-end fs-7">{{ ind.value1 | formatNum }}</span>
                                </div>
                            </td>
                            <td [attr.data-order]="ind.value2">
                                <div class="d-flex flex-stack">
                                    <span class="flex-start" *ngIf="ind.value2">{{ ind.f2Info }}</span>
                                    <span class="flex-end fs-7">{{ ind.value2 | formatNum }}</span>
                                </div>
                            </td>
                            <td [attr.data-order]="ind.value3">
                                <div class="d-flex flex-stack">
                                    <span class="flex-start" *ngIf="ind.value3">{{ ind.f3Info }}</span>
                                    <span class="flex-end fs-7">{{ ind.value3 | formatNum }}</span>
                                </div>
                            </td>
                            <td [attr.data-order]="ind.value4">
                                <div class="d-flex flex-stack">
                                    <span class="flex-start" *ngIf="ind.value4">{{ ind.f4Info }}</span>
                                    <span class="flex-end fs-7">{{ ind.value4 | formatNum }}</span>
                                </div>
                            </td>
                        </ng-container>
                        <!-- Formulas -->
                        <ng-template #tdFormulas>
                            <td><code *ngIf="ind.f1Text">{{ ind.f1Text }}</code><span class="text-muted" *ngIf="!ind.f1Text">N/A</span></td>
                            <td><code>{{ ind.f2Text }}</code></td>
                            <td><code *ngIf="ind.f3Text">{{ ind.f3Text }}</code><span class="text-muted" *ngIf="!ind.f3Text">N/A</span></td>
                            <td><code>{{ ind.f4Text }}</code></td>
                        </ng-template>
                        <th class="text-end p-0" *ngIf="isAdmin">
                            <!-- <button class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary h-25px w-30px" (click)="onAdd(ind.order)" ngbTooltip="Add new after this">
                                <span [inlineSVG]="'./assets/media/icons/duotune/arrows/arr075.svg'" class="svg-icon svg-icon-4"></span>
                            </button> -->
                            <button class="btn btn-sm btn-icon btn-bg-light btn-active-color-primary h-25px w-30px" (click)="onEdit(ind)" ngbTooltip="Edit">
                                <span [inlineSVG]="'./assets/media/icons/duotune/general/gen055.svg'" class="svg-icon svg-icon-4"></span>
                            </button>
                            <button class="btn btn-sm btn-icon btn-bg-light btn-active-color-danger h-25px w-30px ms-2" (click)="onDelete(ind.id)" ngbTooltip="Delete">
                                <span [inlineSVG]="'./assets/media/icons/duotune/general/trash.svg'" class="svg-icon svg-icon-4"></span>
                            </button>
                        </th>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Indicator Form Modal -->
<indicator-form-modal [types]="types" (done)="onDone($event)"></indicator-form-modal>