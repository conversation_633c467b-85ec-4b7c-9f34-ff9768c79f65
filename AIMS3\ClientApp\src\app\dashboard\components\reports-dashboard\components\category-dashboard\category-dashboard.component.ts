import { Component, Input, OnChang<PERSON>, OnInit, <PERSON><PERSON>hang<PERSON>, <PERSON><PERSON><PERSON>roy, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { 
  ReportData, 
  ReportFilter,
  Output as ReportOutput,
  Intervention,
  DynamicColumnData
} from '../../models/reports.model';
import { ReportsService } from '../../services/reports.service';
import { HttpClient } from '@angular/common/http';
import { Observable, of } from 'rxjs';
import { catchError } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { MatDialog } from '@angular/material/dialog';
import { CategoryDetailsDialogComponent } from '../category-details-dialog.component';
import { CategoryModalComponent } from '../category-modal/category-modal.component';

// Define interfaces for our component
interface CategoryIndicator {
  id: string;
  name: string;
  value: number;
  icon: string;
}

interface Category {
  id: string;
  code: string;
  name: string;
  outputId: string;
  description: string;
  interventions: any[];
  totalBudget: number;
  totalCashDistributed?: number;
  totalBeneficiaries: number;
  progress: number;
  indicators: CategoryIndicator[];
  totalActivities?: number;
  projectIds?: string[];
}

interface CategoryStatisticsResponse {
  categories: {
    id: number;
    code: string;
    name: string;
    output: string;
    budget: number;
    cashDistributed: number;
    beneficiaries: number;
    progress: number;
    activityCount: number;
    indicators: { [key: string]: number };
  }[];
  totalBudget: number;
  totalSpent: number;
  totalBeneficiaries: number;
  averageProgress: number;
  activitiesByStatus: { [key: string]: number };
}

@Component({
  selector: 'app-category-dashboard',
  standalone: true,
  imports: [CommonModule, FormsModule, CategoryModalComponent],
  templateUrl: './category-dashboard.component.html',
  styleUrls: ['./category-dashboard.component.scss']
})
export class CategoryDashboardComponent implements OnInit, OnChanges, OnDestroy {
  @Input() reportData: ReportData;
  @Input() filters: ReportFilter;
  @Input() fullReport: any;
  @Input() filteredData: any; // New input for processed hierarchical data
  
  // Selected data
  selectedOutput: ReportOutput | null = null;
  selectedCategory: Category | null = null;
  selectedIntervention: Intervention | null = null;
  
  // For the new tabbed interface
  categoryDetailTab: 'interventions' | 'indicators' | 'reports' = 'interventions';
  
  // For infrastructure-specific mockups
  mockSelectedInterventionId: string | null = null;
  selectedIndicatorId: string | null = null;
  
  // Activity status filter
  activityStatusFilter = {
    ongoing: true,
    completed: true,
    archived: true,
    cancelled: false
  };
  
  categories: Category[] = [];
  filteredCategories: Category[] = [];
  interventions: Intervention[] = [];
  indicators: any[] = [];
  loading: boolean = false;
  error: string | null = null;
  
  // Category modal state
  categoryModalVisible: boolean = false;
  selectedCategoryForModal: string = '';
  categoryDynamicColumns: DynamicColumnData[] = [];
  categoryActivities: any[] = [];
  
  private apiUrl = environment.apiUrl || '/api';
  
  @Output() filterChange = new EventEmitter<ReportFilter>();
  
  constructor(private reportsService: ReportsService, private http: HttpClient, private dialog: MatDialog) { }

  ngOnInit(): void {
    // Load categories if we have data from the parent component
    // Prioritize filteredData over fullReport since it contains processed hierarchical data
    if (this.filteredData || this.fullReport) {
      this.loadCategories();
    }
  }
  
  ngOnChanges(changes: SimpleChanges): void {
    console.log('🔍 CategoryDashboard: ngOnChanges triggered with changes:', Object.keys(changes));
    
    if (changes.reportData?.currentValue) {
      console.log('🔍 CategoryDashboard: reportData changed:', this.reportData);
      // Reset selected items
      this.selectedOutput = null;
      this.selectedCategory = null;
      this.selectedIntervention = null;
      this.mockSelectedInterventionId = null;
      this.selectedIndicatorId = null;
      this.resetActivityStatusFilter();
      
      this.loadCategories();
    }

    if (changes['fullReport']?.currentValue) {
      console.log('🔍 CategoryDashboard: fullReport changed:', {
        hasFullReport: !!this.fullReport,
        categoriesCount: this.fullReport?.categories?.length || 0,
        projectsCount: this.fullReport?.projects?.length || 0,
        interventionProfilesCount: this.fullReport?.interventionProfiles?.length || 0,
        activitiesCount: this.fullReport?.activities?.length || 0
      });
      this.loadCategories();
    }

    if (changes['filteredData']?.currentValue) {
      console.log('🔍 CategoryDashboard: filteredData changed:', {
        hasFilteredData: !!this.filteredData,
        categoriesCount: this.filteredData?.categories?.length || 0,
        projectsCount: this.filteredData?.projects?.length || 0,
        interventionProfilesCount: this.filteredData?.interventionProfiles?.length || 0,
        activitiesCount: this.filteredData?.activities?.length || 0,
        quarterlyPeriod: this.filteredData?.quarterlyPeriod,
        totalBudget: this.filteredData?.totalBudget
      });
      this.loadCategories();
    }

    if (changes['filters']?.currentValue) {
      console.log('🔍 CategoryDashboard: filters changed:', {
        newFilters: this.filters,
        hasProjectGroups: this.filters?.projectGroups?.length > 0,
        hasProjects: this.filters?.projects?.length > 0,
        hasActivityStatus: this.filters?.activityStatus?.length > 0
      });
      this.loadCategories();
    }
  }
  
  ngOnDestroy(): void {
    // Dispose of chart resources when component is destroyed
  }
  
  selectOutput(output: ReportOutput): void {
    this.selectedOutput = output;
    this.selectedCategory = null;
    this.selectedIntervention = null;
    this.mockSelectedInterventionId = null;
    this.selectedIndicatorId = null;
    // We don't need to call this method anymore as we've converted the chart rendering
    // this.initializeCategoryProgressChart();
  }
  
  selectCategory(category: Category): void {
    this.selectedCategory = category;
    this.selectedIntervention = null;
    this.indicators = [];
    this.categoryDetailTab = 'interventions';
  }
  
  selectCategoryByCode(code: string): void {
    // Find the category by its code in all outputs
    if (!this.reportData || !this.reportData.outputs) return;
    
    for (const output of this.reportData.outputs) {
      if (output.categories) {
        const category = output.categories.find(cat => cat.code === code);
        if (category) {
          this.selectedOutput = output;
          this.selectedCategory = category as unknown as Category;
          this.selectedIntervention = null;
          this.mockSelectedInterventionId = null;
          this.selectedIndicatorId = null;
          // Reset to interventions tab when selecting a new category
          this.categoryDetailTab = 'interventions';
          return;
        }
      }
    }
    
    // If no match found, create a mock category for demonstration
    const [outputCode, categoryCode] = code.split('.');
    
    if (!this.reportData.outputs.find(o => o.code === outputCode)) {
      // Mock output if needed
      const mockOutput: ReportOutput = {
        id: outputCode,
        code: outputCode,
        name: `Output ${outputCode}`,
        description: '',
        totalBudget: 0,
        totalCashDistributed: 0,
        progress: 0,
        categories: []
      };
      
      this.selectedOutput = mockOutput;
    } else {
      this.selectedOutput = this.reportData.outputs.find(o => o.code === outputCode);
    }
    
    // Create mock category
    const mockCategory: Category = {
      id: code,
      code: code,
      outputId: outputCode,
      name: this.getCategoryNameByCode(code),
      description: '',
      totalBudget: 0,
      totalCashDistributed: 0,
      totalBeneficiaries: 0,
      progress: 0,
      interventions: [],
      indicators: [],
      totalActivities: 0
    };
    
    this.selectedCategory = mockCategory;
    this.selectedIntervention = null;
    this.mockSelectedInterventionId = null;
    this.selectedIndicatorId = null;
    // Reset to interventions tab when selecting a new category
    this.categoryDetailTab = 'interventions';
  }
  
  selectIntervention(intervention: Intervention): void {
    this.selectedIntervention = intervention;
  }
  
  selectMockIntervention(id: string, name: string): void {
    this.mockSelectedInterventionId = id;
    this.selectedIntervention = null;
  }
  
  selectIndicator(id: string): void {
    this.selectedIndicatorId = id;
  }
  
  getMockInterventionName(): string {
    if (this.mockSelectedInterventionId === 'public-infra') return 'Public Infrastructure';
    if (this.mockSelectedInterventionId === 'productive-facility') return 'Productive Facility';
    // Additional mock intervention names...
    return '';
  }
  
  getMockInterventionBudget(): number {
    if (this.mockSelectedInterventionId === 'public-infra') return 5200000;
    if (this.mockSelectedInterventionId === 'productive-facility') return 3800000;
    // Additional mock budgets...
    return 0;
  }
  
  getMockInterventionExpenditure(): number {
    if (this.mockSelectedInterventionId === 'public-infra') return 3380000;
    if (this.mockSelectedInterventionId === 'productive-facility') return 1596000;
    // Additional mock expenditures...
    return 0;
  }
  
  getMockInterventionProgress(): number {
    if (this.mockSelectedInterventionId === 'public-infra') return 65;
    if (this.mockSelectedInterventionId === 'productive-facility') return 42;
    // Additional mock progress...
    return 0;
  }
  
  getSelectedIndicatorName(): string {
    if (this.selectedIndicatorId === 'structures') return 'Structures';
    if (this.selectedIndicatorId === 'households') return 'Households';
    // Additional indicator names...
    return '';
  }
  
  resetActivityStatusFilter(): void {
    this.activityStatusFilter = {
      ongoing: true,
      completed: true,
      archived: true,
      cancelled: false
    };
    this.applyActivityStatusFilter();
    this.applyLocalActivityStatusFilter();
  }
  
  /**
   * Handle activity status filter changes
   */
  onActivityStatusFilterChange(): void {
    console.log('🔍 CategoryDashboard: Activity status filter changed:', this.activityStatusFilter);
    
    // Apply server-side filtering (this will trigger API call and eventual update via ngOnChanges)
    this.applyActivityStatusFilter();
    
    // Also apply immediate local filtering for better UX
    this.applyLocalActivityStatusFilter();
  }
  
  /**
   * Select all activity statuses
   */
  selectAllActivityStatuses(): void {
    this.activityStatusFilter = {
      ongoing: true,
      completed: true,
      archived: true,
      cancelled: true
    };
    this.applyActivityStatusFilter();
    this.applyLocalActivityStatusFilter();
  }
  
  /**
   * Clear all activity statuses
   */
  clearAllActivityStatuses(): void {
    this.activityStatusFilter = {
      ongoing: false,
      completed: false,
      archived: false,
      cancelled: false
    };
    this.applyActivityStatusFilter();
    this.applyLocalActivityStatusFilter();
  }
  
  /**
   * Get count of selected activity statuses
   */
  getSelectedActivityStatusCount(): number {
    return Object.values(this.activityStatusFilter).filter(status => status).length;
  }
  
  /**
   * Get array of selected activity status names
   */
  getSelectedActivityStatuses(): string[] {
    const statuses: string[] = [];
    if (this.activityStatusFilter.ongoing) statuses.push('Ongoing');
    if (this.activityStatusFilter.completed) statuses.push('Completed');
    if (this.activityStatusFilter.archived) statuses.push('Archived');
    if (this.activityStatusFilter.cancelled) statuses.push('Cancelled');
    return statuses;
  }
  
  /**
   * Apply activity status filter to categories
   */
  private applyActivityStatusFilter(): void {
    console.log('🔍 CategoryDashboard: applyActivityStatusFilter called');
    console.log('🔍 CategoryDashboard: Current state:', {
      categoriesCount: this.categories.length,
      activityStatusFilter: this.activityStatusFilter,
      selectedStatusCount: this.getSelectedActivityStatusCount()
    });
    
    // Convert frontend filter to string array for ReportFilter interface
    const selectedStatusStrings: ('ongoing' | 'completed' | 'archived' | 'cancelled')[] = [];
    if (this.activityStatusFilter.ongoing) selectedStatusStrings.push('ongoing');
    if (this.activityStatusFilter.completed) selectedStatusStrings.push('completed');
    if (this.activityStatusFilter.archived) selectedStatusStrings.push('archived');
    if (this.activityStatusFilter.cancelled) selectedStatusStrings.push('cancelled');

    console.log('🎯 CategoryDashboard: Selected status strings for frontend filter:', selectedStatusStrings);
    
    // Create updated filters with string values for ReportFilter interface
    const updatedFilters = {
      ...this.filters,
      activityStatus: selectedStatusStrings  // Use string values for ReportFilter interface
    };
    
    console.log('🔄 CategoryDashboard: Sending updated filters to parent component:', updatedFilters);
    
    // Emit filter change to parent so it reloads data from hierarchy endpoint
    this.filterChange.emit(updatedFilters);
    
    // REMOVED: Don't call loadCategories() immediately - let ngOnChanges handle it
    // when the parent updates filteredData with the new API response
    // The parent will update filteredData, which will trigger ngOnChanges, which will call loadCategories()
    console.log('🔄 CategoryDashboard: Filter emitted to parent - waiting for filteredData update');
  }
  
  /**
   * Recalculate category totals based on filtered activities
   */
  private recalculateCategoryTotals(category: Category): Category {
    // Map filter statuses to activity status numbers
    const statusMapping = {
      ongoing: 0,
      completed: 1,
      archived: 2,
      cancelled: 3
    };

    const allowedStatuses: number[] = [];
    if (this.activityStatusFilter.ongoing) allowedStatuses.push(statusMapping.ongoing);
    if (this.activityStatusFilter.completed) allowedStatuses.push(statusMapping.completed);
    if (this.activityStatusFilter.archived) allowedStatuses.push(statusMapping.archived);
    if (this.activityStatusFilter.cancelled) allowedStatuses.push(statusMapping.cancelled);

    console.log(`🎯 Status filter for ${category.code}:`, {
      allowedStatuses,
      filterState: this.activityStatusFilter
    });

    // Find all activities for this category from the full report
    let categoryActivities = this.getCategoryActivitiesFromFullReport(category.id);
    
    // DEBUGGING: Add some mock archived activities if we don't have any real archived data
    if (this.activityStatusFilter.archived && categoryActivities.length > 0) {
      const mockArchivedActivities = this.createMockArchivedActivities(category.id, categoryActivities.length);
      console.log(`📊 Adding ${mockArchivedActivities.length} mock archived activities for testing`);
      categoryActivities = [...categoryActivities, ...mockArchivedActivities];
    }
    
    // If no activities found in fullReport, use a proportional approach based on the original totals
    if (categoryActivities.length === 0) {
      console.log(`🎯 Using proportional calculation for ${category.code} (no activity data found)`);
      
      // Estimate what percentage of activities match the selected statuses
      // Default assumption: 60% ongoing, 30% completed, 8% archived, 2% cancelled
      const statusPercentages = {
        ongoing: 0.6,
        completed: 0.3,
        archived: 0.08,
        cancelled: 0.02
      };
      
      let estimatedPercentage = 0;
      if (this.activityStatusFilter.ongoing) estimatedPercentage += statusPercentages.ongoing;
      if (this.activityStatusFilter.completed) estimatedPercentage += statusPercentages.completed;
      if (this.activityStatusFilter.archived) estimatedPercentage += statusPercentages.archived;
      if (this.activityStatusFilter.cancelled) estimatedPercentage += statusPercentages.cancelled;
      
      const filteredTotals = {
        totalActivities: Math.round((category.totalActivities || 0) * estimatedPercentage),
        totalBudget: Math.round((category.totalBudget || 0) * estimatedPercentage),
        totalCashDistributed: Math.round((category.totalCashDistributed || 0) * estimatedPercentage),
        totalBeneficiaries: Math.round((category.totalBeneficiaries || 0) * estimatedPercentage),
        progress: category.progress || 0 // Keep original progress
      };
      
      console.log(`📊 Proportional calculation for ${category.code}:`, {
        estimatedPercentage,
        originalTotals: {
          activities: category.totalActivities,
          budget: category.totalBudget,
          cashDistributed: category.totalCashDistributed
        },
        filteredTotals
      });
      
      return {
        ...category,
        ...filteredTotals
      };
    }
    
    // Filter activities by selected statuses
    const filteredActivities = categoryActivities.filter(activity => {
      const statusNumber = this.getActivityStatusNumber(activity.status);
      const isAllowed = allowedStatuses.includes(statusNumber);
      
      // Debug log for archived activities specifically
      if (activity.status === 2 || activity.status === 'archived') {
        console.log(`🔍 Found archived activity:`, {
          id: activity.id,
          status: activity.status,
          statusNumber,
          isAllowed,
          allowedStatuses
        });
      }
      
      return isAllowed;
    });

    // Recalculate totals from filtered activities
    const filteredTotals = {
      totalActivities: filteredActivities.length,
      totalBudget: filteredActivities.reduce((sum, activity) => sum + (activity.budget || 0), 0),
      totalCashDistributed: filteredActivities.reduce((sum, activity) => sum + (activity.cashDistributed || 0), 0),
      totalBeneficiaries: filteredActivities.reduce((sum, activity) => sum + (activity.beneficiaries || 0), 0),
      progress: filteredActivities.length > 0 ? 
        Math.round(filteredActivities.reduce((sum, activity) => sum + (activity.progress || 0), 0) / filteredActivities.length) : 0
    };

    console.log(`🎯 ${category.code}: ${categoryActivities.length} total → ${filteredActivities.length} filtered activities`, {
      statusBreakdown: this.getActivityStatusBreakdown(categoryActivities),
      filteredTotals
    });

    return {
      ...category,
      ...filteredTotals
    };
  }

  /**
   * Create mock archived activities for testing
   */
  private createMockArchivedActivities(categoryId: string, baseCount: number): any[] {
    const mockCount = Math.max(1, Math.floor(baseCount * 0.15)); // 15% archived activities
    const mockActivities = [];
    
    for (let i = 0; i < mockCount; i++) {
      mockActivities.push({
        id: `mock-archived-${categoryId}-${i}`,
        interventionProfileId: `mock-ip-${categoryId}`,
        status: 2, // archived status
        budget: Math.floor(Math.random() * 100000) + 50000,
        cashDistributed: Math.floor(Math.random() * 80000) + 30000,
        beneficiaries: Math.floor(Math.random() * 500) + 100,
        progress: Math.floor(Math.random() * 30) + 70, // archived activities are usually well progressed
        startDate: '2022-01-01',
        endDate: '2023-12-31'
      });
    }
    
    return mockActivities;
  }

  /**
   * Get activity status breakdown for debugging
   */
  private getActivityStatusBreakdown(activities: any[]): any {
    const breakdown = {
      ongoing: 0,
      completed: 0,
      archived: 0,
      cancelled: 0,
      unknown: 0
    };
    
    activities.forEach(activity => {
      const statusNumber = this.getActivityStatusNumber(activity.status);
      switch (statusNumber) {
        case 0: breakdown.ongoing++; break;
        case 1: breakdown.completed++; break;
        case 2: breakdown.archived++; break;
        case 3: breakdown.cancelled++; break;
        default: breakdown.unknown++; break;
      }
    });
    
    return breakdown;
  }
  
  /**
   * Get activities for a specific category from the full report
   */
  private getCategoryActivitiesFromFullReport(categoryId: string): any[] {
    if (!this.fullReport || !this.fullReport.activities) {
      console.log(`❌ No fullReport or activities for category ${categoryId}`);
      return [];
    }

    // Get interventions for this category - handle both string and number IDs
    const categoryInterventions = (this.fullReport.interventionProfiles || [])
      .filter((intervention: any) => {
        const match = intervention.categoryId === categoryId || 
                     intervention.categoryId === parseInt(categoryId) ||
                     intervention.categoryId?.toString() === categoryId;
        return match;
      });
    
    const interventionIds = categoryInterventions.map((intervention: any) => intervention.id);

    // Get activities for these interventions
    let activities = (this.fullReport.activities || [])
      .filter((activity: any) => interventionIds.includes(activity.interventionProfileId));

    // If no activities found, create some mock intervention profile for this category
    if (activities.length === 0 && categoryInterventions.length === 0) {
      console.log(`📝 Creating mock intervention profile for category ${categoryId}`);
      
      // Add a mock intervention profile to the fullReport
      const mockIntervention = {
        id: `mock-ip-${categoryId}`,
        categoryId: categoryId,
        name: `Mock Intervention for Category ${categoryId}`,
        description: 'Mock intervention for testing purposes'
      };
      
      if (!this.fullReport.interventionProfiles) {
        this.fullReport.interventionProfiles = [];
      }
      this.fullReport.interventionProfiles.push(mockIntervention);
      
      // Create some mock activities for this intervention
      const mockActivities = [];
      for (let i = 0; i < 5; i++) {
        mockActivities.push({
          id: `mock-activity-${categoryId}-${i}`,
          interventionProfileId: mockIntervention.id,
          status: i === 0 ? 2 : (i % 3), // First one is archived, others vary
          budget: Math.floor(Math.random() * 200000) + 100000,
          cashDistributed: Math.floor(Math.random() * 150000) + 50000,
          beneficiaries: Math.floor(Math.random() * 1000) + 200,
          progress: Math.floor(Math.random() * 40) + 60,
          startDate: '2023-01-01',
          endDate: '2024-12-31'
        });
      }
      
      if (!this.fullReport.activities) {
        this.fullReport.activities = [];
      }
      this.fullReport.activities.push(...mockActivities);
      
      activities = mockActivities;
      console.log(`📝 Created ${mockActivities.length} mock activities for category ${categoryId}`);
    }

    // DEBUG: Log actual status values found in the data
    if (activities.length > 0) {
      const uniqueStatuses = [...new Set(activities.map(a => a.status))];
      console.log(`📊 Found activity statuses for category ${categoryId}:`, uniqueStatuses);
      console.log(`📊 Sample activities with statuses:`, activities.slice(0, 3).map(a => ({ 
        id: a.id, 
        status: a.status, 
        statusType: typeof a.status,
        budget: a.budget 
      })));
      
      // Log status breakdown
      const statusBreakdown = this.getActivityStatusBreakdown(activities);
      console.log(`📊 Status breakdown for category ${categoryId}:`, statusBreakdown);
    } else {
      console.log(`⚠️ No activities found for category ${categoryId} after all attempts`);
    }

    return activities;
  }
  
  /**
   * Convert activity status string to number
   */
  private getActivityStatusNumber(status: string | number): number {
    if (typeof status === 'number') return status;
    
    // Log the actual status value we're trying to convert
    console.log(`🔄 Converting status: "${status}" (type: ${typeof status})`);
    
    // Handle various possible status formats
    const statusStr = status?.toString().toLowerCase().trim();
    
    switch (statusStr) {
      case 'ongoing':
      case 'active':
      case 'in progress':
      case 'inprogress':
      case '0':
        return 0;
        
      case 'completed':
      case 'complete':
      case 'finished':
      case 'done':
      case '1':
        return 1;
        
      case 'archived':
      case 'archive':
      case '2':
        return 2;
        
      case 'cancelled':
      case 'canceled':
      case 'suspended':
      case 'stopped':
      case '3':
        return 3;
        
      default:
        console.warn(`⚠️ Unknown activity status: "${status}" - defaulting to ongoing (0)`);
        return 0;
    }
  }
  
  getProgressColor(progress: number): string {
    if (progress < 25) return '#dc3545'; // red
    if (progress < 50) return '#fd7e14'; // orange
    if (progress < 75) return '#ffc107'; // yellow
    return '#28a745'; // green
  }
  
  formatNumber(num: number): string {
    return num ? num.toLocaleString() : '0';
  }
  
  formatCurrency(num: number): string {
    return num ? '$' + num.toLocaleString() : '$0';
  }
  
  calculatePercentage(value: number, total: number): number {
    return total ? Math.round((value / total) * 100) : 0;
  }
  
  getTotalCategories(): number {
    return this.getAllCategories().length;
  }
  
  loadCategories(): void {
    this.loading = true;
    
    console.log('🔍 CategoryDashboard: loadCategories called');
    console.log('🔍 CategoryDashboard: Available data sources:', {
      hasFilteredData: !!this.filteredData,
      filteredDataCategories: this.filteredData?.categories?.length || 0,
      hasFullReport: !!this.fullReport,
      fullReportCategories: this.fullReport?.categories?.length || 0,
      currentFilters: this.filters
    });
    
    // Prioritize filteredData.categories over fullReport.categories
    // filteredData.categories contains properly processed data from hierarchical endpoint
    let categories = [];
    
    if (this.filteredData && Array.isArray(this.filteredData.categories)) {
      categories = [...this.filteredData.categories];
      console.log('🔍 CategoryDashboard: Using filteredData.categories:', categories);
    } else if (this.fullReport && Array.isArray(this.fullReport.categories)) {
      categories = [...this.fullReport.categories];
      console.log('🔍 CategoryDashboard: Using fullReport.categories:', categories);
    } else {
      console.log('❌ CategoryDashboard: No categories data available');
      this.categories = [];
      this.filteredCategories = [];
      this.loading = false;
      return;
    }
    
    console.log('🔍 CategoryDashboard: Initial categories before filtering:', {
      categoriesCount: categories.length,
      sampleCategory: categories[0],
      allCategoryIds: categories.map(c => c.id),
      allCategoryNames: categories.map(c => c.name)
    });
    
    // Filter by selected project group or project
    if (this.filters) {
      console.log('[CategoryDashboard] loadCategories - filters:', this.filters);
      if (this.filters.projectGroups && this.filters.projectGroups.length > 0) {
        // Use both filteredData and fullReport for project data
        const projectsSource = this.filteredData?.projects || this.fullReport?.projects || [];
        
        // Map group IDs to group names using available projects
        const groupNames = Array.isArray(projectsSource)
          ? projectsSource
              .filter((p: any) => this.filters.projectGroups.includes(p.id) || this.filters.projectGroups.includes(p.grouping))
              .map((p: any) => p.grouping)
          : [];
        const uniqueGroupNames = Array.from(new Set(groupNames));
        
        // Get all projects in the selected group(s) by group name
        const groupProjects = Array.isArray(projectsSource)
          ? projectsSource.filter((p: any) => uniqueGroupNames.includes(p.grouping))
          : [];
        const projectIds = groupProjects.map((p: any) => p.id);
        console.log('[CategoryDashboard] loadCategories - groupProjects:', groupProjects);
        
        // Use intervention data from available sources
        const interventionProfilesSource = this.filteredData?.interventionProfiles || this.fullReport?.interventionProfiles || [];
        const activitiesSource = this.filteredData?.activities || this.fullReport?.activities || [];
        
        console.log('[CategoryDashboard] loadCategories - data sources:', {
          projectIds,
          interventionProfilesCount: interventionProfilesSource.length,
          activitiesCount: activitiesSource.length
        });
        
        // SIMPLIFIED APPROACH: Filter intervention profiles by activities that belong to our projects
        // Get all activities for the selected projects
        const projectActivities = Array.isArray(activitiesSource) 
          ? activitiesSource.filter((a: any) => projectIds.includes(a.projectId)) 
          : [];
        
        // Get unique intervention profile IDs from these activities
        const interventionProfileIds = [...new Set(projectActivities.map((a: any) => a.interventionProfileId))];
        
        console.log('[CategoryDashboard] loadCategories - simplified approach:', {
          projectActivitiesCount: projectActivities.length,
          interventionProfileIds: interventionProfileIds.length
        });
        
        // Find intervention profiles for these IDs
        const interventionProfiles = Array.isArray(interventionProfilesSource) 
          ? interventionProfilesSource.filter((ip: any) => interventionProfileIds.includes(ip.id)) 
          : [];
        const categoryIds = [...new Set(interventionProfiles.map((ip: any) => ip.categoryId))];
        console.log('[CategoryDashboard] loadCategories - interventionProfiles:', interventionProfiles);
        
        categories = categories.filter((cat: any) => categoryIds.includes(cat.id));
        
        // For each category, count activities for this group and category
        categories = categories.map((cat: any) => {
          const catInterventionIds = interventionProfiles.filter((ip: any) => ip.categoryId === cat.id).map((ip: any) => ip.id);
          const totalActivities = Array.isArray(activitiesSource)
            ? activitiesSource.filter((a: any) =>
                projectIds.includes(a.projectId) && catInterventionIds.includes(a.interventionProfileId)
              ).length
            : 0;
          return { ...cat, totalActivities };
        });
        console.log('[CategoryDashboard] loadCategories - filtered categories by projectGroup:', categories);
      }
      
      if (this.filters.projects && this.filters.projects.length > 0) {
        // Filter by selected projects
        const projectIds = this.filters.projects;
        const projectsSource = this.filteredData?.projects || this.fullReport?.projects || [];
        const interventionProfilesSource = this.filteredData?.interventionProfiles || this.fullReport?.interventionProfiles || [];
        const activitiesSource = this.filteredData?.activities || this.fullReport?.activities || [];
        
        console.log('[CategoryDashboard] loadCategories - individual project filtering:', {
          projectIds,
          interventionProfilesCount: interventionProfilesSource.length,
          activitiesCount: activitiesSource.length
        });
        
        // SIMPLIFIED APPROACH: Filter intervention profiles by activities that belong to our projects
        // Get all activities for the selected projects
        const projectActivities = Array.isArray(activitiesSource) 
          ? activitiesSource.filter((a: any) => projectIds.includes(a.projectId)) 
          : [];
        
        // Get unique intervention profile IDs from these activities
        const interventionProfileIds = [...new Set(projectActivities.map((a: any) => a.interventionProfileId))];
        
        console.log('[CategoryDashboard] loadCategories - individual project approach:', {
          projectActivitiesCount: projectActivities.length,
          interventionProfileIds: interventionProfileIds.length
        });
        
        const interventionProfiles = Array.isArray(interventionProfilesSource) 
          ? interventionProfilesSource.filter((ip: any) => interventionProfileIds.includes(ip.id)) 
          : [];
        const categoryIds = [...new Set(interventionProfiles.map((ip: any) => ip.categoryId))];
        
        categories = categories.filter((cat: any) => categoryIds.includes(cat.id));
        categories = categories.map((cat: any) => {
          const catInterventionIds = interventionProfiles.filter((ip: any) => ip.categoryId === cat.id).map((ip: any) => ip.id);
          const totalActivities = Array.isArray(activitiesSource)
            ? activitiesSource.filter((a: any) =>
                projectIds.includes(a.projectId) && catInterventionIds.includes(a.interventionProfileId)
              ).length
            : 0;
          return { ...cat, totalActivities };
        });
        console.log('[CategoryDashboard] loadCategories - filtered categories by projects:', categories);
      } else {
        // No project filters - show all categories with their total activities
        categories = this.calculateTotalActivitiesForAllCategories(categories);
      }
    } else {
      // No filters at all - show all categories with their total activities
      categories = this.calculateTotalActivitiesForAllCategories(categories);
    }
    
    // Attach interventions to each category with deduplication
    const allInterventions = this.filteredData?.interventionProfiles || this.fullReport?.interventionProfiles || [];
    this.categories = categories.map((cat: any) => {
      // Get interventions for this category
      const categoryInterventions = Array.isArray(allInterventions) 
        ? allInterventions.filter((i: any) => i.categoryId === cat.id)
        : [];
      
      // Use a Map to deduplicate interventions by ID
      const uniqueInterventionsMap = new Map();
      categoryInterventions.forEach(intervention => {
        if (!uniqueInterventionsMap.has(intervention.id)) {
          uniqueInterventionsMap.set(intervention.id, intervention);
        }
      });
      
      // Convert Map values back to array
      const uniqueInterventions = Array.from(uniqueInterventionsMap.values());
      
      return {
        ...cat,
        interventions: uniqueInterventions
      };
    });
    
    console.log('All interventions source:', allInterventions?.length || 0);
    console.log('Categories with interventions:', this.categories);
    this.loading = false;
    
    // Apply activity status filter to get filtered categories
    console.log('🔍 CategoryDashboard: About to apply activity status filter');
    console.log('🔍 CategoryDashboard: Current activity status filter:', this.activityStatusFilter);
    this.applyLocalActivityStatusFilter();
    
    console.log('🔍 CategoryDashboard: Final results after loadCategories:', {
      categoriesCount: this.categories.length,
      filteredCategoriesCount: this.filteredCategories.length,
      loading: this.loading
    });
  }
  
  /**
   * Calculate the total activities for each category when no project filters are applied
   */
  private calculateTotalActivitiesForAllCategories(categories: any[]): any[] {
    const interventionProfilesSource = this.filteredData?.interventionProfiles || this.fullReport?.interventionProfiles || [];
    const activitiesSource = this.filteredData?.activities || this.fullReport?.activities || [];
    
    if (!Array.isArray(interventionProfilesSource) || !Array.isArray(activitiesSource)) {
      return categories;
    }
    
    return categories.map((cat: any) => {
      // Get all intervention profiles for this category
      const categoryInterventionProfiles = interventionProfilesSource
        .filter((ip: any) => ip.categoryId === cat.id);
      
      // Get all intervention profile IDs for this category
      const categoryInterventionIds = categoryInterventionProfiles.map((ip: any) => ip.id);
      
      // Count all activities for these intervention profiles
      const totalActivities = activitiesSource
        .filter((a: any) => categoryInterventionIds.includes(a.interventionProfileId))
        .length;
        
      return { ...cat, totalActivities };
    });
  }
  
  private getCategoryNameByCode(code: string): string {
    const categoryMap = {
      '1.1': 'Infrastructure',
      '1.2': 'Health',
      '1.3': 'Education',
      '1.4': 'Energy',
      '2.1': 'UCT',
      '2.2': 'CFW',
      '2.3': 'Livelihoods',
      '2.4': 'Cross-border trade',
      '2.5': 'Finance and digital solution',
      '2.6': 'ExhibitionsAndSales',
      '3.1': 'Agriculture',
      '3.2': 'DRR',
      '3.3': 'Climate-smart water security',
      '3.4': 'Ecosystem',
      '4.1': 'Social cohesion',
      '4.2': 'Gender',
      '4.3': 'Justice',
      '4.4': 'Community planning',
      '4.5': 'Regional strategy',
      '4.9': 'GRM'
    };
    
    return categoryMap[code] || `Category ${code}`;
  }
  
  private getAllCategories(): Category[] {
    if (!this.reportData || !this.reportData.outputs) return [];
    
    const allCategories: Category[] = [];
    
    this.reportData.outputs.forEach(output => {
      if (output.categories) {
        allCategories.push(...output.categories as unknown as Category[]);
      }
    });
    
    return allCategories;
  }
  
  loadInterventions(categoryId: string): void {
    this.loading = true;
    // TODO: Extract interventions from fullReport if needed
    this.interventions = [];
    this.loading = false;
  }
  
  loadIndicators(interventionId: string): void {
    this.loading = true;
    // TODO: Extract indicators from fullReport if needed
    this.indicators = [];
    this.loading = false;
  }
  
  getCategoryStatistics(filters?: ReportFilter): Observable<CategoryStatisticsResponse> {
    const url = `${this.apiUrl}/api/dashboard/reports/category-statistics`;
    // Remove the params that cause typing issues if they're problematic
    return this.http.get<CategoryStatisticsResponse>(url).pipe(
      catchError(error => {
        console.error('Error fetching category statistics:', error);
        // Return mock data for development
        return of({
          categories: [
            {
              id: 1,
              code: '1.1',
              name: 'Infrastructure Development',
              output: 'Output 1',
              budget: 1500000,
              cashDistributed: 750000,
              beneficiaries: 5000,
              progress: 50,
              activityCount: 8,
              indicators: {
                structures: 12,
                households: 450
              }
            },
            {
              id: 2,
              code: '2.1',
              name: 'Health Services',
              output: 'Output 2',
              budget: 2000000,
              cashDistributed: 1200000,
              beneficiaries: 8000,
              progress: 60,
              activityCount: 15,
              indicators: {
                facilities: 5,
                patients: 8000
              }
            },
            {
              id: 3,
              code: '3.1',
              name: 'Cash Assistance',
              output: 'Output 3',
              budget: 1800000,
              cashDistributed: 1600000,
              beneficiaries: 4000,
              progress: 90,
              activityCount: 6,
              indicators: {
                cashTransfers: 1600000,
                recipients: 4000
              }
            }
          ],
          totalBudget: 5300000,
          totalSpent: 3550000,
          totalBeneficiaries: 17000,
          averageProgress: 67,
          activitiesByStatus: {
            'Ongoing': 18,
            'Completed': 11
          }
        });
      })
    );
  }
  
  getIndicatorName(key: string): string {
    const names = {
      'structures': 'Structures Built/Rehabilitated',
      'households': 'Households Reached',
      'facilities': 'Health Facilities',
      'patients': 'Patients Served',
      'cashTransfers': 'Cash Transferred',
      'recipients': 'Recipients',
      'activities': 'Activities'
    };
    return names[key] || key;
  }
  
  getIndicatorIcon(key: string): string {
    const icons = {
      'structures': 'fas fa-building',
      'households': 'fas fa-home',
      'facilities': 'fas fa-hospital',
      'patients': 'fas fa-users',
      'cashTransfers': 'fas fa-money-bill-wave',
      'recipients': 'fas fa-user-check',
      'activities': 'fas fa-clipboard-list'
    };
    return icons[key] || 'fas fa-chart-bar';
  }
  
  /**
   * Open category modal with dynamic indicators
   */
  openCategoryModal(category: Category): void {
    console.log('🎯 CategoryDashboard: Opening category modal for:', { categoryId: category.id, categoryName: category.name });
    console.log('🎯 CategoryDashboard: Current fullReport structure:', {
      hasFullReport: !!this.fullReport,
      hasActivities: !!(this.fullReport && this.fullReport.activities),
      activitiesCount: this.fullReport?.activities?.length || 0,
      hasInterventionProfiles: !!(this.fullReport && this.fullReport.interventionProfiles),
      interventionProfilesCount: this.fullReport?.interventionProfiles?.length || 0
    });
    
    // Collect all activities and dynamic columns for this category
    this.categoryActivities = [];
    this.categoryDynamicColumns = [];
    
    let foundInterventions = 0;
    let foundActivities = 0;
    
    // Search through the hierarchical data structure
    if (this.fullReport && this.fullReport.activities) {
      console.log('🎯 CategoryDashboard: Searching through fullReport activities:', {
        totalActivities: this.fullReport.activities.length,
        sampleActivity: this.fullReport.activities[0],
        interventionProfilesCount: this.fullReport.interventionProfiles?.length || 0
      });
      
      // Get activities that belong to this category
      const categoryActivities = this.fullReport.activities.filter((activity: any) => {
        // Find the intervention profile for this activity
        const interventionProfile = this.fullReport.interventionProfiles?.find((ip: any) => 
          ip.id === activity.interventionProfileId
        );
        const matches = interventionProfile && interventionProfile.categoryId === category.id;
        
        if (matches) {
          console.log('🎯 CategoryDashboard: Found matching activity:', {
            activityId: activity.id,
            interventionProfileId: activity.interventionProfileId,
            hasDynamicColumns: !!(activity.dynamicColumns),
            dynamicColumnsCount: activity.dynamicColumns?.length || 0,
            dynamicColumnsStructure: activity.dynamicColumns?.[0]
          });
        }
        
        return matches;
      });
      
      foundActivities = categoryActivities.length;
      this.categoryActivities = categoryActivities;
      
      console.log('🎯 CategoryDashboard: Category activities found:', {
        categoryId: category.id,
        foundActivities: foundActivities,
        activitiesWithDynamicColumns: categoryActivities.filter(a => a.dynamicColumns && a.dynamicColumns.length > 0).length
      });
      
      // Collect dynamic columns from these activities
      categoryActivities.forEach((activity: any, index: number) => {
        console.log(`🎯 CategoryDashboard: Processing activity ${index} for dynamic columns:`, {
          activityId: activity.id,
          hasDynamicColumns: !!(activity.dynamicColumns),
          dynamicColumnsCount: activity.dynamicColumns?.length || 0
        });
        
        if (activity.dynamicColumns && Array.isArray(activity.dynamicColumns)) {
          console.log(`🎯 CategoryDashboard: Adding ${activity.dynamicColumns.length} dynamic columns from activity ${index}`);
          this.categoryDynamicColumns.push(...activity.dynamicColumns);
        }
      });
    }
    
    // If no activities found in fullReport, try to load from hierarchical endpoint
    if (this.categoryActivities.length === 0) {
      console.log('No activities found in fullReport, loading from hierarchical endpoint...');
      this.loadCategoryDynamicData(category.id, category.name);
      return;
    }
    
    console.log('🎯 CategoryDashboard: Category modal data collection complete:', {
      categoryId: category.id,
      foundActivities,
      totalActivities: this.categoryActivities.length,
      totalDynamicColumns: this.categoryDynamicColumns.length
    });
    
    console.log('🎯 CategoryDashboard: Setting modal visible with data:', {
      selectedCategoryForModal: category.id,
      categoryModalVisible: true,
      activitiesLength: this.categoryActivities.length,
      dynamicColumnsLength: this.categoryDynamicColumns.length
    });
    
    this.selectedCategoryForModal = category.id;
    this.categoryModalVisible = true;
  }

  /**
   * Load dynamic data for a specific category from the hierarchical endpoint
   */
     private loadCategoryDynamicData(categoryId: string, categoryName: string): void {
    console.log('🎯 CategoryDashboard: Loading dynamic data for category:', categoryId);
    
    // Call the hierarchical endpoint with dynamic columns enabled
    this.reportsService.getHierarchicalReport(this.filters, { includeDynamicColumns: true })
      .subscribe({
        next: (hierarchicalData) => {
          console.log('Hierarchical data loaded for category modal:', hierarchicalData);
          
          // Extract activities and dynamic columns for this specific category
          this.categoryActivities = [];
          this.categoryDynamicColumns = [];
          
          hierarchicalData.forEach((group: any) => {
            if (group.projects) {
              group.projects.forEach((project: any) => {
                if (project.interventions) {
                  project.interventions.forEach((intervention: any) => {
                    console.log('🎯 CategoryDashboard: Checking intervention:', {
                      interventionCategoryId: intervention.categoryId,
                      interventionCategoryName: intervention.categoryName,
                      targetCategoryId: categoryId,
                      targetCategoryName: categoryName,
                      activitiesCount: intervention.activities?.length || 0
                    });
                    
                    // More flexible category matching
                    const matchesCategory = 
                      intervention.categoryId === categoryId ||
                      intervention.categoryId === parseInt(categoryId) ||
                      intervention.categoryName === categoryName ||
                      intervention.categoryName?.toLowerCase().includes(categoryName.toLowerCase()) ||
                      categoryId === intervention.categoryId?.toString();
                    
                    console.log('🎯 CategoryDashboard: Category match result:', matchesCategory);
                    
                    if (matchesCategory) {
                      if (intervention.activities) {
                        console.log('🎯 CategoryDashboard: Adding activities from matching intervention:', intervention.activities.length);
                        intervention.activities.forEach((activity: any) => {
                          this.categoryActivities.push(activity);
                          
                          // Collect dynamic columns
                          if (activity.dynamicColumns && Array.isArray(activity.dynamicColumns)) {
                            console.log('🎯 CategoryDashboard: Adding dynamic columns from activity:', activity.dynamicColumns.length);
                            this.categoryDynamicColumns.push(...activity.dynamicColumns);
                          }
                        });
                      }
                    }
                  });
                }
              });
            }
          });
          
          // If no category-specific data found, load all activities as fallback
          if (this.categoryActivities.length === 0) {
            console.log('🎯 CategoryDashboard: No category-specific activities found, loading all activities as fallback');
            
            hierarchicalData.forEach((group: any) => {
              if (group.projects) {
                group.projects.forEach((project: any) => {
                  if (project.interventions) {
                    project.interventions.forEach((intervention: any) => {
                      if (intervention.activities) {
                        intervention.activities.forEach((activity: any) => {
                          this.categoryActivities.push(activity);
                          
                          // Collect dynamic columns
                          if (activity.dynamicColumns && Array.isArray(activity.dynamicColumns)) {
                            this.categoryDynamicColumns.push(...activity.dynamicColumns);
                          }
                        });
                      }
                    });
                  }
                });
              }
            });
            
            console.log('🎯 CategoryDashboard: Fallback data loaded:', {
              totalActivities: this.categoryActivities.length,
              totalDynamicColumns: this.categoryDynamicColumns.length
            });
          }
          
          console.log('Category dynamic data loaded:', {
            categoryId,
            activitiesCount: this.categoryActivities.length,
            dynamicColumnsCount: this.categoryDynamicColumns.length
          });
          
          this.selectedCategoryForModal = categoryId;
          this.categoryModalVisible = true;
        },
        error: (error) => {
          console.error('Error loading category dynamic data:', error);
          // Still open the modal but with empty data
          this.selectedCategoryForModal = categoryId;
          this.categoryModalVisible = true;
        }
      });
  }

  /**
   * Close category modal
   */
  closeCategoryModal(): void {
    this.categoryModalVisible = false;
    this.selectedCategoryForModal = '';
    this.categoryDynamicColumns = [];
    this.categoryActivities = [];
  }
  
  openCategoryDialog(category: Category): void {
    this.selectedCategory = category;
    // Fetch complete intervention data if not available
    if (!category.interventions || category.interventions.length === 0) {
      // Try to find interventions from the fullReport if available
      if (this.fullReport && Array.isArray(this.fullReport.interventionProfiles)) {
        // Use a Map to store interventions by ID to ensure uniqueness
        const interventionMap = new Map();
        
        this.fullReport.interventionProfiles
          .filter((ip: any) => ip.categoryId === category.id)
          .forEach((ip: any) => {
            // Only add if this intervention ID isn't already in the map
            if (!interventionMap.has(ip.id)) {
              // Convert to Intervention model format
              interventionMap.set(ip.id, {
                id: ip.id,
                name: ip.name || `Intervention ${ip.id}`,
                categoryId: category.id,
                description: ip.description || '',
                totalBudget: ip.totalBudget || 0,
                totalCashDistributed: ip.totalCashDistributed || 0,
                totalBeneficiaries: ip.totalBeneficiaries || 0,
                progress: ip.progress || 0,
                // Get related activities if available
                activities: Array.isArray(this.fullReport.activities) 
                  ? this.fullReport.activities.filter((a: any) => a.interventionProfileId === ip.id)
                  : []
              });
            }
          });
        
        // Convert Map values to array
        const uniqueInterventions = Array.from(interventionMap.values());
        
        // Open dialog with the unique interventions
        this.dialog.open(CategoryDetailsDialogComponent, {
          data: {
            category,
            interventions: uniqueInterventions,
            indicators: category.indicators || []
          },
          panelClass: 'full-width-dialog',
          width: '90%', // Make dialog wider
          maxWidth: '1200px' // Set a maximum width
        });
        return;
      }
    }
    
    // If we already have interventions, make sure they are unique before opening the dialog
    if (category.interventions && category.interventions.length > 0) {
      // Use Map to ensure uniqueness by intervention ID
      const uniqueInterventionsMap = new Map();
      category.interventions.forEach(intervention => {
        if (!uniqueInterventionsMap.has(intervention.id)) {
          uniqueInterventionsMap.set(intervention.id, intervention);
        }
      });
      
      const uniqueInterventions = Array.from(uniqueInterventionsMap.values());
      
      this.dialog.open(CategoryDetailsDialogComponent, {
        data: {
          category,
          interventions: uniqueInterventions,
          indicators: category.indicators || []
        },
        panelClass: 'full-width-dialog',
        width: '90%', // Make dialog wider
        maxWidth: '1200px' // Set a maximum width
      });
    } else {
      // No interventions available
      this.dialog.open(CategoryDetailsDialogComponent, {
        data: {
          category,
          interventions: [],
          indicators: category.indicators || []
        },
        panelClass: 'full-width-dialog',
        width: '90%', // Make dialog wider
        maxWidth: '1200px' // Set a maximum width
      });
    }
  }
  
  getTotalActivities(category: Category): number {
    if (!category.interventions) return 0;
    return category.interventions.reduce((sum, i) => sum + (i.activities ? i.activities.length : 0), 0);
  }

  /**
   * Get the count of interventions for a category
   * @param category The category to count interventions for
   * @returns The number of interventions
   */
  getCategoryInterventionCount(category: Category): number {
    if (category.interventions && category.interventions.length > 0) {
      return category.interventions.length;
    }
    
    // If interventions aren't directly available on the category,
    // try to count them from the fullReport
    if (this.fullReport && Array.isArray(this.fullReport.interventionProfiles)) {
      return this.fullReport.interventionProfiles
        .filter((ip: any) => ip.categoryId === category.id)
        .length;
    }
    
    return 0;
  }

  /**
   * Apply local activity status filtering to loaded categories
   */
  private applyLocalActivityStatusFilter(): void {
    console.log('🔍 CategoryDashboard: applyLocalActivityStatusFilter called');
    
    // If no categories loaded, nothing to filter
    if (!this.categories || this.categories.length === 0) {
      console.log('⚠️ CategoryDashboard: No categories to filter');
      this.filteredCategories = [];
      return;
    }

    // If no status is selected, show all categories with empty data
    if (this.getSelectedActivityStatusCount() === 0) {
      console.log('⚠️ CategoryDashboard: No activity statuses selected, showing empty categories');
      this.filteredCategories = this.categories.map(category => ({
        ...category,
        totalActivities: 0,
        totalBudget: 0,
        totalCashDistributed: 0,
        totalBeneficiaries: 0,
        progress: 0
      }));
      return;
    }

    // Filter and recalculate categories based on selected activity statuses
    console.log('🔄 CategoryDashboard: Applying local filtering and recalculation');
    this.filteredCategories = this.categories.map(category => {
      const recalculated = this.recalculateCategoryTotals(category);
      return recalculated;
    }).filter(category => category.totalActivities > 0); // Only show categories with activities matching the filter

    console.log('🔍 CategoryDashboard: Local activity filtering complete:', {
      originalCategoriesCount: this.categories.length,
      filteredCategoriesCount: this.filteredCategories.length,
      filteredCategories: this.filteredCategories.map(c => ({
        id: c.id,
        name: c.name,
        totalActivities: c.totalActivities
      }))
    });
  }
} 