<div class="category-dashboard">
  <div class="row">
    <!-- Loading indicator -->
    <div class="col-12" *ngIf="loading">
      <div class="alert alert-info">Loading category data...</div>
    </div>
    
    <!-- Error message -->
    <div class="col-12" *ngIf="error">
      <div class="alert alert-danger">{{ error }}</div>
    </div>
    
    <!-- Activity Status Filter -->
    <div class="col-12 mb-4" *ngIf="categories?.length > 0">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">Activity Status Filter</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-12">
              <div class="form-check-inline">
                <input 
                  class="form-check-input" 
                  type="checkbox" 
                  id="status-ongoing" 
                  [(ngModel)]="activityStatusFilter.ongoing"
                  (change)="onActivityStatusFilterChange()">
                <label class="form-check-label" for="status-ongoing">
                  <span class="badge badge-primary me-1">Ongoing</span>
                </label>
              </div>
              <div class="form-check-inline ms-3">
                <input 
                  class="form-check-input" 
                  type="checkbox" 
                  id="status-completed" 
                  [(ngModel)]="activityStatusFilter.completed"
                  (change)="onActivityStatusFilterChange()">
                <label class="form-check-label" for="status-completed">
                  <span class="badge badge-success me-1">Completed</span>
                </label>
              </div>
              <div class="form-check-inline ms-3">
                <input 
                  class="form-check-input" 
                  type="checkbox" 
                  id="status-archived" 
                  [(ngModel)]="activityStatusFilter.archived"
                  (change)="onActivityStatusFilterChange()">
                <label class="form-check-label" for="status-archived">
                  <span class="badge badge-secondary me-1">Archived</span>
                </label>
              </div>
              <div class="form-check-inline ms-3">
                <input 
                  class="form-check-input" 
                  type="checkbox" 
                  id="status-cancelled" 
                  [(ngModel)]="activityStatusFilter.cancelled"
                  (change)="onActivityStatusFilterChange()">
                <label class="form-check-label" for="status-cancelled">
                  <span class="badge badge-danger me-1">Cancelled</span>
                </label>
              </div>
              <div class="form-check-inline ms-4">
                <button 
                  class="btn btn-sm btn-outline-primary" 
                  (click)="selectAllActivityStatuses()">
                  Select All
                </button>
                <button 
                  class="btn btn-sm btn-outline-secondary ms-2" 
                  (click)="clearAllActivityStatuses()">
                  Clear All
                </button>
              </div>
            </div>
          </div>
          <div class="row mt-2" *ngIf="getSelectedActivityStatusCount() > 0">
            <div class="col-12">
              <small class="text-muted">
                Showing categories based on activities with status: 
                <span *ngFor="let status of getSelectedActivityStatuses(); let last = last">
                  <span class="badge badge-light">{{ status }}</span><span *ngIf="!last">, </span>
                </span>
                ({{ filteredCategories.length }} categories found)
              </small>
            </div>
          </div>
          <div class="row mt-2" *ngIf="getSelectedActivityStatusCount() === 0">
            <div class="col-12">
              <small class="text-warning">
                <i class="fas fa-exclamation-triangle me-1"></i>
                No activity statuses selected. Please select at least one status to view data.
              </small>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Category Cards -->
  <div class="row" *ngIf="filteredCategories?.length > 0">
    <div class="col-12 mb-3">
      <h4>Categories ({{ filteredCategories.length }} shown)</h4>
    </div>
    
    <div class="col-md-4 mb-4" *ngFor="let category of filteredCategories">
      <div class="card h-100" 
           (click)="openCategoryModal(category)"
           style="cursor: pointer;">
        <div class="card-header d-flex justify-content-between align-items-center">
          <h5 class="m-0">{{category.code}}: {{category.name}}</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-6 mb-2">
              <div class="small text-muted">Total Activities</div>
              <div class="font-weight-bold">
                {{ category.totalActivities || 0 }}
              </div>
            </div>
            <div class="col-6 mb-2">
              <div class="small text-muted">Cash Distributed</div>
              <div class="font-weight-bold">{{ formatCurrency(category.totalCashDistributed) }}</div>
            </div>
            <div class="col-6 mb-2">
              <div class="small text-muted">Beneficiaries</div>
              <div class="font-weight-bold">{{ formatNumber(category.totalBeneficiaries) }}</div>
            </div>
            <div class="col-6 mb-2">
              <div class="small text-muted">Interventions</div>
              <div class="font-weight-bold">{{ getCategoryInterventionCount(category) }}</div>
            </div>
          </div>
          <!-- Category-specific indicators -->
          <div class="mt-3 category-indicators" *ngIf="category.indicators?.length > 0">
            <div class="small text-muted mb-2">Key Indicators</div>
            <div class="row">
              <div class="col-6 mb-2" *ngFor="let indicator of category.indicators">
                <div class="d-flex align-items-center">
                  <i class="{{indicator.icon}} fa-fw text-primary mr-2"></i>
                  <div>
                    <div class="small">{{indicator.name}}</div>
                    <div class="font-weight-bold">{{formatNumber(indicator.value)}}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Selected Category Details -->
  <!-- Removed: This section is now shown only in the modal dialog -->
  
  <!-- Category Modal for Dynamic Indicators -->
  <app-category-modal
    [isVisible]="categoryModalVisible"
    [categoryId]="selectedCategoryForModal"
    [dynamicColumns]="categoryDynamicColumns"
    [activities]="categoryActivities"
    (closeModal)="closeCategoryModal()">
  </app-category-modal>
</div> 