#map {
  .notice {
    position: absolute;
    left: 5rem;
    top: 1.2rem;
    padding: 0.5rem 1rem;
  }
}

canvas {
  border-top-right-radius: var(--bs-border-radius) !important;
  border-bottom-right-radius: var(--bs-border-radius) !important;
}

.esri-attribution {
  border-bottom-right-radius: var(--bs-border-radius) !important;
}

#mapContextMenu {
  z-index: 105;
  position: absolute;
  inset: 0px 0px auto auto;
  margin: 0px;

  &.show {
    display: flex;
    will-change: transform;
    animation: menu-sub-dropdown-animation-fade-in 0.3s ease 1, menu-sub-dropdown-animation-move-up 0.3s ease 1;
  }
}