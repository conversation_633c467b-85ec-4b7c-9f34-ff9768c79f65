.exp-container {
    position: relative;
}
.exp-container .exp-suggestions {
  width: 100%;
  background-color: white;
  box-shadow: 0px 5px 7px 2px rgba(0,0,0,0.3);
  min-height: 50px;
  max-height: 300px;
  overflow-y: auto;
  position: absolute;
  display: none;
  z-index: 1069;
}

.exp-container .exp-suggestions.up {
  bottom: 36px;
}

.exp-container .exp-suggestions .exp-suggestion-item {
  cursor: pointer;
  width: 100%;
  padding: 3px 5px;
  min-width: 400px;
}

.exp-container .exp-suggestions .exp-suggestion-item.selected {
  font-weight: 500;
  background-color: var(--qs-primary-light);
}

.exp-container .exp-suggestions .exp-suggestion-item:hover {
  background-color: #337ab7;
  color: white;
}
#formula1, #formula2, #formula3, #formula4 {
  font-family: var(--bs-font-monospace);
}