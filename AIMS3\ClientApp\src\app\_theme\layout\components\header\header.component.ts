import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute, NavigationCancel, NavigationEnd, Router } from '@angular/router';
import { Subscription } from 'rxjs';
import { LayoutService } from '../../core/layout.service';
import { MenuComponent } from '../../../core/components';
import { ILayout, LayoutType } from '../../core/configs/config';

@Component({
    selector: 'app-header',
    templateUrl: './header.component.html',
    styleUrls: ['./header.component.scss'],
})
export class HeaderComponent implements OnInit, OnDestroy {
    private unsubscribe: Subscription[] = [];
    // Public props
    currentLayoutType: LayoutType | null;

    appHeaderDisplay: boolean;
    appHeaderDefaultFixedDesktop: boolean;
    appHeaderDefaultFixedMobile: boolean;

    appHeaderDefaultContainer: 'fixed' | 'fluid';
    headerContainerCssClass: string = '';
    appHeaderDefaultContainerClass: string = '';

    appHeaderDefaultStacked: boolean;

    // view
    appSidebarDefaultCollapseDesktopEnabled: boolean;
    appSidebarDisplay: boolean;
    appHeaderDefaultContent: string = '';
    appHeaderDefaulMenuDisplay: boolean;
    appPageTitleDisplay: boolean;

    constructor(private layout: LayoutService, private router: Router, private activatedRoute: ActivatedRoute) {
        this.routingChanges();
    }

    updateProps(config: ILayout) {
        this.appHeaderDisplay = this.layout.getProp(
            'app.header.display',
            config
        ) as boolean;
        // view
        this.appSidebarDefaultCollapseDesktopEnabled = this.layout.getProp(
            'app.sidebar.default.collapse.desktop.enabled',
            config
        ) as boolean;
        this.appSidebarDisplay = this.layout.getProp(
            'app.sidebar.display',
            config
        ) as boolean;
        this.appHeaderDefaultContent = this.layout.getProp(
            'app.header.default.content',
            config
        ) as string;
        this.appHeaderDefaulMenuDisplay = this.layout.getProp(
            'app.header.default.menu.display',
            config
        ) as boolean;
        this.appPageTitleDisplay = this.layout.getProp(
            'app.pageTitle.display',
            config
        ) as boolean;

        // body attrs and container css classes
        this.appHeaderDefaultFixedDesktop = this.layout.getProp(
            'app.header.default.fixed.desktop',
            config
        ) as boolean;
        if (this.appHeaderDefaultFixedDesktop) {
            document.body.setAttribute('data-qs-app-header-fixed', 'true');
        }

        this.appHeaderDefaultFixedMobile = this.layout.getProp(
            'app.header.default.fixed.mobile',
            config
        ) as boolean;
        if (this.appHeaderDefaultFixedMobile) {
            document.body.setAttribute('data-qs-app-header-fixed-mobile', 'true');
        }

        this.appHeaderDefaultContainer = this.layout.getProp(
            'appHeaderDefaultContainer',
            config
        ) as 'fixed' | 'fluid';
        this.headerContainerCssClass =
            this.appHeaderDefaultContainer === 'fixed'
                ? 'container-xxl'
                : 'container-fluid';

        this.appHeaderDefaultContainerClass = this.layout.getProp(
            'app.header.default.containerClass',
            config
        ) as string;
        if (this.appHeaderDefaultContainerClass) {
            this.headerContainerCssClass += ` ${this.appHeaderDefaultContainerClass}`;
        }

        this.appHeaderDefaultStacked = this.layout.getProp(
            'app.header.default.stacked',
            config
        ) as boolean;
        if (this.appHeaderDefaultStacked) {
            document.body.setAttribute('data-qs-app-header-stacked', 'true');
        }
        
        // Primary header
        // Secondary header
    }

    ngOnInit(): void {
        const subscr = this.layout.layoutConfigSubject
            .asObservable()
            .subscribe((config: ILayout) => {
                this.updateProps(config);
            });
        this.unsubscribe.push(subscr);
        const layoutSubscr = this.layout.currentLayoutTypeSubject
            .asObservable()
            .subscribe((layout) => {
                this.currentLayoutType = layout;
            });
        this.unsubscribe.push(layoutSubscr);

        this.appPageTitleDisplay = !this.activatedRoute.firstChild.firstChild.snapshot.data?.hidePageTitle;
    }

    routingChanges() {
        const routerSubscription = this.router.events.subscribe((event) => {
            if (event instanceof NavigationEnd || event instanceof NavigationCancel) {
                MenuComponent.reinitialization();
            }

            this.appPageTitleDisplay = !this.activatedRoute.firstChild.firstChild.snapshot.data?.hidePageTitle;
        });
        this.unsubscribe.push(routerSubscription);
    }

    ngOnDestroy() {
        this.unsubscribe.forEach((sb) => sb.unsubscribe());
    }
}