.is-loading {
  div {
    background: #eee;
    background: linear-gradient(110deg, var(--qs-gray-200) 8%, #f5f5f5 18%, var(--qs-gray-200) 33%);
    border-radius: 5px;
    background-size: 200% 100%;
    animation: 1.5s shine linear infinite;
  }

  .control-1 {
    height: 30px;
    margin-right: 5px;
  }
}


@keyframes shine {
  to {
    background-position-x: -200%;
  }
}

// forked from https://codepen.io/viktorstrate/pen/yoBRLy

$base-color: var(--qs-app-bg-color);
$shine-color: var(--qs-gray-200);
$animation-duration: 2.0s;
$avatar-offset: 52 + 16;

// this unfortunately uses set px widths for the background-gradient, I never got around to changing it to work with all widths :(
@mixin background-gradient {
  background-image: linear-gradient(90deg, $base-color 0px, $shine-color 40px, $base-color 80px);
  background-size: 600px;
}

table {
    border-radius: .25rem;
    width: 100%;
}
.tg {
  border-collapse: collapse;
  border-spacing: 0;
  border-color: #ccc;
}

.tg td {
  font-family: Arial, sans-serif;
  font-size: 14px;
  padding: 10px 20px 5px 20px;
  border-style: solid;
  border-width: 0px;
  overflow: hidden;
  word-break: normal;
  border-color: #ccc;
  color: #333;
  background-color: var(--qs-white);
}

.tg th {
  font-family: Arial, sans-serif;
  font-size: 14px;
  font-weight: normal;
  padding: 10px 20px 5px 20px;
  border-style: solid;
  border-width: 0px;
  overflow: hidden;
  word-break: normal;
  border-color: #ccc;
  color: #333;
  background-color: var(--qs-gray-200);
}

.line {
  float: left;
  width: 100%;
  height: 16px;
  margin-top: 12px;
  border-radius: 7px;

  @include background-gradient;
  animation: shine-lines $animation-duration infinite ease-out;
}

tr:last-child > td {
    padding-bottom: 1.5rem;
}

@keyframes shine-lines {
  0% {
    background-position: -100px;
  }

  40%, 100% {
    background-position: 140px;
  }
}
