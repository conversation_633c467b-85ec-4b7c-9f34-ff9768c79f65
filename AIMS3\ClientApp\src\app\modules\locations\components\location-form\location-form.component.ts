import { ChangeDetectorRef, Component, EventEmitter, Input, OnDestroy, OnInit, Output, TemplateRef, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { NgbModal, NgbModalRef } from '@ng-bootstrap/ng-bootstrap';
import { lastValueFrom, Subject, Subscription } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import { MessageService } from '../../../../shared/services/message.service';
import { SharedService } from '../../../../shared/services/shared.service';
import { AppUtilities } from '../../../../shared/utilities';
import { ModalConfig } from '../../../../_theme/partials';
import { AuthService } from '../../../auth';
import { Community, CommunityList } from '../../models/community.model';
import { District, IProvince } from '../../models/location.model';
import { LocationService } from '../../services/location.service';
import { LocationMapComponent } from '../location-map/location-map.component';

@Component({
    selector: 'location-form-modal',
    templateUrl: './location-form.component.html',
    styleUrls: ['./location-form.component.scss']
})
export class LocationFormComponent implements OnInit, OnDestroy {
    working: boolean = false;
    gettingDist: boolean = false;

    //user: UserType;
    isAdmin: boolean = false;

    @Input() location: CommunityList;
    @Input() provinces: IProvince[] = [];
    @Input() editMode: boolean = false;

    form: FormGroup;
    districts: District[] = [];
    similarLocations: CommunityList[] = [];

    @ViewChild('modal', { static: false })
    private modalContent: TemplateRef<LocationFormComponent>;
    private modalRef: NgbModalRef;
    LOCATIONS_WITHIN_RADIUS: number = environment.similarLocsInRadius;

    modalConfig: ModalConfig = {
        modalTitle: 'Add new location',
        cancelButtonLabel: 'Cancel',
        doneButtonLabel: 'Add',
        disableDoneButton: true,
        options: { size: 'xxl' },
        shouldDo: () => this.save(),
        shouldCancel: () => { this.ngOnDestroy(); return true; }
    };

    isReady: boolean = true;
    isConfirmed: boolean = false;
    @Output() done = new EventEmitter();

    private locMapComponent: LocationMapComponent = null;

    private subscriptions: Subscription[] = [];
    private gpsSubscriptions: Subscription[] = [];
    constructor(
        private authService: AuthService,
        private modalService: NgbModal,
        private locationsService: LocationService,
        private sharedService: SharedService,
        private messageService: MessageService,
        private cdr: ChangeDetectorRef
    ) {
        //this.user = this.authService.currentUserValue;
        if (this.authService.currentUserValue?.roles) {
            this.isAdmin = this.authService.currentUserValue?.roles
                .findIndex(r => r === 'Admin' || r === 'Approver') > -1;
        }
        
        this.location = new CommunityList(0, 0, '', '');
    }

    async ngOnInit() {
        this.initForm();
    }

    onMapComponentInit(component: LocationMapComponent) {
        this.locMapComponent = component;
    }

    async onOpen() {
        // if readyonly mode
        if (!this.editMode) {
            return new Promise<boolean>((resolve) => {
                this.modalRef = this.modalService.open(this.modalContent, { size: 'xxl' });
                this.modalRef.result.then(resolve, resolve);
            });
        }

        this.initForm();

        return new Promise<boolean>((resolve) => {
            this.modalRef = this.modalService.open(this.modalContent, this.modalConfig.options);
            AppUtilities().initSelect2();

            // listen to dropdown changes
            $('#province').on('change', (e: any) => {
                const selVal = $(e.target).val() || 0;
                //this.f.provId.markAsDirty();
                this.f.provId.setValue(+selVal);
                this.getDistricts();
            });
            $('#district').on('change', (e: any) => {
                const selVal = $(e.target).val() || 0;
                //this.f.distId.markAsDirty();
                this.f.distId.setValue(+selVal);
            });

            // show modal
            this.modalRef.result.then(resolve, resolve);
        });
    }

    // convenient getter for easy access to form fields
    get f() {
        return this.form.controls;
    }

    initForm() {
        // reset dropdowns
        $('#province').val(this.location.provinceId).trigger('change');
        this.districts = [];
        $('#district').val(this.location.districtId || '').trigger('change');

        this.similarLocations = [];
        this.isReady = true;
        this.isConfirmed = false;
        this.modalConfig.disableDoneButton = true;
        this.modalConfig.working = false;
        this.working = false;

        this.form = new FormGroup({
            id: new FormControl({ value: this.location.id, disabled: true }),
            provId: new FormControl(this.location.provinceId, [Validators.required, Validators.min(1)]),
            distId: new FormControl(this.location.districtId),
            distName: new FormControl(this.location.districtId > 0 ? '' : this.location.distName),
            name: new FormControl(this.location.name, Validators.required),
            gpsLat: new FormControl(this.location.gpsLat, [Validators.required, Validators.min(29.00000001), Validators.max(38.99999999)]),
            gpsLon: new FormControl(this.location.gpsLon, [Validators.required, Validators.min(60.00000001), Validators.max(74.99999999)]),
            isVerified: new FormControl(this.location.isVerified),
            //remarks: new FormControl(this.location.remarks),
            isNewDist: new FormControl(!this.location.id && !this.location.locationId ? false : !this.location.districtId)
        });

        if (this.location.id > 0 || this.location.locationId)
            this.getDistricts();
        
        this.subscriptions.push(
            this.form.valueChanges.subscribe((res) => {
                this.isConfirmed = false;

                if (!this.form.valid) {
                    this.modalConfig.disableDoneButton = true;
                    return;
                }

                if (this.f.isNewDist.value && !this.f.distName.value) {
                    this.modalConfig.disableDoneButton = true;
                    return;
                }

                if (!this.f.isNewDist.value && !this.f.distId.value) {
                    this.modalConfig.disableDoneButton = true;
                    return;
                }
                this.isReady = true;
                this.modalConfig.disableDoneButton = false;
            })
        );

        // bind gps inputs to update main marker on the map
        this.gpsSubscriptions.push(
            this.f.gpsLat.valueChanges
                .subscribe(() => {
                    this.refreshMainMarker();
                }));

        this.gpsSubscriptions.push(
            this.f.gpsLon.valueChanges
                .subscribe(() => {
                    this.refreshMainMarker();
                }));

        // get similar locations if user is approving
        if (this.modalConfig.doneButtonLabel === 'Approve')
            this.getSimilarLocations();
    }

    toggleNewDist(isNew: boolean): void {
        //if (isNew) {
        //    this.f.distName.setValidators(Validators.required);
        //    this.f.distId.removeValidators(Validators.required);
        //} else {console.log(this.f.isNewDist.value)
        //    this.f.distId.setValidators(Validators.required);
        //    this.f.distName.removeValidators(Validators.required);
        //}

        this.f.isNewDist.setValue(isNew);
    }

    // get a list of province's districts
    getDistricts(): void {
        this.gettingDist = true;

        this.subscriptions.push(
            this.sharedService.getDistricts(this.f.provId.value).subscribe({
                next: (dists: District[]) => {
                    this.districts = dists;
                },
                error: (e) => {
                    console.log(e);
                    this.gettingDist = false;
                },
                complete: () => {
                    this.cdr.detectChanges();
                    setTimeout(() => {
                        $('#district').val(this.f.distId.value).trigger('change');
                    }, 500);
                    
                    this.gettingDist = false;
                }
            })
        );
    }

    refreshMainMarker(): void {
        if (this.f.gpsLat.invalid || this.f.gpsLon.invalid)
            return;

        let gps = [+this.f.gpsLat.value, +this.f.gpsLon.value];
        let distProv = [this.f.distName.value, this.provinces.find(p => p.id == +this.f.provId.value)?.name];
        if (!this.f.isNewDist.value)
            distProv[0] = this.districts.find(d => d.id == +this.f.distId.value)?.name;

        this.locMapComponent
            .refreshMainMarker(this.f.name.value, distProv, gps);
    }

    getSimilarLocations(): void {
        this.working = true;
        this.modalConfig.working = true;

        const location = new Community(this.location.id || 0, this.location.locationId || 0,
            +this.f.provId.value, this.f.name.value, this.f.gpsLat.value,
            this.f.gpsLon.value);
        
        this.subscriptions.push(this.locationsService.getSimilarLocations(location).subscribe({
            next: (result: CommunityList[]) => {
                this.similarLocations = [];     // empty the list

                // if there are similar locations,
                if (result) {
                    // remove current location from the list
                    if (this.location.id)
                        this.similarLocations = result.filter(l => l.id !== this.location.id);
                    else
                        this.similarLocations = result.filter(l => l.locationId !== this.location.locationId);
                    
                    this.isReady = false;
                    this.isConfirmed = false;
                }
            },
            error: (err) => {
                this.working = false;
                this.modalConfig.working = false;
                console.log(err);
            },
            complete: () => {
                this.working = false;
                this.modalConfig.working = false;

                if (!this.similarLocations.length) {
                    this.isConfirmed = true;
                    if (this.modalConfig.doneButtonLabel !== 'Approve') {
                        this.save().then();
                    } else if (this.isConfirmed) {
                        this.messageService.info("Please click 'Approve' again to approve.");
                        this.isReady = true;
                    }
                    return;
                }
                    
                // add markers to the map
                this.locMapComponent.addMarkers(this.similarLocations, this.LOCATIONS_WITHIN_RADIUS);
            }
        }));
    }

    updateGps(gps: number[]): void {
        // first, remove subscription from gpsLat form control
        this.gpsSubscriptions.forEach((sb) => sb.unsubscribe());

        this.subscriptions.find
        this.f.gpsLat.setValue(gps[0]);
        this.f.gpsLat.markAsDirty();
        this.f.gpsLon.setValue(gps[1]);
        this.f.gpsLon.markAsDirty();

        // refresh marker
        this.refreshMainMarker();

        // resubscribe to gps value changes
        this.gpsSubscriptions.push(
            this.f.gpsLat.valueChanges
                .subscribe(() => {
                    this.refreshMainMarker();
                }));

        this.gpsSubscriptions.push(
            this.f.gpsLon.valueChanges
                .subscribe(() => {
                    this.refreshMainMarker();
                }));
    }

    async save(): Promise<boolean> {
        const result = new Subject<boolean>();

        if (!this.isConfirmed) {
            this.getSimilarLocations();
            return await lastValueFrom(result.asObservable());
        }

        this.modalConfig.working = true;

        const location = new Community(this.location.id || 0, this.location.locationId || 0,
            +this.f.provId.value, this.f.name.value, this.f.gpsLat.value, this.f.gpsLon.value,
            this.f.isVerified.value, this.isAdmin && this.modalConfig.doneButtonLabel === 'Approve',
            +this.f.distId.value, this.f.distName.value);//, this.f.remarks.value);

        if (this.f.isNewDist.value)
            location.districtId = 0;
        else
            location.distName = '';

        // update location info
        let operation = location.id > 0 || location.locationId > 0 ?
            this.locationsService.updateCommunity(location) :
            this.locationsService.addCommunity(location);

        this.subscriptions.push(operation.subscribe({
            error: (err) => {
                this.modalConfig.working = false;
                console.log(err);

                result.next(false);
                result.complete();
            },
            complete: () => {
                let successMsg = 'The location has been added successfully.';
                if (this.modalConfig.doneButtonLabel === 'Approve')
                    successMsg = 'The location has been approved successfully.';
                else if (location.id > 0 || location.locationId > 0)
                    successMsg = "The location has been updated successfully.";

                this.messageService.success(successMsg);

                this.modalConfig.working = false;
                this.modalRef.close();

                // notify to refresh locations table
                this.done.emit();
                
                result.next(true);
                result.complete();
            }
        }));
        
        return await lastValueFrom(result.asObservable());
    }

    // close dialogue
    async close(): Promise<void> {
        if (this.modalConfig.disableCancelButton !== undefined) {
            return;
        }

        if (
            this.modalConfig.shouldCancel === undefined ||
            (await this.modalConfig.shouldCancel())
        ) {
            const result =
                this.modalConfig.onCancel === undefined ||
                (await this.modalConfig.onCancel());
            this.modalRef.dismiss(result);
        }
    }

    ngOnDestroy() {
        this.locMapComponent = null;
        this.subscriptions.forEach((sb) => sb.unsubscribe());
        this.gpsSubscriptions.forEach((sb) => sb.unsubscribe());
    }
}