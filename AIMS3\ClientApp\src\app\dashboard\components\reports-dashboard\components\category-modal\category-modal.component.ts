import { Component, Input, Output, EventEmitter, OnInit, OnChanges, SimpleChanges, ChangeDetectionStrategy, ChangeDetectorRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { NgApexchartsModule } from 'ng-apexcharts';
import { 
  CategoryModalConfig, 
  IndicatorConfig, 
  ProcessedIndicator,
  DynamicColumnData,
  ApexChartData,
  IndicatorDataPoint,
  TableData,
  ReportFilter
} from '../../models/reports.model';
import { IndicatorConfigService } from '../../services/indicator-config.service';
import { ReportsService } from '../../services/reports.service'; // Add ReportsService import

// Interface for tab data structure
interface CategoryTabData {
  interventions: any[];
  activities: any[];
  indicators: any[];
  progress: any;
  analytics: any;
  performance: any;
}

// Interface for pie chart data specific to overview indicators
interface PieChartData {
  series: number[];
  options: {
    chart: any;
    labels: string[];
    colors: string[];
    legend: any;
    dataLabels: any;
    plotOptions: any;
    tooltip: any;
    title: any;
  };
}

// Interface specifically for overview indicators
interface OverviewIndicator {
  config: IndicatorConfig;
  data: IndicatorDataPoint[];
  chartData: PieChartData;
  summary: {
    total: number;
    average: number;
    count: number;
  };
}

@Component({
  selector: 'app-category-modal',
  standalone: true,
  imports: [CommonModule, FormsModule, NgApexchartsModule],
  changeDetection: ChangeDetectionStrategy.OnPush,
  template: `
    <div class="modal fade" [class.show]="isVisible" [style.display]="isVisible ? 'block' : 'none'" 
         tabindex="-1" role="dialog" aria-labelledby="categoryModalLabel" aria-hidden="true">
      <div class="modal-dialog modal-fullscreen" role="document">
        <div class="modal-content">
          <!-- Modal Header -->
          <div class="modal-header bg-primary text-white">
            <div class="d-flex align-items-center w-100">
              <div class="me-auto">
                <h4 class="modal-title mb-0" id="categoryModalLabel">
                  <i class="bi bi-collection-fill me-2"></i>
                  {{ getCategoryDisplayName() }}
                </h4>
                <p class="mb-0 opacity-75 small">
                  {{ categoryActivities.length }} Activities • {{ categoryInterventions.length }} Interventions
                </p>
              </div>
              <div class="d-flex gap-2">
                <button type="button" class="btn btn-light btn-sm" (click)="exportAllData()">
                  <i class="bi bi-download me-1"></i>Export All
                </button>
                <button type="button" class="btn btn-light btn-sm" (click)="refreshAllData()">
                  <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                </button>
                <button type="button" class="btn-close btn-close-white" (click)="close()" aria-label="Close"></button>
              </div>
            </div>
          </div>
          
          <!-- Tab Navigation -->
          <div class="modal-header-tabs bg-light border-bottom">
            <ul class="nav nav-tabs nav-fill w-100" id="categoryTabs">
              <li class="nav-item">
                <button class="nav-link" 
                        [class.active]="activeTab === 'overview'"
                        (click)="switchTab('overview')"
                        type="button">
                  <i class="bi bi-speedometer2 me-1"></i>
                  Overview
                  <span class="badge bg-primary ms-1">{{ getTabBadgeCount('overview') }}</span>
                </button>
              </li>
              <li class="nav-item">
                <button class="nav-link" 
                        [class.active]="activeTab === 'interventions'"
                        (click)="switchTab('interventions')"
                        type="button">
                  <i class="bi bi-diagram-3 me-1"></i>
                  Interventions
                  <span class="badge bg-success ms-1">{{ categoryInterventions.length }}</span>
                </button>
              </li>
              <li class="nav-item">
                <button class="nav-link" 
                        [class.active]="activeTab === 'indicators'"
                        (click)="switchTab('indicators')"
                        type="button">
                  <i class="bi bi-graph-up me-1"></i>
                  Indicators
                  <span class="badge bg-warning ms-1">{{ availableIndicators.length }}</span>
                </button>
              </li>
              <li class="nav-item">
                <button class="nav-link" 
                        [class.active]="activeTab === 'analytics'"
                        (click)="switchTab('analytics')"
                        type="button">
                  <i class="bi bi-graph-up-arrow me-1"></i>
                  Analytics
                  <span class="badge bg-dark ms-1">{{ getAnalyticsCount() }}</span>
                </button>
              </li>
            </ul>
          </div>
          
          <!-- Modal Body with Tab Content -->
          <div class="modal-body p-0">
            <!-- Loading State -->
            <div *ngIf="loading" class="text-center p-5">
              <div class="spinner-border text-primary mb-3" role="status" style="width: 3rem; height: 3rem;">
                <span class="visually-hidden">Loading category data...</span>
              </div>
              <h5>Loading {{ getCategoryDisplayName() }} Data</h5>
              <p class="text-muted">Processing interventions, activities, and indicators...</p>
            </div>
            
            <!-- Error State -->
            <div *ngIf="error && !loading" class="alert alert-danger m-4">
              <h6><i class="bi bi-exclamation-triangle me-2"></i>Error Loading Data</h6>
              <p class="mb-0">{{ error }}</p>
            </div>
            
            <!-- Overview Tab -->
            <div *ngIf="activeTab === 'overview' && !loading" class="tab-content p-4">
              <div class="row g-4">
                <!-- Dynamic Column Selection and Chart in One Row -->
                <div class="col-12" *ngIf="availableDynamicColumnOptions.length > 0">
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0">
                        <i class="bi bi-sliders me-2"></i>Indicator Overview
                      </h6>
                    </div>
                    <div class="card-body">
                      <div class="row align-items-center">
                        <!-- Dropdown Selection -->
                        <div class="col-md-4">
                          <label class="form-label small">Select Indicator</label>
                          <select 
                            class="form-select"
                            [value]="selectedOverviewIndicator || ''"
                            (change)="onOverviewIndicatorChange($event)">
                            <option value="">Select Indicator</option>
                            <option 
                              *ngFor="let option of availableDynamicColumnOptions" 
                              [value]="option.id">
                              {{ option.name }} ({{ option.unit }})
                            </option>
                          </select>
                        </div>
                        
                        <!-- Semi-Circle Chart -->
                        <div class="col-md-8" *ngIf="hasSelectedOverviewIndicator()">
                          <div class="text-center">
                            <h6 class="mb-3">{{ getSelectedIndicatorName() }}</h6>
                            <div class="chart-container">
                              <apx-chart
                                [series]="selectedOverviewChart.series"
                                [chart]="selectedOverviewChart.options.chart"
                                [labels]="selectedOverviewChart.options.labels"
                                [colors]="selectedOverviewChart.options.colors"
                                [plotOptions]="selectedOverviewChart.options.plotOptions"
                                [fill]="selectedOverviewChart.options.fill">
                              </apx-chart>
                            </div>
                            <div class="mt-2">
                              <small class="text-muted">
                                Value: {{ getSelectedIndicatorValue() | number:'1.0-0' }} {{ getSelectedIndicatorUnit() }}
                              </small>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- No Dynamic Columns Message -->
                <div class="col-12" *ngIf="availableDynamicColumnOptions.length === 0">
                  <div class="card">
                    <div class="card-body text-center p-5">
                      <i class="bi bi-pie-chart text-muted" style="font-size: 3rem;"></i>
                      <h5 class="text-muted mt-3">No Quantitative Indicators Available</h5>
                      <p class="text-muted">This {{ getCategoryDisplayName().toLowerCase() }} doesn't have quantitative data that can be displayed as charts.</p>
                      <div class="row g-3 mt-4">
                        <div class="col-md-3">
                          <div class="card bg-primary text-white">
                            <div class="card-body text-center p-3">
                              <i class="bi bi-diagram-3 h2 mb-2"></i>
                              <h4 class="card-title">{{ categoryInterventions.length }}</h4>
                              <p class="card-text small">Interventions</p>
                            </div>
                          </div>
                        </div>
                        <div class="col-md-3">
                          <div class="card bg-success text-white">
                            <div class="card-body text-center p-3">
                              <i class="bi bi-list-check h2 mb-2"></i>
                              <h4 class="card-title">{{ categoryActivities.length }}</h4>
                              <p class="card-text small">Activities</p>
                            </div>
                          </div>
                        </div>
                        <div class="col-md-3">
                          <div class="card bg-info text-white">
                            <div class="card-body text-center p-3">
                              <i class="bi bi-graph-up h2 mb-2"></i>
                              <h4 class="card-title">{{ availableIndicators.length }}</h4>
                              <p class="card-text small">Indicators</p>
                            </div>
                          </div>
                        </div>
                        <div class="col-md-3">
                          <div class="card bg-warning text-white">
                            <div class="card-body text-center p-3">
                              <i class="bi bi-percent h2 mb-2"></i>
                              <h4 class="card-title">{{ getCategoryProgress() }}%</h4>
                              <p class="card-text small">Progress</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Category Information -->
                <div class="col-md-6">
                  <div class="card h-100">
                    <div class="card-header">
                      <h5 class="card-title mb-0">
                        <i class="bi bi-info-circle me-2"></i>Category Information
                      </h5>
                    </div>
                    <div class="card-body">
                      <dl class="row mb-0">
                        <dt class="col-sm-5">Category Name:</dt>
                        <dd class="col-sm-7">{{ getCategoryDisplayName() }}</dd>
                        <dt class="col-sm-5">Total Cash Distributed:</dt>
                        <dd class="col-sm-7">\${{ getTotalCashDistributed() | number:'1.0-0' }}</dd>
                        <dt class="col-sm-5">Number of Projects:</dt>
                        <dd class="col-sm-7">{{ getNumberOfProjects() }}</dd>
                        <dt class="col-sm-5">Number of Activities:</dt>
                        <dd class="col-sm-7">{{ getNumberOfActivities() }}</dd>
                      </dl>
                    </div>
                  </div>
                </div>
                
                <!-- Quick Stats -->
                <div class="col-md-6">
                  <div class="card h-100">
                    <div class="card-header">
                      <h5 class="card-title mb-0">
                        <i class="bi bi-bar-chart me-2"></i>Quick Statistics
                      </h5>
                    </div>
                    <div class="card-body">
                      <div class="row g-3">
                        <div class="col-6">
                          <div class="border rounded p-3 text-center">
                            <div class="h4 text-success mb-1">{{ getActiveInterventions() }}</div>
                            <small class="text-muted">Active Interventions</small>
                          </div>
                        </div>
                        <div class="col-6">
                          <div class="border rounded p-3 text-center">
                            <div class="h4 text-primary mb-1">{{ getCompletedActivities() }}</div>
                            <small class="text-muted">Completed Activities</small>
                          </div>
                        </div>
                        <div class="col-6">
                          <div class="border rounded p-3 text-center">
                            <div class="h4 text-warning mb-1">{{ getPendingActivities() }}</div>
                            <small class="text-muted">Pending Activities</small>
                          </div>
                        </div>
                        <div class="col-6">
                          <div class="border rounded p-3 text-center">
                            <div class="h4 text-info mb-1">{{ getBeneficiariesCount() }}</div>
                            <small class="text-muted">Beneficiaries</small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Interventions Tab -->
            <div *ngIf="activeTab === 'interventions' && !loading" class="tab-content p-4">
              <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0">{{ getCategoryDisplayName() }} Interventions ({{ categoryInterventions.length }})</h5>
                <div class="d-flex gap-2">
                  <button class="btn btn-outline-primary btn-sm" (click)="exportInterventions()">
                    <i class="bi bi-download me-1"></i>Export
                  </button>
                  <button class="btn btn-outline-secondary btn-sm" (click)="filterInterventions()">
                    <i class="bi bi-funnel me-1"></i>Filter
                  </button>
                </div>
              </div>
              
              <div class="row g-3">
                <div class="col-md-6 col-lg-4" *ngFor="let intervention of categoryInterventions; trackBy: trackIntervention">
                  <div class="card h-100 intervention-card" (click)="viewInterventionDetails(intervention)">
                    <div class="card-header d-flex justify-content-between align-items-start">
                      <h6 class="card-title mb-0">{{ intervention.title || intervention.name }}</h6>
                      <span class="badge" [class]="getInterventionStatusClass(intervention)">
                        {{ intervention.status || 'Active' }}
                      </span>
                    </div>
                    <div class="card-body">
                      <p class="card-text small text-muted mb-2">
                        {{ intervention.description || intervention.summary || 'No description available' }}
                      </p>
                      <div class="mb-2">
                        <small class="text-muted">Total Cash Distributed:</small>
                        <div class="fw-semibold text-success">\${{ getInterventionCashDistributed(intervention) | number:'1.0-0' }}</div>
                      </div>
                      <div class="mb-2">
                        <small class="text-muted">Activities:</small>
                        <div class="fw-semibold">{{ getInterventionActivitiesCount(intervention) }}</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- No Interventions State -->
              <div *ngIf="categoryInterventions.length === 0" class="text-center p-5">
                <i class="bi bi-diagram-3 text-muted" style="font-size: 3rem;"></i>
                <h5 class="text-muted mt-3">No Interventions Found</h5>
                <p class="text-muted">This {{ getCategoryDisplayName().toLowerCase() }} doesn't have any interventions yet.</p>
              </div>
            </div>
            
            <!-- Activities Tab Removed -->
            
            <!-- Indicators Tab -->
            <div *ngIf="activeTab === 'indicators' && !loading" class="tab-content p-4">
              <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0">{{ getCategoryDisplayName() }} Indicators ({{ availableIndicators.length }})</h5>
                <div class="d-flex gap-2">
                  <button class="btn btn-outline-success btn-sm" (click)="addCustomIndicator()">
                    <i class="bi bi-plus me-1"></i>Add Indicator
                  </button>
                  <button class="btn btn-outline-primary btn-sm" (click)="exportIndicators()">
                    <i class="bi bi-download me-1"></i>Export
                  </button>
                </div>
              </div>
              
              <!-- Indicator Selection -->
              <div class="card mb-4">
                <div class="card-header">
                  <h6 class="mb-0">Select Indicators to Analyze</h6>
                </div>
                <div class="card-body">
                  <div class="row">
                    <div class="col-md-4" *ngFor="let indicator of availableIndicators">
                      <div class="form-check mb-2">
                        <input class="form-check-input" type="checkbox" 
                               [id]="'indicator-' + indicator.id"
                               [checked]="isIndicatorSelected(indicator.id)"
                               (change)="toggleIndicator(indicator.id, $event)">
                        <label class="form-check-label" [for]="'indicator-' + indicator.id">
                          <div class="fw-semibold">{{ indicator.name }}</div>
                          <small class="text-muted">{{ indicator.description || indicator.unit }}</small>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Indicators Display -->
              <div *ngIf="selectedIndicatorIds.size > 0">
                <div class="card">
                  <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0">Indicators Analysis ({{ selectedIndicatorIds.size }} selected)</h6>
                    <div class="btn-group btn-group-sm">
                      <button class="btn btn-outline-primary" 
                              [class.active]="currentView === 'chart'"
                              (click)="toggleCombinedView('chart')">
                        <i class="bi bi-bar-chart me-1"></i>Chart
                      </button>
                      <button class="btn btn-outline-primary" 
                              [class.active]="currentView === 'table'"
                              (click)="toggleCombinedView('table')">
                        <i class="bi bi-table me-1"></i>Table
                      </button>
                    </div>
                  </div>
                  <div class="card-body">
                    <!-- Chart View -->
                    <div *ngIf="currentView === 'chart' && combinedData?.chartData" class="chart-container">
                      <apx-chart
                        [series]="combinedData.chartData.series"
                        [chart]="combinedData.chartData.options.chart"
                        [colors]="combinedData.chartData.options.colors"
                        [dataLabels]="combinedData.chartData.options.dataLabels"
                        [legend]="combinedData.chartData.options.legend"
                        [tooltip]="combinedData.chartData.options.tooltip"
                        [xaxis]="combinedData.chartData.options.xaxis"
                        [yaxis]="combinedData.chartData.options.yaxis"
                        [plotOptions]="combinedData.chartData.options.plotOptions">
                      </apx-chart>
                    </div>
                    
                    <!-- Table View -->
                    <div *ngIf="currentView === 'table' && combinedData?.tableData" class="table-responsive">
                      <table class="table table-striped">
                        <thead class="table-dark">
                          <tr>
                            <th *ngFor="let header of combinedData.tableData.headers">{{ header }}</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr *ngFor="let row of combinedData.tableData.rows; trackBy: trackByRowIndex">
                            <td *ngFor="let header of combinedData.tableData.headers">
                              {{ formatTableValue(row[header], header) }}
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- No Indicators Selected -->
              <div *ngIf="selectedIndicatorIds.size === 0 && availableIndicators.length > 0" class="text-center p-5">
                <i class="bi bi-graph-up text-muted" style="font-size: 3rem;"></i>
                <h5 class="text-muted mt-3">Select Indicators to View Analysis</h5>
                <p class="text-muted">Choose one or more indicators from the checkboxes above.</p>
              </div>
              
              <!-- No Indicators Available -->
              <div *ngIf="availableIndicators.length === 0" class="text-center p-5">
                <i class="bi bi-graph-up text-muted" style="font-size: 3rem;"></i>
                <h5 class="text-muted mt-3">No Indicators Available</h5>
                <p class="text-muted">No quantitative indicators found for this {{ getCategoryDisplayName().toLowerCase() }}.</p>
              </div>
            </div>
            
            <!-- Progress Tab -->
            <div *ngIf="activeTab === 'progress' && !loading" class="tab-content p-4">
              <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0">Progress Tracking</h5>
                <div class="d-flex gap-2">
                  <select class="form-select form-select-sm" style="width: auto;" [(ngModel)]="progressTimeframe">
                    <option value="monthly">Monthly</option>
                    <option value="quarterly">Quarterly</option>
                    <option value="yearly">Yearly</option>
                  </select>
                  <button class="btn btn-outline-primary btn-sm" (click)="exportProgress()">
                    <i class="bi bi-download me-1"></i>Export
                  </button>
                </div>
              </div>
              
              <div class="row g-4">
                <!-- Overall Progress -->
                <div class="col-md-6">
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0">Overall {{ getCategoryDisplayName() }} Progress</h6>
                    </div>
                    <div class="card-body text-center">
                      <div class="progress-circle mb-3">
                        <svg width="120" height="120" class="progress-ring">
                          <circle cx="60" cy="60" r="50" stroke="#e9ecef" stroke-width="8" fill="transparent"></circle>
                          <circle cx="60" cy="60" r="50" stroke="#28a745" stroke-width="8" fill="transparent"
                                  [attr.stroke-dasharray]="getCircleProgress(getCategoryProgress())"
                                  stroke-dashoffset="0" stroke-linecap="round"></circle>
                        </svg>
                        <div class="progress-text">
                          <span class="display-6 fw-bold text-success">{{ getCategoryProgress() }}%</span>
                        </div>
                      </div>
                      <p class="text-muted mb-0">{{ getCategoryDisplayName() }} Completion</p>
                    </div>
                  </div>
                </div>
                
                <!-- Progress Breakdown -->
                <div class="col-md-6">
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0">Progress Breakdown</h6>
                    </div>
                    <div class="card-body">
                      <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                          <span>Interventions Progress</span>
                          <span>{{ getInterventionsProgress() }}%</span>
                        </div>
                        <div class="progress">
                          <div class="progress-bar bg-primary" 
                               [style.width.%]="getInterventionsProgress()"></div>
                        </div>
                      </div>
                      <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                          <span>Activities Progress</span>
                          <span>{{ getActivitiesProgress() }}%</span>
                        </div>
                        <div class="progress">
                          <div class="progress-bar bg-success" 
                               [style.width.%]="getActivitiesProgress()"></div>
                        </div>
                      </div>
                      <div class="mb-3">
                        <div class="d-flex justify-content-between mb-1">
                          <span>Indicators Achievement</span>
                          <span>{{ getIndicatorsProgress() }}%</span>
                        </div>
                        <div class="progress">
                          <div class="progress-bar bg-info" 
                               [style.width.%]="getIndicatorsProgress()"></div>
                        </div>
                      </div>
                      <div>
                        <div class="d-flex justify-content-between mb-1">
                          <span>Budget Utilization</span>
                          <span>{{ getBudgetUtilization() }}%</span>
                        </div>
                        <div class="progress">
                          <div class="progress-bar bg-warning" 
                               [style.width.%]="getBudgetUtilization()"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Progress Timeline -->
                <div class="col-12">
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0">Progress Timeline</h6>
                    </div>
                    <div class="card-body">
                      <div class="timeline">
                        <div class="timeline-item" *ngFor="let milestone of getProgressMilestones()">
                          <div class="timeline-marker" [class]="milestone.status"></div>
                          <div class="timeline-content">
                            <h6 class="mb-1">{{ milestone.title }}</h6>
                            <p class="text-muted mb-1">{{ milestone.description }}</p>
                            <small class="text-muted">{{ milestone.date | date:'mediumDate' }}</small>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            
            <!-- Analytics Tab -->
            <div *ngIf="activeTab === 'analytics' && !loading" class="tab-content p-4">
              <div class="d-flex justify-content-between align-items-center mb-4">
                <h5 class="mb-0">Advanced Analytics</h5>
                <div class="d-flex gap-2">
                  <select class="form-select form-select-sm" style="width: auto;" [(ngModel)]="analyticsTimeframe">
                    <option value="3months">Last 3 Months</option>
                    <option value="6months">Last 6 Months</option>
                    <option value="1year">Last Year</option>
                    <option value="all">All Time</option>
                  </select>
                  <button class="btn btn-outline-primary btn-sm" (click)="exportAnalytics()">
                    <i class="bi bi-download me-1"></i>Export
                  </button>
                </div>
              </div>
              
              <div class="row g-4">
                <!-- Performance Metrics -->
                <div class="col-lg-8">
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0">Performance Trends</h6>
                    </div>
                    <div class="card-body">
                      <div class="chart-container">
                        <apx-chart
                          [series]="getPerformanceChartData().series"
                          [chart]="getPerformanceChartData().options.chart"
                          [colors]="getPerformanceChartData().options.colors"
                          [dataLabels]="getPerformanceChartData().options.dataLabels"
                          [legend]="getPerformanceChartData().options.legend"
                          [tooltip]="getPerformanceChartData().options.tooltip"
                          [xaxis]="getPerformanceChartData().options.xaxis"
                          [yaxis]="getPerformanceChartData().options.yaxis">
                        </apx-chart>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Key Metrics -->
                <div class="col-lg-4">
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0">Key Metrics</h6>
                    </div>
                    <div class="card-body">
                      <div class="metric-item mb-3">
                        <div class="d-flex justify-content-between">
                          <span class="text-muted">Efficiency Score</span>
                          <span class="fw-bold text-success">{{ getEfficiencyScore() }}%</span>
                        </div>
                        <div class="progress mt-1" style="height: 4px;">
                          <div class="progress-bar bg-success" [style.width.%]="getEfficiencyScore()"></div>
                        </div>
                      </div>
                      <div class="metric-item mb-3">
                        <div class="d-flex justify-content-between">
                          <span class="text-muted">Impact Rating</span>
                          <span class="fw-bold text-primary">{{ getImpactRating() }}/10</span>
                        </div>
                        <div class="progress mt-1" style="height: 4px;">
                          <div class="progress-bar bg-primary" [style.width.%]="getImpactRating() * 10"></div>
                        </div>
                      </div>
                      <div class="metric-item mb-3">
                        <div class="d-flex justify-content-between">
                          <span class="text-muted">Risk Level</span>
                          <span class="fw-bold" [class]="getRiskLevelClass()">{{ getRiskLevel() }}</span>
                        </div>
                      </div>
                      <div class="metric-item">
                        <div class="d-flex justify-content-between">
                          <span class="text-muted">Sustainability</span>
                          <span class="fw-bold text-info">{{ getSustainabilityScore() }}%</span>
                        </div>
                        <div class="progress mt-1" style="height: 4px;">
                          <div class="progress-bar bg-info" [style.width.%]="getSustainabilityScore()"></div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- Comparative Analysis -->
                <div class="col-12">
                  <div class="card">
                    <div class="card-header">
                      <h6 class="mb-0">Comparative Analysis</h6>
                    </div>
                    <div class="card-body">
                      <div class="table-responsive">
                        <table class="table table-bordered">
                          <thead class="table-light">
                            <tr>
                              <th>Metric</th>
                              <th>Current Period</th>
                              <th>Previous Period</th>
                              <th>Change</th>
                              <th>Trend</th>
                            </tr>
                          </thead>
                          <tbody>
                            <tr *ngFor="let metric of getComparativeMetrics()">
                              <td class="fw-semibold">{{ metric.name }}</td>
                              <td>{{ metric.current }}</td>
                              <td>{{ metric.previous }}</td>
                              <td>
                                <span [class]="metric.changeClass">
                                  {{ metric.change }}
                                </span>
                              </td>
                              <td>
                                <i [class]="metric.trendIcon" [class]="metric.trendClass"></i>
                              </td>
                            </tr>
                          </tbody>
                        </table>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Modal Footer -->
          <div class="modal-footer bg-light">
            <div class="d-flex justify-content-between align-items-center w-100">
              <div class="text-muted small">
                Last updated: {{ lastUpdated | date:'medium' || 'Just now' }}
              </div>
              <div class="d-flex gap-2">
                <button type="button" class="btn btn-outline-secondary" (click)="toggleDebugInfo()" *ngIf="!showDebugInfo">
                  <i class="bi bi-bug me-1"></i>Debug Info
                </button>
                <button type="button" class="btn btn-secondary" (click)="close()">
                  Close
                </button>
                <button type="button" class="btn btn-primary" (click)="refreshAllData()" [disabled]="loading">
                  <span *ngIf="loading" class="spinner-border spinner-border-sm me-1"></span>
                  <i *ngIf="!loading" class="bi bi-arrow-clockwise me-1"></i>
                  Refresh All
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Debug Panel (floating) -->
    <div *ngIf="showDebugInfo" class="debug-panel-overlay">
      <div class="debug-panel-content">
        <div class="card border-info">
          <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
            <h6 class="mb-0">Debug Information</h6>
            <button type="button" class="btn-close btn-close-white" (click)="toggleDebugInfo()"></button>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-6">
                <strong>Category ID:</strong> {{ categoryId || 'Not set' }}<br>
                <strong>Activities Count:</strong> {{ activities?.length || 0 }}<br>
                <strong>Dynamic Columns Count:</strong> {{ dynamicColumns?.length || 0 }}<br>
                <strong>Available Indicators:</strong> {{ availableIndicators?.length || 0 }}
              </div>
              <div class="col-md-6">
                <strong>Config Loaded:</strong> {{ config ? 'Yes' : 'No' }}<br>
                <strong>Modal Visible:</strong> {{ isVisible ? 'Yes' : 'No' }}<br>
                <strong>Active Tab:</strong> {{ activeTab }}<br>
                <strong>Selected Indicators:</strong> {{ selectedIndicatorIds.size }}
              </div>
            </div>
            <div class="mt-2" *ngIf="activities?.length > 0">
              <strong>Sample Activity:</strong>
              <pre class="small bg-light p-2 rounded">{{ getSampleActivityInfo() }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .modal-fullscreen {
      // margin-top: 90px;
      width: 85vw;
      max-width: none;
      /* height: 78%; */
      margin: 90px auto 10px auto; 
    }
    
    /* Modal Backdrop Blur Effect */
    .modal {
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
    }
    
    .modal.show {
      background-color: rgba(0, 0, 0, 0.6) !important;
    }
    
    .modal-fullscreen .modal-content {
      height: 100vh;
      border-radius: 12px;
      box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3), 
                  0 10px 20px rgba(0, 0, 0, 0.2);
      border: 1px solid rgba(255, 255, 255, 0.1);
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
      position: relative;
      z-index: 1055;
    }
    
    /* Enhanced Modal Focus */
    .modal-content::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      background: linear-gradient(45deg, #007bff, #28a745, #ffc107, #dc3545);
      border-radius: 14px;
      z-index: -1;
      opacity: 0.1;
      animation: modalGlow 3s ease-in-out infinite alternate;
    }
    
    @keyframes modalGlow {
      0% { opacity: 0.1; }
      100% { opacity: 0.3; }
    }
    
    /* Blur page content behind modal */
    body.modal-open .main-content,
    body.modal-open .navbar,
    body.modal-open .sidebar,
    body.modal-open [class*="container"] {
      filter: blur(3px);
      transition: filter 0.3s ease-in-out;
    }
    
    /* Remove blur when modal closes */
    body:not(.modal-open) .main-content,
    body:not(.modal-open) .navbar,
    body:not(.modal-open) .sidebar,
    body:not(.modal-open) [class*="container"] {
      filter: none;
      transition: filter 0.3s ease-in-out;
    }
    
    .modal-header-tabs {
      padding: 0;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      background: rgba(248, 249, 250, 0.95);
    }
    
    .nav-tabs .nav-link {
      border-bottom: none;
      color: #6c757d;
      font-weight: 500;
      padding: 1rem 1.5rem;
      backdrop-filter: blur(5px);
      -webkit-backdrop-filter: blur(5px);
      transition: all 0.3s ease;
    }
    
    .nav-tabs .nav-link:hover {
      background-color: rgba(255, 255, 255, 0.9);
      transform: translateY(-1px);
    }
    
    .nav-tabs .nav-link.active {
      background-color: rgba(255, 255, 255, 0.95);
      border-color: #dee2e6 #dee2e6 rgba(255, 255, 255, 0.95);
      color: #495057;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .tab-content {
      min-height: calc(100vh - 300px);
      background: rgba(248, 249, 250, 0.98);
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }
    
    /* Enhanced Cards with Glass Effect */
    .card {
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      background: rgba(255, 255, 255, 0.9);
      border: 1px solid rgba(255, 255, 255, 0.2);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
      transition: all 0.3s ease;
    }
    
    .card:hover {
      background: rgba(255, 255, 255, 0.95);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .intervention-card {
      cursor: pointer;
      transition: all 0.2s ease;
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
    }
    
    .intervention-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .indicator-chart-card {
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      transition: all 0.3s ease;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .indicator-chart-card:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      background: rgba(255, 255, 255, 0.95);
    }
    
    .chart-container {
      backdrop-filter: blur(5px);
      -webkit-backdrop-filter: blur(5px);
    }
    
    .progress-circle {
      position: relative;
      display: inline-block;
    }
    
    .progress-ring {
      transform: rotate(-90deg);
      filter: drop-shadow(0 2px 8px rgba(0, 0, 0, 0.2));
    }
    
    .progress-text {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .timeline {
      position: relative;
      padding-left: 30px;
    }
    
    .timeline::before {
      content: '';
      position: absolute;
      left: 15px;
      top: 0;
      bottom: 0;
      width: 2px;
      background: linear-gradient(to bottom, #007bff, #28a745);
      box-shadow: 0 0 10px rgba(0, 123, 255, 0.3);
    }
    
    .timeline-item {
      position: relative;
      margin-bottom: 20px;
    }
    
    .timeline-marker {
      position: absolute;
      left: -22px;
      top: 0;
      width: 12px;
      height: 12px;
      border-radius: 50%;
      background: #28a745;
      border: 2px solid white;
      box-shadow: 0 0 0 2px #dee2e6, 0 2px 8px rgba(0, 0, 0, 0.2);
    }
    
    .timeline-marker.pending {
      background: #ffc107;
    }
    
    .timeline-marker.cancelled {
      background: #dc3545;
    }
    
    .metric-item {
      padding: 0.5rem 0;
      border-bottom: 1px solid rgba(233, 236, 239, 0.5);
      backdrop-filter: blur(5px);
      -webkit-backdrop-filter: blur(5px);
    }
    
    .metric-item:last-child {
      border-bottom: none;
    }
    
    .debug-panel-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: rgba(0, 0, 0, 0.5);
      backdrop-filter: blur(8px);
      -webkit-backdrop-filter: blur(8px);
      z-index: 9999;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .debug-panel-content {
      max-width: 800px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      backdrop-filter: blur(20px);
      -webkit-backdrop-filter: blur(20px);
    }
    
    .badge.bg-purple {
      background-color: #6f42c1 !important;
      box-shadow: 0 2px 8px rgba(111, 66, 193, 0.3);
    }
    
    .text-purple {
      color: #6f42c1 !important;
    }
    
    .btn-group .btn.active {
      background-color: var(--bs-primary);
      border-color: var(--bs-primary);
      color: white;
      box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    }
    
    /* Enhanced Button Effects */
    .btn {
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      transition: all 0.3s ease;
    }
    
    .btn:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    }
    
    /* Table Enhancement */
    .table {
      backdrop-filter: blur(10px);
      -webkit-backdrop-filter: blur(10px);
      background: rgba(255, 255, 255, 0.9);
    }
    
    .table th {
      backdrop-filter: blur(15px);
      -webkit-backdrop-filter: blur(15px);
      background: rgba(52, 58, 64, 0.95) !important;
    }
  `]
})
export class CategoryModalComponent implements OnInit, OnChanges {
  @Input() isVisible: boolean = false;
  @Input() categoryId: string | number = ''; // Allow both string and number types
  @Input() categoryName: string = ''; // Add category name input
  @Input() dynamicColumns: DynamicColumnData[] = [];
  @Input() activities: any[] = [];
  @Output() closeModal = new EventEmitter<void>();

  // Tab management
  activeTab: string = 'overview';
  lastUpdated: Date = new Date();

  // Data properties
  config: CategoryModalConfig | null = null;
  availableIndicators: IndicatorConfig[] = [];
  selectedIndicatorIds: Set<string> = new Set();
  processedIndicators: ProcessedIndicator[] = [];
  indicatorViews: { [key: string]: 'table' | 'chart' } = {};
  
  // Tab-specific data
  categoryInterventions: any[] = [];
  categoryActivities: any[] = [];
  overviewIndicators: OverviewIndicator[] = []; // Updated to use OverviewIndicator interface
  selectedDynamicColumns: string[] = []; // Track selected dynamic columns for overview
  availableDynamicColumnOptions: { name: string; id: string; unit: string }[] = []; // Available columns for selection
  selectedOverviewIndicator: string = ''; // Single selected indicator for overview
  consolidatedIndicatorMapping = new Map<string, { name: string; id: string; unit: string; totalValue: number; count: number; columns: DynamicColumnData[] }>(); // Store consolidated indicator mappings
  tabData: CategoryTabData = {
    interventions: [],
    activities: [],
    indicators: [],
    progress: null,
    analytics: null,
    performance: null
  };

  // Combined data for multiple indicators
  combinedData: any = null;
  currentView: 'table' | 'chart' = 'chart';

  // Filter states
  activityStatusFilter: string = '';
  progressTimeframe: string = 'quarterly';
  analyticsTimeframe: string = '6months';

  loading: boolean = false;
  error: string | null = null;
  showDebugInfo: boolean = false;

  constructor(private indicatorConfigService: IndicatorConfigService, private changeDetectorRef: ChangeDetectorRef, private reportsService: ReportsService) {}

  ngOnInit(): void {
    console.log('🎯 CategoryModal: Component initialized');
    if (this.isVisible && this.categoryId) {
      this.loadCategoryData();
    }
  }

  ngOnChanges(changes: SimpleChanges): void {
    console.log('🎯 CategoryModal: Changes detected:', changes);
    
    // Check categoryId changes with detailed logging
    if (changes['categoryId']) {
      console.log('🎯 CategoryModal: CategoryId changed:', {
        previousValue: changes['categoryId'].previousValue,
        currentValue: changes['categoryId'].currentValue,
        isFirstChange: changes['categoryId'].firstChange
      });
    }
    
    if (changes['isVisible']?.currentValue && this.categoryId) {
      console.log('🎯 CategoryModal: Modal becoming visible with categoryId:', this.categoryId);
      this.loadCategoryData();
    }
    
    if (changes['categoryId']?.currentValue) {
      console.log('🎯 CategoryModal: CategoryId updated, loading data for:', this.categoryId);
      this.loadCategoryData();
    }
    
    if (changes['activities'] || changes['dynamicColumns']) {
      console.log('🎯 CategoryModal: Activities or dynamic columns changed, reloading data');
      this.loadCategoryData();
    }
  }

  // Tab Management Methods
  switchTab(tab: string): void {
    this.activeTab = tab;
    console.log(`🎯 CategoryModal: Switched to tab: ${tab}`);
    
    // Load tab-specific data if needed
    this.loadTabData(tab);
    this.changeDetectorRef.detectChanges();
  }

  getTabBadgeCount(tab: string): number {
    switch (tab) {
      case 'overview': return 1;
      case 'interventions': return this.categoryInterventions.length;
      case 'indicators': return this.availableIndicators.length;
      case 'analytics': return this.getAnalyticsCount();
      default: return 0;
    }
  }

  // Data Loading Methods
  private loadCategoryData(): void {
    this.loading = true;
    this.error = null;
    this.lastUpdated = new Date();
    
    // Validate categoryId before proceeding - handle both string and number types
    const categoryIdStr = this.categoryId?.toString();
    if (!categoryIdStr || categoryIdStr.trim() === '') {
      console.error('🎯 CategoryModal: categoryId is empty or undefined:', this.categoryId);
      this.error = 'Category ID is required to load data';
      this.loading = false;
      this.changeDetectorRef.detectChanges();
      return;
    }
    
    try {
      console.log('🎯 CategoryModal: Loading category data using hierarchical endpoint for category:', this.categoryId);
      
      // Create filters for this specific category - ensure it's a string for the filter
      const categoryFilters: ReportFilter = {
        catIds: [categoryIdStr] // Use the string version for the filter
      };
      
      console.log('🎯 CategoryModal: Making API call with filters:', categoryFilters);
      
      // Call hierarchical endpoint with dynamic columns enabled
      this.reportsService.getHierarchicalReport(categoryFilters, { includeDynamicColumns: true })
        .subscribe({
          next: (hierarchicalData) => {
            console.log('🎯 CategoryModal: Hierarchical data loaded:', hierarchicalData);
            
            // Extract category-specific data from hierarchical response
            this.processCategoryHierarchicalData(hierarchicalData);
            
            // Process overview indicators for pie charts
            this.processOverviewIndicators();
            
            // Set active tab to overview
            this.activeTab = 'overview';
            
            this.loading = false;
            this.changeDetectorRef.detectChanges();
            
            console.log('🎯 CategoryModal: Category data processing complete:', {
              categoryId: this.categoryId,
              activities: this.categoryActivities.length,
              interventions: this.categoryInterventions.length,
              indicators: this.availableIndicators.length,
              overviewIndicators: this.overviewIndicators.length
            });
          },
          error: (error) => {
            console.error('🎯 CategoryModal: Error loading hierarchical data:', error);
            this.error = `Failed to load category data from server: ${error.message || error}`;
            this.loading = false;
            this.changeDetectorRef.detectChanges();
            
            // Fallback to original method if hierarchical fails
            this.loadCategoryDataFallback();
          }
        });
      
    } catch (error) {
      console.error('🎯 CategoryModal: Error in loadCategoryData:', error);
      this.error = 'Failed to load category data';
      this.loading = false;
      this.changeDetectorRef.detectChanges();
    }
  }

  // Process hierarchical data specifically for this category
  private processCategoryHierarchicalData(hierarchicalData: any[]): void {
    console.log('🎯 CategoryModal: Processing hierarchical data for category:', this.categoryId);
    console.log('🎯 CategoryModal: Hierarchical data structure:', {
      dataLength: hierarchicalData.length,
      sampleStructure: hierarchicalData.length > 0 ? Object.keys(hierarchicalData[0]) : [],
      firstItem: hierarchicalData.length > 0 ? hierarchicalData[0] : null
    });
    
    // Initialize arrays
    this.categoryActivities = [];
    this.categoryInterventions = [];
    const allDynamicColumns: DynamicColumnData[] = [];
    let foundCategoryName: string | null = null;
    
    // Use a Map to deduplicate interventions by ID
    const interventionsMap = new Map<string, any>();
    
    // Convert categoryId to string for comparison
    const categoryIdStr = this.categoryId?.toString();
    
    // Extract activities and dynamic columns from hierarchical data
    hierarchicalData.forEach((group, groupIndex) => {
      console.log(`🎯 CategoryModal: Processing group ${groupIndex}:`, {
        hasProjects: !!group.projects,
        projectsCount: group.projects?.length || 0,
        groupKeys: Object.keys(group),
        categoryId: group.categoryId,
        name: group.name || group.title
      });
      
      if (group.projects && Array.isArray(group.projects)) {
        group.projects.forEach((project: any, projectIndex: number) => {
          console.log(`🎯 CategoryModal: Processing project ${projectIndex}:`, {
            hasInterventions: !!project.interventions,
            interventionsCount: project.interventions?.length || 0,
            projectId: project.id,
            projectName: project.name || project.title
          });
          
          if (project.interventions && Array.isArray(project.interventions)) {
            project.interventions.forEach((intervention: any, interventionIndex: number) => {
              console.log(`🎯 CategoryModal: Processing intervention ${interventionIndex}:`, {
                interventionId: intervention.id,
                interventionCategoryId: intervention.categoryId,
                targetCategoryId: categoryIdStr,
                matches: intervention.categoryId?.toString() === categoryIdStr,
                hasActivities: !!intervention.activities,
                activitiesCount: intervention.activities?.length || 0
              });
              
              // Check if this intervention belongs to our category
              if (intervention.categoryId?.toString() === categoryIdStr) {
                
                // Capture category name from various sources
                if (!foundCategoryName) {
                  foundCategoryName = intervention.categoryName || 
                                    intervention.categoryTitle ||
                                    intervention.category?.name ||
                                    intervention.category?.title ||
                                    null;
                }
                
                // Add intervention to our deduplication map
                if (!interventionsMap.has(intervention.id)) {
                  interventionsMap.set(intervention.id, {
                    id: intervention.id,
                    title: intervention.name || intervention.title,
                    name: intervention.name || intervention.title,
                    description: intervention.description,
                    status: intervention.status || 'Active',
                    budget: intervention.summary?.totalBudget || intervention.budget || 0,
                    activities: [],
                    categoryName: foundCategoryName,
                    category: intervention.category
                  });
                  
                  console.log('🎯 CategoryModal: Added new intervention to map:', {
                    id: intervention.id,
                    name: intervention.name || intervention.title,
                    mapSize: interventionsMap.size
                  });
                } else {
                  console.log('🎯 CategoryModal: Intervention already exists in map, skipping duplicate:', {
                    id: intervention.id,
                    name: intervention.name || intervention.title
                  });
                }
                
                // Extract activities with their dynamic columns
                if (intervention.activities && Array.isArray(intervention.activities)) {
                  intervention.activities.forEach((activity: any, activityIndex: number) => {
                    console.log(`🎯 CategoryModal: Processing activity ${activityIndex}:`, {
                      activityId: activity.id,
                      activityName: activity.name || activity.title,
                      hasDynamicColumns: !!activity.dynamicColumns,
                      dynamicColumnsCount: activity.dynamicColumns?.length || 0,
                      dynamicColumnsSample: activity.dynamicColumns?.slice(0, 2)
                    });
                    
                    // Add activity to our list
                    this.categoryActivities.push({
                      ...activity,
                      intervention: intervention,
                      dynamicColumns: activity.dynamicColumns || [],
                      categoryName: foundCategoryName,
                      category: intervention.category
                    });
                    
                    // Add activity to the intervention's activities list in the map
                    const interventionInMap = interventionsMap.get(intervention.id);
                    if (interventionInMap) {
                      interventionInMap.activities.push(activity);
                    }
                    
                    // Collect dynamic columns with detailed processing
                    if (activity.dynamicColumns && Array.isArray(activity.dynamicColumns)) {
                      activity.dynamicColumns.forEach((col: any, colIndex: number) => {
                        const processedColumn: DynamicColumnData = {
                          id: col.id || `${activity.id}_${colIndex}`,
                          columnId: col.columnId || col.ColumnId || col.id,
                          columnName: col.columnName || col.Name || col.name || `Column_${colIndex}`,
                          displayName: col.displayName || col.DisplayName || activity.uniqueId || activity.name || `Activity ${activity.id}`,
                          columnType: col.columnType || col.DataType || col.dataType || 'string',
                          dataType: col.dataType || col.DataType || col.columnType || 'string',
                          value: col.value !== undefined ? col.value : col.Value,
                          unit: col.unit || col.Unit || '',
                          description: col.description || col.Description || '',
                          order: col.order || col.Order || colIndex
                        };
                        
                        allDynamicColumns.push(processedColumn);
                        
                        // Log first few dynamic columns for debugging
                        if (allDynamicColumns.length <= 5) {
                          console.log(`🎯 CategoryModal: Processed dynamic column ${allDynamicColumns.length}:`, {
                            columnName: processedColumn.columnName,
                            value: processedColumn.value,
                            unit: processedColumn.unit,
                            dataType: processedColumn.dataType,
                            activityName: activity.name || activity.title,
                            activityId: activity.id
                          });
                        }
                      });
                    }
                  });
                }
              }
            });
          }
        });
      }
    });
    
    // Convert interventions map to array to remove duplicates
    this.categoryInterventions = Array.from(interventionsMap.values());
    
    // Store the found category name if we don't have one already
    if (foundCategoryName && !this.categoryName) {
      this.categoryName = foundCategoryName;
    }
    
    // Set the dynamic columns for indicator processing
    this.dynamicColumns = allDynamicColumns;
    
    console.log('🎯 CategoryModal: Processed hierarchical data complete:', {
      categoryId: categoryIdStr,
      categoryName: foundCategoryName || this.categoryName,
      activities: this.categoryActivities.length,
      interventions: this.categoryInterventions.length,
      uniqueInterventions: interventionsMap.size,
      dynamicColumns: allDynamicColumns.length,
      uniqueColumnNames: [...new Set(allDynamicColumns.map(col => col.columnName))],
      columnValueTypes: allDynamicColumns.map(col => ({ 
        name: col.columnName, 
        value: col.value, 
        type: typeof col.value 
      })).slice(0, 5)
    });
    
    // Create indicators from dynamic columns
    this.createCategoryIndicators();
  }

  // Create indicators from the category's dynamic columns
  private createCategoryIndicators(): void {
    console.log('🎯 CategoryModal: Creating indicators from hierarchical endpoint dynamic columns:', this.dynamicColumns.length);
    
    // Log sample of all dynamic columns received from hierarchical endpoint
    if (this.dynamicColumns.length > 0) {
      console.log('🎯 CategoryModal: Sample dynamic columns from hierarchical endpoint:', 
        this.dynamicColumns.slice(0, 3).map(col => ({
          columnName: col.columnName,
          value: col.value,
          unit: col.unit,
          dataType: col.dataType || col.columnType,
          displayName: col.displayName
        }))
      );
    }
    
    // Filter for quantitative columns that can be used as indicators using isQuantitativeColumn method
    const indicatorColumns = this.dynamicColumns.filter(col => this.isQuantitativeColumn(col));
    console.log('🎯 CategoryModal: Found quantitative columns after isQuantitativeColumn filter:', indicatorColumns.length);
    
    // Log sample of filtered quantitative columns
    if (indicatorColumns.length > 0) {
      console.log('🎯 CategoryModal: Sample quantitative columns for dropdown:', 
        indicatorColumns.slice(0, 3).map(col => ({
          columnName: col.columnName,
          value: col.value,
          unit: col.unit,
          isQuantitative: this.isQuantitativeColumn(col)
        }))
      );
    }
    
    // Create indicators from the filtered quantitative columns
    this.availableIndicators = this.createIndicatorsFromQuantitativeColumns(indicatorColumns);
    
    // Create available dynamic column options for dropdown selection
    this.availableDynamicColumnOptions = this.createDynamicColumnOptions(indicatorColumns);
    
    // Log created dropdown options
    console.log('🎯 CategoryModal: Created dropdown options from hierarchical data:', 
      this.availableDynamicColumnOptions.map(opt => ({
        id: opt.id,
        name: opt.name,
        unit: opt.unit
      }))
    );
    
    // Initialize with the first selected by default (for single dropdown)
    if (this.availableDynamicColumnOptions.length > 0) {
      this.selectedOverviewIndicator = this.availableDynamicColumnOptions[0].id;
    }
    
    console.log('🎯 CategoryModal: Successfully created indicators from hierarchical endpoint for category:', {
      categoryId: this.categoryId,
      totalDynamicColumns: this.dynamicColumns.length,
      quantitativeColumns: indicatorColumns.length,
      availableIndicators: this.availableIndicators.length,
      dropdownOptions: this.availableDynamicColumnOptions.length,
      selectedOverviewIndicator: this.selectedOverviewIndicator,
      indicators: this.availableIndicators.map(i => ({
        id: i.id,
        name: i.name,
        chartType: i.chartType,
        unit: i.unit
      }))
    });
  }

  // Create available dynamic column options for selection
  private createDynamicColumnOptions(indicatorColumns: DynamicColumnData[]): { name: string; id: string; unit: string }[] {
    console.log('🎯 CategoryModal: Creating consolidated dynamic column options from', indicatorColumns.length, 'quantitative columns');
    
    // Group columns by their beautified name (consolidate same indicators from different projects)
    const consolidatedIndicators = new Map<string, { 
      name: string; 
      id: string; 
      unit: string; 
      totalValue: number; 
      count: number; 
      columns: DynamicColumnData[] 
    }>();
    
    indicatorColumns.forEach((col, index) => {
      const beautifiedName = this.beautifyColumnName(col.columnName);
      const unit = col.unit || this.determineUnit(col.columnName);
      
      // Use beautified name as the grouping key (this consolidates same indicators)
      const groupingKey = beautifiedName.toLowerCase();
      
      if (consolidatedIndicators.has(groupingKey)) {
        // Add to existing indicator group
        const existing = consolidatedIndicators.get(groupingKey)!;
        const currentValue = this.parseNumericValue(col.value);
        
        existing.totalValue += isNaN(currentValue) ? 0 : currentValue;
        existing.count += 1;
        existing.columns.push(col);
        
        console.log('🎯 CategoryModal: Added to existing indicator group:', {
          groupName: beautifiedName,
          currentValue: currentValue,
          newTotal: existing.totalValue,
          count: existing.count
        });
      } else {
        // Create new indicator group
        const currentValue = this.parseNumericValue(col.value);
        const indicatorId = `consolidated_${groupingKey.replace(/\s+/g, '_')}`;
        
        consolidatedIndicators.set(groupingKey, {
          name: beautifiedName,
          id: indicatorId,
          unit: unit,
          totalValue: isNaN(currentValue) ? 0 : currentValue,
          count: 1,
          columns: [col]
        });
        
        console.log('🎯 CategoryModal: Created new indicator group:', {
          groupName: beautifiedName,
          id: indicatorId,
          unit: unit,
          initialValue: currentValue,
          count: 1
        });
      }
    });
    
    // Convert to final options format with consolidated information
    const options = Array.from(consolidatedIndicators.values())
      .filter(group => group.totalValue > 0) // Only include groups with actual values
      .map(group => {
        // Create display name that shows consolidation if multiple activities
        let displayName = group.name;
        if (group.count > 1) {
          displayName = `${group.name} (${group.count} activities)`;
        }
        
        return {
          id: group.id,
          name: displayName,
          unit: group.unit,
          totalValue: group.totalValue, // Store total value for reference
          count: group.count // Store count for reference
        };
      })
      .sort((a, b) => a.name.localeCompare(b.name));
    
    console.log('🎯 CategoryModal: Created', options.length, 'consolidated indicator options:', 
      options.map(opt => ({
        name: opt.name,
        unit: opt.unit,
        totalValue: opt.totalValue,
        activityCount: opt.count
      }))
    );
    
    // Store the consolidated mapping for value retrieval
    this.consolidatedIndicatorMapping = consolidatedIndicators;
    
    return options.map(opt => ({ id: opt.id, name: opt.name, unit: opt.unit }));
  }

  // Method to update selected dynamic columns
  updateSelectedDynamicColumn(index: number, columnId: string): void {
    if (index >= 0 && index < 4) {
      this.selectedDynamicColumns[index] = columnId;
      this.processOverviewIndicators();
      this.changeDetectorRef.detectChanges();
    }
  }

  // Get available options excluding already selected ones for a specific index
  getAvailableOptionsForIndex(currentIndex: number): { name: string; id: string; unit: string }[] {
    const selectedAtOtherIndexes = this.selectedDynamicColumns.filter((_, index) => index !== currentIndex);
    return this.availableDynamicColumnOptions.filter(option => 
      !selectedAtOtherIndexes.includes(option.id)
    );
  }

  // Get selected column info for display
  getSelectedColumnInfo(index: number): { name: string; id: string; unit: string } | null {
    if (index >= 0 && index < this.selectedDynamicColumns.length) {
      const columnId = this.selectedDynamicColumns[index];
      return this.availableDynamicColumnOptions.find(opt => opt.id === columnId) || null;
    }
    return null;
  }

  // Fallback method using original approach
  private loadCategoryDataFallback(): void {
    console.log('🎯 CategoryModal: Using fallback data loading method');
    
    try {
      // Process activities for this category
      this.categoryActivities = this.filterActivitiesByCategory();
      
      // Extract interventions from activities
      this.categoryInterventions = this.extractInterventionsFromActivities();
      
      // Load category configuration and indicators
      this.loadCategoryConfig();
      
      // Process overview indicators for pie charts
      this.processOverviewIndicators();
      
      // Initialize with overview tab
      this.activeTab = 'overview';
      
      console.log('🎯 CategoryModal: Fallback data loaded', {
        categoryId: this.categoryId,
        activities: this.categoryActivities.length,
        interventions: this.categoryInterventions.length,
        indicators: this.availableIndicators.length
      });
      
    } catch (error) {
      console.error('🎯 CategoryModal: Error in fallback loading:', error);
      this.error = 'Failed to load category data';
    } finally {
      this.loading = false;
      this.changeDetectorRef.detectChanges();
    }
  }

  private loadTabData(tab: string): void {
    switch (tab) {
      case 'indicators':
        this.processSelectedIndicators();
        break;
      case 'analytics':
        this.calculateAnalyticsData();
        break;
    }
  }

  private extractInterventionsFromActivities(): any[] {
    const interventionsMap = new Map();
    
    this.categoryActivities.forEach(activity => {
      const intervention = activity.intervention || activity.interventionProfile || activity.profile;
      if (intervention && intervention.id) {
        if (!interventionsMap.has(intervention.id)) {
          interventionsMap.set(intervention.id, {
            id: intervention.id,
            title: intervention.title || intervention.name,
            name: intervention.name || intervention.title,
            description: intervention.description || intervention.summary,
            status: intervention.status || 'Active',
            budget: intervention.budget || 0,
            activities: []
          });
        }
        interventionsMap.get(intervention.id).activities.push(activity);
      }
    });
    
    return Array.from(interventionsMap.values());
  }

  // Overview Tab Methods
  getCategoryDisplayName(): string {
    // Priority order: 1. Input categoryName, 2. Extract from activities, 3. Extract from interventions, 4. Config name, 5. Fallback
    if (this.categoryName && this.categoryName.trim()) {
      return this.categoryName;
    }
    
    // Try to extract category name from loaded interventions
    const categoryFromInterventions = this.extractCategoryNameFromInterventions();
    if (categoryFromInterventions) {
      return categoryFromInterventions;
    }
    
    // Try to extract category name from activities
    const categoryFromActivities = this.extractCategoryNameFromActivities();
    if (categoryFromActivities) {
      return categoryFromActivities;
    }
    
    // Use config name if available
    if (this.config?.categoryName && this.config.categoryName !== `Category ${this.categoryId}`) {
      return this.config.categoryName;
    }
    
    // Fallback
    return `Category ${this.categoryId}`;
  }

  private extractCategoryNameFromInterventions(): string | null {
    if (!this.categoryInterventions || this.categoryInterventions.length === 0) return null;
    
    // Try to find category name from intervention data
    for (const intervention of this.categoryInterventions) {
      // Check for category object with name
      if (intervention.category?.name) {
        return intervention.category.name;
      }
      if (intervention.category?.title) {
        return intervention.category.title;
      }
      // Check for category name directly on intervention
      if (intervention.categoryName) {
        return intervention.categoryName;
      }
      if (intervention.categoryTitle) {
        return intervention.categoryTitle;
      }
    }
    
    return null;
  }

  private extractCategoryNameFromActivities(): string | null {
    if (!this.activities || this.activities.length === 0) return null;
    
    // Try to find category name from various possible sources in activities
    for (const activity of this.activities) {
      // Check intervention/profile category information
      const intervention = activity.intervention || activity.interventionProfile || activity.profile;
      if (intervention) {
        // Check for category object with name
        if (intervention.category?.name) {
          return intervention.category.name;
        }
        if (intervention.category?.title) {
          return intervention.category.title;
        }
        // Check for category name directly on intervention
        if (intervention.categoryName) {
          return intervention.categoryName;
        }
      }
      
      // Check directly on activity
      if (activity.category?.name) {
        return activity.category.name;
      }
      if (activity.category?.title) {
        return activity.category.title;
      }
      if (activity.categoryName) {
        return activity.categoryName;
      }
    }
    
    return null;
  }

  getCategoryProgress(): number {
    if (this.categoryActivities.length === 0) return 0;
    
    const totalProgress = this.categoryActivities.reduce((sum, activity) => {
      return sum + (activity.progress || activity.completion || 0);
    }, 0);
    
    return Math.round(totalProgress / this.categoryActivities.length);
  }

  getCategoryBudget(): number {
    return this.categoryInterventions.reduce((sum, intervention) => {
      return sum + (intervention.budget || 0);
    }, 0);
  }

  getCategoryStatus(): string {
    const activeCount = this.categoryInterventions.filter(i => 
      i.status === 'Active' || i.status === 'Ongoing'
    ).length;
    
    if (activeCount === 0) return 'Completed';
    if (activeCount === this.categoryInterventions.length) return 'Active';
    return 'Mixed';
  }

  getCategoryStatusClass(): string {
    const status = this.getCategoryStatus();
    switch (status) {
      case 'Active': return 'badge bg-success';
      case 'Completed': return 'badge bg-primary';
      case 'Mixed': return 'badge bg-warning';
      default: return 'badge bg-secondary';
    }
  }

  getCategoryStartDate(): Date | null {
    const dates = this.categoryActivities
      .map(a => a.startDate)
      .filter(d => d)
      .map(d => new Date(d))
      .sort((a, b) => a.getTime() - b.getTime());
    
    return dates.length > 0 ? dates[0] : null;
  }

  getCategoryEndDate(): Date | null {
    const dates = this.categoryActivities
      .map(a => a.endDate)
      .filter(d => d)
      .map(d => new Date(d))
      .sort((a, b) => b.getTime() - a.getTime());
    
    return dates.length > 0 ? dates[0] : null;
  }

  getActiveInterventions(): number {
    return this.categoryInterventions.filter(i => 
      i.status === 'Active' || i.status === 'Ongoing'
    ).length;
  }

  getCompletedActivities(): number {
    return this.categoryActivities.filter(a => 
      a.status === 'Completed' || (a.progress || 0) === 100
    ).length;
  }

  getPendingActivities(): number {
    return this.categoryActivities.filter(a => 
      a.status === 'Pending' || a.status === 'Planned'
    ).length;
  }

  getBeneficiariesCount(): number {
    return this.categoryActivities.reduce((sum, activity) => {
      return sum + (activity.beneficiaries || activity.targetBeneficiaries || 0);
    }, 0);
  }

  // Interventions Tab Methods
  trackIntervention(index: number, intervention: any): any {
    return intervention.id;
  }

  getInterventionStatusClass(intervention: any): string {
    const status = intervention.status || 'Active';
    switch (status.toLowerCase()) {
      case 'active':
      case 'ongoing': return 'badge bg-success';
      case 'completed': return 'badge bg-primary';
      case 'cancelled': return 'badge bg-danger';
      case 'paused': return 'badge bg-warning';
      default: return 'badge bg-secondary';
    }
  }

  getInterventionActivitiesCount(intervention: any): number {
    return intervention.activities?.length || 0;
  }

  getInterventionProgress(intervention: any): number {
    if (!intervention.activities || intervention.activities.length === 0) return 0;
    
    const totalProgress = intervention.activities.reduce((sum: number, activity: any) => {
      return sum + (activity.progress || activity.completion || 0);
    }, 0);
    
    return Math.round(totalProgress / intervention.activities.length);
  }

  getProgressBarClass(progress: number): string {
    if (progress >= 80) return 'bg-success';
    if (progress >= 60) return 'bg-info';
    if (progress >= 40) return 'bg-warning';
    return 'bg-danger';
  }

  viewInterventionDetails(intervention: any): void {
    console.log('🎯 CategoryModal: Viewing intervention details:', intervention);
    // Implementation for viewing intervention details
  }

  exportInterventions(): void {
    console.log('🎯 CategoryModal: Exporting interventions');
    // Implementation for exporting interventions
  }

  filterInterventions(): void {
    console.log('🎯 CategoryModal: Filtering interventions');
    // Implementation for filtering interventions
  }

  // Activities Tab Methods
  trackActivity(index: number, activity: any): any {
    return activity.id;
  }

  getFilteredActivities(): any[] {
    if (!this.activityStatusFilter) {
      return this.categoryActivities;
    }
    
    return this.categoryActivities.filter(activity => {
      const status = (activity.status || 'active').toLowerCase();
      return status === this.activityStatusFilter.toLowerCase();
    });
  }

  getActivityIntervention(activity: any): string {
    const intervention = activity.intervention || activity.interventionProfile || activity.profile;
    return intervention?.title || intervention?.name || 'N/A';
  }

  getActivityStatusClass(activity: any): string {
    const status = (activity.status || 'active').toLowerCase();
    switch (status) {
      case 'ongoing':
      case 'active': return 'badge bg-success';
      case 'completed': return 'badge bg-primary';
      case 'cancelled': return 'badge bg-danger';
      case 'planned': return 'badge bg-warning';
      default: return 'badge bg-secondary';
    }
  }

  getActivityProgress(activity: any): number {
    return activity.progress || activity.completion || 0;
  }

  viewActivityDetails(activity: any): void {
    console.log('🎯 CategoryModal: Viewing activity details:', activity);
    // Implementation for viewing activity details
  }

  exportActivities(): void {
    console.log('🎯 CategoryModal: Exporting activities');
    // Implementation for exporting activities
  }

  // Indicators Tab Methods
  addCustomIndicator(): void {
    console.log('🎯 CategoryModal: Adding custom indicator');
    // Implementation for adding custom indicators
  }

  exportIndicators(): void {
    console.log('🎯 CategoryModal: Exporting indicators');
    // Implementation for exporting indicators
  }

  // Progress Tab Methods
  getProgressCount(): number {
    return this.categoryActivities.filter(a => (a.progress || 0) > 0).length;
  }

  getCircleProgress(percentage: number): string {
    const circumference = 2 * Math.PI * 50; // radius = 50
    const strokeDasharray = circumference;
    const strokeDashoffset = circumference - (percentage / 100) * circumference;
    return `${strokeDasharray} ${strokeDashoffset}`;
  }

  getInterventionsProgress(): number {
    if (this.categoryInterventions.length === 0) return 0;
    
    const totalProgress = this.categoryInterventions.reduce((sum, intervention) => {
      return sum + this.getInterventionProgress(intervention);
    }, 0);
    
    return Math.round(totalProgress / this.categoryInterventions.length);
  }

  getActivitiesProgress(): number {
    return this.getCategoryProgress();
  }

  getIndicatorsProgress(): number {
    // Mock calculation based on selected indicators
    return Math.min(85, this.selectedIndicatorIds.size * 20);
  }

  getBudgetUtilization(): number {
    const totalBudget = this.getCategoryBudget();
    if (totalBudget === 0) return 0;
    
    // Mock calculation - in real implementation, this would use actual expenditure data
    const progress = this.getCategoryProgress();
    return Math.round(progress * 0.8); // Assuming 80% budget efficiency
  }

  getProgressMilestones(): any[] {
    // Mock milestone data - in real implementation, this would come from the database
    return [
      {
        title: 'Project Initiation',
        description: 'Category implementation started',
        date: this.getCategoryStartDate(),
        status: 'completed'
      },
      {
        title: 'Mid-term Review',
        description: 'Progress assessment completed',
        date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        status: 'completed'
      },
      {
        title: 'Current Phase',
        description: 'Active implementation ongoing',
        date: new Date(),
        status: 'ongoing'
      },
      {
        title: 'Project Completion',
        description: 'Expected completion date',
        date: this.getCategoryEndDate(),
        status: 'pending'
      }
    ].filter(m => m.date);
  }

  exportProgress(): void {
    console.log('🎯 CategoryModal: Exporting progress data');
    // Implementation for exporting progress
  }

  private calculateProgressData(): void {
    // Calculate progress-specific data when tab is loaded
    console.log('🎯 CategoryModal: Calculating progress data');
  }

  // Analytics Tab Methods
  getAnalyticsCount(): number {
    return 6; // Number of analytics metrics
  }

  getPerformanceChartData(): any {
    // Mock performance chart data
    return {
      series: [{
        name: 'Performance Score',
        data: [65, 70, 75, 80, 85, 82]
      }, {
        name: 'Budget Efficiency',
        data: [60, 65, 72, 78, 80, 85]
      }],
      options: {
        chart: {
          type: 'line',
          height: 400
        },
        colors: ['#007bff', '#28a745'],
        dataLabels: {
          enabled: false
        },
        legend: {
          position: 'top'
        },
        tooltip: {
          shared: true
        },
        xaxis: {
          categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun']
        },
        yaxis: {
          title: {
            text: 'Score (%)'
          }
        }
      }
    };
  }

  getEfficiencyScore(): number {
    const progress = this.getCategoryProgress();
    const budgetUtil = this.getBudgetUtilization();
    return Math.round((progress + budgetUtil) / 2);
  }

  getImpactRating(): number {
    const beneficiaries = this.getBeneficiariesCount();
    const activities = this.categoryActivities.length;
    // Mock calculation based on scale
    return Math.min(10, Math.round((beneficiaries / 100) + (activities / 10)));
  }

  getRiskLevel(): string {
    const progress = this.getCategoryProgress();
    const delays = this.categoryActivities.filter(a => 
      new Date(a.endDate) < new Date() && (a.progress || 0) < 100
    ).length;
    
    if (delays > this.categoryActivities.length * 0.3) return 'High';
    if (progress < 50) return 'Medium';
    return 'Low';
  }

  getRiskLevelClass(): string {
    const risk = this.getRiskLevel();
    switch (risk) {
      case 'High': return 'text-danger';
      case 'Medium': return 'text-warning';
      case 'Low': return 'text-success';
      default: return 'text-secondary';
    }
  }

  getSustainabilityScore(): number {
    // Mock calculation based on project characteristics
    const completedActivities = this.getCompletedActivities();
    const totalActivities = this.categoryActivities.length;
    
    if (totalActivities === 0) return 0;
    return Math.round((completedActivities / totalActivities) * 100 * 0.9);
  }

  getComparativeMetrics(): any[] {
    // Mock comparative data
    return [
      {
        name: 'Activities Completed',
        current: this.getCompletedActivities(),
        previous: Math.max(0, this.getCompletedActivities() - 2),
        change: '+2',
        changeClass: 'text-success',
        trendIcon: 'bi bi-arrow-up',
        trendClass: 'text-success'
      },
      {
        name: 'Budget Utilization (%)',
        current: this.getBudgetUtilization() + '%',
        previous: Math.max(0, this.getBudgetUtilization() - 5) + '%',
        change: '+5%',
        changeClass: 'text-success',
        trendIcon: 'bi bi-arrow-up',
        trendClass: 'text-success'
      },
      {
        name: 'Beneficiaries Reached',
        current: this.getBeneficiariesCount(),
        previous: Math.max(0, this.getBeneficiariesCount() - 50),
        change: '+50',
        changeClass: 'text-success',
        trendIcon: 'bi bi-arrow-up',
        trendClass: 'text-success'
      }
    ];
  }

  exportAnalytics(): void {
    console.log('🎯 CategoryModal: Exporting analytics data');
    // Implementation for exporting analytics
  }

  private calculateAnalyticsData(): void {
    // Calculate analytics-specific data when tab is loaded
    console.log('🎯 CategoryModal: Calculating analytics data');
  }

  // Export and Refresh Methods
  exportAllData(): void {
    console.log('🎯 CategoryModal: Exporting all category data');
    // Implementation for exporting all data
  }

  refreshAllData(): void {
    console.log('🎯 CategoryModal: Refreshing all category data');
    this.loadCategoryData();
  }

  // Original CategoryModal Methods (preserved from existing implementation)
  private loadCategoryConfig(): void {
    this.loading = true;
    this.error = null;
    
    console.log('🎯 CategoryModal: Loading category config for:', this.categoryId);
    
    if (!this.categoryId) {
      this.error = 'Category ID is required';
      this.loading = false;
      this.changeDetectorRef.detectChanges();
      return;
    }
    
    // Process the current activities and dynamic columns
    const filteredActivities = this.filterActivitiesByCategory();
    console.log('🎯 CategoryModal: Filtered activities:', filteredActivities.length);
    
    // Extract all dynamic columns from the activities
    const allColumns = this.extractDynamicColumnsFromActivities(filteredActivities);
    console.log('🎯 CategoryModal: All columns extracted:', allColumns.length);
    
    // Filter for quantitative columns that can be used as indicators
    const indicatorColumns = allColumns.filter(col => this.isQuantitativeColumn(col));
    console.log('🎯 CategoryModal: Quantitative columns found:', indicatorColumns.length);
    
    // Create indicators from the selected columns
    this.availableIndicators = this.createIndicatorsFromQuantitativeColumns(indicatorColumns);
    
    console.log('🎯 CategoryModal: Created indicators for category:', {
      categoryId: this.categoryId,
      count: this.availableIndicators.length,
      indicators: this.availableIndicators.map(i => ({
        id: i.id,
        name: i.name,
        chartType: i.chartType,
        unit: i.unit
      }))
    });
    
    // Set basic config
    this.config = {
      categoryId: this.categoryId.toString(),
      categoryName: this.getCategoryDisplayName(),
      defaultIndicators: this.availableIndicators,
      customIndicators: [],
      allowUserCustomization: false
    };
    
    this.loading = false;
    this.changeDetectorRef.detectChanges();
  }

  private processSelectedIndicators(): void {
    if (this.selectedIndicatorIds.size === 0) {
      this.combinedData = null;
      this.changeDetectorRef.detectChanges();
      return;
    }

    const selectedIndicators = this.availableIndicators.filter(indicator => 
      this.selectedIndicatorIds.has(indicator.id)
    );

    if (selectedIndicators.length === 0) {
      this.combinedData = null;
      this.changeDetectorRef.detectChanges();
      return;
    }

    // Process indicators and create combined data
    this.processedIndicators = selectedIndicators.map(indicator => 
      this.processIndicator(indicator)
    ).filter(processed => processed !== null) as ProcessedIndicator[];

    if (this.processedIndicators.length > 0) {
      this.combinedData = this.createCombinedData(this.processedIndicators);
    } else {
      this.combinedData = null;
    }

    this.changeDetectorRef.detectChanges();
  }

  private processIndicator(indicator: IndicatorConfig): ProcessedIndicator | null {
    try {
      // Get activities for this category
      const categoryActivities = this.filterActivitiesByCategory();
      
      if (categoryActivities.length === 0) {
        console.log('🎯 CategoryModal: No activities found for indicator processing');
        return this.createFallbackIndicatorData(indicator);
      }

      // Extract matching dynamic columns
      const matchingColumns = this.dynamicColumns.filter(col => {
        const columnName = col.columnName.toLowerCase();
        const pattern = indicator.columnPattern?.toLowerCase() || indicator.name.toLowerCase();
        return columnName.includes(pattern);
      });

      if (matchingColumns.length === 0) {
        console.log(`🎯 CategoryModal: No matching columns found for indicator ${indicator.name}`);
        return this.createFallbackIndicatorData(indicator);
      }

      // Calculate aggregated values
      const dataPoints: IndicatorDataPoint[] = matchingColumns.map(col => {
        const values = categoryActivities
          .map(activity => this.parseNumericValue(col.value))
          .filter(val => !isNaN(val) && val > 0);
        
        const total = values.reduce((sum, val) => sum + val, 0);
        
        return {
          label: col.columnName,
          value: total,
          unit: col.unit || indicator.unit || 'units',
          category: 'Activity Data',
          metadata: {
            activity: col.displayName || 'Unknown',
            region: 'Multiple'
          }
        };
      });

      const tableData: TableData = {
        headers: ['Indicator', 'Value', 'Activity', 'Region'],
        rows: dataPoints.map(dp => ({
          'Indicator': dp.label,
          'Value': dp.value,
          'Activity': dp.metadata?.activity || 'Unknown',
          'Region': dp.metadata?.region || 'Unknown'
        })),
        totals: {
          'Value': dataPoints.reduce((sum, dp) => sum + dp.value, 0)
        }
      };

      const chartData = this.createChartDataForIndicator(indicator, dataPoints);

      return {
        config: indicator,
        data: dataPoints,
        summary: {
          total: dataPoints.reduce((sum, dp) => sum + dp.value, 0),
          average: dataPoints.length > 0 ? dataPoints.reduce((sum, dp) => sum + dp.value, 0) / dataPoints.length : 0,
          count: dataPoints.length
        },
        tableData,
        chartData
      };

    } catch (error) {
      console.error('🎯 CategoryModal: Error processing indicator:', error);
      return this.createFallbackIndicatorData(indicator);
    }
  }

  private createChartDataForIndicator(indicator: IndicatorConfig, dataPoints: IndicatorDataPoint[]): any {
    const chartType = indicator.chartType || 'bar';
    
    switch (chartType) {
      case 'line':
        return {
          series: [{
            name: indicator.name,
            data: dataPoints.map(dp => dp.value)
          }],
          options: {
            chart: { type: 'line', height: 350 },
            xaxis: { categories: dataPoints.map(dp => dp.label) },
            title: { text: indicator.name }
          }
        };
      
      case 'donut':
        return {
          series: dataPoints.map(dp => dp.value),
          options: {
            chart: { type: 'donut', height: 350 },
            labels: dataPoints.map(dp => dp.label),
            title: { text: indicator.name }
          }
        };
      
      default: // bar
        return {
          series: [{
            name: indicator.name,
            data: dataPoints.map(dp => dp.value)
          }],
          options: {
            chart: { type: 'bar', height: 350 },
            xaxis: { categories: dataPoints.map(dp => dp.label) },
            title: { text: indicator.name }
          }
        };
    }
  }

  toggleIndicatorView(indicatorId: string, view: 'table' | 'chart'): void {
    this.indicatorViews[indicatorId] = view;
  }

  getIndicatorView(indicatorId: string): 'table' | 'chart' {
    return this.indicatorViews[indicatorId] || 'chart';
  }

  toggleCombinedView(view: 'table' | 'chart'): void {
    this.currentView = view;
  }

  private renderCharts(): void {
    // Chart rendering is handled by ApexCharts component
    setTimeout(() => {
      this.changeDetectorRef.detectChanges();
    }, 100);
  }

  private createCombinedData(indicators: ProcessedIndicator[]): any {
    if (indicators.length === 0) return null;

    // Create combined table data
    const allHeaders = new Set<string>();
    indicators.forEach(processed => {
      if (processed.tableData?.headers) {
        processed.tableData.headers.forEach(header => allHeaders.add(header));
      }
    });

    const combinedHeaders = Array.from(allHeaders);
    const combinedRows: any[] = [];

    indicators.forEach(processed => {
      if (processed.tableData?.rows) {
        processed.tableData.rows.forEach(row => {
          const combinedRow: any = {};
          combinedHeaders.forEach(header => {
            combinedRow[header] = row[header] || '';
          });
          combinedRows.push(combinedRow);
        });
      }
    });

    // Create combined chart data - replace flatMap with concat approach
    const allDataPoints: any[] = [];
    indicators.forEach(processed => {
      if (processed.data) {
        allDataPoints.push(...processed.data);
      }
    });
    const combinedChartData = this.createCombinedChartData(allDataPoints);

    return {
      tableData: {
        headers: combinedHeaders,
        rows: combinedRows,
        totals: this.calculateCombinedTotals(combinedRows, combinedHeaders)
      },
      chartData: combinedChartData
    };
  }

  private calculateCombinedTotals(rows: any[], headers: string[]): any {
    const totals: any = { 'Indicator': 'Total' };
    
    headers.forEach(header => {
      if (header === 'Value') {
        totals[header] = rows.reduce((sum, row) => {
          const value = this.parseNumericValue(row[header]);
          return sum + (isNaN(value) ? 0 : value);
        }, 0);
      } else if (header !== 'Indicator') {
        totals[header] = `${rows.length} items`;
      }
    });
    
    return totals;
  }

  private createCombinedChartData(dataPoints: any[]): any {
    if (dataPoints.length === 0) return null;

    // Group data by indicator name for better visualization
    const groupedData = new Map<string, number>();
    dataPoints.forEach(dp => {
      const key = dp.name || 'Unknown';
      groupedData.set(key, (groupedData.get(key) || 0) + dp.value);
    });

    const categories = Array.from(groupedData.keys());
    const values = Array.from(groupedData.values());

    return {
      series: [{
        name: 'Combined Values',
        data: values
      }],
      options: {
        chart: {
          type: 'bar',
          height: 400,
          toolbar: {
            show: true
          }
        },
        colors: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8'],
        dataLabels: {
          enabled: true
        },
        legend: {
          position: 'top'
        },
        tooltip: {
          shared: true,
          intersect: false
        },
        xaxis: {
          categories: categories,
          title: {
            text: 'Indicators'
          }
        },
        yaxis: {
          title: {
            text: 'Values'
          }
        },
        plotOptions: {
          bar: {
            horizontal: false,
            columnWidth: '60%'
          }
        },
        title: {
          text: 'Combined Indicators Analysis',
          align: 'center'
        }
      }
    };
  }

  exportData(): void {
    console.log('🎯 CategoryModal: Exporting data');
    
    if (!this.combinedData) {
      console.log('🎯 CategoryModal: No data to export');
      return;
    }

    const exportData = {
      categoryId: this.categoryId,
      selectedIndicators: Array.from(this.selectedIndicatorIds),
      tableData: this.combinedData.tableData,
      exportedAt: new Date().toISOString()
    };

    // Create and download CSV
    const csvContent = this.convertTableDataToCSV(this.combinedData.tableData);
    this.downloadCSV(csvContent, `category-${this.categoryId}-indicators.csv`);
  }

  private convertTableDataToCSV(tableData: any): string {
    if (!tableData || !tableData.headers || !tableData.rows) return '';
    
    const csvRows = [];
    csvRows.push(tableData.headers.join(','));
    
    tableData.rows.forEach((row: any) => {
      const values = tableData.headers.map((header: string) => {
        const value = row[header] || '';
        return `"${value}"`;
      });
      csvRows.push(values.join(','));
    });
    
    return csvRows.join('\n');
  }

  private downloadCSV(content: string, filename: string): void {
    const blob = new Blob([content], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    }
  }

  refreshData(): void {
    console.log('🎯 CategoryModal: Refreshing data');
    this.loadCategoryConfig();
    this.processSelectedIndicators();
  }

  formatTableValue(value: any, header: string): string {
    if (value === null || value === undefined || value === '') {
      return 'N/A';
    }
    
    if (header.toLowerCase().includes('value') || header.toLowerCase().includes('total')) {
      const numValue = this.parseNumericValue(value);
      if (!isNaN(numValue)) {
        return numValue.toLocaleString();
      }
    }
    
    return value.toString();
  }

  trackByRowIndex(index: number, item: any): number {
    return index;
  }

  close(): void {
    this.closeModal.emit();
  }

  toggleDebugInfo(): void {
    this.showDebugInfo = !this.showDebugInfo;
  }

  getSampleActivityInfo(): string {
    if (!this.activities || this.activities.length === 0) {
      return 'No activities available';
    }

    const sample = this.activities[0];
    const info = {
      id: sample.id || 'N/A',
      title: sample.title || sample.name || 'N/A',
      status: sample.status || 'N/A',
      hasDynamicColumns: !!sample.dynamicColumns,
      dynamicColumnsCount: sample.dynamicColumns?.length || 0,
      intervention: sample.intervention?.title || sample.interventionProfile?.title || 'N/A'
    };

    return JSON.stringify(info, null, 2);
  }

  // Helper Methods
  toggleIndicator(indicatorId: string, event: any): void {
    if (event.target.checked) {
      this.selectedIndicatorIds.add(indicatorId);
    } else {
      this.selectedIndicatorIds.delete(indicatorId);
    }
    this.processSelectedIndicators();
  }

  isIndicatorSelected(indicatorId: string): boolean {
    return this.selectedIndicatorIds.has(indicatorId);
  }

  private createIndicatorsFromQuantitativeColumns(columns: DynamicColumnData[]): IndicatorConfig[] {
    const indicators: IndicatorConfig[] = [];
    const groupedColumns = new Map<string, DynamicColumnData[]>();
    const processedNames = new Set<string>(); // Track processed indicator names to avoid duplicates

    // Group columns by similar names/patterns, but keep more specificity
    columns.forEach(col => {
      const key = this.normalizeColumnName(col.columnName);
      if (!groupedColumns.has(key)) {
        groupedColumns.set(key, []);
      }
      groupedColumns.get(key)!.push(col);
    });

    console.log('🎯 CategoryModal: Grouped columns into', groupedColumns.size, 'groups');

    // Create indicators from grouped columns
    groupedColumns.forEach((cols, key) => {
      if (cols.length > 0) {
        const firstCol = cols[0];
        const beautifiedName = this.beautifyColumnName(firstCol.columnName);
        
        // Check if we already have an indicator with this name
        let finalName = beautifiedName;
        let counter = 1;
        while (processedNames.has(finalName)) {
          finalName = `${beautifiedName} ${counter}`;
          counter++;
        }
        
        processedNames.add(finalName);
        
        indicators.push({
          id: `indicator-${key}-${indicators.length}`, // Make ID more unique
          name: finalName,
          categoryId: this.categoryId.toString(),
          columnPattern: key,
          displayType: 'both',
          chartType: this.determineChartType(firstCol.columnName),
          groupBy: 'activity',
          aggregationType: 'sum',
          unit: this.determineUnit(firstCol.columnName),
          description: `Indicator for ${firstCol.columnName} (${cols.length} instances)`,
          order: indicators.length + 1
        });
        
        console.log('🎯 CategoryModal: Created indicator:', {
          name: finalName,
          key: key,
          columnInstances: cols.length,
          originalColumnNames: cols.map(c => c.columnName).slice(0, 3)
        });
      }
    });

    console.log('🎯 CategoryModal: Created', indicators.length, 'unique indicators from', columns.length, 'columns');
    return indicators;
  }

  private normalizeColumnName(name: string): string {
    // Less aggressive normalization to preserve uniqueness
    // Only convert to lowercase and replace spaces with underscores
    // Keep numbers and most special characters to maintain distinctiveness
    return name.toLowerCase()
      .replace(/\s+/g, '_')  // Replace spaces with underscores
      .replace(/[^\w\-_]/g, '') // Remove only truly problematic characters, keep numbers, hyphens, underscores
      .trim();
  }

  private beautifyColumnName(name: string): string {
    return name
      .replace(/_/g, ' ')
      .replace(/([A-Z])/g, ' $1')
      .replace(/\b\w/g, l => l.toUpperCase())
      .trim();
  }

  private determineChartType(columnName: string): 'bar' | 'line' | 'donut' {
    const name = columnName.toLowerCase();
    if (name.includes('percentage') || name.includes('ratio') || name.includes('rate')) {
      return 'donut';
    }
    if (name.includes('trend') || name.includes('time') || name.includes('month')) {
      return 'line';
    }
    return 'bar';
  }

  private determineUnit(columnName: string): string {
    const name = columnName.toLowerCase();
    if (name.includes('percentage') || name.includes('rate')) return '%';
    if (name.includes('amount') || name.includes('cost') || name.includes('budget')) return 'USD';
    if (name.includes('people') || name.includes('beneficiar') || name.includes('participant')) return 'people';
    if (name.includes('days') || name.includes('day')) return 'days';
    if (name.includes('hours') || name.includes('hour')) return 'hours';
    return 'units';
  }

  private filterActivitiesByCategory(): any[] {
    if (!this.activities || !Array.isArray(this.activities) || !this.categoryId) {
      console.log('🎯 CategoryModal: No activities or categoryId to filter');
      return [];
    }
    
    console.log('🎯 CategoryModal: Filtering activities by categoryId:', this.categoryId);
    console.log('🎯 CategoryModal: Total activities received:', this.activities.length);
    
    // Check if activities are already pre-filtered by parent component
    const hasExplicitCategoryInfo = this.activities.some(activity => {
      return activity.categoryId || 
             activity.category?.id || 
             activity.intervention?.categoryId ||
             activity.interventionProfile?.categoryId ||
             activity.profile?.categoryId;
    });
    
    console.log('🎯 CategoryModal: Activities have explicit category info:', hasExplicitCategoryInfo);
    
    // If activities don't have explicit category info but we have a reasonable number,
    // assume they're already filtered by the parent component (hierarchical report)
    if (!hasExplicitCategoryInfo && this.activities.length <= 200) {
      console.log('🎯 CategoryModal: Activities appear to be pre-filtered by parent component, using all provided activities');
      return this.activities;
    }
    
    // Otherwise, try to filter based on category information
    let filteredActivities: any[] = [];
    let loggedActivitiesCount = 0;
    
    for (const activity of this.activities) {
      let matchesCategory = false;
      
      // Check various possible category references
      if (activity.categoryId === this.categoryId) {
        matchesCategory = true;
      } else if (activity.category?.id === this.categoryId) {
        matchesCategory = true;
      } else if (activity.intervention?.categoryId === this.categoryId) {
        matchesCategory = true;
      } else if (activity.interventionProfile?.categoryId === this.categoryId) {
        matchesCategory = true;
      } else if (activity.profile?.categoryId === this.categoryId) {
        matchesCategory = true;
      }
      
      if (matchesCategory) {
        filteredActivities.push(activity);
        
        // Log first few matching activities for debugging
        if (loggedActivitiesCount < 3) {
          console.log(`🎯 CategoryModal: Matched activity ${loggedActivitiesCount + 1}:`, {
            id: activity.id,
            title: activity.title || activity.name,
            categoryId: activity.categoryId,
            category: activity.category,
            intervention: activity.intervention,
            interventionProfile: activity.interventionProfile
          });
          loggedActivitiesCount++;
        }
      }
    }
    
    console.log('🎯 CategoryModal: Filtered activities count:', filteredActivities.length);
    
    // If no activities match and we have a small set, return all activities
    // This handles cases where category filtering might not be properly configured
    if (filteredActivities.length === 0 && this.activities.length <= 50) {
      console.log('🎯 CategoryModal: No category matches found, but activity count is small. Using all activities as fallback.');
      return this.activities;
    }
    
    return filteredActivities;
  }

  private extractDynamicColumnsFromActivities(activities: any[]): DynamicColumnData[] {
    console.log('🎯 CategoryModal: Extracting dynamic columns from activities:', activities.length);
    
    const allColumns: DynamicColumnData[] = [];
    
    activities.forEach((activity, index) => {
      // Check if activity has dynamic columns
      if (activity.dynamicColumns && Array.isArray(activity.dynamicColumns)) {
        activity.dynamicColumns.forEach((col: any) => {
          allColumns.push({
            ...col,
            displayName: col.displayName || activity.title || activity.name || `Activity ${activity.id}`
          });
        });
      }
      
      // Also check the dynamicColumns input directly
      if (index === 0 && this.dynamicColumns && this.dynamicColumns.length > 0) {
        console.log('🎯 CategoryModal: Using provided dynamicColumns input:', this.dynamicColumns.length);
        this.dynamicColumns.forEach(col => {
          allColumns.push({
            ...col,
            displayName: col.displayName || 'Unknown Activity'
          });
        });
      }
    });
    
    console.log('🎯 CategoryModal: Total dynamic columns extracted:', allColumns.length);
    console.log('🎯 CategoryModal: Sample columns:', allColumns.slice(0, 3).map(c => ({
      columnName: c.columnName,
      value: c.value,
      displayName: c.displayName
    })));
    
    return allColumns;
  }

  private isQuantitativeColumn(column: DynamicColumnData): boolean {
    if (!column || !column.columnName) return false;
    
    const value = column.value;
    const columnName = column.columnName.toLowerCase();
    const dataType = (column.dataType || column.columnType || '').toLowerCase();
    
    // First check: Must have a meaningful numeric value
    const numericValue = this.parseNumericValue(value);
    const isActuallyNumeric = !isNaN(numericValue) && isFinite(numericValue) && numericValue !== 0;
    
    // Second check: Value should not be a text description of units or dimensions
    const isTextValue = typeof value === 'string' && (
      value.includes('(') || // Contains parentheses like "Area(m2)"
      value.includes('Unit') || // Contains word "Unit"
      value.includes('m2') || value.includes('km2') || // Area units
      value.includes('kg') || value.includes('ton') || // Weight units
      value.includes('USD') || value.includes('$') || // Currency symbols
      value.includes('percent') || value.includes('%') || // Percentage symbols
      /^[a-zA-Z\s\(\)]+$/.test(value) // Only contains letters, spaces, and parentheses
    );
    
    // Third check: Column name patterns that are typically non-quantitative
    const nonQuantitativeColumnNames = [
      'dimension', 'unit', 'type', 'category', 'status', 'name', 'title', 
      'description', 'note', 'comment', 'remark', 'text', 'label',
      'classification', 'group', 'sector', 'region', 'location'
    ];
    
    const isNonQuantitativeColumn = nonQuantitativeColumnNames.some(pattern => 
      columnName.includes(pattern)
    );
    
    // If it's a text value or non-quantitative column name, exclude it
    if (isTextValue || isNonQuantitativeColumn) {
      console.log(`🎯 CategoryModal: Column "${column.columnName}" excluded (text value or non-quantitative column):`, {
        value: value,
        isTextValue: isTextValue,
        isNonQuantitativeColumn: isNonQuantitativeColumn
      });
      return false;
    }
    
    // Check data type indicators for numeric types
    const numericDataTypes = ['number', 'int', 'integer', 'float', 'double', 'decimal', 'numeric'];
    const hasNumericDataType = numericDataTypes.some(type => dataType.includes(type));
    
    // Check column name patterns that suggest quantitative data
    const quantitativePatterns = [
      'count', 'total', 'sum', 'amount', 'quantity', 'size',
      'percent', 'percentage', 'rate', 'ratio', 'score', 'value',
      'budget', 'cost', 'price', 'revenue', 'income', 'expense',
      'beneficiar', 'participant', 'trainee', 'student', 'people',
      'days', 'hours', 'weeks', 'months', 'years', 'duration',
      'target', 'achieved', 'completed', 'progress', 'outcome',
      'indicator', 'metric', 'measure', 'kpi', 'performance',
      'result', 'output', 'impact', 'reach', 'coverage', 'fund',
      'allocation', 'disbursement', 'utilization', 'delivery'
    ];
    
    const hasQuantitativePattern = quantitativePatterns.some(pattern => 
      columnName.includes(pattern)
    );
    
    // Final determination: Must have actual numeric value AND (proper data type OR quantitative pattern)
    const result = isActuallyNumeric && (hasNumericDataType || hasQuantitativePattern);
    
    console.log(`🎯 CategoryModal: Column "${column.columnName}" quantitative analysis:`, {
      value: value,
      numericValue: numericValue,
      isActuallyNumeric: isActuallyNumeric,
      dataType: dataType,
      hasNumericDataType: hasNumericDataType,
      hasQuantitativePattern: hasQuantitativePattern,
      isTextValue: isTextValue,
      isNonQuantitativeColumn: isNonQuantitativeColumn,
      finalResult: result
    });
    
    return result;
  }

  private createFallbackIndicatorData(indicator: IndicatorConfig): ProcessedIndicator {
    console.log('🎯 CategoryModal: Creating fallback data for indicator:', indicator.name);
    
    // Create mock data for demonstration
    const mockDataPoints: IndicatorDataPoint[] = [
      { 
        label: `${indicator.name} - Sample 1`, 
        value: 25, 
        unit: indicator.unit || 'units',
        category: 'Sample',
        metadata: { activity: 'Activity A', region: 'Region 1' }
      },
      { 
        label: `${indicator.name} - Sample 2`, 
        value: 30, 
        unit: indicator.unit || 'units',
        category: 'Sample',
        metadata: { activity: 'Activity B', region: 'Region 2' }
      },
      { 
        label: `${indicator.name} - Sample 3`, 
        value: 20, 
        unit: indicator.unit || 'units',
        category: 'Sample',
        metadata: { activity: 'Activity C', region: 'Region 1' }
      }
    ];
    
    const tableData: TableData = {
      headers: ['Indicator', 'Value', 'Activity', 'Region'],
      rows: mockDataPoints.map(dp => ({
        'Indicator': dp.label,
        'Value': dp.value,
        'Activity': dp.metadata?.activity || 'Unknown',
        'Region': dp.metadata?.region || 'Unknown'
      })),
      totals: {
        'Value': mockDataPoints.reduce((sum, dp) => sum + dp.value, 0)
      }
    };
    
    const chartData = this.createChartDataForIndicator(indicator, mockDataPoints);
    
    return {
      config: indicator,
      data: mockDataPoints,
      summary: {
        total: mockDataPoints.reduce((sum, dp) => sum + dp.value, 0),
        average: mockDataPoints.reduce((sum, dp) => sum + dp.value, 0) / mockDataPoints.length,
        count: mockDataPoints.length
      },
      tableData,
      chartData
    };
  }

  private parseNumericValue(value: any): number {
    if (typeof value === 'number') return value;
    
    if (typeof value === 'string') {
      // Return 0 for obviously non-numeric strings
      if (value.trim() === '' || 
          value.includes('(') || 
          value.includes('Unit') || 
          value.includes('Area') ||
          value.includes('m2') || 
          value.includes('kg') ||
          /^[a-zA-Z\s\(\)]+$/.test(value.trim())) {
        return 0;
      }
      
      // Remove common non-numeric characters but be more careful
      const cleaned = value.replace(/[,$%]/g, '').trim();
      
      // Check if cleaned string is purely numeric
      if (/^\d*\.?\d+$/.test(cleaned)) {
        const parsed = parseFloat(cleaned);
        return isNaN(parsed) ? 0 : parsed;
      }
      
      return 0;
    }
    
    return 0;
  }

  // New method to process overview indicators for pie charts
  private processOverviewIndicators(): void {
    console.log('🎯 CategoryModal: Processing overview indicators for semi-circle chart');
    
    // Set default selected indicator if none selected and options are available
    if (!this.selectedOverviewIndicator && this.availableDynamicColumnOptions.length > 0) {
      // Auto-select the first quantitative indicator
      this.selectedOverviewIndicator = this.availableDynamicColumnOptions[0].id;
    }
    
    console.log('🎯 CategoryModal: Selected overview indicator:', this.selectedOverviewIndicator);
  }

  // Get indicators based on selected dynamic columns
  private getSelectedIndicatorsForOverview(): IndicatorConfig[] {
    if (this.selectedDynamicColumns.length === 0) {
      // Fallback to top 4 if no selection
      return this.getTop4IndicatorsForOverview();
    }
    
    const selectedIndicators: IndicatorConfig[] = [];
    
    this.selectedDynamicColumns.forEach(columnId => {
      const indicator = this.availableIndicators.find(ind => 
        this.normalizeColumnName(ind.name) === columnId || 
        ind.id.includes(columnId)
      );
      
      if (indicator) {
        selectedIndicators.push(indicator);
      } else {
        // Create indicator for this column if not found
        const columnOption = this.availableDynamicColumnOptions.find(opt => opt.id === columnId);
        if (columnOption) {
          selectedIndicators.push({
            id: `indicator-${columnId}`,
            name: columnOption.name,
            categoryId: this.categoryId.toString(),
            columnPattern: columnId,
            displayType: 'both',
            chartType: 'donut',
            groupBy: 'value',
            aggregationType: 'sum',
            unit: columnOption.unit,
            description: `Indicator for ${columnOption.name}`,
            order: selectedIndicators.length + 1
          });
        }
      }
    });
    
    return selectedIndicators;
  }

  // Get the top 4 most relevant indicators for overview display
  private getTop4IndicatorsForOverview(): IndicatorConfig[] {
    if (this.availableIndicators.length === 0) {
      return [];
    }
    
    // Sort indicators by relevance (prioritize common quantitative indicators)
    const sortedIndicators = this.availableIndicators.sort((a, b) => {
      const aScore = this.calculateIndicatorRelevanceScore(a);
      const bScore = this.calculateIndicatorRelevanceScore(b);
      return bScore - aScore;
    });
    
    // Return top 4
    return sortedIndicators.slice(0, 4);
  }

  // Calculate relevance score for indicator prioritization
  private calculateIndicatorRelevanceScore(indicator: IndicatorConfig): number {
    let score = 0;
    const name = indicator.name.toLowerCase();
    
    // Higher priority keywords
    if (name.includes('beneficiar') || name.includes('participant')) score += 100;
    if (name.includes('budget') || name.includes('cost') || name.includes('amount')) score += 90;
    if (name.includes('completed') || name.includes('achieved')) score += 80;
    if (name.includes('target') || name.includes('goal')) score += 70;
    if (name.includes('percentage') || name.includes('rate')) score += 60;
    if (name.includes('count') || name.includes('number') || name.includes('total')) score += 50;
    
    // Boost score for indicators that typically have good data distribution
    if (indicator.chartType === 'donut' || indicator.chartType === 'pie') score += 20;
    
    return score;
  }

  // Process individual indicator for overview pie chart
  private processIndicatorForOverview(indicator: IndicatorConfig): OverviewIndicator | null {
    try {
      const categoryActivities = this.categoryActivities;
      
      if (categoryActivities.length === 0) {
        return this.createFallbackOverviewIndicator(indicator);
      }

      // Extract matching dynamic columns based on column pattern
      const pattern = indicator.columnPattern?.toLowerCase() || indicator.name.toLowerCase();
      const matchingColumns = this.dynamicColumns.filter(col => {
        const columnName = col.columnName.toLowerCase();
        return columnName.includes(pattern) || this.normalizeColumnName(columnName) === pattern;
      });

      if (matchingColumns.length === 0) {
        return this.createFallbackOverviewIndicator(indicator);
      }

      // Create simple value-based data points (no regional grouping)
      const values: number[] = [];
      const labels: string[] = [];
      
      matchingColumns.forEach((col, index) => {
        const value = this.parseNumericValue(col.value);
        if (!isNaN(value) && value > 0) {
          values.push(value);
          labels.push(`Value ${index + 1}`);
        }
      });

      // If we have individual values, create pie slices for each
      if (values.length > 0) {
        // For better visualization, group similar values or show top values
        const dataPoints: IndicatorDataPoint[] = values.map((value, index) => ({
          label: labels[index],
          value: value,
          unit: indicator.unit || 'units',
          category: 'Data',
          metadata: { index: index }
        }));

        // If too many values, aggregate smaller ones
        let finalDataPoints = dataPoints;
        if (dataPoints.length > 8) {
          const sortedPoints = dataPoints.sort((a, b) => b.value - a.value);
          const topPoints = sortedPoints.slice(0, 7);
          const others = sortedPoints.slice(7);
          const othersSum = others.reduce((sum, point) => sum + point.value, 0);
          
          if (othersSum > 0) {
            topPoints.push({
              label: 'Others',
              value: othersSum,
              unit: indicator.unit || 'units',
              category: 'Data',
              metadata: { aggregated: true }
            });
          }
          
          finalDataPoints = topPoints;
        }

        // Create pie chart data
        const chartData = this.createSimplePieChartData(indicator, finalDataPoints);

        return {
          config: indicator,
          data: finalDataPoints,
          summary: {
            total: finalDataPoints.reduce((sum, dp) => sum + dp.value, 0),
            average: finalDataPoints.reduce((sum, dp) => sum + dp.value, 0) / finalDataPoints.length,
            count: finalDataPoints.length
          },
          chartData
        };
      } else {
        return this.createFallbackOverviewIndicator(indicator);
      }

    } catch (error) {
      console.error('🎯 CategoryModal: Error processing overview indicator:', error);
      return this.createFallbackOverviewIndicator(indicator);
    }
  }

  // Create simple pie chart data without regional grouping
  private createSimplePieChartData(indicator: IndicatorConfig, dataPoints: IndicatorDataPoint[]): PieChartData {
    return {
      series: dataPoints.map(dp => dp.value),
      options: {
        chart: {
          type: 'donut',
          height: 250,
          toolbar: { show: false }
        },
        labels: dataPoints.map(dp => `${dp.value} ${dp.unit || ''}`),
        colors: ['#007bff', '#28a745', '#ffc107', '#dc3545', '#17a2b8', '#6f42c1', '#e83e8c', '#fd7e14'],
        legend: {
          position: 'bottom',
          fontSize: '11px',
          markers: { width: 6, height: 6 }
        },
        dataLabels: {
          enabled: true,
          formatter: function (val: number) {
            return Math.round(val) + '%';
          },
          style: { fontSize: '10px' }
        },
        plotOptions: {
          pie: {
            donut: {
              size: '60%',
              labels: {
                show: true,
                total: {
                  show: true,
                  label: 'Total',
                  formatter: function () {
                    const total = dataPoints.reduce((sum, dp) => sum + dp.value, 0);
                    return total.toLocaleString();
                  }
                }
              }
            }
          }
        },
        tooltip: {
          y: {
            formatter: function (val: number) {
              return val.toLocaleString() + ' ' + (indicator.unit || 'units');
            }
          }
        },
        title: {
          text: indicator.name,
          align: 'center',
          style: {
            fontSize: '13px',
            fontWeight: '600'
          }
        }
      }
    };
  }

  // Create fallback indicator data for overview when no real data available
  private createFallbackOverviewIndicator(indicator: IndicatorConfig): OverviewIndicator {
    const mockDataPoints: IndicatorDataPoint[] = [
      { label: 'Value 1', value: 35, unit: indicator.unit || 'units', category: 'Sample', metadata: {} },
      { label: 'Value 2', value: 25, unit: indicator.unit || 'units', category: 'Sample', metadata: {} },
      { label: 'Value 3', value: 20, unit: indicator.unit || 'units', category: 'Sample', metadata: {} },
      { label: 'Value 4', value: 20, unit: indicator.unit || 'units', category: 'Sample', metadata: {} }
    ];
    
    const chartData = this.createSimplePieChartData(indicator, mockDataPoints);
    
    return {
      config: indicator,
      data: mockDataPoints,
      summary: {
        total: mockDataPoints.reduce((sum, dp) => sum + dp.value, 0),
        average: 25,
        count: mockDataPoints.length
      },
      chartData
    };
  }

  // Helper method to get chart data for overview indicators
  getOverviewIndicatorChart(index: number): any {
    if (index >= 0 && index < this.overviewIndicators.length) {
      return this.overviewIndicators[index].chartData;
    }
    return null;
  }

  // Helper method to get indicator name for overview
  getOverviewIndicatorName(index: number): string {
    if (index >= 0 && index < this.overviewIndicators.length) {
      return this.overviewIndicators[index].config.name;
    }
    return `Indicator ${index + 1}`;
  }

  // Helper method to check if overview indicators are available
  hasOverviewIndicators(): boolean {
    return this.overviewIndicators.length > 0;
  }

  // Tracking method for overview indicators
  trackOverviewIndicator(index: number, indicator: OverviewIndicator): string {
    return indicator.config.id;
  }

  onDynamicColumnChange(index: number, event: any): void {
    this.selectedDynamicColumns[index] = event.target.value;
    this.processOverviewIndicators();
    this.changeDetectorRef.detectChanges();
  }

  onOverviewIndicatorChange(event: any): void {
    this.selectedOverviewIndicator = event.target.value;
    this.processOverviewIndicators();
    this.changeDetectorRef.detectChanges();
  }

  hasSelectedOverviewIndicator(): boolean {
    return !!this.selectedOverviewIndicator;
  }

  getSelectedIndicatorName(): string {
    const selectedOption = this.availableDynamicColumnOptions.find(opt => opt.id === this.selectedOverviewIndicator);
    return selectedOption ? selectedOption.name : 'N/A';
  }

  getSelectedIndicatorTotal(): number {
    if (!this.selectedOverviewChart || !this.selectedOverviewChart.series) return 0;
    return this.selectedOverviewChart.series.reduce((sum: number, val: number) => sum + val, 0);
  }

  getSelectedIndicatorAverage(): number {
    if (!this.selectedOverviewChart || !this.selectedOverviewChart.series) return 0;
    const total = this.selectedOverviewChart.series.reduce((sum: number, val: number) => sum + val, 0);
    return this.selectedOverviewChart.series.length > 0 ? total / this.selectedOverviewChart.series.length : 0;
  }

  getSelectedIndicatorCount(): number {
    if (!this.selectedOverviewChart || !this.selectedOverviewChart.series) return 0;
    return this.selectedOverviewChart.series.length;
  }

  get selectedOverviewChart(): any {
    if (!this.selectedOverviewIndicator) return null;
    
    // Get single aggregated value for the selected indicator
    const indicatorValue = this.getSelectedIndicatorValue();
    const maxValue = this.getSelectedIndicatorMaxValue();
    const percentage = maxValue > 0 ? Math.round((indicatorValue / maxValue) * 100) : 0;
    
    return this.createProgressSemiCircleChart(percentage, indicatorValue);
  }

  private createProgressSemiCircleChart(percentage: number, value: number): any {
    // Get the selected indicator info to include count in title
    const selectedOption = this.availableDynamicColumnOptions.find(opt => opt.id === this.selectedOverviewIndicator);
    const indicatorName = selectedOption ? selectedOption.name : 'Indicator';
    
    // Get count from consolidated mapping if available
    let activityCount = 1;
    for (const [key, consolidatedData] of this.consolidatedIndicatorMapping.entries()) {
      if (consolidatedData.id === this.selectedOverviewIndicator) {
        activityCount = consolidatedData.count;
        break;
      }
    }
    
    // Create chart title with count
    const chartTitle = activityCount > 1 ? `${activityCount} - ${indicatorName}` : indicatorName;
    
    return {
      series: [percentage],
      options: {
        chart: {
          type: 'radialBar',
          height: 200,
          sparkline: {
            enabled: true
          }
        },
        plotOptions: {
          radialBar: {
            startAngle: -90,
            endAngle: 90,
            track: {
              background: '#e7e7e7',
              strokeWidth: '97%',
              margin: 5
            },
            dataLabels: {
              name: {
                show: true,
                fontSize: '12px',
                fontWeight: 600,
                color: '#333',
                offsetY: -10,
                formatter: function () {
                  return chartTitle;
                }
              },
              value: {
                show: true,
                fontSize: '20px',
                fontWeight: 600,
                color: '#007bff',
                offsetY: 10,
                formatter: function () {
                  return value.toLocaleString();
                }
              }
            }
          }
        },
        colors: ['#007bff'],
        fill: {
          type: 'gradient',
          gradient: {
            shade: 'light',
            shadeIntensity: 0.1,
            inverseColors: false,
            opacityFrom: 1,
            opacityTo: 0.8,
            stops: [0, 50, 53, 91]
          }
        },
        labels: ['Progress']
      }
    };
  }

  getSelectedIndicatorValue(): number {
    if (!this.selectedOverviewIndicator) return 0;
    
    console.log('🎯 CategoryModal: Getting value for consolidated indicator:', this.selectedOverviewIndicator);
    
    // First, try to get value from consolidated mapping
    for (const [key, consolidatedData] of this.consolidatedIndicatorMapping.entries()) {
      if (consolidatedData.id === this.selectedOverviewIndicator) {
        console.log('🎯 CategoryModal: Found consolidated indicator:', {
          selectedIndicator: this.selectedOverviewIndicator,
          consolidatedKey: key,
          totalValue: consolidatedData.totalValue,
          activityCount: consolidatedData.count,
          name: consolidatedData.name
        });
        return consolidatedData.totalValue;
      }
    }
    
    // Fallback to original method for backward compatibility
    console.log('🎯 CategoryModal: Using fallback method for indicator value');
    
    // Get all matching columns for the selected indicator using the unique key approach
    const matchingColumns = this.dynamicColumns.filter(col => {
      // Match by the unique key format: columnName_columnId
      const uniqueKey = `${col.columnName}_${col.columnId || col.id}`;
      const matchesUniqueKey = uniqueKey === this.selectedOverviewIndicator;
      
      // Also try fallback matching for backward compatibility
      const pattern = this.selectedOverviewIndicator.toLowerCase();
      const columnName = col.columnName.toLowerCase();
      const matchesPattern = columnName.includes(pattern) || this.normalizeColumnName(columnName) === pattern;
      
      return matchesUniqueKey || matchesPattern;
    });

    console.log('🎯 CategoryModal: Found matching columns (fallback):', {
      selectedIndicator: this.selectedOverviewIndicator,
      matchingColumnsCount: matchingColumns.length,
      matchingColumns: matchingColumns.map(col => ({
        columnName: col.columnName,
        value: col.value,
        columnId: col.columnId,
        uniqueKey: `${col.columnName}_${col.columnId || col.id}`
      }))
    });

    // Sum all values for this indicator
    const totalValue = matchingColumns.reduce((sum, col) => {
      const value = this.parseNumericValue(col.value);
      console.log('🎯 CategoryModal: Processing column value (fallback):', {
        columnName: col.columnName,
        rawValue: col.value,
        numericValue: value,
        isValid: !isNaN(value)
      });
      return sum + (isNaN(value) ? 0 : value);
    }, 0);

    console.log('🎯 CategoryModal: Calculated total value (fallback):', totalValue);
    return totalValue;
  }

  getSelectedIndicatorMaxValue(): number {
    // Simple approach: use a reasonable max based on the current value
    const currentValue = this.getSelectedIndicatorValue();
    if (currentValue === 0) return 100;
    
    // Set max as 1.5x current value or minimum 100
    return Math.max(currentValue * 1.5, 100);
  }

  getSelectedIndicatorUnit(): string {
    if (!this.selectedOverviewIndicator) return '';
    
    const selectedOption = this.availableDynamicColumnOptions.find(opt => opt.id === this.selectedOverviewIndicator);
    return selectedOption ? selectedOption.unit : '';
  }

  // Method to refresh dynamic columns and dropdown options from hierarchical endpoint
  refreshDynamicColumnsFromHierarchicalEndpoint(): void {
    console.log('🎯 CategoryModal: Refreshing dynamic columns from hierarchical endpoint for category:', this.categoryId);
    
    // Create filters for this specific category
    const categoryFilters: ReportFilter = {
      catIds: [this.categoryId.toString()]
    };
    
    // Call hierarchical endpoint with dynamic columns enabled
    this.reportsService.getHierarchicalReport(categoryFilters, { includeDynamicColumns: true })
      .subscribe({
        next: (hierarchicalData) => {
          console.log('🎯 CategoryModal: Refreshed hierarchical data with dynamic columns:', hierarchicalData);
          
          // Re-process the hierarchical data to extract updated dynamic columns
          this.processCategoryHierarchicalData(hierarchicalData);
          
          // Trigger change detection
          this.changeDetectorRef.detectChanges();
          
          console.log('🎯 CategoryModal: Successfully refreshed dynamic columns dropdown options');
        },
        error: (error) => {
          console.error('🎯 CategoryModal: Error refreshing dynamic columns from hierarchical endpoint:', error);
          this.error = 'Failed to refresh dynamic columns data';
          this.changeDetectorRef.detectChanges();
        }
      });
  }

  // Helper method to get dropdown options summary for debugging
  getDynamicColumnOptionsSummary(): any {
    return {
      totalOptions: this.availableDynamicColumnOptions.length,
      selectedIndicator: this.selectedOverviewIndicator,
      options: this.availableDynamicColumnOptions.map(opt => ({
        id: opt.id,
        name: opt.name,
        unit: opt.unit
      })),
      sourceData: {
        totalDynamicColumns: this.dynamicColumns.length,
        quantitativeColumns: this.dynamicColumns.filter(col => this.isQuantitativeColumn(col)).length
      }
    };
  }

  getTotalCashDistributed(): number {
    // Sum up cash distributed from interventions - look for specific disbursement fields
    return this.categoryInterventions.reduce((sum, intervention) => {
      // Priority order: disbursedAmount > cashDistributed > actualBudget > budget
      const cashAmount = intervention.disbursedAmount || 
                        intervention.cashDistributed || 
                        intervention.actualSpent || 
                        intervention.actualBudget || 
                        intervention.budget || 
                        0;
      return sum + cashAmount;
    }, 0);
  }

  getNumberOfProjects(): number {
    return this.categoryInterventions.length;
  }

  getNumberOfActivities(): number {
    return this.categoryActivities.length;
  }

  getInterventionCashDistributed(intervention: any): number {
    // Priority order for intervention-level cash distributed fields:
    // 1. disbursedAmount, 2. cashDistributed, 3. actualSpent, 4. actualBudget
    let interventionCash = intervention.disbursedAmount || 
                          intervention.cashDistributed || 
                          intervention.actualSpent || 
                          intervention.actualBudget || 
                          0;
    
    // If no intervention-level cash amount, calculate from activities
    if (interventionCash === 0 && intervention.activities && intervention.activities.length > 0) {
      interventionCash = intervention.activities.reduce((sum: number, activity: any) => {
        const activityCash = activity.disbursedAmount || 
                            activity.cashDistributed || 
                            activity.actualSpent || 
                            activity.actualBudget || 
                            activity.budget || 
                            0;
        return sum + activityCash;
      }, 0);
    }
    
    return interventionCash;
  }
} 
