import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { environment } from '../../../../environments/environment';
import { FormsModule } from '@angular/forms';
import { firstValueFrom } from 'rxjs';

@Component({
  selector: 'app-debug-api',
  template: `
    <div class="card mb-4">
      <div class="card-header">
        <h3 class="card-title">API Debug Tool</h3>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-12 mb-3">
            <label class="form-label">API Endpoint</label>
            <input type="text" class="form-control" [(ngModel)]="apiEndpoint" />
          </div>
          <div class="col-md-12 mb-3">
            <button class="btn btn-primary me-2" (click)="testEndpoint()">Test Endpoint</button>
            <button class="btn btn-secondary" (click)="testAllEndpoints()">Test All Dashboard Endpoints</button>
          </div>
        </div>
        
        <div *ngIf="loading" class="d-flex justify-content-center my-3">
          <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
          </div>
        </div>
        
        <div *ngIf="error" class="alert alert-danger">
          {{ error }}
        </div>
        
        <div *ngIf="results" class="mt-4">
          <h4>Results:</h4>
          <pre class="bg-light p-3 rounded">{{ results | json }}</pre>
        </div>
      </div>
    </div>
  `,
  standalone: true,
  imports: [CommonModule, FormsModule]
})
export class DebugApiComponent implements OnInit {
  // Try different API URL formats to determine which one works
  apiEndpoint = `${environment.apiUrl}/api/dashboard/reports/category-statistics`;
  loading = false;
  error: string | null = null;
  results: any = null;
  
  constructor(private http: HttpClient) {}
  
  ngOnInit() {
    console.log('Debug component initialized with endpoint:', this.apiEndpoint);
  }
  
  testEndpoint() {
    this.loading = true;
    this.error = null;
    this.results = null;
    
    console.log('Testing endpoint:', this.apiEndpoint);
    
    this.http.get(this.apiEndpoint).subscribe({
      next: (response) => {
        console.log('API response:', response);
        this.results = response;
        this.loading = false;
      },
      error: (err) => {
        console.error('API Error:', err);
        this.error = `Error: ${err.status} - ${err.statusText || 'Unknown error'}`;
        if (err.error) {
          this.error += `\nDetails: ${JSON.stringify(err.error)}`;
        }
        this.loading = false;
      }
    });
  }
  
  async testAllEndpoints() {
    const baseUrl = `${environment.apiUrl}/api/dashboard`;
    const endpoints = [
      `${baseUrl}/reports/category-statistics`,
      `${baseUrl}/reports`,
      `${baseUrl}/reports/summary`,
      `${baseUrl}/reports/timeseries`
    ];
    
    this.loading = true;
    this.error = null;
    this.results = [];
    
    try {
      for (const endpoint of endpoints) {
        try {
          console.log('Testing endpoint:', endpoint);
          const response = await firstValueFrom(this.http.get(endpoint));
          this.results.push({ endpoint, status: 'Success', data: response });
        } catch (err: any) {
          console.error(`Error for ${endpoint}:`, err);
          this.results.push({ 
            endpoint, 
            status: 'Error', 
            error: {
              status: err.status,
              message: err.message,
              details: err.error
            } 
          });
        }
      }
    } catch (error) {
      console.error('Error testing endpoints:', error);
    } finally {
      this.loading = false;
    }
  }
} 