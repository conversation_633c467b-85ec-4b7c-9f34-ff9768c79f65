// Add these type declarations at the top of the file
declare module '@amcharts/amcharts5' {
  namespace am5 {
    interface Root {
      container: any;
      setThemes(themes: any[]): void;
      dispose(): void;
      resize(): void;
      verticalLayout: any;
      addDisposer(disposer: any): void;
      events: {
        on(type: string, callback: () => void): any;
      };
      p100: any;
    }
    
    interface IRootEvents {
      resize: any;
    }
  }

  namespace am5map {
    interface MapChart {
      set(key: string, value: any): void;
      get(key: string): any;
      zoomToGeoPoint(point: { longitude: number; latitude: number }, zoom: number, animate?: boolean, duration?: number): void;
    }
    
    interface IZoomControlSettings {
      marginRight?: number;
      marginTop?: number;
    }
  }
}

import { Component, OnInit, AfterViewInit, OnDestroy, ViewChild, HostBinding } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ChartComponent } from "ng-apexcharts";

// AmCharts imports
import * as am5 from '@amcharts/amcharts5';
import * as am5map from '@amcharts/amcharts5/map';
import am5themes_Animated from '@amcharts/amcharts5/themes/Animated';
import am5themes_Responsive from '@amcharts/amcharts5/themes/Responsive';
import am5geodata_worldLow from '@amcharts/amcharts5-geodata/worldLow';

// Define interfaces
interface Coordinates {
  lat: number;
  lng: number;
}

interface RegionData {
  id: number;
  name: string;
  projects: number;
  value: number;
}

// Add District interface
interface District {
  id: number;
  name: string;
  provinceId: number;
  provinceName: string;
  coordinates?: { lat: number; lng: number };
  population?: number;
  interventions?: any[];
}

interface ProjectData {
  name: string;
  region: string;
  status: string;
  budget: number;
}

interface KpiData {
  totalProjects: number;
  activeProjects: number;
  totalBudget: number;
  disbursedAmount: number;
  beneficiaries: number;
}

interface MapRegionData extends RegionData {
  code: string;  // Region code for map matching
  coordinates?: { lat: number; lng: number };  // Geographic coordinates
  interventions: any[];  // List of interventions in this region
  outputs: Set<string>;  // Set of unique output codes
  totalBeneficiaries: number;  // Total beneficiaries in the region
  beneficiaries: number;
  interventionCount: number;  // Count of interventions in the region
  districts?: District[];  // Add districts array to regions
}

interface Intervention {
    id: string;
    name: string;
    category: string;
  categoryColor: string;
    region: string;
    progress: number;
    beneficiaries: number;
    activities: number;
    startDate: string;
    endDate: string;
  output?: any;
  budget?: number;
  icon?: string;
  description?: string;
  progressColor?: string;
  variables?: any[];
}

// Add a Category interface after the other interfaces
interface Category {
  code: string;
        name: string;
  interventionCount: number;
  beneficiaries: number;
}

@Component({
    selector: 'app-new-dashboard',
    templateUrl: './new-dashboard.component.html',
    styleUrls: ['./new-dashboard.component.scss']
})
export class NewDashboardComponent implements OnInit, AfterViewInit, OnDestroy {
    @HostBinding('attr.data-qs-app-toolbar-fixed') pgToolbar = 'true';
    @HostBinding('attr.data-qs-app-header-fixed') pgHeader = 'true';
    @HostBinding('attr.data-qs-app-sidebar-push-toolbar') pushToolbar = 'true';
    
    @ViewChild("barChart") barChart: ChartComponent;
    @ViewChild("pieChart") pieChart: ChartComponent;

    // Data properties
    kpiData: KpiData;
    mapData: MapRegionData[] = [];
    recentProjects: ProjectData[] = [];
    private mapRoot!: am5.Root;
    interventions: Intervention[] = [];
    filteredInterventions: Intervention[] = [];
    selectedModalInterventions: Intervention[] = [];
    selectedRegion: MapRegionData | null = null;
    selectedCategory: Category | null = null;
    modalTitle: string = '';
    isLoading: boolean = false;
    activeInterventionTab: number = 0;
    projectStatusFilter: string = 'ongoing';
    selectedIntervention: Intervention | null = null;
    selectedModalIntervention: Intervention | null = null;
    modalDetailView: boolean = false;
    interventionSearchTerm: string = '';
    categoryInterventions: Intervention[] = [];
    infrastructureTabs: {id: string, name: string}[] = [
      {id: 'public-infrastructure', name: 'Public Infrastructure'},
      {id: 'productive-facility', name: 'Productive Facilities'}
    ];
    activeInfrastructureTab: string = 'public-infrastructure';
    
    // Map control functions
    extraZoomOut: () => void = () => {};

    // Add districts data
    private provincesWithDistricts: { [provinceName: string]: string[] } = {};
    
    // Add selectedDistrict property
    selectedDistrict: { name: string, province: string } | null = null;
    
    // Property to hold the reset button
    private resetButton: any = null;
    
    // Property to hold the reset map view function
    resetMapView: (chart: am5map.MapChart) => void = () => {};
    
    // Property to hold the clear districts function
    clearDistrictHighlights: () => void = () => {};

    // Add a property to track the provinces series
    private provincesMapSeries: am5map.MapPolygonSeries | null = null;

    constructor(private http: HttpClient) {
      // Initialize the province districts mapping
      this.initializeProvincesWithDistricts();
    }

    ngOnInit(): void {
    // Initialize data
    this.loadDummyData();
  }

  ngAfterViewInit(): void {
    // Initialize charts
    this.initBarChart(['Category 1', 'Category 2', 'Category 3', 'Category 4', 'Category 5']);
    this.initPieChart(['Complete', 'In Progress', 'Pending', 'Delayed'], [35, 25, 22, 18]);
    
    // First, ensure the map container exists and has proper dimensions
    const container = document.getElementById("mapChart");
    if (container) {
      // Force explicit height/width to ensure container is visible
      container.style.height = '500px';
      container.style.width = '100%';
      // Set a background to make sure container is visible
      container.style.backgroundColor = '#f5f5f5';
      container.style.border = '1px solid #ddd';
      container.style.position = 'relative'; // Add position relative
      
      // Add a loading indicator with absolute positioning that won't affect layout
      // Give it an ID so we can easily remove it later
      container.innerHTML = '<div id="mapLoadingSpinner" style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; display: flex; justify-content: center; align-items: center; z-index: 10;"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div></div>';
    }
    
    // Initialize the map with a longer delay to ensure the container is fully rendered
        setTimeout(() => {
      this.initMap();
    }, 1500);
  }

  ngOnDestroy(): void {
    // Dispose of the map to prevent memory leaks
        if (this.mapRoot) {
      // Only dispose if the component is truly being destroyed
      try {
        // Remove any event listeners first
        window.removeEventListener('resize', this.handleResize);
            this.mapRoot.dispose();
      } catch (e) {
        console.error("Error disposing map:", e);
      }
    }
  }

  // Add a handler reference for the resize event
  private handleResize = () => {
    if (this.mapRoot && !this.mapRoot.isDisposed()) {
      try {
        this.mapRoot.resize();
      } catch (e) {
        console.error("Error during resize:", e);
      }
    }
  };

  private loadDummyData(): void {
    // Load dummy data for demonstration
    this.kpiData = {
      totalProjects: 51,
      activeProjects: 38,
      totalBudget: 37500000,
      disbursedAmount: 24600000,
      beneficiaries: 125000
    };
    
    // Dummy map region data with districts
    this.mapData = [
      { 
        id: 1, 
        code: 'NR', 
        name: 'Northern Region', 
        projects: 12, 
        value: 8500000, 
        coordinates: { lat: 36.5, lng: 67.5 }, 
        interventions: [], 
        outputs: new Set<string>(), 
        totalBeneficiaries: 45000, 
        beneficiaries: 45000, 
        interventionCount: 8,
        districts: this.generateDistrictsForRegion('Northern Region', ['Jowzjan', 'Balkh', 'Samangan', 'Sar-e Pol', 'Faryab'])
      },
      { 
        id: 2, 
        code: 'SR', 
        name: 'Southern Region', 
        projects: 8, 
        value: 6200000, 
        coordinates: { lat: 31.5, lng: 67.0 }, 
        interventions: [], 
        outputs: new Set<string>(), 
        totalBeneficiaries: 30000, 
        beneficiaries: 30000, 
        interventionCount: 5,
        districts: this.generateDistrictsForRegion('Southern Region', ['Nimroz', 'Helmand', 'Kandahar', 'Zabul', 'Uruzgan'])
      },
      { 
        id: 3, 
        code: 'ER', 
        name: 'Eastern Region', 
        projects: 10, 
        value: 7800000, 
        coordinates: { lat: 34.0, lng: 70.5 }, 
        interventions: [], 
        outputs: new Set<string>(), 
        totalBeneficiaries: 38000, 
        beneficiaries: 38000, 
        interventionCount: 7,
        districts: this.generateDistrictsForRegion('Eastern Region', ['Nuristan', 'Kunar', 'Laghman', 'Nangarhar'])
      },
      { 
        id: 4, 
        code: 'WR', 
        name: 'Western Region', 
        projects: 6, 
        value: 5400000, 
        coordinates: { lat: 33.5, lng: 63.5 }, 
        interventions: [], 
        outputs: new Set<string>(), 
        totalBeneficiaries: 25000, 
        beneficiaries: 25000, 
        interventionCount: 4,
        districts: this.generateDistrictsForRegion('Western Region', ['Herat', 'Badghis', 'Farah'])
      },
      { 
        id: 5, 
        code: 'CR', 
        name: 'Central Region', 
        projects: 15, 
        value: 9600000, 
        coordinates: { lat: 34.5, lng: 67.5 }, 
        interventions: [], 
        outputs: new Set<string>(), 
        totalBeneficiaries: 52000, 
        beneficiaries: 52000, 
        interventionCount: 11,
        districts: this.generateDistrictsForRegion('Central Region', ['Maidan Wardak', 'Parwan', 'Kapisa', 'Panjsher', 'Kabul'])
      }
    ];
    
    // Dummy projects
    this.recentProjects = [
      { name: 'Water Access Initiative', region: 'Northern Region', status: 'Complete', budget: 1200000 },
      { name: 'Sustainable Agriculture Program', region: 'Eastern Region', status: 'In Progress', budget: 2450000 },
      { name: 'Healthcare Development Project', region: 'Central Region', status: 'Pending', budget: 1875000 },
      { name: 'Education Support Initiative', region: 'Southern Region', status: 'In Progress', budget: 980000 },
      { name: 'Infrastructure Improvement', region: 'Western Region', status: 'Delayed', budget: 3200000 }
    ];
    
    // Generate dummy interventions
    this.interventions = [
      {
        id: "INF-2023-001",
        name: "Infrastructure Development Program",
        category: "Infrastructure",
        categoryColor: "primary",
        region: "Northern Region",
        progress: 65,
        beneficiaries: 45000,
        activities: 24,
        startDate: "2023-01-15",
        endDate: "2024-07-31",
        output: { code: "1.1", name: "Infrastructure Development" },
        budget: 1200000
      },
      {
        id: "HLT-2023-002",
        name: "Healthcare Services Improvement",
        category: "Health",
        categoryColor: "success",
        region: "Eastern Region",
        progress: 78,
        beneficiaries: 38000,
        activities: 32,
        startDate: "2023-02-01",
        endDate: "2024-11-30",
        output: { code: "1.2", name: "Health Services" },
        budget: 2450000
      },
      {
        id: "EDU-2023-003",
        name: "Educational Access Initiative",
        category: "Education",
        categoryColor: "info",
        region: "Central Region",
        progress: 92,
        beneficiaries: 52000,
        activities: 28,
        startDate: "2022-11-15",
        endDate: "2023-12-15",
        output: { code: "1.3", name: "Education Access" },
        budget: 1875000
      },
      {
        id: "AGR-2023-004",
        name: "Agricultural Development Project",
        category: "Agriculture",
        categoryColor: "warning",
        region: "Southern Region",
        progress: 45,
        beneficiaries: 30000,
        activities: 18,
        startDate: "2023-03-01",
        endDate: "2025-02-28",
        output: { code: "2.1", name: "Agriculture Development" },
        budget: 1400000
      },
      {
        id: "ENV-2023-005",
        name: "Environmental Protection Program",
        category: "Environment",
        categoryColor: "danger",
        region: "Western Region",
        progress: 32,
        beneficiaries: 25000,
        activities: 15,
        startDate: "2023-04-15",
        endDate: "2025-04-14",
        output: { code: "3.1", name: "Environmental Protection" },
        budget: 980000
      }
    ];
    
    this.filteredInterventions = [...this.interventions];
  }

  private initBarChart(categories: string[]): void {
    // Implementation would go here
    console.log("Bar chart initialized with categories:", categories);
    }

    private initPieChart(labels: string[], data: number[]): void {
    // Implementation would go here
    console.log("Pie chart initialized with labels:", labels);
  }

  initMap(): void {
    console.log("Initializing map...");
    
    // First, check if the container exists and has dimensions
    const container = document.getElementById("mapChart");
    if (!container) {
        console.error("Map container element not found!");
        // Retry after a delay if container is not found
        setTimeout(() => this.initMap(), 1000);
        return;
    }
    
    // Ensure container has proper dimensions and is visible
    if (!container.style.height || container.style.height === '0px' || container.clientHeight === 0) {
        console.log("Setting default height for map container");
        container.style.height = '500px';
    }
    
    if (!container.style.width || container.style.width === '0px' || container.clientWidth === 0) {
        console.log("Setting default width for map container");
        container.style.width = '100%';
    }
    
    // Log container dimensions to help debug
    console.log(`Map container dimensions: ${container.clientWidth}x${container.clientHeight}`);
    if (container.clientWidth === 0 || container.clientHeight === 0) {
        console.error("Container has zero dimensions, waiting...");
        setTimeout(() => this.initMap(), 1000);
        return;
    }

    // Prevent multiple initializations
    if (this.mapRoot && !this.mapRoot.isDisposed()) {
        console.log("Map already initialized, skipping");
        this.removeLoadingSpinner();
        return;
    }
            
    try {
        // Dispose of previous instance if it exists
        if (this.mapRoot) {
            try {
            this.mapRoot.dispose();
            } catch (e) {
                console.error("Error disposing previous map:", e);
            }
        }

        // Create root element with explicit container reference
        this.mapRoot = am5.Root.new("mapChart");
        
        if (!this.mapRoot) {
            console.error("Failed to create map root element");
            this.removeLoadingSpinner();
            return;
        }
        
        // Store reference to the root in window for debugging
        (window as any).mapRoot = this.mapRoot;
        
        // Set themes
        this.mapRoot.setThemes([
            am5themes_Animated.new(this.mapRoot),
            am5themes_Responsive.new(this.mapRoot)
        ]);

        // Create map chart
        const chart = this.mapRoot.container.children.push(
            am5map.MapChart.new(this.mapRoot, {
                panX: "translateX",
                panY: "translateY",
                projection: am5map.geoMercator(),
                maxZoomLevel: 32,
                minZoomLevel: 0.5,
                wheelY: "zoom",
                wheelX: "none",
                pinchZoom: true
            })
        );
        
        // Store reference to the chart in window for debugging
        (window as any).mapChart = chart;
        
        if (!chart) {
            console.error("Failed to create chart element");
            this.removeLoadingSpinner();
            return;
        }
        
        // Try to prevent chart disposal (this approach may not work due to typing issues)
        try {
            // @ts-ignore - Ignore the typing error for this event
            chart.events.on("beforedisposed", () => {
                console.log("Preventing chart disposal");
                return true;
            });
        } catch (e) {
            console.error("Could not set beforedisposed event handler", e);
        }
        
        // Add zoom control
        const zoomControl = am5map.ZoomControl.new(this.mapRoot, {});
        zoomControl.set("marginRight", 20);
        zoomControl.set("marginTop", 20);
        chart.set("zoomControl", zoomControl);
        
        // Define Afghanistan bounds
        const afghBounds = {
            north: 38.5,
            south: 29.4,
            east: 74.9,
            west: 60.5
        };
        
        const afghCenter = { 
            longitude: (afghBounds.east + afghBounds.west) / 2, 
            latitude: (afghBounds.north + afghBounds.south) / 2 
        };
        
        // Add global method for extra zoom out
        this.extraZoomOut = () => {
            if (chart && !chart.isDisposed()) {
          chart.zoomToGeoPoint(afghCenter, 1, true, 900);
            }
        };

        // Define regions
        const northeastProvinces = ["Kunduz", "Takhar", "Badakhshan", "Baghlan"];
        const northeastColor = "#a7bccf";
        
        const northProvinces = ["Jowzjan", "Balkh", "Samangan", "Sar-e Pol", "Faryab"];
        const northColor = "#afb593";

        const centralHighlands = ["Bamyan", "Ghor", "Daykundi"];
        const centralHighlandsColor = "#999ed8";

        const centralRegion = ["Maidan Wardak", "Parwan", "Kapisa", "Panjsher", "Kabul"];
        const centralRegionColor = "#9cd6c0";

        const easternRegion = ["Nuristan", "Kunar", "Laghman", "Nangarhar"];
        const easternRegionColor = "#dde2a0";

        const southeasternRegion = ["Ghazni", "Logar", "Paktya", "Khost", "Paktika"];
        const southeasternRegionColor = "#99d597";

        const southernRegion = ["Nimroz", "Helmand", "Kandahar", "Zabul", "Uruzgan"];
        const southernRegionColor = "#cfe0bc";

        const westernRegion = ["Herat", "Badghis", "Farah"];
        const westernRegionColor = "#82b4b1";

        // First, directly add a simple fallback map that will show immediately
        try {
            this.addFallbackAfghanistanMap(chart);
        } catch (e) {
            console.error("Error adding fallback map:", e);
        }
        
        // Then immediately try to load province data
        try {
            this.loadProvinceDataDirectly(chart, 
          northeastProvinces, northeastColor,
          northProvinces, northColor,
          centralHighlands, centralHighlandsColor,
          centralRegion, centralRegionColor,
          easternRegion, easternRegionColor,
          southeasternRegion, southeasternRegionColor,
          southernRegion, southernRegionColor,
          westernRegion, westernRegionColor
        );
        } catch (e) {
            console.error("Error pre-loading province data:", e);
        }
        
        // Multiple removal points for the loading spinner to ensure it's removed
        this.removeLoadingSpinner();
        
        // Set initial zoom with delay to ensure map is ready
        setTimeout(() => {
            if (chart && !chart.isDisposed()) {
                chart.zoomToGeoPoint(afghCenter, 1, true, 500);
                // Remove the loading spinner once map is zoomed
                this.removeLoadingSpinner();
            }
        }, 200);
        
        // Setup resize handler with the reference method instead of an inline function
        window.removeEventListener('resize', this.handleResize); // Remove any existing listeners
        window.addEventListener('resize', this.handleResize);
        
        // Define custom resize handler
        this.mapRoot.resize = () => {
            if (chart && !chart.isDisposed()) {
          setTimeout(() => {
                    try {
                        chart.zoomToGeoPoint(afghCenter, 1, true, 300);
                    } catch (e) {
                        console.error("Error during resize zoom:", e);
                    }
          }, 100);
            }
        };
        
        // Instead of using chart.events.on("ready"), use a timeout to check if the chart is ready
        setTimeout(() => {
            if (chart && !chart.isDisposed()) {
                console.log("Map is fully ready");
                this.removeLoadingSpinner();
            }
        }, 500);
        
        // Add an extra final check to ensure map is still there
        setTimeout(() => {
            if (chart && !chart.isDisposed()) {
                console.log("Final map visibility check passed");
                // Ensure container is still visible
                if (container && container.style.display === 'none') {
                    console.log("Map container was hidden, making visible again");
                    container.style.display = 'block';
                }
            } else {
                console.error("Map was disposed prematurely, attempting to reinitialize");
                this.initMap();
            }
        }, 2000);
        
        console.log("Map initialization complete");
        
    } catch (error) {
        console.error("Error initializing map:", error);
        
        if (container) {
        container.innerHTML = '<div class="p-4 text-center text-danger">Error loading map. Please refresh the page.</div>';
        }
        
        // Remove loading spinner in case of error
        this.removeLoadingSpinner();
        
        // Try to initialize again after a delay
        setTimeout(() => {
            if (!this.mapRoot || this.mapRoot.isDisposed()) {
                console.log("Retrying map initialization...");
                this.initMap();
            }
        }, 2000);
    }
  }

  // Helper method to remove the loading spinner
  private removeLoadingSpinner(): void {
    const spinner = document.getElementById('mapLoadingSpinner');
    if (spinner) {
      spinner.remove();
    }
  }

  // New direct data loading method for faster province display
  private loadProvinceDataDirectly(
    chart: am5map.MapChart,
    northeastProvinces: string[], northeastColor: string,
    northProvinces?: string[], northColor?: string,
    centralHighlands?: string[], centralHighlandsColor?: string,
    centralRegion?: string[], centralRegionColor?: string,
    easternRegion?: string[], easternRegionColor?: string,
    southeasternRegion?: string[], southeasternRegionColor?: string,
    southernRegion?: string[], southernRegionColor?: string,
    westernRegion?: string[], westernRegionColor?: string
  ): void {
    // Safety check
    if (!chart || chart.isDisposed()) return;
    
    // Create a container to hold district series
    const districtSeriesContainer: am5map.MapPolygonSeries[] = [];
    
    // Set up clear function
    const clearFn = () => {
      districtSeriesContainer.forEach(series => {
        if (series && !series.isDisposed()) {
          series.dispose();
        }
      });
      districtSeriesContainer.length = 0;
    };
    
    this.clearDistrictHighlights = clearFn;
    
    const handleProvinceData = (data: any) => {
        try {
            if (!chart || chart.isDisposed()) return;
            
            if (!data.features || data.features.length === 0) {
                console.error("No features found in GeoJSON data");
                return;
            }

            // First remove all existing map series
            this.removeAllOutlineMaps(chart);

                // Create province series
                const provincesSeries = chart.series.push(
                    am5map.MapPolygonSeries.new(this.mapRoot, {
                    geoJSON: data,
                    stroke: am5.color(0x333333),
                    id: "provincesMap" // Add an ID to identify this as the main provinces map
                    })
                );
                
            // Store reference to the provinces series
            this.provincesMapSeries = provincesSeries;
            
            // Set polygon appearance
                provincesSeries.mapPolygons.template.setAll({
                    tooltipText: "{name}",
                    fillOpacity: 0.8,
                fill: am5.color(0xDDDDDD),
                strokeWidth: 1.5,
                strokeOpacity: 1
            });
            
            // Add reset button
            const buttonContainer = this.mapRoot?.container?.children?.push(
                am5.Container.new(this.mapRoot, {
                    x: am5.p100,
                    y: 10,
                    dx: -10,
                    paddingTop: 5,
                    layout: this.mapRoot.horizontalLayout,
                    centerY: 0,
                    centerX: am5.p100
                })
            );

            this.resetButton = buttonContainer?.children?.push(
                am5.Button.new(this.mapRoot, {
                    paddingTop: 2,
                    paddingBottom: 2,
                    paddingLeft: 4,
                    paddingRight: 4,
                    label: am5.Label.new(this.mapRoot, {
                        text: "Exit District View",
                        fontSize: 12
                    }),
                    visible: false
                })
            );

            if (this.resetButton) {
                this.resetButton.events.on("click", () => {
                    this.resetMapView(chart);
                });
            }

            // Define reset view function
            this.resetMapView = (mapChart: am5map.MapChart) => {
                if (!mapChart || mapChart.isDisposed()) return;
                
                this.clearDistrictHighlights();
                
                const afghCenter = { 
                    longitude: (74.9 + 60.5) / 2,
                    latitude: (38.5 + 29.4) / 2
                };
                mapChart.zoomToGeoPoint(afghCenter, 1, true, 900);
                
                if (this.resetButton) {
                    this.resetButton.set("visible", false);
                }
            };
            
            // Add tooltips
            provincesSeries.mapPolygons.template.adapters.add("tooltipText", (text, target) => {
                const dataContext = target.dataItem?.dataContext as any;
                if (!dataContext) return text;
                
                const provinceName = dataContext.name || dataContext.shapeName || dataContext.ADM1_EN;
                if (!provinceName) return text;
                
                const districts = this.provincesWithDistricts[provinceName];
                if (!districts || districts.length === 0) return provinceName;
                
                return `[bold]${provinceName}[/]\nDistricts: ${districts.slice(0, 5).join(", ")}`;
            });
            
            // Add click handlers
                provincesSeries.mapPolygons.template.events.on("click", (ev) => {
                    const polygon = ev.target;
                    const dataContext = polygon.dataItem.dataContext as any;
                const provinceName = dataContext.name || dataContext.shapeName || "Unknown";
                
                const districts = this.provincesWithDistricts[provinceName];
                if (districts && districts.length > 0) {
                    this.clearDistrictHighlights();
                    this.displayDistrictsForProvince(chart, provinceName, districts, districtSeriesContainer);
                }
            });
            
            // Add hover state
                provincesSeries.mapPolygons.template.states.create("hover", {
                    fillOpacity: 0.9,
                    strokeWidth: 2
                });
                
            // Apply colors
                provincesSeries.events.on("datavalidated", () => {
                if (!provincesSeries.mapPolygons) return;
                
                // Get matcher function
                    const matchProvinceName = (actualName: string, targetName: string): boolean => {
                        if (!actualName) return false;
                        
                        const normalize = (name: string) => name.toLowerCase()
                            .normalize("NFD").replace(/[\u0300-\u036f]/g, "")
                            .replace(/\s+province$/, "").trim();
                        
                        const normalizedActual = normalize(actualName);
                        const normalizedTarget = normalize(targetName);
                        
                        if (normalizedActual === normalizedTarget) return true;
                        
                        if (targetName === "Paktika" && 
                            (normalizedActual.includes("paktik") || 
                             normalizedActual.includes("paktīk") ||
                             normalizedActual.includes("pakteka"))) {
                            return true;
                        }
                        
                        if (targetName === "Parwan" && 
                            (normalizedActual.includes("parwa") || 
                             normalizedActual.includes("parva") ||
                             normalizedActual.includes("parwā"))) {
                            return true;
                        }
                        
                        return false;
                    };
                    
                // Color provinces
                    provincesSeries.mapPolygons.each(polygon => {
                        const dataContext = polygon.dataItem.dataContext as any;
                        const provinceName = dataContext.name || dataContext.shapeName || dataContext.ADM1_EN;
                        
                    if (!provinceName) return;
                    
                        let isProcessed = false;
                        
                    // Handle special cases first
                        if (centralRegion && centralRegionColor && provinceName) {
                            for (const targetName of centralRegion) {
                                if (matchProvinceName(provinceName, targetName)) {
                                    polygon.set("fill", am5.color(centralRegionColor));
                                    isProcessed = true;
                                    break;
                                }
                            }
                        }
                        
                        if (!isProcessed && southeasternRegion && southeasternRegionColor && provinceName) {
                            for (const targetName of southeasternRegion) {
                                if (matchProvinceName(provinceName, targetName)) {
                                    polygon.set("fill", am5.color(southeasternRegionColor));
                                    isProcessed = true;
                                    break;
                                }
                            }
                        }
                        
                    // Handle other regions
                        if (!isProcessed) {
                            if (provinceName && northeastProvinces.includes(provinceName)) {
                                polygon.set("fill", am5.color(northeastColor));
                            } else if (northProvinces && northColor && provinceName && northProvinces.includes(provinceName)) {
                                polygon.set("fill", am5.color(northColor));
                            } else if (centralHighlands && centralHighlandsColor && provinceName && centralHighlands.includes(provinceName)) {
                                polygon.set("fill", am5.color(centralHighlandsColor));
                            } else if (centralRegion && centralRegionColor && provinceName && centralRegion.includes(provinceName)) {
                                polygon.set("fill", am5.color(centralRegionColor));
                            } else if (easternRegion && easternRegionColor && provinceName && easternRegion.includes(provinceName)) {
                                polygon.set("fill", am5.color(easternRegionColor));
                            } else if (southeasternRegion && southeasternRegionColor && provinceName && 
                                (southeasternRegion.includes(provinceName) || 
                                (provinceName && ["Paktika", "Paktīkā", "Paktikah", "Paktiká"].includes(provinceName) && southeasternRegion.includes("Paktika")))) {
                                polygon.set("fill", am5.color(southeasternRegionColor));
                            } else if (southernRegion && southernRegionColor && provinceName && southernRegion.includes(provinceName)) {
                                polygon.set("fill", am5.color(southernRegionColor));
                            } else if (westernRegion && westernRegionColor && provinceName && westernRegion.includes(provinceName)) {
                                polygon.set("fill", am5.color(westernRegionColor));
                            }
                        }
                    });
                });
        } catch (error) {
            console.error("Error processing province data:", error);
        }
    };

    // Immediate attempt to load provinces
    const provincesGeoJsonPath = '/assets/data/afghanistan-provinces.geojson';
    console.log(`Attempting to load provinces GeoJSON from ${window.location.origin}${provincesGeoJsonPath}`);
    
    fetch(provincesGeoJsonPath)
        .then(response => {
            console.log(`Provinces GeoJSON fetch response status: ${response.status} ${response.statusText}`);
            if (!response.ok) {
                if (response.status === 404) {
                    console.error(`Provinces GeoJSON file not found at path: ${provincesGeoJsonPath}`);
                    throw new Error(`File Not Found: ${provincesGeoJsonPath}`);
                }
                throw new Error(`Failed to load province data: ${response.status}`);
            }
            return response.json();
        })
        .then(data => {
            console.log("Provinces GeoJSON data successfully loaded:", data ? `Data received with ${data.features?.length || 0} features` : "No data");
            handleProvinceData(data);
        })
        .catch(error => {
            console.error("Error loading province data:", error.message);
        });
  }

  // Replace the previous method with a more aggressive approach
  private removeAllOutlineMaps(chart: am5map.MapChart): void {
    if (!chart || chart.isDisposed()) return;
    
    try {
      console.log("Removing all outline maps");
      
      // Get a count of series before removal for logging
      let seriesCount = 0;
      chart.series.each(() => { seriesCount++; });
      console.log(`Found ${seriesCount} total series before cleanup`);
      
      // Make a copy of the series array to avoid modification during iteration
      const seriesToCheck: any[] = [];
      chart.series.each((series) => {
        if (series instanceof am5map.MapPolygonSeries) {
          seriesToCheck.push(series);
        }
      });
      
      console.log(`Found ${seriesToCheck.length} map polygon series to examine`);
      
      // Now iterate through the copy and remove all polygon series except our provinces series
      for (const series of seriesToCheck) {
        // If this is the provinces series we want to keep, skip it
        if (series === this.provincesMapSeries) {
          console.log("Keeping the main provinces series");
          continue;
        }
        
        // Remove all other map polygon series - they are outline or fallback maps
        const seriesId = series.get("id");
        console.log(`Removing map series ${seriesId || "(unnamed)"}`);
        if (!series.isDisposed()) {
          series.dispose();
        }
      }
      
      // Count remaining series for verification
      let remainingCount = 0;
      chart.series.each(() => { remainingCount++; });
      console.log(`${remainingCount} series remain after cleanup`);
      
    } catch (e) {
      console.error("Error removing outline maps:", e);
    }
  }

  private addFallbackAfghanistanMap(chart: am5map.MapChart): void {
    // Don't attempt to add fallback if chart is disposed
    if (!chart || chart.isDisposed()) {
        console.log("Chart is disposed, not adding fallback");
        return;
    }

    try {
        console.log("Starting Afghanistan map loading process");
        // First add very basic outline immediately to ensure something is displayed
        console.log("Adding emergency fallback outline as initial placeholder");
        this.addEmergencyFallbackOutline(chart);
        
        // Then try to load better outline from GeoJSON file
        const geoJsonPath = '/assets/data/afghanistan-outline.geojson';
        console.log(`Attempting to load GeoJSON outline from ${window.location.origin}${geoJsonPath}`);
        
        fetch(geoJsonPath)
            .then(response => {
                console.log(`GeoJSON fetch response status: ${response.status} ${response.statusText}`);
                if (!response.ok) {
                    if (response.status === 404) {
                        console.error(`GeoJSON file not found at path: ${geoJsonPath}`);
                        throw new Error(`File Not Found: ${geoJsonPath}`);
                    }
                    throw new Error(`Failed to load outline data: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log("GeoJSON data successfully loaded:", data ? "Data received" : "No data");
                if (!chart || chart.isDisposed()) {
                    console.log("Chart was disposed during GeoJSON loading");
                    return;
                } // Check again as fetch is async
                
                try {
                    console.log("Creating polygon series from GeoJSON data");
                // Create a fallback polygon series
                const fallbackSeries = chart.series.push(
                    am5map.MapPolygonSeries.new(this.mapRoot, {
                            geoJSON: data,
                            // Add a property to identify this as a fallback layer
                            id: "fallbackOutline"
                    })
                );
                
                    console.log("Styling the Afghanistan polygon");
                // Style the fallback polygon
                fallbackSeries.mapPolygons.template.setAll({
                    fill: am5.color(0x9cd6c0),
                    stroke: am5.color(0x333333),
                    strokeWidth: 1.5,
                    fillOpacity: 0.8,
                    tooltipText: "Afghanistan"
                });
                
                    console.log("Zooming to Afghanistan");
                // Zoom to the fallback polygon
                chart.zoomToGeoPoint({ longitude: 66.0, latitude: 33.5 }, 1.8, true, 1000);
                
                    console.log("Successfully added fallback Afghanistan map from GeoJSON");
                } catch (innerError) {
                    console.error("Error creating fallback series:", innerError);
                    console.log("Using emergency fallback since GeoJSON processing failed");
                    // The emergency fallback is already added, so no need to add it again
                }
            })
            .catch(error => {
                console.error("Error loading fallback outline:", error.message);
                console.log("Using emergency fallback since GeoJSON couldn't be loaded");
                // No need to add emergency fallback again as we already did it at the start
            });
    } catch (error) {
        console.error("Error adding fallback map:", error);
        console.log("Using emergency fallback as last resort");
        // Try the emergency fallback as a last resort
        this.addEmergencyFallbackOutline(chart);
    }
  }
  
  // Added as a last resort fallback if even the GeoJSON file fails to load
  private addEmergencyFallbackOutline(chart: am5map.MapChart): void {
    if (!chart || chart.isDisposed()) return;
    
    try {
        console.log("Creating emergency fallback outline with simplified coordinates");
        // Extremely simplified outline with minimal points - only used if everything else fails
        const simplifiedOutline = [
            { longitude: 61.210817, latitude: 35.650072 },
            { longitude: 62.230651, latitude: 35.270664 },
            { longitude: 62.984662, latitude: 35.404041 },
            { longitude: 63.193538, latitude: 35.857166 },
            { longitude: 63.982896, latitude: 36.007957 },
            { longitude: 64.546479, latitude: 36.312073 },
            { longitude: 64.746105, latitude: 37.111818 },
            { longitude: 65.588948, latitude: 37.305217 },
            { longitude: 65.745631, latitude: 37.661164 },
            { longitude: 66.217385, latitude: 37.393790 },
            { longitude: 66.518607, latitude: 37.362784 },
            { longitude: 67.075782, latitude: 37.356144 },
            { longitude: 67.829999, latitude: 37.144994 },
            { longitude: 68.135562, latitude: 37.023115 },
            { longitude: 68.859446, latitude: 37.344336 },
            { longitude: 69.196273, latitude: 37.151144 },
            { longitude: 69.518785, latitude: 37.608997 },
            { longitude: 70.116578, latitude: 37.588223 },
            { longitude: 70.270574, latitude: 37.735165 },
            { longitude: 70.376304, latitude: 38.138396 },
            { longitude: 70.806821, latitude: 38.486282 },
            { longitude: 71.348131, latitude: 38.258905 },
            { longitude: 71.239404, latitude: 37.953265 },
            { longitude: 71.541918, latitude: 37.905774 },
            { longitude: 71.448693, latitude: 37.065645 },
            { longitude: 71.844638, latitude: 36.738171 },
            { longitude: 72.193041, latitude: 36.948288 },
            { longitude: 72.636889, latitude: 37.047558 },
            { longitude: 73.260056, latitude: 37.495257 },
            { longitude: 73.948696, latitude: 37.421566 },
            { longitude: 74.980002, latitude: 37.419990 },
            { longitude: 75.158028, latitude: 37.133031 },
            { longitude: 74.575893, latitude: 37.020841 },
            { longitude: 74.067552, latitude: 36.836176 },
            { longitude: 72.920025, latitude: 36.720007 },
            { longitude: 71.846292, latitude: 36.509942 },
            { longitude: 71.262348, latitude: 36.074388 },
            { longitude: 71.498768, latitude: 35.650563 },
            { longitude: 71.613076, latitude: 35.153203 },
            { longitude: 71.115019, latitude: 34.733126 },
            { longitude: 71.156773, latitude: 34.348911 },
            { longitude: 70.881803, latitude: 33.988856 },
            { longitude: 69.930543, latitude: 34.020432 },
            { longitude: 70.323594, latitude: 33.358533 },
            { longitude: 69.687147, latitude: 33.105499 },
            { longitude: 69.262522, latitude: 32.501944 },
            { longitude: 69.317764, latitude: 31.901412 },
            { longitude: 68.926677, latitude: 31.620189 },
            { longitude: 68.556932, latitude: 31.713310 },
            { longitude: 67.792689, latitude: 31.582293 },
            { longitude: 67.683394, latitude: 31.303154 },
            { longitude: 66.938891, latitude: 31.304911 },
            { longitude: 66.381458, latitude: 30.738899 },
            { longitude: 66.346473, latitude: 29.887943 },
            { longitude: 65.046862, latitude: 29.472181 },
            { longitude: 64.350419, latitude: 29.560031 },
            { longitude: 64.148002, latitude: 29.340819 },
            { longitude: 63.550261, latitude: 29.468331 },
            { longitude: 62.549857, latitude: 29.318572 },
            { longitude: 60.874248, latitude: 29.829239 },
            { longitude: 61.781222, latitude: 31.379506 },
            { longitude: 61.699314, latitude: 31.781771 },
            { longitude: 60.941945, latitude: 31.548075 },
            { longitude: 60.863655, latitude: 32.182320 },
            { longitude: 60.536078, latitude: 32.981269 },
            { longitude: 60.963700, latitude: 33.528832 },
            { longitude: 60.528429, latitude: 33.676446 },
            { longitude: 60.803193, latitude: 34.404102 },
            { longitude: 61.210817, latitude: 35.650072 }
        ];
        
        // Create a polygon from the outline points
        const polygonCoords = simplifiedOutline.map(point => [point.longitude, point.latitude]);
        
        const emergencySeries = chart.series.push(
            am5map.MapPolygonSeries.new(this.mapRoot, {
                // Add id to identify this as an emergency fallback layer
                id: "emergencyFallback"
            })
        );
        
        emergencySeries.data.setAll([{
            geometry: { type: "Polygon", coordinates: [polygonCoords] }
        }]);
        
        // Style the emergency polygon
        emergencySeries.mapPolygons.template.setAll({
            fill: am5.color(0xcccccc),
            stroke: am5.color(0x333333),
            strokeWidth: 1.5,
            fillOpacity: 0.8,
            tooltipText: "Afghanistan"
        });
        
        // Zoom to the fallback polygon
        chart.zoomToGeoPoint({ longitude: 67.5, latitude: 33.5 }, 2, true, 1000);
        
        console.log("Emergency fallback Afghanistan outline added successfully");
    } catch (e) {
        console.error("Failed to create even simplified outline:", e);
    }
  }

  // Method called from the template to filter interventions
  filterInterventions(): void {
    console.log('Filtering interventions');
    // Filter logic would go here
    this.filteredInterventions = [...this.interventions];
  }

  // Method called to load dashboard for a specific category
  closeModalAndLoadDashboard(categoryCode: string, categoryName: string): void {
    console.log(`Loading dashboard for category ${categoryCode}: ${categoryName}`);
    // Dashboard loading logic would go here
  }

  // Method called to view category details
  viewCategoryDetails(categoryCode: string, categoryName: string): void {
    console.log(`Viewing details for category ${categoryCode}: ${categoryName}`);
    // Category details logic would go here
  }

  // Method to get interventions by category
    getCategoryInterventions(categoryCode: string, categoryName: string): Intervention[] {
    // Filter interventions based on the category code or name
    return this.interventions.filter(intervention => 
      intervention.output?.code === categoryCode || 
      intervention.category === categoryName
    );
  }

  // Get the total count of regions
  getRegionCount(): number {
    return this.mapData.length;
  }
  
  // Clear all selections
  clearSelections(): void {
    this.selectedRegion = null;
    this.selectedCategory = null;
    this.selectedModalInterventions = [];
    this.modalTitle = '';
  }
  
  // Show region details in modal
  showRegionDetails(region: MapRegionData): void {
    this.selectedRegion = region;
    this.modalTitle = `${region.name} - Interventions`;
    this.selectedModalInterventions = this.interventions.filter(i => i.region === region.name);
    
    // Log districts for this region if available
    if (region.districts && region.districts.length > 0) {
      console.log(`Districts in ${region.name}:`);
      region.districts.forEach(district => {
        console.log(`- ${district.name} (${district.provinceName}): Population ~${district.population?.toLocaleString()}`);
      });
    }
  }
  
  // Get districts for a specific province
  getProvincesDistricts(provinceName: string): string[] {
    return this.provincesWithDistricts[provinceName] || [];
  }

  // Get all districts for a region
  getRegionDistricts(regionName: string): District[] {
    const region = this.mapData.find(r => r.name === regionName);
    return region?.districts || [];
  }

  // Get count of districts in a region
  getDistrictCount(regionName: string): number {
    return this.getRegionDistricts(regionName).length;
  }

  // Group districts by province
  getDistrictsByProvince(regionName: string): {province: string, districts: string[]}[] {
    const result: {province: string, districts: string[]}[] = [];
    const region = this.mapData.find(r => r.name === regionName);
    
    if (region?.districts) {
      // Group districts by province
      const provinceMap = new Map<string, string[]>();
      
      region.districts.forEach(district => {
        if (!provinceMap.has(district.provinceName)) {
          provinceMap.set(district.provinceName, []);
        }
        provinceMap.get(district.provinceName)?.push(district.name);
      });
      
      // Convert map to array
      provinceMap.forEach((districts, province) => {
        result.push({
          province,
          districts
        });
      });
    }
    
    return result;
  }
  
  // Get count of interventions for a specific output
  getOutputInterventionsCount(outputId: number): number {
    // Find interventions that match this output ID
    return this.interventions.filter(intervention => 
      intervention.output?.code === outputId.toString() || 
      intervention.output?.code === `${outputId}.1` ||
      intervention.output?.code === `${outputId}.2` ||
      intervention.output?.code === `${outputId}.3`
    ).length;
  }
  
  // Get percentage of interventions for a specific output
  getOutputInterventionsPercent(outputId: number): number {
    const count = this.getOutputInterventionsCount(outputId);
    const total = this.interventions.length;
    return total > 0 ? Math.round((count / total) * 100) : 0;
  }
  
  // Get cash distribution for a specific output
  getOutputCashDistribution(outputId: number): number {
    // Calculate cash distribution for the given output
    return this.interventions
      .filter(intervention => 
        intervention.output?.code === outputId.toString() || 
        intervention.output?.code === `${outputId}.1` ||
        intervention.output?.code === `${outputId}.2` ||
        intervention.output?.code === `${outputId}.3`
      )
      .reduce((total, intervention) => total + (intervention.budget || 0), 0);
  }
  
  // Get total cash distribution across all outputs
  getTotalCashDistribution(): number {
    return this.interventions.reduce((total, intervention) => total + (intervention.budget || 0), 0);
  }
  
  // Get intervention by index
  getInterventionByIndex(index: number): Intervention | null {
    return this.interventions.length > index ? this.interventions[index] : null;
  }
  
  // Helper method to change active intervention tab
  setActiveInterventionTab(index: number): void {
    this.activeInterventionTab = index;
  }
  
  // Reset dashboard to initial state
  resetDashboard(): void {
    this.clearSelections();
    this.selectedIntervention = null;
    this.projectStatusFilter = 'ongoing';
    this.filterInterventions();
  }
  
  // Open the interventions modal
  openInterventionsModal(): void {
    this.selectedModalInterventions = [...this.interventions];
    this.modalDetailView = false;
    this.interventionSearchTerm = '';
  }
  
  // Close the interventions modal
    closeInterventionsModal(): void {
    this.selectedModalInterventions = [];
    this.selectedModalIntervention = null;
    this.modalDetailView = false;
  }
  
  // Show detail view for a specific intervention
  selectIntervention(intervention: Intervention): void {
        this.selectedModalIntervention = intervention;
        this.modalDetailView = true;
    }
    
  // Return to the interventions list
    backToInterventionsList(): void {
        this.modalDetailView = false;
        this.selectedModalIntervention = null;
    }
    
  // Close the category details modal
  closeCategoryDetailsModal(): void {
    this.selectedCategory = null;
    this.categoryInterventions = [];
  }
  
  // Toggle map mode between bubble and heat
  toggleMapMode(): void {
    console.log('Toggle map mode');
    // For now, just show a message that this feature is coming soon
    alert("Map mode toggle feature coming soon!");
    
    // Remove any existing bubble points that might show "Herat: 15000"
    if (this.mapRoot && !this.mapRoot.isDisposed()) {
      const chart = this.mapRoot.container.children.getIndex(0) as am5map.MapChart;
      
      if (chart && !chart.isDisposed()) {
        // Remove any point series (which would contain the red dots)
        chart.series.each(function(series) {
          if (series instanceof am5map.MapPointSeries) {
            series.data.clear();
          }
        });
      }
    }
  }
  
  // Toggle fullscreen for the map
  toggleMapFullscreen(): void {
    console.log('Toggle map fullscreen');
    // Get map container and card
    const mapContainer = document.getElementById('mapChart');
    const mapCard = document.getElementById('mapChartCard');

    if (!mapContainer || !mapCard) return;

    // Toggle fullscreen class
    mapCard.classList.toggle('fullscreen-map');
    
    // If we're in fullscreen mode now
    if (mapCard.classList.contains('fullscreen-map')) {
      // Save the previous height
      mapContainer.setAttribute('data-previous-height', mapContainer.style.height);
      // Expand the map to fill most of the viewport
      mapContainer.style.height = 'calc(100vh - 150px)';
      // Update the button icon
      const buttonIcon = document.querySelector('#mapToggleFullscreen i');
      if (buttonIcon && buttonIcon instanceof HTMLElement) {
        buttonIcon.classList.remove('bi-fullscreen');
        buttonIcon.classList.add('bi-fullscreen-exit');
      }
    } else {
      // Restore previous height or set default
      const previousHeight = mapContainer.getAttribute('data-previous-height');
      mapContainer.style.height = previousHeight || '500px';
      // Update the button icon back
      const buttonIcon = document.querySelector('#mapToggleFullscreen i');
      if (buttonIcon && buttonIcon instanceof HTMLElement) {
        buttonIcon.classList.remove('bi-fullscreen-exit');
        buttonIcon.classList.add('bi-fullscreen');
      }
    }
    
    // Give the resize event time to trigger
    setTimeout(() => {
      // Manually trigger resize on the root to adjust the map properly
      if (this.mapRoot) {
        this.mapRoot.resize();
      }
    }, 100);
  }
  
  // Toggle data view between tabular and chart
  toggleDataView(): void {
    console.log('Toggle data view');
    // Implementation would change the data visualization
  }
  
  // Get abbreviation from an ID
  getAbbreviation(id: string): string {
    return id.split('-')[0];
  }
  
  // Get estimated completion date based on progress
  getEstimatedCompletion(intervention: Intervention): string {
    if (intervention.progress >= 100) {
      return 'Completed';
    }
    
    const startDate = new Date(intervention.startDate);
    const endDate = new Date(intervention.endDate);
    const totalDays = (endDate.getTime() - startDate.getTime()) / (1000 * 3600 * 24);
    const daysRemaining = totalDays * (1 - intervention.progress / 100);
    
    const today = new Date();
    const estimatedDate = new Date(today.getTime() + daysRemaining * 24 * 3600 * 1000);
    
    return estimatedDate.toLocaleDateString();
  }
  
  // Set active infrastructure tab
  setActiveInfrastructureTab(tabId: string): void {
    this.activeInfrastructureTab = tabId;
  }
  
  // Show all interventions data
    showAllInterventionsData(): void {
        this.activeInterventionTab = -1;
  }
  
  // Get total structures for the selected category
  getCategoryStructuresTotal(): number {
    return this.selectedCategory ? 850 : 0; // Placeholder value
  }
  
  // Get total households for the selected category
  getCategoryHouseholdsTotal(): number {
    return this.selectedCategory ? 3200 : 0; // Placeholder value
  }
  
  // Get total cash for the selected category
  getCategoryCashTotal(): number {
    if (!this.selectedCategory) return 0;
    
    return this.interventions
      .filter(i => i.category === this.selectedCategory?.name || i.output?.code === this.selectedCategory?.code)
      .reduce((total, i) => total + (i.budget || 0), 0);
  }
  
  // Get total activities for the selected category
  getCategoryActivitiesTotal(): number {
    if (!this.selectedCategory) return 0;
    
    return this.interventions
      .filter(i => i.category === this.selectedCategory?.name || i.output?.code === this.selectedCategory?.code)
      .reduce((total, i) => total + i.activities, 0);
    }

  // Initialize districts for provinces
  private initializeProvincesWithDistricts(): void {
    // Northeast provinces
    this.provincesWithDistricts["Kunduz"] = ["Aliabad", "Chahar Dara", "Imam Sahib", "Khan Abad", "Kunduz Center"];
    this.provincesWithDistricts["Takhar"] = ["Baharak", "Bangi", "Chah Ab", "Chal", "Taloqan"];
    this.provincesWithDistricts["Badakhshan"] = ["Baharak", "Faizabad", "Ishkashim", "Jurm", "Wakhan"];
    this.provincesWithDistricts["Baghlan"] = ["Andarab", "Baghlan-e-Jadid", "Doshi", "Pul-e-Khumri", "Tala wa Barfak"];
    
    // Northern provinces
    this.provincesWithDistricts["Jowzjan"] = ["Aqcha", "Darzab", "Khwaja Du Koh", "Mardyan", "Sheberghan"];
    this.provincesWithDistricts["Balkh"] = ["Balkh", "Charbolak", "Chimtal", "Mazar-i-Sharif", "Nahr-e-Shahi"];
    this.provincesWithDistricts["Samangan"] = ["Aybak", "Dara-i-Suf Bala", "Dara-i-Suf Payin", "Hazrat-e-Sultan", "Khuram Wa Sarbagh"];
    this.provincesWithDistricts["Sar-e Pol"] = ["Balkhab", "Kohistanat", "Sangcharak", "Sar-e-Pol", "Sozma Qala"];
    this.provincesWithDistricts["Faryab"] = ["Almar", "Andkhoy", "Khwaja Sabz Posh", "Maymana", "Qaysar"];
    
    // Central Highlands
    this.provincesWithDistricts["Bamyan"] = ["Bamyan", "Kahmard", "Panjab", "Sayghan", "Waras"];
    this.provincesWithDistricts["Ghor"] = ["Chaghcharan", "Lal Wa Sarjangal", "Pasaband", "Saghar", "Tulak"];
    this.provincesWithDistricts["Daykundi"] = ["Gizab", "Ishtarlay", "Kajran", "Nili", "Shahristan"];
    
    // Central Region
    this.provincesWithDistricts["Maidan Wardak"] = ["Chak", "Daymirdad", "Jalrez", "Maidan Shar", "Saydabad"];
    this.provincesWithDistricts["Parwan"] = ["Bagram", "Charikar", "Jabal Saraj", "Salang", "Shinwari"];
    this.provincesWithDistricts["Kapisa"] = ["Alasay", "Hesa Awal", "Kohistan", "Mahmud Raqi", "Nijrab"];
    this.provincesWithDistricts["Panjsher"] = ["Anaba", "Bazarak", "Dara", "Khenj", "Shotul"];
    this.provincesWithDistricts["Kabul"] = ["Bagrami", "Char Asyab", "Kabul City", "Paghman", "Surobi"];
    
    // Eastern Region
    this.provincesWithDistricts["Nuristan"] = ["Barg-e-Matal", "Kamdesh", "Mandol", "Nurgram", "Waygal"];
    this.provincesWithDistricts["Kunar"] = ["Asadabad", "Dara-I-Pech", "Khas Kunar", "Narang", "Shigal"];
    this.provincesWithDistricts["Laghman"] = ["Alingar", "Alishang", "Dawlat Shah", "Mehtarlam", "Qarghayi"];
    this.provincesWithDistricts["Nangarhar"] = ["Achin", "Behsud", "Jalalabad", "Khogyani", "Surkhrod"];
    
    // Southeastern Region
    this.provincesWithDistricts["Ghazni"] = ["Andar", "Gelan", "Ghazni City", "Jaghori", "Qarabagh"];
    this.provincesWithDistricts["Logar"] = ["Azra", "Baraki Barak", "Charkh", "Khoshi", "Pul-e-Alam"];
    this.provincesWithDistricts["Paktya"] = ["Ahmadabad", "Dand Patan", "Gardez", "Jaji", "Zurmat"];
    this.provincesWithDistricts["Khost"] = ["Bak", "Gurbuz", "Khost Matun", "Spera", "Tani"];
    this.provincesWithDistricts["Paktika"] = ["Barmal", "Gomal", "Orgun", "Sharana", "Zarghun Shahr"];
    
    // Southern Region
    this.provincesWithDistricts["Nimroz"] = ["Chahar Burjak", "Chakhansur", "Kang", "Khash Rod", "Zaranj"];
    this.provincesWithDistricts["Helmand"] = ["Garmsir", "Lashkar Gah", "Nad Ali", "Nawa-I-Barakzayi", "Sangin"];
    this.provincesWithDistricts["Kandahar"] = ["Arghandab", "Daman", "Kandahar City", "Panjwayi", "Spin Boldak"];
    this.provincesWithDistricts["Zabul"] = ["Arghandab", "Daychopan", "Qalat", "Shahjoy", "Shamulzayi"];
    this.provincesWithDistricts["Uruzgan"] = ["Chora", "Deh Rawud", "Khas Uruzgan", "Shahid-e-Hasas", "Tirin Kot"];
    
    // Western Region
    this.provincesWithDistricts["Herat"] = ["Adraskan", "Guzara", "Herat City", "Injil", "Karukh"];
    this.provincesWithDistricts["Badghis"] = ["Ab Kamari", "Ghormach", "Jawand", "Muqur", "Qala-i-Naw"];
    this.provincesWithDistricts["Farah"] = ["Anar Dara", "Bakwa", "Farah City", "Gulistan", "Lash-e-Juwayn"];
  }

  // Generate districts for a region based on its provinces
  private generateDistrictsForRegion(regionName: string, provinceNames: string[]): District[] {
    const districts: District[] = [];
    let districtId = 1;
    
    provinceNames.forEach((provinceName, provinceIdx) => {
      const provinceDistricts = this.provincesWithDistricts[provinceName] || [];
      
      provinceDistricts.forEach((districtName, idx) => {
        districts.push({
          id: districtId++,
          name: districtName,
          provinceId: provinceIdx + 1,
          provinceName: provinceName,
          population: 10000 + Math.floor(Math.random() * 90000),  // Random population between 10,000 and 100,000
          interventions: []
        });
      });
    });
    
    return districts;
  }

  // Display districts for a province on the map
  private displayDistrictsForProvince(
    chart: am5map.MapChart, 
    provinceName: string, 
    districts: string[],
    seriesContainer: am5map.MapPolygonSeries[]
  ): void {
    // Create approximate coordinates for districts based on province location
    const provinceCoordinates = this.getProvinceApproximateCoordinates(provinceName);
    if (!provinceCoordinates) {
      console.log(`Cannot display districts: coordinates for province ${provinceName} not found`);
      return;
    }
    
    // Show the reset button
    this.resetButton.set("visible", true);
    
    // Try to load district boundaries from GeoJSON
    this.loadDistrictBoundaries(chart, provinceName, districts, seriesContainer);
  }
  
  // New method to load district boundaries from GeoJSON
  private loadDistrictBoundaries(
    chart: am5map.MapChart,
    provinceName: string,
    districtNames: string[],
    seriesContainer: am5map.MapPolygonSeries[]
  ): void {
    // Attempt to load district boundaries from a GeoJSON file
    fetch('/assets/data/afghanistan-districts.geojson')
      .then(response => {
        if (!response.ok) {
          throw new Error(`Failed to load district data: ${response.status}`);
        }
        return response.json();
      })
      .then(data => {
        try {
          console.log("District GeoJSON data loaded successfully");
          
          if (!data.features || data.features.length === 0) {
            console.error("No district features found in GeoJSON data");
            this.createDistrictPoints(chart, provinceName, districtNames, seriesContainer);
            return;
          }
          
          // Filter features for the selected province
          const provinceDistricts = data.features.filter((feature: any) => {
            const properties = feature.properties || {};
            const featureProvince = properties.province || properties.PROVINCE || properties.ADM1_EN;
            return this.matchProvinceNames(featureProvince, provinceName);
          });
          
          console.log(`Found ${provinceDistricts.length} districts for province ${provinceName}`);
          
          if (provinceDistricts.length === 0) {
            // Fallback to points if no boundaries found
            this.createDistrictPoints(chart, provinceName, districtNames, seriesContainer);
            return;
          }
          
          // Create polygon series for the districts
          const districtPolygonSeries = chart.series.push(
            am5map.MapPolygonSeries.new(this.mapRoot, {
              geoJSON: { type: "FeatureCollection", features: provinceDistricts },
              stroke: am5.color(0xFFFFFF) // White borders
            })
          );
          
          seriesContainer.push(districtPolygonSeries);
          
          // Configure district polygon tooltips and settings
          districtPolygonSeries.mapPolygons.template.setAll({
            tooltipText: "{name}",
            templateField: "polygonSettings",
            interactive: true,
            fill: am5.color(0x2196f3),  // Blue fill
            fillOpacity: 0.7,
            strokeWidth: 0.5
          });
          
          // Configure district hover states
          districtPolygonSeries.mapPolygons.template.states.create("hover", {
            fillOpacity: 0.9,
            strokeWidth: 2
          });
          
          // Add click handlers to district polygons
          districtPolygonSeries.mapPolygons.template.events.on("click", (ev) => {
            const polygon = ev.target;
            const dataContext = polygon.dataItem.dataContext as any;
            const districtName = dataContext.name || dataContext.properties?.name || "Unknown District";
            
            this.showDistrictDetails(districtName, provinceName);
          });
          
          // Zoom to the province
          const provinceCoords = this.getProvinceApproximateCoordinates(provinceName);
          if (provinceCoords) {
            chart.zoomToGeoPoint(provinceCoords, 3, true, 1000);
          }
          
        } catch (error) {
          console.error("Error processing district GeoJSON:", error);
          // Fallback to points if processing fails
          this.createDistrictPoints(chart, provinceName, districtNames, seriesContainer);
        }
      })
      .catch(error => {
        console.error("Error loading district GeoJSON:", error);
        // Fallback to points if fetch fails
        this.createDistrictPoints(chart, provinceName, districtNames, seriesContainer);
      });
  }

  // Helper method to match province names with variations
  private matchProvinceNames(name1: string, name2: string): boolean {
    if (!name1 || !name2) return false;
    
    // Normalize names for comparison
    const normalize = (name: string) => name.toLowerCase()
      .normalize("NFD").replace(/[\u0300-\u036f]/g, "")
      .replace(/\s+province$/, "").trim();
    
    const normalized1 = normalize(name1);
    const normalized2 = normalize(name2);
    
    // Direct match
    if (normalized1 === normalized2) return true;
    
    // Check for partial matches
    return normalized1.includes(normalized2) || normalized2.includes(normalized1);
  }

  // Rename the existing point creation logic to serve as fallback
  private createDistrictPoints(
    chart: am5map.MapChart,
    provinceName: string,
    districts: string[],
    seriesContainer: am5map.MapPolygonSeries[]
  ): void {
    const provinceCoordinates = this.getProvinceApproximateCoordinates(provinceName);
    if (!provinceCoordinates) return;
    
    // Create a point series for the districts
    const districtSeries = chart.series.push(
      am5map.MapPointSeries.new(this.mapRoot, {})
    );
    
    seriesContainer.push(districtSeries as any);
    
    // Generate points for each district with slight offsets
    const districtPoints = districts.map((districtName, index) => {
      // Create a spiral pattern around the province center
      const angle = (index * 72) * (Math.PI / 180); // 72 degrees = 360/5 for even distribution
      const radius = 0.15 + (index * 0.03); // Increasing radius creates a spiral
      
      // Don't include values to prevent displaying "Herat: 15000" or similar
      return {
        longitude: provinceCoordinates.longitude + radius * Math.cos(angle),
        latitude: provinceCoordinates.latitude + radius * Math.sin(angle),
        name: districtName,
        province: provinceName
        // No 'value' property here to prevent "Name: Value" in tooltips
      };
    });
    
    // Configure district markers
    districtSeries.bullets.push((root, series, dataItem) => {
      const container = am5.Container.new(root, {});
      
      // Circle for the district
      const circle = container.children.push(
        am5.Circle.new(root, {
          radius: 5,
          fill: am5.color(0x2196f3),
          stroke: am5.color(0xFFFFFF),
          strokeWidth: 2,
          tooltipText: "{name}" // Only show name, not name: value
        })
      );
      
      // Add click event for district details
      container.events.on("click", (ev) => {
        const dataContext = dataItem.dataContext as any;
        this.showDistrictDetails(dataContext.name, dataContext.province);
      });
      
      return am5.Bullet.new(root, {
        sprite: container
      });
    });
    
    // Set the data for the district series
    districtSeries.data.setAll(districtPoints);
    
    // Zoom to the province
    chart.zoomToGeoPoint(
      provinceCoordinates,
      3, // Zoom level
      true, // animate
      1000 // duration
    );
  }

  // Get approximate coordinates for a province
  private getProvinceApproximateCoordinates(provinceName: string): { longitude: number, latitude: number } | null {
    // Define approximate coordinates for each province
    const provinceCoordinates: { [key: string]: { longitude: number, latitude: number } } = {
      // Northeast provinces
      "Kunduz": { longitude: 68.8, latitude: 36.7 },
      "Takhar": { longitude: 69.5, latitude: 36.7 },
      "Badakhshan": { longitude: 71.5, latitude: 37.1 },
      "Baghlan": { longitude: 68.7, latitude: 36.1 },
      
      // Northern provinces
      "Jowzjan": { longitude: 65.5, latitude: 36.9 },
      "Balkh": { longitude: 67.1, latitude: 36.7 },
      "Samangan": { longitude: 68.0, latitude: 36.3 },
      "Sar-e Pol": { longitude: 66.7, latitude: 36.2 },
      "Faryab": { longitude: 64.9, latitude: 36.1 },
      
      // Central Highlands
      "Bamyan": { longitude: 67.8, latitude: 34.8 },
      "Ghor": { longitude: 65.3, latitude: 34.1 },
      "Daykundi": { longitude: 66.2, latitude: 33.7 },
      
      // Central Region
      "Maidan Wardak": { longitude: 68.4, latitude: 34.4 },
      "Parwan": { longitude: 69.0, latitude: 35.0 },
      "Kapisa": { longitude: 69.6, latitude: 35.0 },
      "Panjsher": { longitude: 69.5, latitude: 35.3 },
      "Kabul": { longitude: 69.2, latitude: 34.5 },
      
      // Eastern Region
      "Nuristan": { longitude: 70.9, latitude: 35.3 },
      "Kunar": { longitude: 71.2, latitude: 34.8 },
      "Laghman": { longitude: 70.2, latitude: 34.6 },
      "Nangarhar": { longitude: 70.5, latitude: 34.2 },
      
      // Southeastern Region
      "Ghazni": { longitude: 68.4, latitude: 33.5 },
      "Logar": { longitude: 69.0, latitude: 33.9 },
      "Paktya": { longitude: 69.4, latitude: 33.7 },
      "Khost": { longitude: 69.9, latitude: 33.3 },
      "Paktika": { longitude: 69.3, latitude: 32.6 },
      
      // Southern Region
      "Nimroz": { longitude: 62.5, latitude: 31.0 },
      "Helmand": { longitude: 64.4, latitude: 31.6 },
      "Kandahar": { longitude: 65.7, latitude: 31.6 },
      "Zabul": { longitude: 67.2, latitude: 32.2 },
      "Uruzgan": { longitude: 66.0, latitude: 32.9 },
      
      // Western Region
      "Herat": { longitude: 62.2, latitude: 34.3 },
      "Badghis": { longitude: 63.8, latitude: 35.2 },
      "Farah": { longitude: 62.1, latitude: 32.5 }
    };
    
    return provinceCoordinates[provinceName] || null;
  }

  // Show district details when clicking on a district
  showDistrictDetails(districtName: string, provinceName: string): void {
    console.log(`Showing details for district ${districtName} in ${provinceName}`);
    
    this.selectedDistrict = { name: districtName, province: provinceName };
    
    // Generate random data for this district
    const districtData = {
      name: districtName,
      province: provinceName,
      population: 10000 + Math.floor(Math.random() * 90000),
      interventions: Math.floor(Math.random() * 5) + 1,
      projects: Math.floor(Math.random() * 10) + 1,
      budget: (Math.floor(Math.random() * 500) + 100) * 1000
    };
    
    // In a real application, you would fetch actual district data from your API
    
    // Show district information in a modal or panel
    // For this demo, we'll just log it to console, but in a real app
    // you would display this in your UI
    console.log('District Details:', districtData);
    
    // Sample code for showing a modal - you would implement this in your template
    /*
      Modal implementation would go here - for example:
      this.modalTitle = `${districtName}, ${provinceName} - District Details`;
      this.selectedModalContent = districtData;
      this.isDistrictModalVisible = true;
    */
  }

  // Clear district selection
  clearDistrictSelection(): void {
    this.selectedDistrict = null;
    }
} 