import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, HostBinding, OnDestroy, OnInit, ViewChild, AfterViewInit } from '@angular/core';
import { CellClickedEvent } from 'ag-grid-community';
import { Subscription, forkJoin } from 'rxjs';
import { FilterDropdownList } from '../../../../shared/components/filter-dropdown/filter-ddl.control';
import { ActivityStatus, Region } from '../../../../shared/enums';
import { MessageService } from '../../../../shared/services/message.service';
import { SharedService } from '../../../../shared/services/shared.service';
import { compareSort, getMonthName } from '../../../../shared/utilities';
import { Category } from '../../../admin/models/category.model';
import { Project } from '../../../admin/models/project.model';
import { AuthService } from '../../../auth';
import { ProfileInfo } from '../../../data-entry/models/profile.model';
import { ProfileService } from '../../../data-entry/services/profile.service';
import { IDataFilter, IDataIntervention } from '../../models/data.model';
import { ViewDataService } from '../../services/view-data.service';
import { ReadOnlyGridComponent } from './grid/readonly-grid.component';


@Component({
    selector: 'aims-view-data',
    templateUrl: './view-data.component.html',
    styleUrls: ['./view-data.component.scss']
})
export class ViewDataComponent implements OnInit, OnDestroy, AfterViewInit {
    @HostBinding('attr.data-qs-app-toolbar-fixed') pgToolbar = 'true';
    @HostBinding('attr.data-qs-app-header-fixed') pgHeader = 'true';
    @HostBinding('attr.data-qs-app-sidebar-push-toolbar') pushToolbar = 'true';
    //appHeader: HTMLElement;

    // **** Profiles ----------------------------------------------------------
    working: boolean = false; gridWorking: boolean = false;
    isApprover: boolean = false; isGlobalUser: boolean = false;
    downloading: boolean = false;
    userOrgId: number = 0;

    projGroups: any[] = [];
    projects: Project[] = [];
    outputs = [];
    categories: Category[] = [];
    profiles: any = [];
    orgs: any[] = [];
    submitStatus = [{ id: 0, name: 'Draft' }, { id: 1, name: 'Submitted' }, { id: 2, name: 'Approved' }];
    regions = [];
    qtrs = [{ id: 1, name: 'Q-1' }, { id: 2, name: 'Q-2' }, { id: 3, name: 'Q-3' }, { id: 4, name: 'Q-4' }];
    years = [];

    interventions: ProfileInfo[] = [];
    allInterventions: ProfileInfo[] = [];
    selTabId: number = 0;

    filters: IDataFilter = {
        isTarget: false,
        approvalMode: false,
        dataStatus: [1,2]
    };
    filtered: string[] = [''];
    strPeriod: string[] = ['', ''];

    // Add property to control filtering of activities without meaningful progress data
    hideNAPeriods: boolean = true; // Default to hiding activities with N/A periods or empty progress

    @ViewChild('output')
    private outputFilterCtrl: FilterDropdownList;
    @ViewChild('category')
    private catFilterCtrl: FilterDropdownList;
    @ViewChild('profile')
    private profFilterCtrl: FilterDropdownList;

    @ViewChild('pgroups')
    private projGroupsFilterCtrl: FilterDropdownList;
    @ViewChild('project')
    private projFilterCtrl: FilterDropdownList;
    @ViewChild('partner')
    private partnerFilterCtrl: FilterDropdownList;
    @ViewChild('status')
    private dStatusFilterCtrl: FilterDropdownList;
    @ViewChild('region')
    private regionFilterCtrl: FilterDropdownList;

    // **** Grid --------------------------------------------------------------
    @ViewChild(ReadOnlyGridComponent, { static: true })
    private gridComponent: ReadOnlyGridComponent;

    private subscriptions: Subscription[] = [];
    constructor(
        private authService: AuthService,
        private profService: ProfileService,
        private dataService: ViewDataService,
        private sharedService: SharedService,
        private messageService: MessageService,
        private cdr: ChangeDetectorRef
    ) {
        const roles = this.authService.currentUserValue.roles;
        this.isGlobalUser = roles.includes('Admin') || roles.includes('Approver') || roles.includes('LocalApprover') || roles.includes('Viewer');
        this.isApprover = roles.includes('Admin') || roles.includes('Approver') || roles.includes('LocalApprover');
        const isLocalUser = roles.includes('DataEntry') || roles.includes('LocalViewer') || roles.includes('LocalApprover');

        this.filters.dataStatus = [1, 2];
        //this.appHeader = document.querySelector('app-header');
        Object.values(Region)
            .forEach((v, i) => {
                if ((typeof v === 'string' && v !== 'National'))
                    this.regions.push({ id: i, name: v });
            });
        this.regions.sort((x, y) => x.name < y.name ? -1 : 1);

        const currYear = new Date().getFullYear();
        for (let i = 2020; i <= currYear+5; i++)
            this.years.push({ id: i, name: String(i) });

        // Initialize userOrgId
        if (this.authService.currentUserValue.org) {
            this.userOrgId = this.authService.currentUserValue.org.id;
        }
    }

    ngOnInit(): void {
        this.gridComponent.isGlobalUser = this.isGlobalUser;
        this.gridComponent.isApprover = this.isApprover;
        // this.gridComponent.selectedVals.org = this.authService.currentUserValue.org.shortName;
        this.getDropdownItems();
    }

    // Initialize the project filter dropdown after view init
    ngAfterViewInit(): void {
        // Give the DOM time to initialize
        setTimeout(() => {
            this.initializeProjectFilter();
        }, 500);
    }

    // Add this new method to handle project filter initialization
    private initializeProjectFilter(): void {
        // Make sure project filter is properly initialized
        if (this.projFilterCtrl) {
            // Ensure we have projects
            if (!this.projects || this.projects.length === 0) {
                return;
            }
            
            // Force a refresh of the options and items
            // Create fresh copies to avoid reference issues
            this.projFilterCtrl.options = [...this.projects];
            this.projFilterCtrl.items = [...this.projects];
            
            // Set the default filter options
            this.projFilterCtrl.showAll = true;
            this.projFilterCtrl.showSearch = true;
            
            // For global users only, also initialize project group filter
            if (this.isGlobalUser && this.projGroupsFilterCtrl) {
                console.log('Initializing project group filter with', this.projGroups.length, 'groups');
                // Create fresh copies to avoid reference issues
                this.projGroupsFilterCtrl.options = [...this.projGroups];
                this.projGroupsFilterCtrl.items = [...this.projGroups];
                this.projGroupsFilterCtrl.showAll = false;
                this.projGroupsFilterCtrl.showSearch = true;
            }
            
            // Force change detection
            this.cdr.detectChanges();
        }
    }

       // Add this new method to handle partner filter initialization
       private initializePartnerFilter(): void {
        // Only initialize for global users (since partner filter is hidden for local users)
        if (this.isGlobalUser && this.partnerFilterCtrl) {
            // Ensure we have organizations
            if (!this.orgs || this.orgs.length === 0) {
                // console.log('No organizations available to display in partner filter');
                return;
            }
            
            // console.log('Initializing partner filter with', this.orgs.length, 'organizations');
            
            // Force a refresh of the options and items
            // Create fresh copies to avoid reference issues
            this.partnerFilterCtrl.options = [...this.orgs];
            this.partnerFilterCtrl.items = [...this.orgs];
            
            // Set the default filter options
            this.partnerFilterCtrl.showAll = true;
            this.partnerFilterCtrl.showSearch = true;
            
            // Force change detection
            this.cdr.detectChanges();
        }
    }

    // get categories and profiles
    private getDropdownItems(): void {
        this.working = true;

        // For LDE users, use the filtered API endpoint
        const roles = this.authService.currentUserValue.roles;
        const isLDEUser = roles.includes('DataEntry') || 
                          roles.includes('LocalViewer') || 
                          roles.includes('LocalApprover');
        
        // Choose the appropriate API call based on user role
        let projectsApi = this.sharedService.getProjectsWithGrouping();
        if (!this.isGlobalUser && isLDEUser) {
            // Use filtered API which automatically filters by user org ID on server side
            console.log('Using filtered API for LDE user');
            projectsApi = this.sharedService.getProjectsFiltered();
        }

        let endPoints: any = {
            projs: projectsApi,
            cats: this.sharedService.getCategories(),
            profs: this.profService.getProfiles(),
            orgs: this.sharedService.getOrgsList()
        };

        this.subscriptions.push(
            forkJoin(endPoints).subscribe({
                next: ({ projs, cats, profs, orgs }) => {
                    // Debug log to check the data received
                    // console.log('Projects from API:', projs);
                    // console.log('Current user org ID:', this.userOrgId);
                    
                    // For GLOBAL users, show all projects and project groups
                    if (this.isGlobalUser) {
                        this.projects = projs;

                        // Extract all project groups
                        this.projGroups = [];
                        projs.forEach(proj => {
                            if (proj.grouping && proj.grouping.trim() !== '') {
                                const existingGroup = this.projGroups.find(pg => pg.id === proj.grouping);
                                if (!existingGroup) {
                                    this.projGroups.push({ id: proj.grouping, name: proj.grouping });
                                }
                            }
                        });
                        
                        // Sort project groups alphabetically
                        this.projGroups.sort((x, y) => compareSort(x.name, y.name));
                    } else {
                        // For LDE users, use the filtered projects - project group filter is hidden in UI
                        // Check if API returned IProjectIntervention[] or Project[]
                        if (Array.isArray(projs) && projs.length > 0 && 'organizations' in projs[0]) {
                            // Convert IProjectIntervention[] to Project[]
                            console.log('Converting filtered projects to standard format');
                            this.projects = projs.map(p => {
                                return {
                                    id: p.id,
                                    name: p.name,
                                    abbreviation: p.abbreviation,
                                    grouping: p.grouping || '',
                                    partners: p.organizations?.map(orgId => ({
                                        organizationId: orgId,
                                        projectId: p.id
                                    }))
                                };
                            });
                        } else {
                            // Use projects as-is from API
                            this.projects = projs;
                        }
                        
                        console.log(`LDE user projects: ${this.projects.length}`);
                        
                        // Project groups are not needed for LDE users (hidden in UI)
                        this.projGroups = [];
                    }

                     // Transform organizations to match the expected structure for filter dropdown
                     this.orgs = [];
                     orgs.forEach(org => {
                         this.orgs.push({
                             id: org.id,
                             name: org.shortName
                         });
                     });
                     this.orgs.sort((x, y) => compareSort(x.name, y.name));
                     console.log('Transformed organizations for dropdown:', this.orgs);
                     
                    this.allInterventions = [...profs];

                    this.categories = cats;
                    this.outputs = [];
                    this.categories.forEach(cat => {
                        cat.name = cat.code + ' ' + cat.name;

                        const outputExists = this.outputs.findIndex(o => o.name === cat.output) > -1;
                        if (!outputExists) {
                            this.outputs.push({
                                id: cat.output,
                                name: cat.output,
                                categories: this.categories.filter(c => c.output === cat.output)
                            });
                        }
                    });

                    // Initialize for both global and local users immediately
                    setTimeout(() => {
                        this.initializeProjectFilter();
                        this.initializePartnerFilter();
                    }, 100);
                },
                error: (err) => {
                    this.working = false;
                },
                complete: () => {
                    this.createProfileFilters();
                    this.cdr.detectChanges();
                    this.working = false;
                    this.getData();
                }
            })
        );
    }

    private createProfileFilters(): void {
        this.profiles = [];
        this.allInterventions.forEach(prof => {
            this.profiles.push({
                id: prof.id,
                catId: prof.categoryId,
                name: prof.name
            });
        });

        // sort it alphabetically
        this.profiles.sort((x, y) => x.name > y.name ? 1 : -1);
    }

    // initial data when filter is applied
    private dataResult: IDataIntervention;
    private getData(): void {
        this.gridWorking = true;
        
        // Create a deep copy of the filters to ensure we're sending a fresh object
        const filtersCopy = JSON.parse(JSON.stringify(this.filters));

        this.subscriptions.push(
            this.dataService.getData(filtersCopy).subscribe({
                next: (result) => {
                    this.dataResult = result;
                },
                error: (err) => {
                    this.gridWorking = false;
                    this.messageService.error('Failed to fetch data. Please try again.', 'Data Fetch Error');
                },
                complete: () => {
                    if (!this.dataResult || !this.dataResult.interventions || this.dataResult.interventions.length === 0) {
                        this.interventions = [];
                        this.gridWorking = false;
                        return;
                    }
                    
                    // Get interventions that match IDs in the data result
                    let filteredInterventions = this.allInterventions.filter(i => 
                        this.dataResult.interventions.includes(i.id)
                    );
                    
                    // For local users (non-global), filter interventions to only show those
                    // associated with the user's organization
                    if (!this.isGlobalUser) {
                        const currentUser = this.authService.currentUserValue;
                        if (currentUser.org) {
                            // Filter interventions based on the data associated with the user's organization
                            filteredInterventions = filteredInterventions.filter(intervention => {
                                // Check if there's any data entry associated with this intervention 
                                // and the user's organization
                                const orgId = currentUser.org.id;
                                const data = this.dataResult.data;
                                
                                // Check if there's any data entry that matches both the intervention ID and org ID
                                return data.some(item => {
                                    // For progress data, we can check directly
                                    if ('orgId' in item) {
                                        return item.profId === intervention.id && item.orgId === orgId;
                                    }
                                    // For target data, check the partner field
                                    return item.partner && (
                                        item.partner.includes(currentUser.org.shortName) || 
                                        item.partner.includes(currentUser.org.fullName)
                                    );
                                });
                            });
                        }
                    }
                    
                    this.interventions = filteredInterventions;
                    this.interventions.sort((x, y) => compareSort(x.category.code, y.category.code) || compareSort(x.name, y.name));

                    // Filter out data with N/A periods (applies to progress data only)
                    if (this.dataResult.data && !this.filters.isTarget) {
                        this.dataResult.data = this.filterOutNAPeriods(this.dataResult.data);
                    }

                    this.gridSortedBy = -1;
                    //this.gridGroupedBy = -1;
                    this.gridComponent.gridApi.setFilterModel(null);
                    //this.gridComponent.gridApi.setRowGroupPanelShow('never');
                    if (this.isPivotEnabled)
                        this.enableGridGroupAndPivot();

                    this.cdr.detectChanges();

                    this.createProfileTabs();
                    this.gridWorking = false;
                }
            })
        );
    }

    /**
     * Filter out data rows that have N/A periods (for progress data only) and
     * activities without meaningful cumulative progress data in the filtered time series.
     * This method prioritizes activities with meaningful data but ensures each intervention
     * has at least one activity to prevent empty interventions.
     * 
     * @param data The data array to filter
     * @returns Filtered data array with balanced filtering approach
     */
    private filterOutNAPeriods(data: any[]): any[] {
        // If hideNAPeriods is disabled, return all data
        if (!this.hideNAPeriods) {
            return data;
        }
        
        // Group data by intervention (profId) to ensure each intervention has at least one activity
        const dataByIntervention = data.reduce((groups, item) => {
            const profId = item.profId || 'unknown';
            if (!groups[profId]) {
                groups[profId] = [];
            }
            groups[profId].push(item);
            return groups;
        }, {});
        
        const filteredData = [];
        
        // Process each intervention separately
        Object.keys(dataByIntervention).forEach(profId => {
            const interventionData = dataByIntervention[profId];
            const validActivities = [];
            
            // For progress data, apply validation for time series filtering
            if (interventionData.length > 0 && 'asOf' in interventionData[0]) {
                
                // First pass: find activities that meet strict criteria
                for (const item of interventionData) {
                    // Check if this is an ongoing activity - only apply strict filtering to ongoing activities
                    const isOngoingActivity = item.status === 0; // ActivityStatus.Ongoing = 0
                    
                    // STEP 1: Basic validation - must have valid asOf and progressId
                    const hasValidAsOf = item.asOf && 
                                       item.asOf !== 'N/A' && 
                                       item.asOf.trim() !== '';
                    
                    const hasValidProgressId = item.progressId && item.progressId > 0;
                    
                    if (hasValidAsOf && hasValidProgressId) {
                        if (isOngoingActivity) {
                            // STEP 2: For ongoing activities with time series filtering, validate meaningful progress data
                            if (this.filters.period > 0 || this.filters.periodEnd > 0) {
                                const hasProgressValues = item.colVals && 
                                                        Object.keys(item.colVals).some(key => {
                                                            const value = item.colVals[key];
                                                            
                                                            if (value === null || value === undefined || value === '') {
                                                                return false;
                                                            }
                                                            
                                                            const stringValue = value.toString().trim();
                                                            
                                                            // Filter out common empty/default values
                                                            if (stringValue === '' || 
                                                                stringValue === '0' || 
                                                                stringValue === '0.0' || 
                                                                stringValue === '0.00' ||
                                                                stringValue === 'N/A' ||
                                                                stringValue === '-' ||
                                                                stringValue === 'null' ||
                                                                stringValue === 'undefined') {
                                                                return false;
                                                            }
                                                            
                                                            // For numeric values, ensure they're greater than 0
                                                            const numValue = parseFloat(stringValue.replace(/,/g, ''));
                                                            if (!isNaN(numValue)) {
                                                                return numValue > 0;
                                                            }
                                                            
                                                            // For non-numeric values, they should be meaningful strings
                                                            return stringValue.length > 0;
                                                        });
                                
                                if (hasProgressValues) {
                                    validActivities.push(item);
                                }
                            } else {
                                // No time series filtering for ongoing activities, include if basic criteria are met
                                validActivities.push(item);
                            }
                        } else {
                            // For non-ongoing activities (Completed, Archived, Cancelled), apply more lenient filtering
                            // Just check that it's not completely empty
                            validActivities.push(item);
                        }
                    }
                }
                
                // If no activities passed strict validation, include the most recent activity
                // to ensure the intervention doesn't disappear completely
                if (validActivities.length === 0 && interventionData.length > 0) {
                    // Find the most recent activity (prefer those with valid asOf dates)
                    const activitiesWithDates = interventionData.filter(item => 
                        item.asOf && item.asOf !== 'N/A' && item.asOf.trim() !== ''
                    );
                    
                    let mostRecentActivity;
                    if (activitiesWithDates.length > 0) {
                        // Sort by asOf date (assuming format like "dd MMM yyyy")
                        mostRecentActivity = activitiesWithDates.sort((a, b) => {
                            // Simple date comparison - in a real scenario you'd want proper date parsing
                            return b.asOf.localeCompare(a.asOf);
                        })[0];
                    } else {
                        // Fallback to first activity if no valid dates
                        mostRecentActivity = interventionData[0];
                    }
                    
                    validActivities.push(mostRecentActivity);
                }
                
                filteredData.push(...validActivities);
            } else {
                // For non-progress data (like target data), keep all items
                filteredData.push(...interventionData);
            }
        });
        
        return filteredData;
    }

    onFilterChange(ctrl): void {
        // Check if event structure has changed to use selectedValues instead of selVals
        const selectedValues = ctrl.selectedValues || ctrl.selVals || [];

        if (ctrl.id === 'projGroups') {
            // Only relevant for global users now, since project group filter is hidden for LDE
            this.projFilterCtrl.clearSelection();

            if (!selectedValues.length) {
                this.projFilterCtrl.options = [...this.projects];
            } else {
                this.projFilterCtrl.options = this.projects.filter(p => 
                    p.grouping && selectedValues.includes(p.grouping)
                );
                this.projFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));

                // Auto-select all projects in the selected groups
                this.projFilterCtrl.setSelectedValues(this.projFilterCtrl.options.map(o => o.id));
            }
            
            this.projFilterCtrl.items = [...this.projFilterCtrl.options];
        } else if (ctrl.id === 'outputs') {
            this.catFilterCtrl.clearSelection();
            this.profFilterCtrl.clearSelection();

            if (!selectedValues.length) {
                this.catFilterCtrl.options = [...this.categories];
                this.profFilterCtrl.options = [...this.profiles];
            } else {
                this.catFilterCtrl.options = this.categories.filter(c => selectedValues.includes(c.output));
                this.catFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));
                this.profFilterCtrl.options = this.profiles.filter(p => this.catFilterCtrl.options.findIndex(c => c.id === p.catId) > -1);
                this.profFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));
            }

            this.catFilterCtrl.items = [...this.catFilterCtrl.options];
            this.profFilterCtrl.items = [...this.profFilterCtrl.options];
        } else if (ctrl.id === 'catIds') {
            this.profFilterCtrl.clearSelection();

            if (!selectedValues.length) {
                this.profFilterCtrl.options = [...this.profiles];
            } else {
                this.profFilterCtrl.options = this.profiles.filter(p => selectedValues.includes(p.catId));
                this.profFilterCtrl.options.sort((x, y) => (x.name > y.name) ? 1 : ((x.name < y.name) ? -1 : 0));
            }
            this.profFilterCtrl.items = [...this.profFilterCtrl.options];
        }

        if(ctrl.id === 'period' || ctrl.id === 'year')
            this.filters[ctrl.id] = selectedValues[0];
        else
            this.filters[ctrl.id] = selectedValues;
    }

    onDataTypeChange(isTarget?: boolean): void {
        this.filters.isTarget = isTarget || false;
        this.filters.period = 0;
        this.filters.periodEnd = 0;
        this.filters.year = 0;
        this.filters.yearEnd = 0;

        // Reset hideNAPeriods to true when switching to Progress data
        if (!this.filters.isTarget) {
            this.hideNAPeriods = true;
        }

        // reapply filter
        this.gridComponent.selectedVals.isTarget = isTarget;
        this.getData();
    }

    onApprovalMode(): void {
        this.filters.approvalMode = !this.filters.approvalMode;
        this.gridComponent.approvalMode = this.filters.approvalMode;

        if (this.filters.approvalMode) {
            const pFrom = document.querySelector('#periodFrom') as HTMLInputElement;
            if (pFrom)
                pFrom.value = '';
        }

        // reapply filter
        this.getData();
    }

    onToggleNAPeriods(): void {
        this.hideNAPeriods = !this.hideNAPeriods;
        
        // Refresh the data to apply/remove the N/A filter
        this.getData();
    }

    onPeriodChange(e: any): void {
        const target = e.target as HTMLInputElement;
        const date = target.value?.split('-');
        
        if (target.id === 'periodFrom') {
            if (!date || date.length < 2) {
                this.filters.period = 0;
                this.filters.year = 0;
                this.strPeriod[0] = '';
            } else {
                this.filters.period = +date[1];
                this.filters.year = +date[0];
                this.strPeriod[0] = `01 ${ getMonthName(this.filters.period) } ${ this.filters.year }`;
            }
        } else {
            if (!date || date.length < 2) {
                this.filters.periodEnd = 0;
                this.filters.yearEnd = 0;
                this.strPeriod[1] = '';
            } else {
                this.filters.periodEnd = +date[1];
                this.filters.yearEnd = +date[0];
                this.strPeriod[1] = new Date(this.filters.yearEnd, this.filters.periodEnd, 0).getDate() +
                    ` ${getMonthName(this.filters.periodEnd)} ${this.filters.yearEnd}`;
            }
        }
    }

    resetFilters(): void {
        // Reset all filter controls
        if (this.isGlobalUser && this.projGroupsFilterCtrl) {
            this.projGroupsFilterCtrl.clearSelection();
        }
        
        if (this.projFilterCtrl) {
            this.projFilterCtrl.clearSelection();
            this.projFilterCtrl.options = [...this.projects];
            this.projFilterCtrl.items = [...this.projFilterCtrl.options];
        }
        
        if (this.outputFilterCtrl) {
            this.outputFilterCtrl.clearSelection();
        }
        
        if (this.catFilterCtrl) {
            this.catFilterCtrl.clearSelection();
        }
        
        if (this.profFilterCtrl) {
            this.profFilterCtrl.clearSelection();
        }
        
        if (this.isGlobalUser && this.partnerFilterCtrl) {
            this.partnerFilterCtrl.clearSelection();
        }
        
        if (this.dStatusFilterCtrl) {
            this.dStatusFilterCtrl.setSelectedValues([1, 2]);
        }
        
        if (this.regionFilterCtrl) {
            this.regionFilterCtrl.clearSelection();
        }

        // Reset filters object
        this.filters = {
            isTarget: this.filters.isTarget,
            approvalMode: this.filters.approvalMode,
            dataStatus: [1, 2]
        };
        
        // Reset hideNAPeriods to default (true)
        this.hideNAPeriods = true;
        
        this.filtered = [''];

        // Clear period dates
        const periodFrom = document.getElementById('periodFrom') as HTMLInputElement;
        const periodTo = document.getElementById('periodTo') as HTMLInputElement;
        if (periodFrom) periodFrom.value = '';
        if (periodTo) periodTo.value = '';
        
        // Reset target-specific filters
        if (this.filters.isTarget) {
            if (this.projFilterCtrl) {
                this.projFilterCtrl.clearSelection();
            }
        }

        this.getData();
    }

    onFilterData(): void {
        if (!this.filters.approvalMode) {
            if (this.filters.period > 0 && this.filters.year > 0 &&
                this.filters.periodEnd > 0 && this.filters.yearEnd > 0) {
                const date = new Date(this.filters.year, this.filters.period - 1, 1);
                const dateEnd = new Date(this.filters.yearEnd, this.filters.periodEnd - 1, 1);

                if (dateEnd < date) {
                    this.messageService.error('<span class="fw-semibold">Period (To)</span> should be greater ' +
                        'than <span class="fw-semibold">Period (From)</span>.', 'Invalid period', { enableHtml: true });
                    return;
                }
            }
        }

        // reapply filter
        this.getData();

        // create filters
        this.filtered = [''];
        if (this.filters.projGroups?.length)
            this.filtered.push('Project Group');
        if (this.filters.projIds?.length)
            this.filtered.push('Project');
        if (this.filters.profIds?.length)
            this.filtered.push('Intervention');
        else if (this.filters.catIds?.length)
            this.filtered.push('Category');
        else if (this.filters.outputs?.length)
            this.filtered.push('Output');
        if (this.filters.orgIds?.length)
            this.filtered.push('Partner');
        if (this.filters.regions?.length)
            this.filtered.push('Region');
        if (this.filters.period > 0 || this.filters.periodEnd > 0)
            this.filtered.push('Period');

        this.filtered[0] = this.filtered.filter((f, i) => i > 0).join(', ');
    }

    private createProfileTabs(selProfId?: number): void {
        let selProfile;

        if (!selProfId)
            selProfId = this.dataResult.interventions[0];

        selProfile = this.interventions.find(i => i.id === selProfId);

        if (selProfile) {
            this.selTabId = selProfile.id;
            this.gridComponent.selectedVals.profId = selProfile.id;
            this.gridComponent.selectedVals.prof = selProfile.abbreviation;
    
            if (!this.filters.isTarget) {
                this.gridComponent.dynamicColumns = selProfile.variables
                    .filter(v => [2, 4, 5].includes(v.type)); // ColumnVarType.Progress, Info, Static
            } else {
                this.gridComponent.dynamicColumns = selProfile.variables
                    .filter(v => [0, 1, 3].includes(v.type)); // ColumnVarType.Target, Info, Static
            }

            this.gridComponent.renameAsOfCol = false;
            if(this.filters.period && this.filters.periodEnd)
                this.gridComponent.renameAsOfCol = true;
            
            this.gridComponent.initGrid(this.filters.isTarget);
            this.gridComponent.refreshGridRows(this.dataResult.data);
        }
    }

    getInterventionData(profId: number): void {
        this.gridComponent.working = true;

        this.subscriptions.push(
            this.dataService.getInterventionData(profId, this.dataResult.filters).subscribe({
            next: (res) => {
                this.dataResult.data = [...res];
            },
            error: (err) => {
                this.gridComponent.working = false;
            },
            complete: () => {
                // reset grid filters, sorting, grouping
                this.gridSortedBy = -1;
                //this.gridGroupedBy = -1;
                if (this.isPivotEnabled)
                    this.enableGridGroupAndPivot();

                this.gridComponent.gridApi.setFilterModel(null);
                this.gridComponent.gridApi.setRowGroupPanelShow('never');

                this.createProfileTabs(profId);
                this.gridComponent.working = false;
            }
        }));
    }

    gridFilteredBy: string[] = ['-'];
    gridFiltered(col: string): void {
        if (col.startsWith('-')) {
            col = col.replace('-', '');
            this.gridFilteredBy = this.gridFilteredBy.filter(c => c !== col);
        } else if (!this.gridFilteredBy.includes(col))
            this.gridFilteredBy.push(col);
        
        this.gridFilteredBy[0] = this.gridFilteredBy
            .slice(1, this.gridFilteredBy.length).join(', ');
    }

    gridSortedBy: number = -1;
    enableGridSort(): void {
        let isSortable = false;
        if (this.gridSortedBy === -1) {
            isSortable = true;
            this.gridSortedBy = 0;
        } else {
            isSortable = false;
            this.gridSortedBy = -1;
        }
        this.gridComponent.gridColumnApi.getColumns().forEach(col => {
            const colId = col.getColId();
            if (!colId.startsWith('new') && !colId.startsWith('cols-hidden')) {
                let colDef = col.getColDef();
                colDef.sortable = isSortable;
                col.setColDef(colDef, col.getColDef());
            }
        });

        this.gridComponent.gridApi.refreshHeader();
        this.gridComponent.gridColumnApi.autoSizeAllColumns();
    }
    /*
    gridGroupedBy: number = -1;
    enableGridGroup(): void {
        let enableRowGroup = false;
        if (this.gridGroupedBy === -1) {
            enableRowGroup = true;
            this.gridComponent.gridApi.setRowGroupPanelShow('always');
            this.gridGroupedBy = 0;
        } else {
            enableRowGroup = false;
            this.gridComponent.gridApi.setRowGroupPanelShow('never');
            this.gridComponent.gridColumnApi.getRowGroupColumns().forEach(col => {
                this.gridComponent.gridColumnApi.removeRowGroupColumn(col.getColId());
            });
            this.gridGroupedBy = -1;
        }
        this.gridComponent.gridColumnApi.getColumns().forEach(col => {
            const colId = col.getColId();
            if (!colId.startsWith('cols-hidden')) {
                let colDef = col.getColDef();
                colDef.enableRowGroup = enableRowGroup;
                col.setColDef(colDef, col.getColDef());
            }
        });
    }
    */
    isPivotEnabled: boolean = false;
    enableGridGroupAndPivot(): void {
        if (this.isPivotEnabled) {
            this.gridComponent.gridApi.setPivotMode(false);
            this.gridComponent.gridApi.setSideBar(false);

            this.gridComponent.gridColumnApi.getRowGroupColumns().forEach(col => {
                this.gridComponent.gridColumnApi.removeRowGroupColumn(col.getColId());
            });

            this.isPivotEnabled = false;
            return;
        }

        //this.gridComponent.gridApi.setPivotMode(true);
        this.gridComponent.gridApi.setSideBar(['columns', 'filters']);
        this.isPivotEnabled = true;
    }

    private resetSortAndGroup(): void {
        if (this.gridSortedBy > -1) {
            this.gridComponent.defaultColDefs.sortable = false;
            this.gridSortedBy = -1;
        }

        if (this.isPivotEnabled)
            this.enableGridGroupAndPivot();
        /*if (this.gridGroupedBy > -1) {
            this.gridComponent.defaultColDefs.enableRowGroup = false;
            this.gridComponent.gridApi.setRowGroupPanelShow('never');
            this.gridComponent.gridColumnApi.getRowGroupColumns().forEach(col => {      // remove all group cols except status
                this.gridComponent.gridColumnApi.removeRowGroupColumn(col.getColId());
            });
            this.gridGroupedBy = -1;
        }*/
    }

    /** Approve data */
    onApproveData(event: CellClickedEvent): void {
        if (!event.data.dateSubmitted)
            return;

        let operation;
        if (this.filters.isTarget) {
            operation =
                this.dataService.approveTargetData([event.data.id],
                    event.data.dateApproved ? true : false)
        } else {
            operation = 
                this.dataService.approveActivityData([event.data.progressId],
                    event.data.dateApproved ? true : false);
        }

        this.subscriptions.push(
            operation.subscribe({
                error: (err) => {
                    console.error(err);
                },
                complete: () => {
                    if (event.data.dateApproved) {
                        event.data.dateApproved = null;

                        // change status
                        if (!this.filters.isTarget) {
                            if (event.data.eMonth)
                                event.data.status = ActivityStatus.Completed;
                            else
                                event.data.status = ActivityStatus.Ongoing;
                        }
                    } else {
                        event.data.dateApproved = new Date();

                        // change status to 'Archived'
                        if (!this.filters.isTarget && event.data.status === ActivityStatus.Completed)
                            event.data.status = ActivityStatus.Archived;
                    }

                    this.gridComponent.gridApi.refreshCells({
                        rowNodes: [event.node],
                        columns: ['dataStatus','status'],
                        force: true,
                        suppressFlash: true
                    });

                    this.messageService.success('The data approve status has been changed.',
                        event.data.dateApproved ? 'Approved' : 'Approval retreated');
                }
        }));
    }

    /** Approve all data */
    onApproveAll(isApproved?: boolean): void {
        let approveIds: number[] = [];

        this.gridComponent.gridApi.forEachNodeAfterFilter(node => {
            if (node.data.dateSubmitted) {
                if (this.filters.isTarget)
                    approveIds.push(node.data.id);
                else
                    approveIds.push(node.data.progressId);
            }
        });

        const operation = this.filters.isTarget
            ? this.dataService.approveTargetData(approveIds, isApproved)
            : this.dataService.approveActivityData(approveIds, isApproved);

        this.subscriptions.push(
            operation.subscribe({
                error: (err) => {
                    console.error(err);
                },
                complete: () => {
                    this.gridComponent.gridApi.forEachNodeAfterFilter(node => {
                        if (isApproved) {
                            node.data.dateApproved = null;

                            // change status
                            if (!this.filters.isTarget) {
                                if (node.data.eMonth)
                                    node.data.status = ActivityStatus.Completed;
                                else
                                    node.data.status = ActivityStatus.Ongoing;
                            }
                        } else if (node.data.dateSubmitted) {
                            node.data.dateApproved = new Date();

                            // change status to 'Archived'
                            if (!this.filters.isTarget && node.data.status === ActivityStatus.Completed)
                                node.data.status = ActivityStatus.Archived;
                        } else {
                            node.data.dateApproved = null;

                            // change status
                            if (!this.filters.isTarget) {
                                if (node.data.eMonth)
                                    node.data.status = ActivityStatus.Completed;
                                else
                                    node.data.status = ActivityStatus.Ongoing;
                            }
                        }
                    });

                    this.gridComponent.gridApi.refreshCells({
                        columns: ['dataStatus','status'],
                        force: true,
                        suppressFlash: true
                    });

                    const btnApproveAll: HTMLButtonElement = document.querySelector('btn-approve-all-header button');
                    if (!isApproved) {
                        btnApproveAll.classList.add('active');
                        btnApproveAll.title = 'Toggle approve all';
                    } else {
                        btnApproveAll.classList.remove('active');
                        btnApproveAll.title = 'Approve all';
                    }

                    this.messageService.success('The data approve status has been changed.',
                        isApproved ? 'Approval retreated' : 'All approved');
                }
            }));
    }

    onDownload(): void {
        this.downloading = true;
        this.gridComponent.gridApi.exportDataAsExcel();
        this.downloading = false;
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }

    // Helper method to check if the partner's organization matches the user's organization
    private isUserPartnerOfProject(partner: any): boolean {
        if (!partner) return false;
        
        // Convert to same type for comparison in case one is string and one is number
        const partnerOrgId = partner.organizationId !== undefined ? Number(partner.organizationId) : undefined;
        const partnerOrgIdAlt = partner.orgId !== undefined ? Number(partner.orgId) : undefined;
        const userOrgIdNum = Number(this.userOrgId);
        
        return partnerOrgId === userOrgIdNum || partnerOrgIdAlt === userOrgIdNum;
    }
}