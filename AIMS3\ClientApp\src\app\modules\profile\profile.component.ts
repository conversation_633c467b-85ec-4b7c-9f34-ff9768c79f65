import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { MessageService } from '../../shared/services/message.service';
import { compressImage } from '../../shared/utilities';
import { AuthService, UserType } from '../auth';
import { AuthHTTPService } from '../auth/services/auth-http';

@Component({
    selector: 'app-profile',
    templateUrl: './profile.component.html',
    styleUrls: ['./profile.component.scss']
})
export class ProfileComponent implements OnInit, OnDestroy {
    working: boolean = false;
    user$: Observable<UserType>;

    private subscriptions: Subscription[] = [];
    constructor(
        private authService: AuthService,
        private userService: AuthHTTPService,
        private messageService: MessageService
    ) { }

    ngOnInit(): void {
        this.user$ = this.authService.currentUserSubject.asObservable();
    }

    // update profile picture
    uploadProfilePicture($event: any): void {
        const inputValue = $event.target;

        if (inputValue) {
            const file: File = inputValue.files[0];
            const imgReader: FileReader = new FileReader();

            imgReader.onloadend = (e) => {
                this.working = true;
                const img = imgReader.result?.toString();
                if (!img)
                    return;

                compressImage(img).then((cImg) => {
                    const compressedImg = cImg?.toString();
                    if (!compressedImg)
                        return;

                    this.subscriptions.push(
                        this.userService.uploadProfilePicture(compressedImg).subscribe({
                            complete: () => {
                                try {
                                    // update the stored user picture
                                    let currentUser = this.authService.currentUserValue;
                                    if (currentUser) {
                                        currentUser.pic = compressedImg;
                                        this.authService.currentUserSubject.next(currentUser);
                                    }

                                    this.working = false;
                                    this.messageService.success('Your profile picture has been updated.');
                                } catch (e) {
                                    this.working = false;
                                    this.messageService.error('Something went wrong.');
                                    console.log(e);
                                }
                            }, error: (err) => {
                                this.working = false;
                                console.error(err);
                            }
                        })
                    );
                });
            }

            imgReader.readAsDataURL(file);
        }
    }

    // remove profile picture
    removeProfilePicture(): void {
        this.working = true;

        this.subscriptions.push(
            this.userService.uploadProfilePicture("").subscribe({
                complete: () => {
                    try {
                        // update the stored user picture
                        let currentUser = this.authService.currentUserValue;
                        if (currentUser) {
                            currentUser.pic = '';
                            this.authService.currentUserSubject.next(currentUser);
                        }

                        this.working = false;
                        this.messageService.success('Your profile picture has been removed.');
                    } catch (e) {
                        this.working = false;
                        this.messageService.error('Something went wrong.');
                        console.log(e);
                    }
                }, error: (err) => {
                    this.working = false;
                    console.error(err);
                }
            })
        );
    }


    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}