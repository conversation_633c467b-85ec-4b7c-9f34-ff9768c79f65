import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { Community, CommunityList, LocFilter } from '../models/community.model';

const API_URL = `${environment.apiUrl}/communities`;

@Injectable({ providedIn: 'root' })
export class LocationService {
    constructor(private http: HttpClient) { }

    getCommunities(top?: number): Observable<CommunityList[]> {
        return this.http.get<CommunityList[]>(API_URL + (top ? '/' + top : ''));
    }

    //getCommunitiesByProvinces(provsId: number[]): Observable<CommunityList[]> {
    //    return this.http.get<CommunityList[]>(API_URL + '/provinces/' + provId);
    //}

    getSimilarLocations(comm: Community): Observable<CommunityList[]> {
        return this.http.post<CommunityList[]>(`${API_URL}/similar-locations/${environment.similarLocsInRadius}`, comm);
    }

    getFilteredLocations(filters: LocFilter): Observable<CommunityList[]> {
        return this.http.post<CommunityList[]>(API_URL + '/filtered', filters);
    }

    addCommunity(comm: Community): Observable<void> {
        return this.http.post<void>(API_URL, comm);
    }

    updateCommunity(comm: Community): Observable<void> {
        return this.http.put<void>(API_URL, comm);
    }

    approveLocations(locIds: number[]): Observable<void> {
        return this.http.patch<void>(API_URL, locIds);
    }

    deleteLocation(locId: number): Observable<void> {
        return this.http.delete<void>(`${API_URL}/location/${locId}`);
    }

    deleteCommunity(commId: number): Observable<void> {
        return this.http.delete<void>(`${API_URL}/${commId}`);
    }
}