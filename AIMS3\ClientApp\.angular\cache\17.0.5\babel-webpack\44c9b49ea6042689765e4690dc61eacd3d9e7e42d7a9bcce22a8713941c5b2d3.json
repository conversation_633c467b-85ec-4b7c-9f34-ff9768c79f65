{"ast": null, "code": "import _asyncToGenerator from \"D:/UNDP/AIMS3System/AIMS3/ClientApp/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { EventEmitter } from '@angular/core';\nimport 'ag-grid-enterprise';\nimport { LicenseManager } from 'ag-grid-enterprise';\nimport { environment } from '../../../../../environments/environment';\nimport { ActivityStatus, ColDataType, ColumnVarType } from '../../../../shared/enums';\nimport { getColWidth } from '../../../../shared/utilities';\nimport { DynamicColumn } from '../../../aims-grid/models/dynamic-column.model';\nimport { CommentComponent } from '../../../comments/components/comment.component';\nimport { AttachmentsFormComponent } from '../../../library/components/attachments/attachments.component';\nimport { SabaDateEditor } from '../../controls/cell-editors/date/date.control';\nimport { SabaInputEditor } from '../../controls/cell-editors/input/input.control';\nimport { SabaNoneEditor } from '../../controls/cell-editors/none/none.control';\nimport { SabaSelectEditor } from '../../controls/cell-editors/select/select.control';\nimport { SabaTextEditor } from '../../controls/cell-editors/text/text.control';\nimport { SabaAttachmentRenderer } from '../../controls/cell-renderers/attachment/attachment.control';\nimport { SabaCheckboxRenderer } from '../../controls/cell-renderers/checkbox/checkbox.control';\nimport { SabaSelectValueRenderer } from '../../controls/cell-renderers/select/select-value.control';\nimport { GridCustomHeader } from '../../controls/grid-header/grid-header.control';\nimport { DynamicColumnFormComponent } from '../modal-forms/dynamic-column/column-form.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/grid.service\";\nimport * as i2 from \"../../../../shared/services/message.service\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"../../../../shared/components/working/working.component\";\nimport * as i5 from \"ag-grid-angular\";\nimport * as i6 from \"../../../library/components/attachments/attachments.component\";\nimport * as i7 from \"../../../comments/components/comment.component\";\nimport * as i8 from \"../modal-forms/dynamic-column/column-form.component\";\nfunction GridComponent_aims_working_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"aims-working\");\n  }\n}\nfunction GridComponent_comment_modal_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"comment-modal\", 6);\n    i0.ɵɵlistener(\"done\", function GridComponent_comment_modal_5_Template_comment_modal_done_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r3 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r3.onComment($event));\n    })(\"genLink\", function GridComponent_comment_modal_5_Template_comment_modal_genLink_0_listener($event) {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r5 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r5.generateLink($event));\n    });\n    i0.ɵɵelementEnd();\n  }\n}\nconst _c0 = (a0, a1) => ({\n  \"ag-theme-alpine\": a0,\n  \"ag-theme-alpine-dark\": a1\n});\nexport class GridComponent {\n  constructor(gridService, messageService) {\n    this.gridService = gridService;\n    this.messageService = messageService;\n    this.working = false;\n    this.isDarkMode = false;\n    this.isAdmin = false;\n    this.components = {\n      'noEditor': SabaNoneEditor,\n      'sabaInputEditor': SabaInputEditor,\n      'sabaTextEditor': SabaTextEditor,\n      'sabaDateEditor': SabaDateEditor,\n      'sabaCheckboxRenderer': SabaCheckboxRenderer,\n      'sabaAttachmentRenderer': SabaAttachmentRenderer,\n      'sabaSelectValRenderer': SabaSelectValueRenderer,\n      'sabaSelectEditor': SabaSelectEditor\n    };\n    this.tooltipShowDelay = 500;\n    this.groupCollapsed = [[], [], [], []];\n    this.gridColumns = {\n      commonCols: [],\n      progressActIdCol: null,\n      progressStatusCols: [],\n      progressCommCol: null,\n      progressAsOfCol: null,\n      targetInfoCols: [],\n      hiddenCol: null // hidden narrow col\n    };\n    // data from grid's parent\n    this.dynamicColumns = [];\n    this.conditionsCols = [];\n    this.colDone = new EventEmitter();\n    this.cellChanged = new EventEmitter();\n    // *** DATA -------------\n    this.rowData = [];\n    this.regionsFilter = [];\n    this.selectedVals = {\n      dataType: 'Progress',\n      sn: 1,\n      orgId: 0,\n      org: '',\n      profId: 0,\n      prof: '',\n      projId: 0,\n      proj: '',\n      month: 0,\n      year: 0\n    };\n    this.subscriptions = [];\n    // *** Other grid's functions --------------------------------\n    this.movingColPos = 0;\n    this.moveByGrid = false;\n    this.invalidCells = []; // col field, rowDataId\n    this.filterApplied = new EventEmitter();\n    this.sortApplied = new EventEmitter();\n    if (document.querySelector('[data-theme=\"dark\"')) this.isDarkMode = true;\n    // AG-GRID\n    LicenseManager.setLicenseKey(environment.agGridConfig.licenseKey);\n    // set all defaults\n    // default grid options\n    this.gridOptions = {\n      groupHeaderHeight: 30,\n      headerHeight: 35,\n      rowHeight: 24,\n      rowDragManaged: true,\n      rowSelection: 'multiple',\n      enableRangeSelection: true,\n      enableFillHandle: true,\n      undoRedoCellEditing: true,\n      undoRedoCellEditingLimit: 20,\n      scrollbarWidth: 10,\n      suppressRowClickSelection: true,\n      enableCellChangeFlash: true,\n      groupDisplayType: 'groupRows',\n      groupDefaultExpanded: -1,\n      groupIncludeFooter: true,\n      groupMaintainOrder: true,\n      groupAllowUnbalanced: true,\n      rowClassRules: {\n        'bg-success': params => params.data?.status == ActivityStatus.Completed,\n        'bg-light-warning text-warning': params => params.data?.status == ActivityStatus.Archived,\n        'bg-light-danger text-danger': params => params.data?.status == ActivityStatus.Cancelled,\n        'text-gray-600': params => !params.data?.status && params.data?.dateApproved // for target data when approved\n      },\n      statusBar: {\n        statusPanels: [{\n          statusPanel: 'agTotalAndFilteredRowCountComponent',\n          align: 'left'\n        },\n        //{ statusPanel: 'agTotalRowCountComponent', align: 'center' },\n        {\n          statusPanel: 'agFilteredRowCountComponent'\n        }, {\n          statusPanel: 'agSelectedRowCountComponent'\n        }, {\n          statusPanel: 'agAggregationComponent'\n        }]\n      },\n      processCellFromClipboard: this.gridService.onPaste,\n      processCellForClipboard: params => this.gridService.processCellForClipboard(params),\n      onCellKeyPress: e => this.gridService.onDisabledCellEditRequest(e, this.defaultColDefs.editable, this.conditionsCols),\n      onCellDoubleClicked: e => this.gridService.onDisabledCellEditRequest(e, this.defaultColDefs.editable, this.conditionsCols),\n      onCellKeyDown: this.gridService.clearCellsInRange,\n      getContextMenuItems: e => this.gridService.getContextMenu(e, this.defaultColDefs.editable)\n    };\n    // default column configurations\n    this.defaultColDefs = {\n      sortable: false,\n      resizable: true,\n      filter: true,\n      editable: false,\n      minWidth: 50,\n      menuTabs: ['filterMenuTab', 'generalMenuTab'],\n      cellClassRules: {\n        'bg-light-danger': params => this.invalidCells.findIndex(c => c.rowDataId === params.data?.id && c.field === params.column.getColDef().field) > -1,\n        'bg-gray-300': params => {\n          if (!params.data) return false;\n          return (params.data.activityId || params.data.uniqueId) && !params.data.status && !params.column.getColId().startsWith('new') && !params.column.getColId().startsWith('cols-hidden') && !params.column.isCellEditable(params.node);\n        },\n        'has-comment': params => this.gridService.getCellCommentClass(params),\n        'is-note': params => this.gridService.getCellCommentClass(params, 'note'),\n        'unresolved': params => this.gridService.getCellCommentClass(params, 'unresolved')\n      },\n      onCellClicked: event => {\n        const target = event.event.target.closest('.ag-cell');\n        if (target?.classList.contains('has-comment')) {\n          const cell = target.getBoundingClientRect();\n          const e = event.event;\n          if (e.y <= cell.top + 8 && e.x >= cell.right - 8) this.commentModalComponent.open(event.colDef, event.node, cell, false, true, target);\n        }\n      },\n      comparator: this.gridService.getComparator\n    };\n    // New dynamic column\n    this.addNewColumn = {\n      colId: 'new',\n      headerName: 'New',\n      headerTooltip: 'Add new column',\n      headerClass: 'text-hover-primary cursor-pointer',\n      headerComponent: GridCustomHeader,\n      cellClass: 'non-selectable-cell',\n      lockPinned: true,\n      suppressMovable: true,\n      width: 62,\n      minWidth: 62,\n      maxWidth: 62,\n      suppressFillHandle: true,\n      editable: false,\n      enableRowGroup: false,\n      suppressCellFlash: true,\n      filter: false,\n      suppressMenu: true,\n      sortable: false,\n      suppressSizeToFit: true,\n      suppressAutoSize: true,\n      lockVisible: true\n    };\n  }\n  // init the default readonly grid\n  ngOnInit() {\n    var _this = this;\n    try {\n      this.defaultColDefs.editable = this.isAdmin;\n      // add default common column definitions\n      this.columnDefs = [{\n        colId: 'rowNum',\n        headerName: '',\n        minWidth: 50,\n        width: 50,\n        maxWidth: 100,\n        suppressMovable: true,\n        filter: false,\n        sortable: false,\n        pinned: 'left',\n        editable: false,\n        enableRowGroup: false,\n        suppressMenu: true,\n        lockPosition: true,\n        suppressFillHandle: true,\n        onCellClicked: this.onCellClicked,\n        cellRenderer: params => {\n          return params.node.footer ? 'Total' : params.node.rowIndex + 1;\n        }\n      }];\n      // progress first few columns\n      this.gridColumns.progressActIdCol = {\n        field: 'activityId',\n        headerName: 'Activity ID',\n        headerClass: 'required',\n        editable: false,\n        suppressFillHandle: true,\n        suppressMovable: true,\n        enableRowGroup: false,\n        pinned: true,\n        lockPinned: true,\n        lockVisible: true,\n        width: 110,\n        cellClass: 'fs-7'\n      };\n      this.gridColumns.progressStatusCols = [{\n        field: 'file',\n        colId: 'file',\n        headerName: 'File',\n        headerClass: 'bg-header-color-1',\n        headerTooltip: 'All files linked to this activity will show up here.',\n        headerComponent: GridCustomHeader,\n        headerComponentParams: {\n          colType: ColDataType.Attachment,\n          showInfoIcon: true\n        },\n        cellEditor: 'noEditor',\n        cellEditorParams: {\n          onComment: (colDef, node, ctxMenu, isNote, readOnly) => {\n            this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\n          },\n          onCellLink: (colId, node) => {\n            if (this.selectedVals.dataType === 'Progress') this.generateLink({\n              activityId: node.data.id,\n              activityProgressId: node.data.progressId,\n              columnId: colId\n            });else this.generateLink({\n              targetId: node.data.id,\n              columnId: colId\n            });\n          }\n        },\n        editable: params => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\n        cellRenderer: 'sabaAttachmentRenderer',\n        suppressMovable: true,\n        enableRowGroup: false,\n        width: 80,\n        columnGroupShow: 'open',\n        comparator: this.gridService.getNumComparator,\n        sortable: false,\n        onCellClicked: event => this.onCellClicked(event),\n        suppressMenu: true,\n        suppressFillHandle: true\n      }, {\n        colId: 'status',\n        field: 'status',\n        headerName: 'Status',\n        headerClass: 'bg-header-color-1',\n        headerComponent: GridCustomHeader,\n        headerComponentParams: {\n          isRequired: true,\n          colType: ColDataType.SelectSingle\n        },\n        width: 110,\n        cellEditor: 'sabaSelectEditor',\n        cellEditorParams: {\n          values: [{\n            id: 0,\n            name: 'Ongoing'\n          }, {\n            id: 1,\n            name: 'Completed'\n          }, {\n            id: 2,\n            name: 'Archived',\n            disabled: true\n          }, {\n            id: 3,\n            name: 'Cancelled'\n          }],\n          onComment: (colDef, node, ctxMenu, isNote, readOnly) => {\n            this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\n          },\n          onCellLink: (colId, node) => {\n            if (this.selectedVals.dataType === 'Progress') this.generateLink({\n              activityId: node.data.id,\n              activityProgressId: node.data.progressId,\n              columnId: colId\n            });else this.generateLink({\n              targetId: node.data.id,\n              columnId: colId\n            });\n          }\n        },\n        editable: params => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\n        valueFormatter: this.gridService.getValueFormatter,\n        filterParams: {\n          valueFormatter: this.gridService.getFilterValueFormatter\n        },\n        cellRenderer: 'sabaSelectValRenderer',\n        cellClassRules: {\n          'text-primary': params => params.value == ActivityStatus.Archived,\n          'text-red': params => params.value == ActivityStatus.Cancelled\n        },\n        lockVisible: true,\n        suppressMovable: true,\n        valueSetter: params => this.gridService.onValueSet(params)\n      }, {\n        colId: 'sMonth',\n        field: 'sMonth',\n        headerName: 'Start date',\n        headerClass: 'bg-header-color-1',\n        headerComponent: GridCustomHeader,\n        headerComponentParams: {\n          isRequired: true,\n          colType: ColDataType.Date // MMM/yyyy\n        },\n\n        cellEditor: 'sabaDateEditor',\n        cellEditorParams: {\n          ph: 'dd/MM/yy',\n          onComment: (colDef, node, ctxMenu, isNote, readOnly) => {\n            this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\n          },\n          onCellLink: (colId, node) => {\n            if (this.selectedVals.dataType === 'Progress') this.generateLink({\n              activityId: node.data.id,\n              activityProgressId: node.data.progressId,\n              columnId: colId\n            });else this.generateLink({\n              targetId: node.data.id,\n              columnId: colId\n            });\n          }\n        },\n        editable: params => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\n        valueFormatter: this.gridService.getValueFormatter,\n        filterParams: {\n          valueFormatter: this.gridService.getFilterValueFormatter\n        },\n        lockPinned: true,\n        lockVisible: true,\n        comparator: this.gridService.getDateComparator,\n        width: 130,\n        columnGroupShow: 'open',\n        suppressMovable: true\n      }, {\n        colId: 'eMonth',\n        field: 'eMonth',\n        headerName: 'End date',\n        headerClass: 'bg-header-color-1',\n        headerTooltip: 'The date when the implementation of the activity has been completed.',\n        headerComponent: GridCustomHeader,\n        headerComponentParams: {\n          isRequired: false,\n          colType: ColDataType.Date,\n          showInfoIcon: true\n        },\n        cellEditor: 'sabaDateEditor',\n        cellEditorParams: {\n          ph: 'dd/MM/yy',\n          lastDay: true,\n          onComment: (colDef, node, ctxMenu, isNote, readOnly) => {\n            this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\n          },\n          onCellLink: (colId, node) => {\n            if (this.selectedVals.dataType === 'Progress') this.generateLink({\n              activityId: node.data.id,\n              activityProgressId: node.data.progressId,\n              columnId: colId\n            });else this.generateLink({\n              targetId: node.data.id,\n              columnId: colId\n            });\n          }\n        },\n        editable: params => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\n        valueFormatter: this.gridService.getValueFormatter,\n        filterParams: {\n          valueFormatter: this.gridService.getFilterValueFormatter\n        },\n        lockPinned: true,\n        lockVisible: true,\n        comparator: this.gridService.getDateComparator,\n        width: 130,\n        columnGroupShow: 'open',\n        suppressMovable: true,\n        valueSetter: this.gridService.onValueSet\n      }];\n      this.conditionsCols.push(new DynamicColumn(-4, null, 'Year', ColumnVarType.TargetInfo, -1, ColDataType.Number));\n      this.conditionsCols.push(new DynamicColumn(-5, null, 'Qtr', ColumnVarType.TargetInfo, 0, ColDataType.Number));\n      this.conditionsCols.push(new DynamicColumn(-6, null, 'Status', ColumnVarType.ProgressInfo, -3, ColDataType.SelectSingle, this.gridColumns.progressStatusCols[1].cellEditorParams.values));\n      this.conditionsCols.push(new DynamicColumn(-1, null, 'StartMonth', ColumnVarType.ProgressInfo, -2, ColDataType.Date, null, 'Start month'));\n      this.conditionsCols.push(new DynamicColumn(-2, null, 'EndMonth', ColumnVarType.ProgressInfo, -1, ColDataType.Date, null, 'Complete month'));\n      // info static column used for group collapse\n      this.gridColumns.hiddenCol = {\n        colId: 'cols-hidden',\n        headerName: '...',\n        headerTooltip: 'Column(s) hidden. Click group handle to expand.',\n        cellClass: 'non-selectable-cell',\n        lockPinned: true,\n        suppressMovable: true,\n        width: 28,\n        minWidth: 28,\n        maxWidth: 28,\n        resizable: false,\n        editable: false,\n        suppressCellFlash: true,\n        filter: false,\n        suppressMenu: true,\n        sortable: false,\n        suppressSizeToFit: true,\n        suppressAutoSize: true,\n        suppressFillHandle: true,\n        lockVisible: true,\n        enableRowGroup: false,\n        columnGroupShow: 'closed'\n      };\n      // location columns\n      this.gridColumns.commonCols = [{\n        field: 'region',\n        headerName: 'Region',\n        headerClass: 'bg-header-color-2',\n        headerComponent: GridCustomHeader,\n        width: 130,\n        suppressMovable: true,\n        cellEditor: 'sabaSelectEditor',\n        cellEditorParams: {\n          values: [{\n            id: 1,\n            name: 'Central'\n          }, {\n            id: 2,\n            name: 'Central Highland'\n          }, {\n            id: 3,\n            name: 'Eastern'\n          }, {\n            id: 4,\n            name: 'South Eastern'\n          }, {\n            id: 5,\n            name: 'North Eastern'\n          }, {\n            id: 6,\n            name: 'Western'\n          }, {\n            id: 7,\n            name: 'Southern'\n          }, {\n            id: 8,\n            name: 'Northern'\n          }],\n          getData: function () {\n            var _ref = _asyncToGenerator(function* (params) {\n              return yield _this.gridService.getMetaData('regs', params, _this.regionsFilter);\n            });\n            return function getData(_x) {\n              return _ref.apply(this, arguments);\n            };\n          }(),\n          onComment: (colDef, node, ctxMenu, isNote, readOnly) => {\n            this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\n          },\n          onCellLink: (colId, node) => {\n            if (this.selectedVals.dataType === 'Progress') this.generateLink({\n              activityId: node.data.id,\n              activityProgressId: node.data.progressId,\n              columnId: colId\n            });else this.generateLink({\n              targetId: node.data.id,\n              columnId: colId\n            });\n          }\n        },\n        editable: params => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\n        cellRenderer: 'sabaSelectValRenderer',\n        valueGetter: params => {\n          const regionIds = params.data?.region?.split(',') || [];\n          if (!regionIds.length || !regionIds[0]) return '';\n          const result = [];\n          const regionNames = ['National', 'Central', 'Central Highland', 'Eastern', 'South Eastern', 'North Eastern', 'Western', 'Southern', 'Northern'];\n          regionIds.forEach(id => {\n            if (regionNames[id]) result.push(regionNames[id]);\n          });\n          return result.join(', ');\n        },\n        valueFormatter: this.gridService.getValueFormatter,\n        filterParams: {\n          valueFormatter: this.gridService.getFilterValueFormatter\n        },\n        onCellValueChanged: params => this.onLocationCellChanged(params),\n        lockVisible: true,\n        columnGroupShow: 'open',\n        comparator: (vA, vB, nA, nB, desc) => this.gridService.getRegionComparator(vA, vB, nA, nB, desc),\n        valueSetter: this.gridService.setValue\n      }, {\n        field: 'province',\n        headerName: 'Province',\n        headerClass: 'bg-header-color-2',\n        headerComponent: GridCustomHeader,\n        headerComponentParams: {\n          colType: this.selectedVals.dataType === 'Targets' ? ColDataType.SelectSingle : ColDataType.SelectMultiple\n        },\n        width: 140,\n        suppressMovable: true,\n        cellEditor: 'sabaSelectEditor',\n        cellEditorParams: {\n          values: [],\n          getData: function () {\n            var _ref2 = _asyncToGenerator(function* (params) {\n              return yield _this.gridService.getMetaData('provs', params, _this.regionsFilter);\n            });\n            return function getData(_x2) {\n              return _ref2.apply(this, arguments);\n            };\n          }(),\n          onComment: (colDef, node, ctxMenu, isNote, readOnly) => {\n            this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\n          },\n          onCellLink: (colId, node) => {\n            if (this.selectedVals.dataType === 'Progress') this.generateLink({\n              activityId: node.data.id,\n              activityProgressId: node.data.progressId,\n              columnId: colId\n            });else this.generateLink({\n              targetId: node.data.id,\n              columnId: colId\n            });\n          }\n        },\n        editable: params => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\n        cellRenderer: 'sabaSelectValRenderer',\n        valueFormatter: this.gridService.getValueFormatter.bind(this.gridService),\n        filterParams: {\n          valueFormatter: this.gridService.getFilterValueFormatter.bind(this.gridService)\n        },\n        onCellValueChanged: params => this.onLocationCellChanged(params),\n        lockVisible: true,\n        columnGroupShow: 'open',\n        comparator: (vA, vB, nA, nB, desc) => this.gridService.getProvinceComparator(vA, vB, nA, nB, desc),\n        valueSetter: this.gridService.setValue\n      }, {\n        field: 'district',\n        headerName: 'District',\n        headerClass: 'bg-header-color-2',\n        headerComponent: GridCustomHeader,\n        headerComponentParams: {\n          colType: this.selectedVals.dataType === 'Targets' ? ColDataType.SelectSingle : ColDataType.SelectMultiple\n        },\n        width: 145,\n        suppressMovable: true,\n        cellEditor: 'sabaSelectEditor',\n        cellEditorParams: {\n          values: [],\n          fuzzySearch: true,\n          getData: function () {\n            var _ref3 = _asyncToGenerator(function* (params) {\n              return yield _this.gridService.getMetaData('dists', params, _this.regionsFilter);\n            });\n            return function getData(_x3) {\n              return _ref3.apply(this, arguments);\n            };\n          }(),\n          onComment: (colDef, node, ctxMenu, isNote, readOnly) => {\n            this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\n          },\n          onCellLink: (colId, node) => {\n            if (this.selectedVals.dataType === 'Progress') this.generateLink({\n              activityId: node.data.id,\n              activityProgressId: node.data.progressId,\n              columnId: colId\n            });else this.generateLink({\n              targetId: node.data.id,\n              columnId: colId\n            });\n          }\n        },\n        editable: params => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\n        cellRenderer: 'sabaSelectValRenderer',\n        valueFormatter: this.gridService.getValueFormatter.bind(this.gridService),\n        filterParams: {\n          valueFormatter: this.gridService.getFilterValueFormatter.bind(this.gridService)\n        },\n        onCellValueChanged: params => this.onLocationCellChanged(params),\n        lockVisible: true,\n        columnGroupShow: 'open',\n        comparator: (vA, vB, nA, nB, desc) => this.gridService.getDistrictComparator(vA, vB, nA, nB, desc),\n        valueSetter: this.gridService.setValue\n      }];\n      // fetch provinces and districts for the first time\n      this.gridService.getMetaData('provs').then(vals => {\n        this.gridColumns.commonCols[1].cellEditorParams.values = vals;\n        // Force grid refresh to update display\n        setTimeout(() => {\n          if (this.gridApi) {\n            this.gridApi.refreshCells({\n              columns: ['province'],\n              force: true\n            });\n          }\n        }, 100);\n      });\n      this.gridService.getMetaData('dists').then(vals => {\n        this.gridColumns.commonCols[2].cellEditorParams.values = vals;\n        this.refreshProvDistGridColumns();\n        // Force grid refresh to update display\n        setTimeout(() => {\n          if (this.gridApi) {\n            this.gridApi.refreshCells({\n              columns: ['district'],\n              force: true\n            });\n          }\n        }, 100);\n      });\n      // progress community column\n      this.gridColumns.progressCommCol = {\n        field: 'community',\n        headerName: 'Community',\n        headerClass: 'bg-header-color-2',\n        headerComponent: GridCustomHeader,\n        width: 170,\n        suppressMovable: true,\n        cellEditor: 'sabaSelectEditor',\n        cellEditorParams: {\n          values: [],\n          fuzzySearch: true,\n          addNewLink: '/data-entry/locations',\n          getData: function () {\n            var _ref4 = _asyncToGenerator(function* (params) {\n              return yield _this.gridService.getMetaData('comms', params);\n            });\n            return function getData(_x4) {\n              return _ref4.apply(this, arguments);\n            };\n          }(),\n          onComment: (colDef, node, ctxMenu, isNote, readOnly) => {\n            this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\n          },\n          onCellLink: (colId, node) => {\n            if (this.selectedVals.dataType === 'Progress') this.generateLink({\n              activityId: node.data.id,\n              activityProgressId: node.data.progressId,\n              columnId: colId\n            });else this.generateLink({\n              targetId: node.data.id,\n              columnId: colId\n            });\n          }\n        },\n        editable: params => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\n        cellRenderer: 'sabaSelectValRenderer',\n        valueFormatter: this.gridService.getValueFormatter.bind(this.gridService),\n        filterParams: {\n          valueFormatter: this.gridService.getFilterValueFormatter.bind(this.gridService)\n        },\n        onCellValueChanged: params => this.onLocationCellChanged(params),\n        lockVisible: true,\n        comparator: (vA, vB, nA, nB, desc) => this.gridService.getCommunityComparator(vA, vB, nA, nB, desc),\n        valueSetter: this.gridService.setValue\n      };\n      // progress static columns\n      this.gridColumns.progressAsOfCol = {\n        colId: 'asOf',\n        field: 'asOf',\n        headerName: 'As of',\n        headerTooltip: 'As of the end of this month. Automatically filled.',\n        headerClass: 'bg-header-color-4',\n        headerComponent: GridCustomHeader,\n        headerComponentParams: {\n          isRequired: true,\n          colType: ColDataType.Date,\n          showInfoIcon: true\n        },\n        cellEditor: 'sabaDateEditor',\n        cellEditorParams: {\n          ph: 'dd/MM/yy',\n          lastDay: true,\n          onComment: (colDef, node, ctxMenu, isNote, readOnly) => {\n            this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\n          },\n          onCellLink: (colId, node) => {\n            if (this.selectedVals.dataType === 'Progress') this.generateLink({\n              activityId: node.data.id,\n              activityProgressId: node.data.progressId,\n              columnId: colId\n            });else this.generateLink({\n              targetId: node.data.id,\n              columnId: colId\n            });\n          }\n        },\n        //editable: this.isAdmin, //(params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\n        valueFormatter: this.gridService.asOfValueFormatter,\n        filterParams: {\n          valueFormatter: this.gridService.asOfValueFormatter\n        },\n        onCellValueChanged: this.gridService.onCellValueChanged,\n        lockPinned: true,\n        comparator: this.gridService.getDateComparator,\n        width: 110,\n        lockVisible: true,\n        cellClassRules: {\n          'text-muted': this.gridService.getCellClassRules\n        },\n        suppressMovable: true\n      };\n      this.conditionsCols.push(new DynamicColumn(-3, null, 'AsOf', ColumnVarType.Progress, 0, ColDataType.Date, null, 'As of'));\n      // target info static columns\n      let years = [];\n      const currYear = new Date().getFullYear();\n      for (let i = 2020; i <= currYear + 3; i++) years.push({\n        id: i,\n        name: `${i}`\n      });\n      this.gridColumns.targetInfoCols = [{\n        colId: 'year',\n        field: 'year',\n        headerName: 'Year',\n        headerClass: 'bg-header-color-3',\n        headerComponent: GridCustomHeader,\n        headerComponentParams: {\n          isRequired: true,\n          colType: ColDataType.SelectSingle,\n          showInfoIcon: false\n        },\n        cellEditor: 'sabaSelectEditor',\n        cellEditorParams: {\n          values: [{\n            id: 0,\n            name: 'All years'\n          }, ...years],\n          onComment: (colDef, node, ctxMenu, isNote, readOnly) => {\n            this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\n          },\n          onCellLink: (colId, node) => {\n            if (this.selectedVals.dataType === 'Progress') this.generateLink({\n              activityId: node.data.id,\n              activityProgressId: node.data.progressId,\n              columnId: colId\n            });else this.generateLink({\n              targetId: node.data.id,\n              columnId: colId\n            });\n          }\n        },\n        editable: params => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\n        cellRenderer: 'sabaSelectValRenderer',\n        valueFormatter: this.gridService.getValueFormatter,\n        filterParams: {\n          valueFormatter: this.gridService.getFilterValueFormatter\n        },\n        lockPinned: true,\n        comparator: this.gridService.getNumComparator,\n        width: 95,\n        lockVisible: true,\n        suppressMovable: true\n      }, {\n        colId: 'qtr',\n        field: 'qtr',\n        headerName: 'Qtr',\n        headerClass: 'bg-header-color-3',\n        headerComponent: GridCustomHeader,\n        headerComponentParams: {\n          isRequired: true,\n          colType: ColDataType.SelectSingle,\n          showInfoIcon: false\n        },\n        cellEditor: 'sabaSelectEditor',\n        cellEditorParams: {\n          values: [\n          //{ id: 0, name: 'All' },\n          {\n            id: 1,\n            name: 'Qtr-1'\n          }, {\n            id: 2,\n            name: 'Qtr-2'\n          }, {\n            id: 3,\n            name: 'Qtr-3'\n          }, {\n            id: 4,\n            name: 'Qtr-4'\n          }],\n          onComment: (colDef, node, ctxMenu, isNote, readOnly) => {\n            this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\n          },\n          onCellLink: (colId, node) => {\n            if (this.selectedVals.dataType === 'Progress') this.generateLink({\n              activityId: node.data.id,\n              activityProgressId: node.data.progressId,\n              columnId: colId\n            });else this.generateLink({\n              targetId: node.data.id,\n              columnId: colId\n            });\n          }\n        },\n        editable: params => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\n        cellRenderer: 'sabaSelectValRenderer',\n        valueFormatter: this.gridService.getValueFormatter,\n        filterParams: {\n          valueFormatter: this.gridService.getFilterValueFormatter\n        },\n        columnGroupShow: 'open',\n        lockPinned: true,\n        lockVisible: true,\n        width: 90,\n        suppressMovable: true\n      }];\n      // push some empty data\n      for (let i = 1; i <= 20; i++) this.rowData.push({\n        id: 0,\n        colVals: {},\n        dirty: false\n      });\n    } catch (ex) {\n      console.error(ex);\n      this.messageService.error('Something went wrong.');\n    }\n  }\n  // generate serial number and activity id based on it\n  onLocationCellChanged(params) {\n    if (this.selectedVals.dataType === 'Progress') this.selectedVals.sn = this.rowData.filter(d => d.activityId).length;else this.selectedVals.sn = this.rowData.filter(d => d.uniqueId).length;\n    // look for duplicate s#\n    this.selectedVals.sn++;\n    for (let i = 0; i < this.rowData.length; i++) {\n      const uId = this.rowData[i]['activityId'] || this.rowData[i]['uniqueId'];\n      if (uId?.startsWith(`${this.selectedVals.sn}-`)) this.selectedVals.sn++;\n    }\n    this.gridService.onLocationChanged(params, this.selectedVals);\n  }\n  refreshProvDistGridColumns() {\n    // refresh province and district cols dropdown values\n    if (this.isAdmin && this.gridColumnApi) {\n      const cols = this.gridColumnApi.getColumns();\n      for (let i = 1; i < 10; i++) {\n        const colDef = cols[i]?.getColDef();\n        if (!colDef) continue;\n        if (colDef.field === 'province') {\n          if (!colDef.cellEditorParams.values?.length) {\n            colDef.cellEditorParams.values = this.gridColumns.commonCols[1].cellEditorParams.values;\n            cols[i].setColDef(colDef, cols[i].getColDef());\n          }\n        } else if (colDef.field === 'district') {\n          if (!colDef.cellEditorParams.values?.length) {\n            colDef.cellEditorParams.values = this.gridColumns.commonCols[2].cellEditorParams.values;\n            cols[i].setColDef(colDef, cols[i].getColDef());\n          }\n          break;\n        }\n      }\n    }\n  }\n  getGroupsState(target) {\n    let storeName = target ? 'tgroup1Collapsed' : 'group1Collapsed';\n    this.groupCollapsed[0] = JSON.parse(localStorage.getItem(storeName) || '[]');\n    storeName = storeName.replace('1', '2');\n    this.groupCollapsed[1] = JSON.parse(localStorage.getItem(storeName) || '[]');\n    storeName = storeName.replace('2', '3');\n    this.groupCollapsed[2] = JSON.parse(localStorage.getItem(storeName) || '[]');\n    storeName = storeName.replace('3', '4');\n    this.groupCollapsed[3] = JSON.parse(localStorage.getItem(storeName) || '[]');\n  }\n  // init as per profile and project selected\n  initGrid(filters) {\n    try {\n      // by reaching this, the gridColumns are already asssigned to by the parent\n      this.working = true;\n      this.gridApi.stopEditing(true);\n      this.columnDefs = [this.columnDefs[0]]; // keep the first rowNum col\n      this.conditionsCols = this.conditionsCols.filter(c => c.id < 0); // reset cond cols to default\n      this.gridColumns.commonCols[0].cellEditorParams.values =\n      // reset region col to exclude 'National'\n      this.gridColumns.commonCols[0].cellEditorParams.values.filter(v => v.id > 0);\n      this.gridApi.setColumnDefs(this.columnDefs); // reset/refresh grid\n      // create columns\n      if (filters.type === 'Progress') {\n        this.columnDefs = this.columnDefs.concat(this.gridColumns.progressActIdCol);\n        this.getGroupsState();\n        // status group and columns\n        this.gridColumns.commonCols[2].columnGroupShow = 'open';\n        this.gridColumns.hiddenCol.headerClass = 'bg-header-color-1';\n        this.columnDefs.push({\n          groupId: 'ProgressStatus',\n          headerTooltip: 'Double click to expand or collapse this section.',\n          headerName: 'Status',\n          headerClass: 'bg-header-group-color-1',\n          suppressMovable: true,\n          marryChildren: true,\n          openByDefault: !this.groupCollapsed[0].includes(this.selectedVals.profId),\n          children: [...this.gridColumns.progressStatusCols, this.gridColumns.hiddenCol]\n        });\n        // location cols\n        let hiddenCol = Object.assign({}, this.gridColumns.hiddenCol);\n        hiddenCol.headerClass = 'bg-header-color-2';\n        this.columnDefs.push({\n          groupId: `${ColumnVarType.ProgressStatic}`,\n          headerTooltip: 'Double click to expand or collapse this section.',\n          headerName: 'Location',\n          headerClass: 'bg-header-group-color-2',\n          suppressMovable: true,\n          marryChildren: true,\n          openByDefault: !this.groupCollapsed[1].includes(this.selectedVals.profId),\n          children: [...this.gridColumns.commonCols, this.gridColumns.progressCommCol, hiddenCol]\n        });\n        // add info and dynamic cols\n        let infoCols = [];\n        let progCols = [];\n        this.dynamicColumns.forEach(col => {\n          // update static cols props\n          if (col.type === ColumnVarType.ProgressStatic) {\n            let staticCol = this.gridColumns.commonCols.concat(this.gridColumns.progressCommCol).find(sc => sc.field === col.name.toLowerCase());\n            if (staticCol) {\n              if (staticCol.field === 'community') this.gridColumns.progressCommCol.colId = col.id;\n              staticCol.colId = col.id;\n              staticCol.headerName = col.displayName || col.name;\n              staticCol.headerTooltip = col.description;\n              staticCol.headerComponentParams = {\n                enableEdit: this.isAdmin,\n                isRequired: col.isRequired,\n                colType: col.fieldType,\n                showInfoIcon: col.description?.length > 0,\n                onColumnEdit: e => {\n                  this.colModalComponent.column = Object.assign({}, col);\n                  // provide cols\n                  this.colModalComponent.columns = this.dynamicColumns.filter(c => c.type === ColumnVarType.ProgressStatic);\n                  this.colModalComponent.open(e, 'static');\n                }\n              };\n              // Note: Removed automatic override to SelectMultiple - respects user configuration\n            }\n            // add national value to Region col if it's selected\n            if (col.name === 'Region' && col.conditionsApplied?.indexOf('showNational') > -1) {\n              // do not add if region for the project is restricted\n              if (!filters.project?.regions?.length) {\n                staticCol.cellEditorParams.values = [{\n                  id: 0,\n                  name: 'All'\n                }, ...staticCol.cellEditorParams.values];\n              }\n            }\n            // add to conditions columns\n            //this.conditionsCols.push(col);\n            return;\n          }\n          // add dynamic column\n          let colDef = {\n            colId: `${col.id}`,\n            field: 'colVals.dCol' + col.id,\n            headerName: col.displayName || col.name,\n            headerTooltip: col.description,\n            headerComponent: GridCustomHeader,\n            headerComponentParams: {\n              enableEdit: this.isAdmin,\n              isRequired: col.isRequired,\n              type: col.type,\n              colType: col.fieldType,\n              colOrder: col.order,\n              showInfoIcon: col.description?.length > 0,\n              onColumnEdit: (e, showMenu) => {\n                // set column on child\n                this.colModalComponent.column = Object.assign({}, col);\n                // provide cols for condition's component\n                this.colModalComponent.columns = this.conditionsCols.filter(c => [ColumnVarType.ProgressInfo, ColumnVarType.Progress].includes(c.type));\n                this.colModalComponent.open(e, `${col.id}`, showMenu);\n              }\n            },\n            cellEditor: this.gridService.getCellEditor(col.fieldType),\n            cellEditorParams: {\n              ...this.gridService.getCellEditorParams(col),\n              onComment: (colDef, node, ctxMenu, isNote, readOnly) => {\n                this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\n              },\n              onCellLink: (colId, node) => {\n                if (this.selectedVals.dataType === 'Progress') this.generateLink({\n                  activityId: node.data.id,\n                  activityProgressId: node.data.progressId,\n                  columnId: colId\n                });else this.generateLink({\n                  targetId: node.data.id,\n                  columnId: colId\n                });\n              }\n            },\n            editable: params => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\n            valueFormatter: this.gridService.getValueFormatter,\n            cellRenderer: this.gridService.getCellRenderer(col.fieldType),\n            //cellRendererParams: { footerValueGetter: (params) => 'Text (' + params.value + ')' },\n            cellClassRules: {\n              'text-end': this.gridService.getCellTextAlign,\n              'text-danger': this.gridService.getCellClassRules\n              //'text-muted': (params) => col.type === ColumnVarType.Progress && params.data?.draft\n            },\n            columnGroupShow: 'open',\n            enableRowGroup: this.gridService.getEnableGrouping(col.fieldType),\n            enableValue: this.gridService.getEnableGrouping(col.fieldType),\n            aggFunc: this.gridService.getAggFunc(col.fieldType),\n            lockPinned: true,\n            lockVisible: true,\n            suppressMovable: !this.isAdmin,\n            width: getColWidth(col.name + (col.isRequired ? '*' : ''), [this.isAdmin, col.description?.length > 0])\n          };\n          // attach cell click for attachment columns\n          if (col.fieldType === ColDataType.Attachment) {\n            colDef.onCellClicked = event => this.onCellClicked(event);\n            colDef.comparator = this.gridService.getNumComparator;\n          }\n          // attach num parser & comparator, and date comparator\n          if (col.fieldType >= ColDataType.Number && col.fieldType <= ColDataType.GPS) {\n            colDef.comparator = this.gridService.getNumComparator;\n            colDef.valueGetter = params => this.gridService.getNumericValue(col.id, params);\n            colDef.valueParser = params => parseFloat(params.newValue);\n          }\n          if (col.fieldType === ColDataType.Date) {\n            //colDef.cellEditorParams.format = 'dd MMM yyyy';\n            colDef.filterParams = {\n              valueFormatter: this.gridService.getFilterValueFormatter\n            };\n            colDef.comparator = this.gridService.getDateComparator;\n          }\n          if (col.type === ColumnVarType.ProgressInfo) {\n            colDef.headerClass = 'bg-header-color-3';\n            infoCols.push(colDef);\n          } else if (col.type === ColumnVarType.Progress) {\n            colDef.headerClass = 'bg-header-color-4';\n            colDef.cellClassRules = {\n              ...colDef.cellClassRules,\n              'text-muted': params => params.data?.draft\n            }; // draft on\n            colDef.onCellValueChanged = params => this.gridService.onCellValueChanged(params); // draft off\n            progCols.push(colDef);\n          }\n          // add to conditions columns\n          this.conditionsCols.push(col);\n        });\n        // sort dynamic cols by order and push to grid's columns\n        infoCols.sort((x, y) => x.headerComponentParams.colOrder > y.headerComponentParams.colOrder ? 1 : -1);\n        progCols.sort((x, y) => x.headerComponentParams.colOrder > y.headerComponentParams.colOrder ? 1 : -1);\n        if (infoCols.length)\n          // make first col always visible in the group\n          infoCols[0].columnGroupShow = null;\n        // add add new column if admin\n        if (this.isAdmin) {\n          let fnOnColAdd = e => {\n            this.colModalComponent.column = new DynamicColumn(0, filters.profId, '', ColumnVarType.ProgressInfo, infoCols.length, 0);\n            // provide cols for condition's component\n            this.colModalComponent.columns = this.conditionsCols.filter(c => [ColumnVarType.ProgressInfo, ColumnVarType.Progress].includes(c.type));\n            this.colModalComponent.open(e);\n          };\n          let addNewCol = {\n            ...this.addNewColumn,\n            headerComponentParams: {\n              addNewCol: true,\n              onColumnEdit: fnOnColAdd\n            }\n          };\n          addNewCol.headerClass = 'bg-header-color-3';\n          infoCols.push(addNewCol);\n          fnOnColAdd = e => {\n            this.colModalComponent.column = new DynamicColumn(0, filters.profId, '', ColumnVarType.Progress, progCols.length, 0);\n            // provide cols for condition's component\n            this.colModalComponent.columns = this.conditionsCols.filter(c => [ColumnVarType.ProgressInfo, ColumnVarType.Progress].includes(c.type));\n            this.colModalComponent.open(e);\n          };\n          addNewCol = {\n            ...this.addNewColumn,\n            headerComponentParams: {\n              addNewCol: true,\n              onColumnEdit: fnOnColAdd.bind({})\n            }\n          };\n          addNewCol.headerClass = 'bg-header-color-4';\n          progCols.push(addNewCol);\n        }\n        hiddenCol = Object.assign({}, this.gridColumns.hiddenCol);\n        hiddenCol.headerClass = 'bg-header-color-3';\n        this.columnDefs.push({\n          groupId: `${ColumnVarType.ProgressInfo}`,\n          headerTooltip: 'Double click to expand or collapse this section.',\n          headerName: 'Info',\n          headerClass: 'bg-header-group-color-3',\n          suppressMovable: true,\n          marryChildren: true,\n          openByDefault: !this.groupCollapsed[2].includes(this.selectedVals.profId),\n          children: infoCols.length > (this.isAdmin ? 2 : 1) ? [...infoCols, hiddenCol] : [...infoCols]\n        });\n        hiddenCol = Object.assign({}, this.gridColumns.hiddenCol);\n        hiddenCol.headerClass = 'bg-header-color-4';\n        this.columnDefs.push({\n          groupId: `${ColumnVarType.Progress}`,\n          headerName: 'Cumulative progress',\n          headerClass: 'bg-header-group-color-4 info-icon',\n          headerTooltip: 'Values entered in previous months will show up in grey.\\nDouble click to expand or collapse this section.',\n          suppressMovable: true,\n          marryChildren: true,\n          openByDefault: !this.groupCollapsed[3].includes(this.selectedVals.profId),\n          children: progCols.length > (this.isAdmin ? 2 : 1) ? [this.gridColumns.progressAsOfCol, hiddenCol, ...progCols] : [this.gridColumns.progressAsOfCol, ...progCols]\n        });\n        // ---------------------------------------------------------------\n      } else {\n        // target\n        // ---------------------------------------------------------------\n        this.getGroupsState(true);\n        // location cols\n        this.gridColumns.commonCols[2].columnGroupShow = null;\n        this.gridColumns.hiddenCol.headerClass = 'bg-header-color-2';\n        this.columnDefs.push({\n          groupId: `${ColumnVarType.TargetStatic}`,\n          headerTooltip: 'Double click to expand or collapse this section.',\n          headerName: 'Location',\n          headerClass: 'bg-header-group-color-2',\n          suppressMovable: true,\n          marryChildren: true,\n          openByDefault: !this.groupCollapsed[1].includes(this.selectedVals.profId),\n          children: [...this.gridColumns.commonCols, this.gridColumns.hiddenCol]\n        });\n        // add info and dynamic cols for target\n        let infoCols = [];\n        let targetCols = [];\n        this.dynamicColumns.forEach(col => {\n          // update static cols props\n          if (col.type === ColumnVarType.TargetStatic) {\n            let staticCol = this.gridColumns.commonCols.find(sc => sc.field === col.name.toLowerCase());\n            if (staticCol) {\n              staticCol.colId = col.id;\n              staticCol.headerName = col.displayName || col.name;\n              staticCol.headerTooltip = col.description;\n              staticCol.headerComponentParams = {\n                enableEdit: this.isAdmin,\n                isRequired: col.isRequired,\n                colType: col.fieldType,\n                showInfoIcon: col.description?.length > 0,\n                onColumnEdit: e => {\n                  this.colModalComponent.column = Object.assign({}, col);\n                  this.colModalComponent.columns = this.dynamicColumns.filter(c => c.type === ColumnVarType.TargetStatic);\n                  this.colModalComponent.open(e, 'static');\n                }\n              };\n              // Note: Removed automatic override to SelectMultiple - respects user configuration\n            }\n            // add national value to Region col if it's selected\n            if (col.name === 'Region' && col.conditionsApplied?.indexOf('showNational') > -1) {\n              // do not add if region for the project is restricted\n              if (!filters.project?.regions?.length) {\n                staticCol.cellEditorParams.values = [{\n                  id: 0,\n                  name: 'National'\n                }, ...staticCol.cellEditorParams.values];\n              }\n            }\n            // add to conditions columns\n            //this.conditionsCols.push(col);\n            return;\n          }\n          // add dynamic column\n          let colDef = {\n            colId: `${col.id}`,\n            field: 'colVals.dCol' + col.id,\n            headerName: col.displayName || col.name,\n            headerTooltip: col.description,\n            headerComponent: GridCustomHeader,\n            headerComponentParams: {\n              enableEdit: this.isAdmin,\n              isRequired: col.isRequired,\n              type: col.type,\n              colType: col.fieldType,\n              colOrder: col.order,\n              showInfoIcon: col.description?.length > 0,\n              onColumnEdit: (e, showMenu) => {\n                // set column on child\n                this.colModalComponent.column = Object.assign({}, col);\n                // provide cosl for condition's component\n                this.colModalComponent.columns = this.conditionsCols.filter(c => [ColumnVarType.TargetInfo, ColumnVarType.Target].includes(c.type));\n                this.colModalComponent.open(e, `${col.id}`, showMenu);\n              }\n            },\n            cellEditor: this.gridService.getCellEditor(col.fieldType),\n            cellEditorParams: {\n              ...this.gridService.getCellEditorParams(col),\n              onComment: (colDef, node, ctxMenu, isNote, readOnly) => {\n                this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\n              },\n              onCellLink: (colId, node) => {\n                if (this.selectedVals.dataType === 'Progress') this.generateLink({\n                  activityId: node.data.id,\n                  activityProgressId: node.data.progressId,\n                  columnId: colId\n                });else this.generateLink({\n                  targetId: node.data.id,\n                  columnId: colId\n                });\n              }\n            },\n            columnGroupShow: 'open',\n            editable: params => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\n            valueFormatter: this.gridService.getValueFormatter,\n            cellRenderer: this.gridService.getCellRenderer(col.fieldType),\n            cellClassRules: {\n              'text-end': this.gridService.getCellTextAlign,\n              'text-danger': this.gridService.getCellClassRules\n            },\n            enableValue: this.gridService.getEnableGrouping(col.fieldType),\n            enableRowGroup: this.gridService.getEnableGrouping(col.fieldType),\n            aggFunc: this.gridService.getAggFunc(col.fieldType),\n            lockPinned: true,\n            lockVisible: true,\n            suppressMovable: !this.isAdmin,\n            width: getColWidth(col.name + (col.isRequired ? '*' : ''), [this.isAdmin, col.description?.length > 0])\n          };\n          // attach cell click for attachment columns\n          if (col.fieldType === ColDataType.Attachment) {\n            colDef.onCellClicked = event => this.onCellClicked(event);\n            colDef.comparator = this.gridService.getNumComparator;\n          }\n          // attach num and date comparators\n          if (col.fieldType >= ColDataType.Number && col.fieldType <= ColDataType.GPS) {\n            colDef.comparator = this.gridService.getNumComparator;\n            colDef.valueGetter = params => this.gridService.getNumericValue(col.id, params);\n            colDef.valueParser = params => parseFloat(params.newValue);\n          }\n          if (col.fieldType === ColDataType.Date) {\n            //colDef.cellEditorParams.format = 'dd MMM yyyy';\n            colDef.filterParams = {\n              valueFormatter: this.gridService.getFilterValueFormatter\n            };\n            colDef.comparator = this.gridService.getDateComparator;\n          }\n          if (col.type === ColumnVarType.TargetInfo) {\n            colDef.headerClass = 'bg-header-color-3';\n            infoCols.push(colDef);\n          } else if (col.type === ColumnVarType.Target) {\n            colDef.headerClass = 'bg-header-color-4';\n            targetCols.push(colDef);\n          }\n          // add to conditions columns\n          this.conditionsCols.push(col);\n        });\n        // sort dynamic cols by order and push to grid's columns\n        infoCols.sort((x, y) => x.headerComponentParams.colOrder > y.headerComponentParams.colOrder ? 1 : -1);\n        targetCols.sort((x, y) => x.headerComponentParams.colOrder > y.headerComponentParams.colOrder ? 1 : -1);\n        if (targetCols.length)\n          // make first col always visible in the group\n          targetCols[0].columnGroupShow = null;\n        // add add new column if admin\n        if (this.isAdmin) {\n          let fnOnColAdd = e => {\n            this.colModalComponent.column = new DynamicColumn(0, filters.profId, '', ColumnVarType.TargetInfo, infoCols.length, 0);\n            // provide cols for condition's component\n            this.colModalComponent.columns = this.conditionsCols.filter(c => [ColumnVarType.TargetInfo, ColumnVarType.Target].includes(c.type));\n            this.colModalComponent.open(e);\n          };\n          let addNewCol = {\n            ...this.addNewColumn,\n            headerComponentParams: {\n              addNewCol: true,\n              onColumnEdit: fnOnColAdd\n            }\n          };\n          addNewCol.headerClass = 'bg-header-color-3';\n          infoCols.push(addNewCol);\n          fnOnColAdd = e => {\n            this.colModalComponent.column = new DynamicColumn(0, filters.profId, '', ColumnVarType.Target, targetCols.length, 0);\n            // provide cols for condition's component\n            this.colModalComponent.columns = this.conditionsCols.filter(c => [ColumnVarType.TargetInfo, ColumnVarType.Target].includes(c.type));\n            this.colModalComponent.open(e);\n          };\n          addNewCol = {\n            ...this.addNewColumn,\n            headerComponentParams: {\n              addNewCol: true,\n              onColumnEdit: fnOnColAdd.bind({})\n            }\n          };\n          addNewCol.headerClass = 'bg-header-color-4';\n          targetCols.push(addNewCol);\n        }\n        // if dynamic info cols are there and info group is collapsed\n        if (infoCols.length && this.groupCollapsed[2]) this.gridColumns.hiddenCol.hide = false;\n        let hiddenCol = Object.assign({}, this.gridColumns.hiddenCol);\n        hiddenCol.headerClass = 'bg-header-color-3';\n        this.columnDefs.push({\n          groupId: `${ColumnVarType.TargetInfo}`,\n          headerTooltip: 'Double click to expand or collapse this section.',\n          headerName: 'Info',\n          headerClass: 'bg-header-group-color-3',\n          suppressMovable: true,\n          marryChildren: true,\n          openByDefault: !this.groupCollapsed[2].includes(this.selectedVals.profId),\n          children: [...this.gridColumns.targetInfoCols, hiddenCol, ...infoCols]\n        });\n        hiddenCol = Object.assign({}, this.gridColumns.hiddenCol);\n        hiddenCol.headerClass = 'bg-header-color-4';\n        this.columnDefs.push({\n          groupId: `${ColumnVarType.Target}`,\n          headerTooltip: 'Double click to expand or collapse this section.',\n          headerName: 'Target',\n          headerClass: 'bg-header-group-color-4',\n          suppressMovable: true,\n          marryChildren: true,\n          openByDefault: !this.groupCollapsed[3].includes(this.selectedVals.profId),\n          children: targetCols.length > (this.isAdmin ? 2 : 1) ? [...targetCols, hiddenCol] : [...targetCols]\n        });\n      }\n      this.gridReady();\n      this.working = false;\n    } catch (ex) {\n      console.error(ex);\n      this.messageService.error('Something went wrong.');\n    }\n  }\n  // init'ed and ready\n  onGridReady(params) {\n    this.gridApi = params.api;\n    this.gridColumnApi = params.columnApi;\n  }\n  gridReady() {\n    // bind to info group expand icon\n    setTimeout(() => {\n      // register click event on top left header cell\n      const headerCell = document.querySelector('.ag-header-cell[col-id=\"rowNum\"]');\n      if (headerCell) {\n        headerCell.addEventListener('click', e => {\n          //this.gridApi.selectAll();\n          this.gridApi.selectAllOnCurrentPage();\n        });\n      }\n      // register click event info column group collapase button\n      const colGroupRow = document.querySelector('.ag-header-container .ag-header-row-column-group');\n      //colGroupRow.setAttribute('title', 'Double click to expand or collapse this section.');\n      colGroupRow.addEventListener('click', e => this.storeGroupState(e));\n      colGroupRow.addEventListener('dblclick', e => this.storeGroupState(e, true));\n      const groupExpandIcons = document.querySelectorAll('.ag-header-group-cell .ag-header-icon.ag-header-expand-icon');\n      if (groupExpandIcons) {\n        groupExpandIcons.forEach(icon => {\n          icon.setAttribute('title', icon.classList.contains('ag-header-expand-icon-expanded') ? 'Collapse columns group' : 'Expand columns group');\n        });\n      }\n      // register an even to capture column id for getting column's position index on the grid when a column start being moved\n      if (this.isAdmin) {\n        document.querySelector('.ag-header')?.addEventListener('mousedown', e => {\n          if (e.target.hasAttribute('col-id') || e.target.closest('.ag-header-cell')) {\n            const colId = e.target.getAttribute('col-id') || e.target.closest('.ag-header-cell')?.getAttribute('col-id');\n            if (colId) {\n              this.movingColPos = this.gridColumnApi.getAllGridColumns().findIndex(c => c.getColId() === colId);\n            }\n          }\n        });\n      }\n      // scroll grid even if cursor is outide the viewport (i.e. in header)\n      const gridHeader = document.querySelector('.ag-header-viewport');\n      const gridViewPort = document.querySelector('.ag-body-viewport');\n      const gridViewPortCenter = document.querySelector('.ag-center-cols-viewport');\n      gridHeader.addEventListener('wheel', e => {\n        e.preventDefault();\n        const deltaY = e.deltaY || e.wheelDeltaY;\n        const deltaX = e.deltaX || e.wheelDeltaX;\n        gridViewPort.scrollTop += deltaY;\n        gridViewPortCenter.scrollLeft += deltaX;\n      });\n      this.setExcelExportParams();\n    }, 500);\n  }\n  storeGroupState(e, dblClick) {\n    let colId;\n    const target = e.target;\n    let group = dblClick ? target.querySelector('.ag-header-group-text') : target.parentElement.parentElement.querySelector('.ag-header-group-text');\n    if (!group) return;\n    if (group.innerText.startsWith('Status')) colId = 1;else if (group.innerText.startsWith('Location')) colId = 2;else if (group.innerText.startsWith('Info')) colId = 3;else colId = 4;\n    const storeName = `${this.selectedVals.dataType !== 'Progress' ? 't' : ''}group${colId}Collapsed`;\n    let stored = JSON.parse(localStorage.getItem(storeName) || '[]');\n    if (stored.includes(this.selectedVals.profId)) stored = stored.filter(i => i !== this.selectedVals.profId);else stored.push(this.selectedVals.profId);\n    localStorage.setItem(storeName, JSON.stringify(stored));\n    this.groupCollapsed[colId - 1] = stored;\n    // expand/collapse group\n    //if (!target.classList.contains('ag-icon')) {\n    //    console.log('trying to double click')\n    //    this.gridApi.column\n    //}\n  }\n\n  onColumnMoved(event) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!event.column) return;\n      if (event.finished) {\n        if (_this2.moveByGrid) {\n          _this2.moveByGrid = false;\n          return;\n        }\n        try {\n          const allCols = event.columnApi.getAllGridColumns();\n          const colIdAtPos = allCols[event.toIndex + 1]?.getColId();\n          const isNewColBefore = event.toIndex === allCols.length - 1 || allCols[event.toIndex - 1]?.getColId() === 'new';\n          const restrictedCols = ['asOf', 'year', 'qtr'];\n          // if to the right is a restrict col, or to left is new col\n          if (restrictedCols.includes(colIdAtPos) || isNewColBefore) {\n            _this2.moveByGrid = true;\n            event.columnApi.moveColumnByIndex(event.toIndex, _this2.movingColPos);\n            _this2.messageService.info('Not allowed to move here. Moved back to its place.');\n          } else {\n            // update column order in arrays here and in parent after updated on the server\n            let colsToOrder = [];\n            const movedColType = +event.column.getColDef().headerComponentParams.type;\n            // get cols of the same type and intervention in the order it appears in grid after move\n            // update column definitions first\n            let gridGroupCols = [];\n            for (let i = 0; i < _this2.columnDefs.length; i++) {\n              if (_this2.columnDefs[i]['groupId'] && _this2.columnDefs[i]['groupId'] === `${movedColType}`) {\n                const group = _this2.columnDefs[i];\n                gridGroupCols = group.children;\n                break;\n              }\n            }\n            // now, update grid columns rendered\n            let gOrder = 0;\n            for (let i = 0; i < allCols.length; i++) {\n              const colDef = allCols[i].getColDef();\n              if (colDef.headerComponentParams?.type === undefined) continue;\n              if (+colDef.headerComponentParams.type === movedColType) {\n                gOrder++;\n                colDef.headerComponentParams.colOrder = gOrder; // update colDef order on grid\n                gridGroupCols.find(c => c.colId === colDef.colId).headerComponentParams.colOrder = gOrder;\n                colsToOrder.push(+colDef.colId);\n              }\n            }\n            gridGroupCols.sort((x, y) => {\n              if (!x.headerComponentParams || !y.headerComponentParams) return 0;\n              return x.headerComponentParams.colOrder > y.headerComponentParams.colOrder ? 1 : -1;\n            });\n            // update on the server\n            if (colsToOrder.length) {\n              if (!(yield _this2.colModalComponent.saveColumnsOrder(colsToOrder))) return;\n              // saved on the server, now order all the arrays in the order of colsToOrder by modifying colOrder\n              for (let i = 0; i < colsToOrder.length; i++) {\n                _this2.dynamicColumns.find(c => c.id === colsToOrder[i]).order = i + 1;\n                _this2.conditionsCols.find(c => c.id === colsToOrder[i]).order = i + 1;\n              }\n              // sort it as per the modified order\n              let cols = _this2.dynamicColumns.filter(c => c.type === movedColType);\n              cols.sort((x, y) => x.order > y.order ? 1 : -1);\n              _this2.conditionsCols.sort((x, y) => x.type === movedColType && y.type === movedColType && x.order > y.order ? 1 : -1);\n              // notify parent to update itself\n              _this2.colDone.emit(cols);\n            }\n          }\n        } catch (ex) {\n          console.error(ex);\n          _this2.messageService.error('Something went wrong.');\n        }\n      }\n    })();\n  }\n  onCellClicked(event) {\n    const colId = event.column.getColId();\n    if (colId === 'rowNum') {\n      // if the row number column was clicked\n      //event.api.deselectAll();                // Deselect all other cells\n      //event.node.setSelected(true);           // Select the entire row\n      const cols = event.columnApi.getColumns().map(c => c.getColId());\n      let lastVisibleCol = cols[cols.length - 1];\n      if (lastVisibleCol.startsWith('cols-hidden')) {\n        for (let i = cols.length - 2; i >= 0; i--) {\n          if (!cols[i].startsWith('cols-hidden')) {\n            lastVisibleCol = cols[i];\n            break;\n          }\n        }\n      }\n      event.api.clearRangeSelection();\n      event.api.addCellRange({\n        columnStart: cols[1],\n        columnEnd: lastVisibleCol,\n        rowStartIndex: event.rowIndex,\n        rowEndIndex: event.rowIndex\n      });\n    } else {\n      if (!event.value && !event.column.isCellEditable(event.node)) return;\n      if (event.data.id > 0 && (event.data.activityId || event.data.uniqueId))\n        // only when record is saved\n        this.attachmentModalComponent.ngOnInit(colId, event);\n    }\n  }\n  onCellFocused(event) {\n    this.gridApi.deselectAll();\n  }\n  onCellValueChanged(event) {\n    if (event.data.region?.length || event.data.province?.length || event.data.district?.length || event.data.community?.length) {\n      event.data.dirty = true; // mark row as dirty\n    } else event.data.dirty = false;\n    if (event.newValue && this.invalidCells.length) {\n      // remove if marked as invalid cells\n      if (this.invalidCells.findIndex(invCell => invCell.rowDataId === event.data.id && invCell.field === event.column.getColDef().field) > -1) this.invalidCells = this.invalidCells.filter(invCell => invCell.rowDataId !== event.data.id && invCell.field !== event.column.getColDef().field);\n      this.gridApi.refreshCells({\n        rowNodes: [event.node],\n        columns: [event.column]\n      });\n    } else {\n      // get all columns with conditions related to this cell's column being changed, so cell val is cleared if condition(s) evaluates to disable col\n      let relColsWithConds = [];\n      this.gridColumnApi.getColumns().forEach(col => {\n        let conds = col.getColDef().cellEditorParams?.conditions;\n        if (conds?.length) {\n          conds = conds.filter(c => ['enable', 'disable'].includes(c.type) && c.expLeft == event.colDef.colId);\n          if (conds.length) relColsWithConds.push(col.getColId());\n        }\n      });\n      if (relColsWithConds.length) this.gridApi.refreshCells({\n        columns: relColsWithConds,\n        rowNodes: [event.node],\n        force: true,\n        suppressFlash: true\n      });\n    }\n    this.cellChanged.emit(event.rowIndex); // emit changes to parent for pending save\n  }\n\n  onFilterApplied(event) {\n    const col = event.columns[0].getColDef().headerName;\n    this.filterApplied.emit(event.columns[0].isFilterActive() ? col : '-' + col);\n  }\n  onSortChanged(event) {\n    let colsOnSort = 0;\n    this.gridColumnApi.getColumns().forEach(c => {\n      if (c.getSort()) colsOnSort++;\n    });\n    this.sortApplied.emit(colsOnSort);\n  }\n  onColumnRowGroupChanged(event) {\n    const colId = event.column?.getColId();\n    if (colId) event.columnApi.applyColumnState({\n      state: [{\n        colId: colId,\n        hide: false\n      }]\n    });\n    if (!this.gridColumnApi.getRowGroupColumns().length) this.gridApi.refreshHeader();\n  }\n  // -----------------------------------------------------------\n  // --- DynamicColumn\n  onColumnDone(col) {\n    try {\n      // get reference to grid's columns'\n      let gridStaticCol;\n      let gridGroupCols = [];\n      for (let i = 0; i < this.columnDefs.length; i++) {\n        if (this.columnDefs[i]['groupId'] && this.columnDefs[i]['groupId'] === `${col.type}`) {\n          const group = this.columnDefs[i];\n          if (col.type === ColumnVarType.TargetStatic || col.type === ColumnVarType.ProgressStatic) {\n            gridStaticCol = group.children.find(c => c['colId'] === col.id);\n            if (gridStaticCol) break;\n          } else {\n            gridGroupCols = group.children;\n            break;\n          }\n        }\n      }\n      // if deleted\n      if (col.id < 0) {\n        // remove column from grid\n        const delInd = gridGroupCols.findIndex(c => c.colId == `${col.id * -1}`);\n        gridGroupCols.splice(delInd, 1);\n        this.gridApi.setColumnDefs(this.columnDefs);\n        // remove from cols arrays\n        this.dynamicColumns = this.dynamicColumns.filter(c => c.id !== col.id * -1);\n        this.conditionsCols = this.conditionsCols.filter(c => c.id !== col.id * -1);\n        // remove from variables in the parent's profile arrays\n        this.colDone.emit([col]); // emitted with -1 as id included\n      } else {\n        // find the col in columns since it's already in grid\n        let colInd = this.dynamicColumns.findIndex(c => c.id === col.id);\n        if (colInd > -1) {\n          // is updated\n          // update the col on the grid\n          if (gridStaticCol) {\n            // static columns are only getting updated, niether added nor duplicated\n            gridStaticCol.headerName = col.displayName || col.name;\n            gridStaticCol.headerTooltip = col.description;\n            gridStaticCol.headerComponentParams.isRequired = col.isRequired;\n            gridStaticCol.headerComponentParams.colType = col.fieldType;\n            gridStaticCol.headerComponentParams.showInfoIcon = col.description?.length > 0 || false;\n          } else {\n            const gColInd = gridGroupCols.findIndex(gc => gc.colId == `${col.id}`);\n            if (gColInd > -1) {\n              gridGroupCols[gColInd].headerName = col.displayName || col.name;\n              gridGroupCols[gColInd].headerTooltip = col.description;\n              gridGroupCols[gColInd].headerComponentParams.isRequired = col.isRequired;\n              gridGroupCols[gColInd].headerComponentParams.colType = col.fieldType;\n              gridGroupCols[gColInd].headerComponentParams.showInfoIcon = col.description?.length > 0 || false;\n              gridGroupCols[gColInd].cellEditor = this.gridService.getCellEditor(col.fieldType);\n              gridGroupCols[gColInd].cellEditorParams = {\n                ...this.gridService.getCellEditorParams(col),\n                onComment: (colDef, node, ctxMenu, isNote, readOnly) => {\n                  this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\n                },\n                onCellLink: (colId, node) => {\n                  if (this.selectedVals.dataType === 'Progress') this.generateLink({\n                    activityId: node.data.id,\n                    activityProgressId: node.data.progressId,\n                    columnId: colId\n                  });else this.generateLink({\n                    targetId: node.data.id,\n                    columnId: colId\n                  });\n                }\n              };\n              gridGroupCols[gColInd].editable = params => this.gridService.getCellEditable(params, this.defaultColDefs.editable);\n              gridGroupCols[gColInd].cellRenderer = this.gridService.getCellRenderer(col.fieldType);\n              gridGroupCols[gColInd].enableRowGroup = this.gridService.getEnableGrouping(col.fieldType);\n              gridGroupCols[gColInd].enableValue = this.gridService.getEnableGrouping(col.fieldType);\n              gridGroupCols[gColInd].aggFunc = this.gridService.getAggFunc(col.fieldType);\n              gridGroupCols[gColInd].width = getColWidth(col.name + (col.isRequired ? '*' : ''), [this.isAdmin, col.description?.length > 0]);\n              // attach cell click for attachment columns\n              if (col.fieldType === ColDataType.Attachment) gridGroupCols[gColInd].onCellClicked = event => this.onCellClicked(event);\n            }\n          }\n          // update changed props only\n          this.dynamicColumns[colInd].name = col.name;\n          this.dynamicColumns[colInd].displayName = col.displayName;\n          this.dynamicColumns[colInd].description = col.description;\n          this.dynamicColumns[colInd].fieldType = col.fieldType;\n          this.dynamicColumns[colInd].fieldTypeValues = col.fieldTypeValues;\n          this.dynamicColumns[colInd].isRequired = col.isRequired;\n          this.dynamicColumns[colInd].conditionsApplied = col.conditionsApplied;\n          // update conditions array as well\n          colInd = this.conditionsCols.findIndex(c => c.id === col.id);\n          if (colInd > -1) {\n            // update only necessary props\n            this.conditionsCols[colInd].name = col.name;\n            this.conditionsCols[colInd].displayName = col.displayName;\n            this.conditionsCols[colInd].fieldType = col.fieldType;\n            this.conditionsCols[colInd].fieldTypeValues = col.fieldTypeValues;\n          }\n          // set defs to grid and auto-size it\n          this.gridApi.setColumnDefs(this.columnDefs);\n          this.gridApi.refreshHeader();\n          this.gridColumnApi.autoSizeColumn(`${col.id}`);\n          // update the col in the parent's profile vars\n          this.colDone.emit([col]);\n        } else {\n          // is added or duplicated\n          // fix the col order\n          if (col.order < 0) col.order *= -1;\n          this.dynamicColumns.forEach(c => {\n            if (c.type === col.type && c.order >= col.order) c.order = c.order++;\n          });\n          // add to the cols arrays\n          this.dynamicColumns.push(col);\n          this.conditionsCols.push(col);\n          // add to the grid at the position in order\n          let condColTypes = [];\n          if (col.type === ColumnVarType.Target || col.type === ColumnVarType.TargetInfo) condColTypes = [ColumnVarType.TargetInfo, ColumnVarType.Target];else condColTypes = [ColumnVarType.ProgressInfo, ColumnVarType.Progress];\n          for (let i = 0; i < gridGroupCols.length; i++) {\n            if (gridGroupCols[i].headerComponentParams?.colOrder >= col.order) gridGroupCols[i].headerComponentParams.colOrder++;\n          }\n          // add to the end\n          let colDef = {\n            colId: `${col.id}`,\n            field: 'colVals.dCol' + col.id,\n            headerName: col.displayName || col.name,\n            headerTooltip: col.description,\n            headerComponent: GridCustomHeader,\n            headerComponentParams: {\n              enableEdit: true,\n              isRequired: col.isRequired,\n              type: col.type,\n              colType: col.fieldType,\n              colOrder: col.order,\n              showInfoIcon: col.description?.length > 0,\n              onColumnEdit: e => {\n                // set column on child\n                this.colModalComponent.column = Object.assign({}, col);\n                // provide cols for condition's component\n                this.colModalComponent.columns = this.conditionsCols.filter(c => condColTypes.includes(c.type));\n                this.colModalComponent.open(e, `${col.id}`);\n              }\n            },\n            cellEditor: this.gridService.getCellEditor(col.fieldType),\n            cellEditorParams: {\n              ...this.gridService.getCellEditorParams(col),\n              onComment: (colDef, node, ctxMenu, isNote, readOnly) => {\n                this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\n              },\n              onCellLink: (colId, node) => {\n                if (this.selectedVals.dataType === 'Progress') this.generateLink({\n                  activityId: node.data.id,\n                  activityProgressId: node.data.progressId,\n                  columnId: colId\n                });else this.generateLink({\n                  targetId: node.data.id,\n                  columnId: colId\n                });\n              }\n            },\n            editable: params => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\n            valueFormatter: this.gridService.getValueFormatter,\n            cellRenderer: this.gridService.getCellRenderer(col.fieldType),\n            cellClassRules: {\n              'text-end': this.gridService.getCellTextAlign,\n              'text-danger': this.gridService.getCellClassRules\n            },\n            width: getColWidth(col.name + (col.isRequired ? '*' : ''), [this.isAdmin, col.description?.length > 0]),\n            enableValue: this.gridService.getEnableGrouping(col.fieldType),\n            enableRowGroup: this.gridService.getEnableGrouping(col.fieldType),\n            aggFunc: this.gridService.getAggFunc(col.fieldType),\n            lockPinned: true,\n            lockVisible: true,\n            suppressMovable: false,\n            columnGroupShow: gridGroupCols.length > 0 ? 'open' : null\n          };\n          if (col.type === ColumnVarType.ProgressInfo || col.type === ColumnVarType.TargetInfo) colDef.headerClass = 'bg-header-color-3';else colDef.headerClass = 'bg-header-color-4';\n          // attach cell click for attachment columns\n          if (col.fieldType === ColDataType.Attachment) colDef.onCellClicked = event => this.onCellClicked(event);\n          gridGroupCols.push(colDef);\n          // now sort it to maintain order\n          gridGroupCols.sort((x, y) => {\n            if (y.colId.startsWith('new')) return -1;\n            if (!x.headerComponentParams?.colOrder || !y.headerComponentParams?.colOrder) return 1;\n            if (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) return 1;\n            return -1;\n          });\n          // set col defs to grid and auto-size it\n          this.gridApi.setColumnDefs(this.columnDefs);\n          this.gridColumnApi.autoSizeColumn(String(col.id)); // s(gridGroupCols.map(c => c.colId));\n          // add to the profile vars in the parent, emit all cols of this type\n          // filter and sort it before emitting to parent\n          let cols = this.dynamicColumns.filter(c => c.type === col.type);\n          cols.sort((x, y) => x.order > y.order ? 1 : -1);\n          this.colDone.emit(cols);\n        }\n      }\n    } catch (ex) {\n      console.error(ex);\n      this.messageService.error('Something went wrong.');\n    }\n  }\n  // *** DATA\n  refreshGridRows(data, enableEditing) {\n    this.defaultColDefs.editable = enableEditing;\n    this.gridApi.setDefaultColDef(this.defaultColDefs);\n    // auto fit dynamic cols: NOT(ColVarType.TargetStatic or ColVarType.ProgressStatic)\n    // this.gridColumnApi.autoSizeColumns(this.dynamicColumns.filter(c => ![3, 5].includes(c.type)).map(c => `${c.id}`));\n    this.invalidCells = [];\n    this.rowData = [...data];\n    for (let i = 1; i <= 20; i++)\n    // append extra rows\n    this.rowData.push({\n      id: 0,\n      colVals: {},\n      dirty: false\n    });\n    // fetch communities for the data\n    if (this.selectedVals.dataType === 'Progress') {\n      const commCol = this.gridColumnApi.getColumns().find(c => c.getColDef().field === 'community');\n      if (data.length) {\n        let allSelProvs = data.map(d => d.province).join(';').replace(/,/g, ';');\n        const provIds = [...new Set(allSelProvs.split(';'))];\n        allSelProvs = provIds.join(';');\n        if (this.gridService.communities.length) {\n          allSelProvs = '';\n          for (let i = 0; i < provIds.length; i++) {\n            if (this.gridService.communities.findIndex(c => c.provinceId === +provIds[i]) === -1) allSelProvs += provIds[i] + ';';\n          }\n          allSelProvs = allSelProvs.length ? allSelProvs.substring(0, allSelProvs.length - 1) : '';\n        }\n        if (allSelProvs.length) {\n          this.gridService.getCommunities(allSelProvs).then(res => {\n            if (res) {\n              // refresh community column\n              commCol.getColDef().cellEditorParams.values = [...this.gridService.communities];\n              this.gridApi.refreshCells({\n                columns: [commCol.getColId()],\n                force: true,\n                suppressFlash: true\n              });\n            }\n          });\n          return;\n        }\n      }\n      if (commCol) {\n        // refresh community column\n        commCol.getColDef().cellEditorParams.values = [...this.gridService.communities];\n        this.gridApi.refreshCells({\n          columns: [commCol.getColId()],\n          force: true,\n          suppressFlash: true\n        });\n      }\n    }\n  }\n  setExcelExportParams() {\n    let fName = `AIMS3_${this.selectedVals.org}_${this.selectedVals.prof}_`;\n    fName += this.selectedVals.proj.replace(/[<>:\"\\/\\\\|?*]+/g, '');\n    fName += this.selectedVals.dataType === 'Progress' ? '_Progress.xlsx' : '_Target.xlsx';\n    let cols = this.gridColumnApi.getColumns().map(c => c.getColId());\n    cols = cols.filter(c => !c.startsWith('new') && !c.startsWith('cols-hidden'));\n    // set excel export params\n    this.gridOptions.defaultExcelExportParams = {\n      author: 'AIMS 3.0',\n      fileName: fName,\n      allColumns: true,\n      sheetName: this.selectedVals.prof.replace(/[<>:\"\\/\\\\|?*]+/g, ''),\n      columnKeys: cols\n    };\n  }\n  onAttached(e) {\n    const rowData = this.rowData.find(r => r.id === e.id);\n    if (e.colId > 0) rowData.colVals['dCol' + e.colId] += e.emitVal;else rowData['file'] += e.emitVal;\n    this.gridApi.refreshCells();\n  }\n  onComment(e) {\n    const node = this.gridApi.getRowNode(e.node.id);\n    if (!node.data.comments) node.data.comments = [];\n    if (e.updated) {\n      const comment = node.data.comments.find(c => c.commentId === e.commentId);\n      if (comment) comment.isResolved = !comment.isResolved;\n    } else if (e.deleted) node.data.comments = node.data.comments.filter(c => c.commentId !== e.commentId);else {\n      node.data.comments = [...node.data.comments, {\n        id: e.isProgress ? node.data.progressId : node.data.id,\n        commentId: e.commentId,\n        colId: e.colId,\n        isNote: e.isNote,\n        isResolved: e.isResolved\n      }];\n    }\n    this.gridApi.refreshCells({\n      rowNodes: [node],\n      force: true,\n      suppressFlash: true\n    });\n  }\n  generateLink(params) {\n    // format: {OrgId}S{ProfId}a{ProjId}b{ActOrTarId}a{Month-Year}n{ColId}\n    let link = `${this.selectedVals.orgId}S${this.selectedVals.profId}`;\n    link += `a${this.selectedVals.projId}`;\n    if (!params.commentOn) {\n      const colMap = this.commentModalComponent.staticColMap.find(c => c.colId === params.columnId);\n      if (colMap) params.columnId = colMap.dColId;else params.columnId = +params.columnId;\n    }\n    if (params.activityId > 0) {\n      link += `b${params.activityId}`;\n      if (params.activityProgressId > 0) link += `a${this.selectedVals.month}-${this.selectedVals.year}`;else link += `aMY`;\n    } else link += `b${params.targetId}a0`;\n    link += `n${params.columnId}`;\n    link = (params.commentOn ? '#comment=' : '#cell=') + link;\n    navigator.clipboard.writeText(location.origin + location.pathname + link);\n    this.messageService.info('Link copied to your clipboard.', 'Link Created');\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sb => sb.unsubscribe());\n  }\n  static #_ = this.ɵfac = function GridComponent_Factory(t) {\n    return new (t || GridComponent)(i0.ɵɵdirectiveInject(i1.GridService), i0.ɵɵdirectiveInject(i2.MessageService));\n  };\n  static #_2 = this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n    type: GridComponent,\n    selectors: [[\"aims-grid\"]],\n    viewQuery: function GridComponent_Query(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵviewQuery(DynamicColumnFormComponent, 5);\n        i0.ɵɵviewQuery(AttachmentsFormComponent, 5);\n        i0.ɵɵviewQuery(CommentComponent, 5);\n      }\n      if (rf & 2) {\n        let _t;\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.colModalComponent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.attachmentModalComponent = _t.first);\n        i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.commentModalComponent = _t.first);\n      }\n    },\n    outputs: {\n      colDone: \"colDone\",\n      cellChanged: \"cellChanged\",\n      filterApplied: \"filterApplied\",\n      sortApplied: \"sortApplied\"\n    },\n    decls: 6,\n    vars: 13,\n    consts: [[4, \"ngIf\"], [2, \"width\", \"100%\", \"height\", \"100%\", 3, \"ngClass\", \"gridOptions\", \"columnDefs\", \"defaultColDef\", \"components\", \"rowData\", \"animateRows\", \"tooltipShowDelay\", \"cellValueChanged\", \"gridReady\", \"columnMoved\", \"cellFocused\", \"filterChanged\", \"sortChanged\", \"columnRowGroupChanged\"], [\"aimsGrid\", \"\"], [\"id\", \"columnForm\", 3, \"done\"], [\"id\", \"attachmentModal\", 3, \"done\"], [\"id\", \"commentForm\", 3, \"done\", \"genLink\", 4, \"ngIf\"], [\"id\", \"commentForm\", 3, \"done\", \"genLink\"]],\n    template: function GridComponent_Template(rf, ctx) {\n      if (rf & 1) {\n        i0.ɵɵtemplate(0, GridComponent_aims_working_0_Template, 1, 0, \"aims-working\", 0);\n        i0.ɵɵelementStart(1, \"ag-grid-angular\", 1, 2);\n        i0.ɵɵlistener(\"cellValueChanged\", function GridComponent_Template_ag_grid_angular_cellValueChanged_1_listener($event) {\n          return ctx.onCellValueChanged($event);\n        })(\"gridReady\", function GridComponent_Template_ag_grid_angular_gridReady_1_listener($event) {\n          return ctx.onGridReady($event);\n        })(\"columnMoved\", function GridComponent_Template_ag_grid_angular_columnMoved_1_listener($event) {\n          return ctx.onColumnMoved($event);\n        })(\"cellFocused\", function GridComponent_Template_ag_grid_angular_cellFocused_1_listener($event) {\n          return ctx.onCellFocused($event);\n        })(\"filterChanged\", function GridComponent_Template_ag_grid_angular_filterChanged_1_listener($event) {\n          return ctx.onFilterApplied($event);\n        })(\"sortChanged\", function GridComponent_Template_ag_grid_angular_sortChanged_1_listener($event) {\n          return ctx.onSortChanged($event);\n        })(\"columnRowGroupChanged\", function GridComponent_Template_ag_grid_angular_columnRowGroupChanged_1_listener($event) {\n          return ctx.onColumnRowGroupChanged($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(3, \"column-form-modal\", 3);\n        i0.ɵɵlistener(\"done\", function GridComponent_Template_column_form_modal_done_3_listener($event) {\n          return ctx.onColumnDone($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵelementStart(4, \"attachments-modal\", 4);\n        i0.ɵɵlistener(\"done\", function GridComponent_Template_attachments_modal_done_4_listener($event) {\n          return ctx.onAttached($event);\n        });\n        i0.ɵɵelementEnd();\n        i0.ɵɵtemplate(5, GridComponent_comment_modal_5_Template, 1, 0, \"comment-modal\", 5);\n      }\n      if (rf & 2) {\n        i0.ɵɵproperty(\"ngIf\", ctx.working);\n        i0.ɵɵadvance(1);\n        i0.ɵɵproperty(\"ngClass\", i0.ɵɵpureFunction2(10, _c0, !ctx.isDarkMode, ctx.isDarkMode))(\"gridOptions\", ctx.gridOptions)(\"columnDefs\", ctx.columnDefs)(\"defaultColDef\", ctx.defaultColDefs)(\"components\", ctx.components)(\"rowData\", ctx.rowData)(\"animateRows\", true)(\"tooltipShowDelay\", ctx.tooltipShowDelay);\n        i0.ɵɵadvance(4);\n        i0.ɵɵproperty(\"ngIf\", ctx.rowData.length);\n      }\n    },\n    dependencies: [i3.NgClass, i3.NgIf, i4.WorkingComponent, i5.AgGridAngular, i6.AttachmentsFormComponent, i7.CommentComponent, i8.DynamicColumnFormComponent],\n    styles: [\"/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IiIsInNvdXJjZVJvb3QiOiIifQ== */\"]\n  });\n}", "map": {"version": 3, "names": ["EventEmitter", "LicenseManager", "environment", "ActivityStatus", "ColDataType", "ColumnVarType", "getColWidth", "DynamicColumn", "CommentComponent", "AttachmentsFormComponent", "SabaDateEditor", "SabaInputEditor", "SabaNoneEditor", "SabaSelectEditor", "SabaTextEditor", "Saba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "SabaCheckboxRenderer", "SabaSelectValueRenderer", "GridCustomHeader", "DynamicColumnFormComponent", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵlistener", "GridComponent_comment_modal_5_Template_comment_modal_done_0_listener", "$event", "ɵɵrestoreView", "_r4", "ctx_r3", "ɵɵnextContext", "ɵɵresetView", "onComment", "GridComponent_comment_modal_5_Template_comment_modal_genLink_0_listener", "ctx_r5", "generateLink", "ɵɵelementEnd", "GridComponent", "constructor", "gridService", "messageService", "working", "isDarkMode", "isAdmin", "components", "tooltipShowDelay", "groupCollapsed", "gridColumns", "commonCols", "progressActIdCol", "progressStatusCols", "progressCommCol", "progressAsOfCol", "targetInfoCols", "hiddenCol", "dynamicColumns", "conditionsCols", "colDone", "cellChanged", "rowData", "regionsFilter", "selectedVals", "dataType", "sn", "orgId", "org", "profId", "prof", "projId", "proj", "month", "year", "subscriptions", "movingColPos", "moveByGrid", "<PERSON><PERSON><PERSON><PERSON>", "filterApplied", "sortApplied", "document", "querySelector", "setLicenseKey", "agGridConfig", "licenseKey", "gridOptions", "groupHeaderHeight", "headerHeight", "rowHeight", "rowDragManaged", "rowSelection", "enableRangeSelection", "enableFillHandle", "undoRedoCellEditing", "undoRedoCellEditingLimit", "scrollbarWidth", "suppressRowClickSelection", "enableCellChangeFlash", "groupDisplayType", "groupDefaultExpanded", "groupInc<PERSON><PERSON><PERSON>er", "groupMaintainOrder", "groupAllowUnbalanced", "rowClassRules", "params", "data", "status", "Completed", "Archived", "Cancelled", "dateApproved", "statusBar", "statusPanels", "statusPanel", "align", "processCellFromClipboard", "onPaste", "processCellForClipboard", "onCellKeyPress", "e", "onDisabledCellEditRequest", "defaultColDefs", "editable", "onCellDoubleClicked", "onCellKeyDown", "clearCellsInRange", "getContextMenuItems", "getContextMenu", "sortable", "resizable", "filter", "min<PERSON><PERSON><PERSON>", "menuTabs", "cellClassRules", "findIndex", "c", "rowDataId", "id", "field", "column", "getColDef", "activityId", "uniqueId", "getColId", "startsWith", "isCellEditable", "node", "getCellCommentClass", "onCellClicked", "event", "target", "closest", "classList", "contains", "cell", "getBoundingClientRect", "y", "top", "x", "right", "commentModalComponent", "open", "colDef", "comparator", "getComparator", "addNewColumn", "colId", "headerName", "headerTooltip", "headerClass", "headerComponent", "cellClass", "lockPinned", "suppressMovable", "width", "max<PERSON><PERSON><PERSON>", "suppressFillHandle", "enableRowGroup", "suppressCellFlash", "suppressMenu", "suppressSizeToFit", "suppressAutoSize", "lockVisible", "ngOnInit", "_this", "columnDefs", "pinned", "lockPosition", "cell<PERSON><PERSON><PERSON>", "footer", "rowIndex", "headerComponentParams", "colType", "Attachment", "showInfoIcon", "cellEditor", "cellEditorParams", "ctxMenu", "isNote", "readOnly", "onCellLink", "activityProgressId", "progressId", "columnId", "targetId", "getCellEditable", "columnGroupShow", "getNumComparator", "isRequired", "SelectSingle", "values", "name", "disabled", "valueFormatter", "getV<PERSON>ueFormatter", "filterParams", "getFilterValueFormatter", "value", "valueSetter", "onValueSet", "Date", "ph", "getDateComparator", "lastDay", "push", "TargetInfo", "Number", "ProgressInfo", "getData", "_ref", "_asyncToGenerator", "getMetaData", "_x", "apply", "arguments", "valueGetter", "regionIds", "region", "split", "length", "result", "regionNames", "for<PERSON>ach", "join", "onCellValueChanged", "onLocationCellChanged", "vA", "vB", "nA", "nB", "desc", "getRegionComparator", "setValue", "SelectMultiple", "_ref2", "_x2", "bind", "getProvinceComparator", "fuzzySearch", "_ref3", "_x3", "getDistrictComparator", "then", "vals", "setTimeout", "gridApi", "refresh<PERSON>ells", "columns", "force", "refreshProvDistGridColumns", "addNewLink", "_ref4", "_x4", "getCommunityComparator", "asOfValueFormatter", "getCellClassRules", "Progress", "years", "currYear", "getFullYear", "i", "colVals", "dirty", "ex", "console", "error", "d", "uId", "onLocationChanged", "gridColumnApi", "cols", "getColumns", "setColDef", "getGroupsState", "storeName", "JSON", "parse", "localStorage", "getItem", "replace", "initGrid", "filters", "stopEditing", "v", "setColumnDefs", "type", "concat", "groupId", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "openByDefault", "includes", "children", "Object", "assign", "ProgressStatic", "infoCols", "progCols", "col", "staticCol", "find", "sc", "toLowerCase", "displayName", "description", "enableEdit", "fieldType", "onColumnEdit", "colModalComponent", "conditionsApplied", "indexOf", "project", "regions", "colOrder", "order", "showMenu", "getCellEditor", "getCellEditorParams", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getCellTextAlign", "getEnableGrouping", "enableValue", "aggFunc", "getAggFunc", "GPS", "getNumericValue", "valueParser", "parseFloat", "newValue", "draft", "sort", "fnOnColAdd", "addNewCol", "TargetStatic", "targetCols", "Target", "hide", "gridReady", "onGridReady", "api", "columnApi", "headerCell", "addEventListener", "selectAllOnCurrentPage", "colGroupRow", "storeGroupState", "groupExpandIcons", "querySelectorAll", "icon", "setAttribute", "hasAttribute", "getAttribute", "getAllGridColumns", "gridHeader", "gridViewPort", "gridViewPortCenter", "preventDefault", "deltaY", "wheelDeltaY", "deltaX", "wheelDeltaX", "scrollTop", "scrollLeft", "setExcelExportParams", "dblClick", "group", "parentElement", "innerText", "stored", "setItem", "stringify", "onColumnMoved", "_this2", "finished", "allCols", "colIdAtPos", "toIndex", "isNewColBefore", "restrictedCols", "moveColumnByIndex", "info", "colsToOrder", "movedColType", "gridGroupCols", "gOrder", "undefined", "saveColumnsOrder", "emit", "map", "lastVisibleCol", "clearRangeSelection", "addCellRange", "columnStart", "columnEnd", "rowStartIndex", "rowEndIndex", "attachmentModalComponent", "onCellFocused", "deselectAll", "province", "district", "community", "invCell", "rowNodes", "relColsWithConds", "conds", "conditions", "expLeft", "suppressFlash", "onFilterApplied", "isFilterActive", "onSortChanged", "colsOnSort", "getSort", "onColumnRowGroupChanged", "applyColumnState", "state", "getRowGroupColumns", "refreshHeader", "onColumnDone", "gridStaticCol", "delInd", "splice", "colInd", "gColInd", "gc", "fieldTypeValues", "autoSizeColumn", "condColTypes", "String", "refreshGridRows", "enableEditing", "setDefaultColDef", "commCol", "allSelProvs", "provIds", "Set", "communities", "provinceId", "substring", "getCommunities", "res", "fName", "defaultExcelExportParams", "author", "fileName", "allColumns", "sheetName", "columnKeys", "onAttached", "r", "emitVal", "getRowNode", "comments", "updated", "comment", "commentId", "isResolved", "deleted", "isProgress", "link", "commentOn", "colMap", "staticColMap", "dColId", "navigator", "clipboard", "writeText", "location", "origin", "pathname", "ngOnDestroy", "sb", "unsubscribe", "_", "ɵɵdirectiveInject", "i1", "GridService", "i2", "MessageService", "_2", "selectors", "viewQuery", "GridComponent_Query", "rf", "ctx", "ɵɵtemplate", "GridComponent_aims_working_0_Template", "GridComponent_Template_ag_grid_angular_cellValueChanged_1_listener", "GridComponent_Template_ag_grid_angular_gridReady_1_listener", "GridComponent_Template_ag_grid_angular_columnMoved_1_listener", "GridComponent_Template_ag_grid_angular_cellFocused_1_listener", "GridComponent_Template_ag_grid_angular_filterChanged_1_listener", "GridComponent_Template_ag_grid_angular_sortChanged_1_listener", "GridComponent_Template_ag_grid_angular_columnRowGroupChanged_1_listener", "GridComponent_Template_column_form_modal_done_3_listener", "GridComponent_Template_attachments_modal_done_4_listener", "GridComponent_comment_modal_5_Template", "ɵɵproperty", "ɵɵadvance", "ɵɵpureFunction2", "_c0"], "sources": ["D:\\UNDP\\AIMS3System\\AIMS3\\ClientApp\\src\\app\\modules\\aims-grid\\components\\grid\\grid.component.ts", "D:\\UNDP\\AIMS3System\\AIMS3\\ClientApp\\src\\app\\modules\\aims-grid\\components\\grid\\grid.component.html"], "sourcesContent": ["import { Component, <PERSON><PERSON><PERSON>ter, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';\r\nimport {\r\n    CellClickedEvent, CellDoubleClickedEvent, CellFocusedEvent, CellKeyPressEvent, CellValueChangedEvent, ColDef, ColGroupDef, ColumnApi,\r\n    ColumnMovedEvent, ColumnRowGroupChangedEvent, FilterChangedEvent, GridApi, GridOptions, GridReadyEvent, ICellRendererParams, IRowNode,\r\n    SortChangedEvent\r\n} from 'ag-grid-community';\r\nimport 'ag-grid-enterprise';\r\nimport { LicenseManager } from 'ag-grid-enterprise';\r\nimport { Subscription } from 'rxjs';\r\nimport { environment } from '../../../../../environments/environment';\r\nimport { ActivityStatus, ColDataType, ColumnVarType } from '../../../../shared/enums';\r\nimport { MessageService } from '../../../../shared/services/message.service';\r\nimport { getColWidth } from '../../../../shared/utilities';\r\nimport { ColumnCondition, DynamicColumn } from '../../../aims-grid/models/dynamic-column.model';\r\nimport { CommentComponent } from '../../../comments/components/comment.component';\r\nimport { Comment } from '../../../comments/models/comment.model';\r\nimport { IProgressData, ITargetData } from '../../../data-entry/models/data.model';\r\nimport { AttachmentsFormComponent } from '../../../library/components/attachments/attachments.component';\r\nimport { SabaDateEditor } from '../../controls/cell-editors/date/date.control';\r\nimport { SabaInputEditor } from '../../controls/cell-editors/input/input.control';\r\nimport { SabaNoneEditor } from '../../controls/cell-editors/none/none.control';\r\nimport { SabaSelectEditor } from '../../controls/cell-editors/select/select.control';\r\nimport { SabaTextEditor } from '../../controls/cell-editors/text/text.control';\r\nimport { SabaAttachmentRenderer } from '../../controls/cell-renderers/attachment/attachment.control';\r\nimport { SabaCheckboxRenderer } from '../../controls/cell-renderers/checkbox/checkbox.control';\r\nimport { SabaSelectValueRenderer } from '../../controls/cell-renderers/select/select-value.control';\r\nimport { GridCustomHeader } from '../../controls/grid-header/grid-header.control';\r\nimport { GridService } from '../../services/grid.service';\r\nimport { DynamicColumnFormComponent } from '../modal-forms/dynamic-column/column-form.component';\r\n\r\n@Component({\r\n    selector: 'aims-grid',\r\n    templateUrl: './grid.component.html',\r\n    styleUrls: ['./grid.component.scss']\r\n})\r\nexport class GridComponent implements OnInit, OnDestroy {\r\n    working: boolean = false;\r\n    public isDarkMode: boolean = false;\r\n    public isAdmin: boolean = false;\r\n\r\n    public gridApi: GridApi;\r\n    public gridColumnApi: ColumnApi;\r\n\r\n    public gridOptions: GridOptions;\r\n    public columnDefs: (ColDef | ColGroupDef)[];\r\n    public defaultColDefs: ColDef;\r\n    public components = {\r\n        'noEditor': SabaNoneEditor,\r\n        'sabaInputEditor': SabaInputEditor,\r\n        'sabaTextEditor': SabaTextEditor,\r\n        'sabaDateEditor': SabaDateEditor,\r\n        'sabaCheckboxRenderer': SabaCheckboxRenderer,\r\n        'sabaAttachmentRenderer': SabaAttachmentRenderer,\r\n        'sabaSelectValRenderer': SabaSelectValueRenderer,\r\n        'sabaSelectEditor': SabaSelectEditor\r\n    };\r\n    public tooltipShowDelay: number = 500;\r\n    private groupCollapsed = [[],[],[],[]];\r\n\r\n    private addNewColumn: ColDef;\r\n    private gridColumns = { //: { [key: string]: ColDef | ColDef[] } = {\r\n        commonCols: [],\r\n        progressActIdCol: null, // activity id\r\n        progressStatusCols: [], // file, status, start, end months\r\n        progressCommCol: null,  // community column\r\n        progressAsOfCol: null,  // as of \r\n        targetInfoCols: [],     // year, qtr\r\n        hiddenCol: null         // hidden narrow col\r\n    };\r\n\r\n    @ViewChild(DynamicColumnFormComponent, { static: false })\r\n    private colModalComponent: DynamicColumnFormComponent;\r\n\r\n    // data from grid's parent\r\n    dynamicColumns: DynamicColumn[] = [];\r\n    conditionsCols: DynamicColumn[] = [];\r\n    @Output() colDone = new EventEmitter<DynamicColumn[]>();\r\n    @Output() cellChanged = new EventEmitter<number>();\r\n\r\n    // *** DATA -------------\r\n    public rowData: (IProgressData | ITargetData)[] = [];\r\n    public regionsFilter: number[] = [];\r\n    public selectedVals = { dataType: 'Progress', sn: 1, orgId: 0, org: '', profId: 0, prof: '', projId: 0, proj: '', month: 0, year: 0 };\r\n\r\n    @ViewChild(AttachmentsFormComponent, { static: false })\r\n    private attachmentModalComponent: AttachmentsFormComponent;\r\n\r\n    @ViewChild(CommentComponent, { static: false })\r\n    commentModalComponent: CommentComponent;\r\n\r\n    private subscriptions: Subscription[] = [];\r\n    constructor(\r\n        private gridService: GridService,\r\n        private messageService: MessageService\r\n    ) {\r\n        if (document.querySelector('[data-theme=\"dark\"'))\r\n            this.isDarkMode = true;\r\n\r\n        // AG-GRID\r\n        LicenseManager.setLicenseKey(environment.agGridConfig.licenseKey);\r\n\r\n        // set all defaults\r\n        // default grid options\r\n        this.gridOptions = {\r\n            groupHeaderHeight: 30,\r\n            headerHeight: 35,\r\n            rowHeight: 24, rowDragManaged: true,\r\n            rowSelection: 'multiple',\r\n            enableRangeSelection: true, enableFillHandle: true,\r\n            undoRedoCellEditing: true, undoRedoCellEditingLimit: 20,\r\n            scrollbarWidth: 10,\r\n            suppressRowClickSelection: true,\r\n            enableCellChangeFlash: true,\r\n            groupDisplayType: 'groupRows',\r\n            groupDefaultExpanded: -1,\r\n            groupIncludeFooter: true,\r\n            groupMaintainOrder: true,\r\n            groupAllowUnbalanced: true,\r\n            rowClassRules: {\r\n                'bg-success': (params) => params.data?.status == ActivityStatus.Completed,\r\n                'bg-light-warning text-warning': (params) => params.data?.status == ActivityStatus.Archived,\r\n                'bg-light-danger text-danger': (params) => params.data?.status == ActivityStatus.Cancelled,\r\n                'text-gray-600': (params) => !params.data?.status && params.data?.dateApproved      // for target data when approved\r\n            }, statusBar: {\r\n                statusPanels: [\r\n                    { statusPanel: 'agTotalAndFilteredRowCountComponent', align: 'left' },\r\n                    //{ statusPanel: 'agTotalRowCountComponent', align: 'center' },\r\n                    { statusPanel: 'agFilteredRowCountComponent' },\r\n                    { statusPanel: 'agSelectedRowCountComponent' },\r\n                    { statusPanel: 'agAggregationComponent' },\r\n                ]\r\n            }, processCellFromClipboard: this.gridService.onPaste, processCellForClipboard: (params) => this.gridService.processCellForClipboard(params),\r\n            onCellKeyPress: (e: CellKeyPressEvent) => this.gridService.onDisabledCellEditRequest(e, this.defaultColDefs.editable, this.conditionsCols),\r\n            onCellDoubleClicked: (e: CellDoubleClickedEvent) => this.gridService.onDisabledCellEditRequest(e, this.defaultColDefs.editable, this.conditionsCols),\r\n            onCellKeyDown: this.gridService.clearCellsInRange, getContextMenuItems: (e) => this.gridService.getContextMenu(e, this.defaultColDefs.editable)\r\n        };\r\n\r\n        // default column configurations\r\n        this.defaultColDefs = {\r\n            sortable: false,\r\n            resizable: true,\r\n            filter: true,\r\n            editable: false,            // enabled if data is fetched when project is selected\r\n            minWidth: 50,\r\n            menuTabs: ['filterMenuTab', 'generalMenuTab'],\r\n            cellClassRules: {           // for data validation\r\n                'bg-light-danger': (params) => this.invalidCells.findIndex(c => c.rowDataId === params.data?.id && c.field === params.column.getColDef().field) > -1,\r\n                'bg-gray-300': (params) => { if (!params.data) return false;\r\n                    return (params.data.activityId || params.data.uniqueId) && !params.data.status && !params.column.getColId().startsWith('new') &&\r\n                        !params.column.getColId().startsWith('cols-hidden') && !params.column.isCellEditable(params.node);\r\n                }, 'has-comment': (params) => this.gridService.getCellCommentClass(params),\r\n                'is-note': (params) => this.gridService.getCellCommentClass(params, 'note'),\r\n                'unresolved': (params) => this.gridService.getCellCommentClass(params, 'unresolved')\r\n            }, onCellClicked: (event) => {  // show comments on clicking cell triangle\r\n                const target = (event.event.target as HTMLElement).closest('.ag-cell') as HTMLElement;\r\n                if (target?.classList.contains('has-comment')) {\r\n                    const cell = target.getBoundingClientRect();\r\n                    const e = (event.event as MouseEvent);\r\n                    if(e.y <= cell.top+8 && e.x >= cell.right-8)\r\n                        this.commentModalComponent.open(event.colDef, event.node, cell, false, true, target);\r\n                }\r\n            },\r\n            comparator: this.gridService.getComparator\r\n        };\r\n\r\n        // New dynamic column\r\n        this.addNewColumn = {\r\n            colId: 'new',\r\n            headerName: 'New',\r\n            headerTooltip: 'Add new column',\r\n            headerClass: 'text-hover-primary cursor-pointer',\r\n            headerComponent: GridCustomHeader,\r\n            cellClass: 'non-selectable-cell',\r\n            lockPinned: true,\r\n            suppressMovable: true,\r\n            width: 62,\r\n            minWidth: 62,\r\n            maxWidth: 62, suppressFillHandle: true,\r\n            editable: false, enableRowGroup: false,\r\n            suppressCellFlash: true,\r\n            filter: false,\r\n            suppressMenu: true,\r\n            sortable: false,\r\n            suppressSizeToFit: true,\r\n            suppressAutoSize: true,\r\n            lockVisible: true\r\n        };\r\n    }\r\n\r\n    // init the default readonly grid\r\n    ngOnInit() {\r\n        try {\r\n            this.defaultColDefs.editable = this.isAdmin;\r\n\r\n            // add default common column definitions\r\n            this.columnDefs = [{        // row num\r\n                colId: 'rowNum',\r\n                headerName: '',\r\n                minWidth: 50, width: 50, maxWidth: 100,\r\n                suppressMovable: true,\r\n                filter: false, sortable: false,\r\n                pinned: 'left',\r\n                editable: false, enableRowGroup: false,\r\n                suppressMenu: true, lockPosition: true,\r\n                suppressFillHandle: true,\r\n                onCellClicked: this.onCellClicked,\r\n                cellRenderer: (params: ICellRendererParams) => { return params.node.footer ? 'Total' : params.node.rowIndex + 1; }\r\n            }];\r\n\r\n            // progress first few columns\r\n            this.gridColumns.progressActIdCol = {  // activity id\r\n                field: 'activityId',\r\n                headerName: 'Activity ID',\r\n                headerClass: 'required',\r\n                editable: false, suppressFillHandle: true,\r\n                suppressMovable: true, enableRowGroup: false,\r\n                pinned: true, lockPinned: true, lockVisible: true,\r\n                width: 110,\r\n                cellClass: 'fs-7'\r\n            };\r\n\r\n            this.gridColumns.progressStatusCols = [{   // file\r\n                field: 'file', colId: 'file',\r\n                headerName: 'File', headerClass: 'bg-header-color-1',\r\n                headerTooltip: 'All files linked to this activity will show up here.',\r\n                headerComponent: GridCustomHeader,\r\n                headerComponentParams: { colType: ColDataType.Attachment, showInfoIcon: true },\r\n                cellEditor: 'noEditor', cellEditorParams: {\r\n                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {\r\n                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\r\n                    }, onCellLink: (colId: string, node: IRowNode) => {\r\n                        if (this.selectedVals.dataType === 'Progress')\r\n                            this.generateLink({ activityId: node.data.id, activityProgressId: node.data.progressId, columnId: colId });\r\n                        else\r\n                            this.generateLink({ targetId: node.data.id, columnId: colId });\r\n                    }\r\n                },\r\n                editable: (params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\r\n                cellRenderer: 'sabaAttachmentRenderer',\r\n                suppressMovable: true, enableRowGroup: false,\r\n                width: 80, columnGroupShow: 'open', comparator: this.gridService.getNumComparator,\r\n                sortable: false, onCellClicked: (event) => this.onCellClicked(event),\r\n                suppressMenu: true, suppressFillHandle: true\r\n            },\r\n            {   // status\r\n                colId: 'status',\r\n                field: 'status',\r\n                headerName: 'Status', headerClass: 'bg-header-color-1',\r\n                headerComponent: GridCustomHeader,\r\n                headerComponentParams: {\r\n                    isRequired: true,\r\n                    colType: ColDataType.SelectSingle\r\n                },\r\n                width: 110, //rowGroup: true, rowGroupIndex: 0,\r\n                cellEditor: 'sabaSelectEditor',\r\n                cellEditorParams: {\r\n                    values: [\r\n                        { id: 0, name: 'Ongoing' },\r\n                        { id: 1, name: 'Completed' },\r\n                        { id: 2, name: 'Archived', disabled: true },\r\n                        { id: 3, name: 'Cancelled' }\r\n                    ], onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {\r\n                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\r\n                    }, onCellLink: (colId: string, node: IRowNode) => {\r\n                        if (this.selectedVals.dataType === 'Progress')\r\n                            this.generateLink({ activityId: node.data.id, activityProgressId: node.data.progressId, columnId: colId });\r\n                        else\r\n                            this.generateLink({ targetId: node.data.id, columnId: colId });\r\n                    }\r\n                },\r\n                editable: (params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\r\n                valueFormatter: this.gridService.getValueFormatter,\r\n                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },\r\n                cellRenderer: 'sabaSelectValRenderer',\r\n                cellClassRules: {\r\n                    'text-primary': (params) => params.value == ActivityStatus.Archived,\r\n                    'text-red': (params) => params.value == ActivityStatus.Cancelled\r\n                }, lockVisible: true,\r\n                suppressMovable: true, valueSetter: (params) => this.gridService.onValueSet(params)\r\n            },\r\n            {  // sMonth: Not required\r\n                colId: 'sMonth',\r\n                field: 'sMonth',\r\n                headerName: 'Start date', headerClass: 'bg-header-color-1',\r\n                headerComponent: GridCustomHeader,\r\n                headerComponentParams: {\r\n                    isRequired: true,\r\n                    colType: ColDataType.Date  // MMM/yyyy\r\n                },\r\n                cellEditor: 'sabaDateEditor',\r\n                cellEditorParams: { ph: 'dd/MM/yy',\r\n                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {\r\n                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\r\n                    }, onCellLink: (colId: string, node: IRowNode) => {\r\n                        if (this.selectedVals.dataType === 'Progress')\r\n                            this.generateLink({ activityId: node.data.id, activityProgressId: node.data.progressId, columnId: colId });\r\n                        else\r\n                            this.generateLink({ targetId: node.data.id, columnId: colId });\r\n                    }\r\n                },\r\n                editable: (params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\r\n                valueFormatter: this.gridService.getValueFormatter,\r\n                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },\r\n                lockPinned: true, lockVisible: true, comparator: this.gridService.getDateComparator,\r\n                width: 130, columnGroupShow: 'open',\r\n                suppressMovable: true\r\n            },\r\n            {   // eMonth: Not always required\r\n                colId: 'eMonth',\r\n                field: 'eMonth',\r\n                headerName: 'End date', headerClass: 'bg-header-color-1',\r\n                headerTooltip: 'The date when the implementation of the activity has been completed.',\r\n                headerComponent: GridCustomHeader,\r\n                headerComponentParams: {\r\n                    isRequired: false,\r\n                    colType: ColDataType.Date,  // MMM/yyyy\r\n                    showInfoIcon: true\r\n                },\r\n                cellEditor: 'sabaDateEditor',\r\n                cellEditorParams: { ph: 'dd/MM/yy', lastDay: true,\r\n                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {\r\n                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\r\n                    }, onCellLink: (colId: string, node: IRowNode) => {\r\n                        if (this.selectedVals.dataType === 'Progress')\r\n                            this.generateLink({ activityId: node.data.id, activityProgressId: node.data.progressId, columnId: colId });\r\n                        else\r\n                            this.generateLink({ targetId: node.data.id, columnId: colId });\r\n                    }\r\n                },\r\n                editable: (params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\r\n                valueFormatter: this.gridService.getValueFormatter,\r\n                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },\r\n                lockPinned: true, lockVisible: true, comparator: this.gridService.getDateComparator,\r\n                width: 130, columnGroupShow: 'open',\r\n                suppressMovable: true, valueSetter: this.gridService.onValueSet\r\n            }];\r\n\r\n            this.conditionsCols.push(new DynamicColumn(-4, null, 'Year', ColumnVarType.TargetInfo, -1, ColDataType.Number));\r\n            this.conditionsCols.push(new DynamicColumn(-5, null, 'Qtr', ColumnVarType.TargetInfo, 0, ColDataType.Number));\r\n            this.conditionsCols.push(new DynamicColumn(-6, null, 'Status', ColumnVarType.ProgressInfo, -3, ColDataType.SelectSingle,\r\n                this.gridColumns.progressStatusCols[1].cellEditorParams.values));\r\n            this.conditionsCols.push(new DynamicColumn(-1, null, 'StartMonth', ColumnVarType.ProgressInfo, -2, ColDataType.Date, null, 'Start month'));\r\n            this.conditionsCols.push(new DynamicColumn(-2, null, 'EndMonth', ColumnVarType.ProgressInfo, -1, ColDataType.Date, null, 'Complete month'));\r\n\r\n            // info static column used for group collapse\r\n            this.gridColumns.hiddenCol = {\r\n                colId: 'cols-hidden',\r\n                headerName: '...',\r\n                headerTooltip: 'Column(s) hidden. Click group handle to expand.',\r\n                cellClass: 'non-selectable-cell',\r\n                lockPinned: true,\r\n                suppressMovable: true,\r\n                width: 28,\r\n                minWidth: 28,\r\n                maxWidth: 28,\r\n                resizable: false,\r\n                editable: false,\r\n                suppressCellFlash: true,\r\n                filter: false,\r\n                suppressMenu: true,\r\n                sortable: false,\r\n                suppressSizeToFit: true,\r\n                suppressAutoSize: true, suppressFillHandle: true,\r\n                lockVisible: true, enableRowGroup: false,\r\n                columnGroupShow: 'closed'\r\n            };\r\n\r\n            // location columns\r\n            this.gridColumns.commonCols = [{  // region\r\n                field: 'region',\r\n                headerName: 'Region', headerClass: 'bg-header-color-2',\r\n                headerComponent: GridCustomHeader,\r\n                width: 130,\r\n                suppressMovable: true,\r\n                cellEditor: 'sabaSelectEditor',\r\n                cellEditorParams: {\r\n                    values: [ //{ id: 0, name: 'National' }, only show when 'National' is selected in col\r\n                        { id: 1, name: 'Central' },\r\n                        { id: 2, name: 'Central Highland' },\r\n                        { id: 3, name: 'Eastern' },\r\n                        { id: 4, name: 'South Eastern' },\r\n                        { id: 5, name: 'North Eastern' },\r\n                        { id: 6, name: 'Western' },\r\n                        { id: 7, name: 'Southern' },\r\n                        { id: 8, name: 'Northern' }\r\n                    ],\r\n                    getData: async (params) => { return await this.gridService.getMetaData('regs', params, this.regionsFilter) },\r\n                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {\r\n                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\r\n                    }, onCellLink: (colId: string, node: IRowNode) => {\r\n                        if (this.selectedVals.dataType === 'Progress')\r\n                            this.generateLink({ activityId: node.data.id, activityProgressId: node.data.progressId, columnId: colId });\r\n                        else\r\n                            this.generateLink({ targetId: node.data.id, columnId: colId });\r\n                    }\r\n                },\r\n                editable: (params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\r\n                cellRenderer: 'sabaSelectValRenderer',\r\n                valueGetter: (params) => {\r\n                    const regionIds = params.data?.region?.split(',') || [];\r\n                    if (!regionIds.length || !regionIds[0]) return '';\r\n                    const result = [];\r\n                    const regionNames = ['National', 'Central', 'Central Highland', 'Eastern', 'South Eastern', 'North Eastern', 'Western', 'Southern', 'Northern'];\r\n                    regionIds.forEach(id => {\r\n                        if (regionNames[id]) result.push(regionNames[id]);\r\n                    });\r\n                    return result.join(', ');\r\n                },\r\n                valueFormatter: this.gridService.getValueFormatter,\r\n                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },\r\n                onCellValueChanged: (params: CellValueChangedEvent<any,any>) => this.onLocationCellChanged(params),\r\n                lockVisible: true, columnGroupShow: 'open',\r\n                comparator: (vA, vB, nA, nB, desc) => this.gridService.getRegionComparator(vA,vB,nA,nB,desc),\r\n                valueSetter: this.gridService.setValue\r\n            },\r\n            {   // province: always required\r\n                field: 'province',\r\n                headerName: 'Province', headerClass: 'bg-header-color-2',\r\n                headerComponent: GridCustomHeader,\r\n                headerComponentParams: {\r\n                    colType: this.selectedVals.dataType === 'Targets' ? ColDataType.SelectSingle : ColDataType.SelectMultiple\r\n                },\r\n                width: 140,\r\n                suppressMovable: true,\r\n                cellEditor: 'sabaSelectEditor',\r\n                cellEditorParams: {\r\n                    values: [], getData: async (params) => { return await this.gridService.getMetaData('provs', params, this.regionsFilter) },\r\n                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {\r\n                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\r\n                    }, onCellLink: (colId: string, node: IRowNode) => {\r\n                        if (this.selectedVals.dataType === 'Progress')\r\n                            this.generateLink({ activityId: node.data.id, activityProgressId: node.data.progressId, columnId: colId });\r\n                        else\r\n                            this.generateLink({ targetId: node.data.id, columnId: colId });\r\n                    }\r\n                },\r\n                editable: (params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\r\n                cellRenderer: 'sabaSelectValRenderer',\r\n\r\n                valueFormatter: this.gridService.getValueFormatter.bind(this.gridService),\r\n                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter.bind(this.gridService) },\r\n                onCellValueChanged: (params: CellValueChangedEvent<any, any>) => this.onLocationCellChanged(params),\r\n                lockVisible: true, columnGroupShow: 'open',\r\n                comparator: (vA, vB, nA, nB, desc) => this.gridService.getProvinceComparator(vA, vB, nA, nB, desc),\r\n                valueSetter: this.gridService.setValue\r\n            },\r\n            {   // district: always required\r\n                field: 'district',\r\n                headerName: 'District', headerClass: 'bg-header-color-2',\r\n                headerComponent: GridCustomHeader,\r\n                headerComponentParams: {\r\n                    colType: this.selectedVals.dataType === 'Targets' ? ColDataType.SelectSingle : ColDataType.SelectMultiple\r\n                },\r\n                width: 145,\r\n                suppressMovable: true,\r\n                cellEditor: 'sabaSelectEditor',\r\n                cellEditorParams: {\r\n                    values: [], fuzzySearch: true, getData: async (params) => { return await this.gridService.getMetaData('dists', params, this.regionsFilter) },\r\n                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {\r\n                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\r\n                    }, onCellLink: (colId: string, node: IRowNode) => {\r\n                        if (this.selectedVals.dataType === 'Progress')\r\n                            this.generateLink({ activityId: node.data.id, activityProgressId: node.data.progressId, columnId: colId });\r\n                        else\r\n                            this.generateLink({ targetId: node.data.id, columnId: colId });\r\n                    }\r\n                },\r\n                editable: (params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\r\n                cellRenderer: 'sabaSelectValRenderer',\r\n\r\n                valueFormatter: this.gridService.getValueFormatter.bind(this.gridService),\r\n                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter.bind(this.gridService) },\r\n                onCellValueChanged: (params: CellValueChangedEvent<any, any>) => this.onLocationCellChanged(params),\r\n                lockVisible: true, columnGroupShow: 'open',\r\n                comparator: (vA, vB, nA, nB, desc) => this.gridService.getDistrictComparator(vA, vB, nA, nB, desc),\r\n                valueSetter: this.gridService.setValue\r\n            }];\r\n\r\n            // fetch provinces and districts for the first time\r\n            this.gridService.getMetaData('provs').then((vals) => {\r\n                this.gridColumns.commonCols[1].cellEditorParams.values = vals;\r\n                // Force grid refresh to update display\r\n                setTimeout(() => {\r\n                    if (this.gridApi) {\r\n                        this.gridApi.refreshCells({ columns: ['province'], force: true });\r\n                    }\r\n                }, 100);\r\n            });\r\n            this.gridService.getMetaData('dists').then((vals) => {\r\n                this.gridColumns.commonCols[2].cellEditorParams.values = vals;\r\n                this.refreshProvDistGridColumns();\r\n                // Force grid refresh to update display\r\n                setTimeout(() => {\r\n                    if (this.gridApi) {\r\n                        this.gridApi.refreshCells({ columns: ['district'], force: true });\r\n                    }\r\n                }, 100);\r\n            });\r\n\r\n            // progress community column\r\n            this.gridColumns.progressCommCol = {   // community\r\n                field: 'community',\r\n                headerName: 'Community', headerClass: 'bg-header-color-2',\r\n                headerComponent: GridCustomHeader,\r\n                width: 170,\r\n                suppressMovable: true,\r\n                cellEditor: 'sabaSelectEditor',\r\n                cellEditorParams: {\r\n                    values: [], fuzzySearch: true, addNewLink: '/data-entry/locations',\r\n                    getData: async (params) => { return await this.gridService.getMetaData('comms', params) },\r\n                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {\r\n                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\r\n                    }, onCellLink: (colId: string, node: IRowNode) => {\r\n                        if (this.selectedVals.dataType === 'Progress')\r\n                            this.generateLink({ activityId: node.data.id, activityProgressId: node.data.progressId, columnId: colId });\r\n                        else\r\n                            this.generateLink({ targetId: node.data.id, columnId: colId });\r\n                    }\r\n                },\r\n                editable: (params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\r\n                cellRenderer: 'sabaSelectValRenderer',\r\n                valueFormatter: this.gridService.getValueFormatter.bind(this.gridService),\r\n                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter.bind(this.gridService) },\r\n                onCellValueChanged: (params: CellValueChangedEvent<any, any>) => this.onLocationCellChanged(params),\r\n                lockVisible: true,\r\n                comparator: (vA, vB, nA, nB, desc) => this.gridService.getCommunityComparator(vA, vB, nA, nB, desc),\r\n                valueSetter: this.gridService.setValue\r\n            };\r\n\r\n            // progress static columns\r\n            this.gridColumns.progressAsOfCol = {  // as of\r\n                colId: 'asOf',\r\n                field: 'asOf',\r\n                headerName: 'As of', headerTooltip: 'As of the end of this month. Automatically filled.', headerClass: 'bg-header-color-4',\r\n                headerComponent: GridCustomHeader,\r\n                headerComponentParams: {\r\n                    isRequired: true,\r\n                    colType: ColDataType.Date,  // MMM/yyyy\r\n                    showInfoIcon: true\r\n                },\r\n                cellEditor: 'sabaDateEditor',\r\n                cellEditorParams: { ph: 'dd/MM/yy', lastDay: true,\r\n                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {\r\n                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\r\n                    }, onCellLink: (colId: string, node: IRowNode) => {\r\n                        if (this.selectedVals.dataType === 'Progress')\r\n                            this.generateLink({ activityId: node.data.id, activityProgressId: node.data.progressId, columnId: colId });\r\n                        else\r\n                            this.generateLink({ targetId: node.data.id, columnId: colId });\r\n                    }\r\n                },\r\n                //editable: this.isAdmin, //(params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\r\n                valueFormatter: this.gridService.asOfValueFormatter,\r\n                filterParams: { valueFormatter: this.gridService.asOfValueFormatter },\r\n                onCellValueChanged: this.gridService.onCellValueChanged,                // draft off\r\n                lockPinned: true, comparator: this.gridService.getDateComparator,\r\n                width: 110, lockVisible: true, cellClassRules: { 'text-muted': this.gridService.getCellClassRules },\r\n                suppressMovable: true\r\n            };\r\n            this.conditionsCols.push(new DynamicColumn(-3, null, 'AsOf', ColumnVarType.Progress, 0, ColDataType.Date, null, 'As of'));\r\n\r\n            // target info static columns\r\n            let years = [];\r\n            const currYear = new Date().getFullYear();\r\n\r\n            for (let i = 2020; i <= currYear + 3; i++)\r\n                years.push({ id: i, name: `${i}` });\r\n\r\n            this.gridColumns.targetInfoCols = [{    // year\r\n                colId: 'year',\r\n                field: 'year',\r\n                headerName: 'Year', headerClass: 'bg-header-color-3',\r\n                headerComponent: GridCustomHeader,\r\n                headerComponentParams: {\r\n                    isRequired: true,\r\n                    colType: ColDataType.SelectSingle,\r\n                    showInfoIcon: false\r\n                },\r\n                cellEditor: 'sabaSelectEditor', //'sabaInputEditor',\r\n                cellEditorParams: {\r\n                    values: [{ id: 0, name: 'All years' }, ...years], // min: 2000, max: 2050 },\r\n                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {\r\n                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\r\n                    }, onCellLink: (colId: string, node: IRowNode) => {\r\n                        if (this.selectedVals.dataType === 'Progress')\r\n                            this.generateLink({ activityId: node.data.id, activityProgressId: node.data.progressId, columnId: colId });\r\n                        else\r\n                            this.generateLink({ targetId: node.data.id, columnId: colId });\r\n                    }\r\n                },\r\n                editable: (params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\r\n                cellRenderer: 'sabaSelectValRenderer',\r\n                valueFormatter: this.gridService.getValueFormatter,\r\n                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },\r\n                lockPinned: true, comparator: this.gridService.getNumComparator,\r\n                width: 95, lockVisible: true,\r\n                suppressMovable: true\r\n            },\r\n            {   // quarter\r\n                colId: 'qtr',\r\n                field: 'qtr',\r\n                headerName: 'Qtr', headerClass: 'bg-header-color-3',\r\n                headerComponent: GridCustomHeader,\r\n                headerComponentParams: {\r\n                    isRequired: true,\r\n                    colType: ColDataType.SelectSingle,\r\n                    showInfoIcon: false\r\n                },\r\n                cellEditor: 'sabaSelectEditor',\r\n                cellEditorParams: {\r\n                    values: [\r\n                        //{ id: 0, name: 'All' },\r\n                        { id: 1, name: 'Qtr-1' },\r\n                        { id: 2, name: 'Qtr-2' },\r\n                        { id: 3, name: 'Qtr-3' },\r\n                        { id: 4, name: 'Qtr-4' }\r\n                    ],\r\n                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {\r\n                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\r\n                    }, onCellLink: (colId: string, node: IRowNode) => {\r\n                        if (this.selectedVals.dataType === 'Progress')\r\n                            this.generateLink({ activityId: node.data.id, activityProgressId: node.data.progressId, columnId: colId });\r\n                        else\r\n                            this.generateLink({ targetId: node.data.id, columnId: colId });\r\n                    }\r\n                },\r\n                editable: (params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\r\n                cellRenderer: 'sabaSelectValRenderer',\r\n                valueFormatter: this.gridService.getValueFormatter,\r\n                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },\r\n                columnGroupShow: 'open',\r\n                lockPinned: true, lockVisible: true,\r\n                width: 90,\r\n                suppressMovable: true\r\n            }];\r\n\r\n            // push some empty data\r\n            for (let i = 1; i <= 20; i++)\r\n                this.rowData.push({ id: 0, colVals: {}, dirty: false } as IProgressData);\r\n        } catch (ex) { console.error(ex); this.messageService.error('Something went wrong.'); }\r\n    }\r\n\r\n    // generate serial number and activity id based on it\r\n    private onLocationCellChanged(params: CellValueChangedEvent<any, any>): void {\r\n        if (this.selectedVals.dataType === 'Progress')\r\n            this.selectedVals.sn = this.rowData.filter((d: IProgressData) => d.activityId).length;\r\n        else\r\n            this.selectedVals.sn = this.rowData.filter((d: ITargetData) => d.uniqueId).length;\r\n\r\n        // look for duplicate s#\r\n        this.selectedVals.sn++;\r\n\r\n        for (let i = 0; i < this.rowData.length; i++) {\r\n            const uId = this.rowData[i]['activityId'] || this.rowData[i]['uniqueId'];\r\n            \r\n            if (uId?.startsWith(`${this.selectedVals.sn}-`))\r\n                this.selectedVals.sn++;\r\n        }\r\n\r\n        this.gridService.onLocationChanged(params, this.selectedVals);\r\n    }\r\n\r\n    private refreshProvDistGridColumns(): void {\r\n        // refresh province and district cols dropdown values\r\n        if (this.isAdmin && this.gridColumnApi) {\r\n            const cols = this.gridColumnApi.getColumns();\r\n            for (let i = 1; i < 10; i++) {\r\n                const colDef = cols[i]?.getColDef();\r\n                if (!colDef) continue;\r\n                if (colDef.field === 'province') {\r\n                    if (!colDef.cellEditorParams.values?.length) {\r\n                        colDef.cellEditorParams.values = this.gridColumns.commonCols[1].cellEditorParams.values;\r\n                        cols[i].setColDef(colDef, cols[i].getColDef());\r\n                    }\r\n                } else if (colDef.field === 'district') {\r\n                    if (!colDef.cellEditorParams.values?.length) {\r\n                        colDef.cellEditorParams.values = this.gridColumns.commonCols[2].cellEditorParams.values;\r\n                        cols[i].setColDef(colDef, cols[i].getColDef());\r\n                    }\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    private getGroupsState(target?: boolean): void {\r\n        let storeName = target ? 'tgroup1Collapsed' : 'group1Collapsed';\r\n        this.groupCollapsed[0] = JSON.parse(localStorage.getItem(storeName) || '[]');\r\n        storeName = storeName.replace('1', '2');\r\n        this.groupCollapsed[1] = JSON.parse(localStorage.getItem(storeName) || '[]');\r\n        storeName = storeName.replace('2', '3');\r\n        this.groupCollapsed[2] = JSON.parse(localStorage.getItem(storeName) || '[]');\r\n        storeName = storeName.replace('3', '4');\r\n        this.groupCollapsed[3] = JSON.parse(localStorage.getItem(storeName) || '[]');\r\n    }\r\n\r\n    // init as per profile and project selected\r\n    initGrid(filters: any): void {\r\n        try {\r\n            // by reaching this, the gridColumns are already asssigned to by the parent\r\n            this.working = true;\r\n            this.gridApi.stopEditing(true);\r\n            this.columnDefs = [this.columnDefs[0]];                             // keep the first rowNum col\r\n            this.conditionsCols = this.conditionsCols.filter(c => c.id < 0);    // reset cond cols to default\r\n            this.gridColumns.commonCols[0].cellEditorParams.values =            // reset region col to exclude 'National'\r\n                this.gridColumns.commonCols[0].cellEditorParams.values.filter(v => v.id > 0);\r\n\r\n            this.gridApi.setColumnDefs(this.columnDefs);        // reset/refresh grid\r\n\r\n            // create columns\r\n            if (filters.type === 'Progress') {\r\n                this.columnDefs = this.columnDefs.concat(this.gridColumns.progressActIdCol);\r\n                this.getGroupsState();\r\n\r\n                // status group and columns\r\n                this.gridColumns.commonCols[2].columnGroupShow = 'open';\r\n                this.gridColumns.hiddenCol.headerClass = 'bg-header-color-1';\r\n                this.columnDefs.push({\r\n                    groupId: 'ProgressStatus', headerTooltip: 'Double click to expand or collapse this section.',\r\n                    headerName: 'Status', headerClass: 'bg-header-group-color-1',\r\n                    suppressMovable: true,\r\n                    marryChildren: true,\r\n                    openByDefault: !this.groupCollapsed[0].includes(this.selectedVals.profId),\r\n                    children: [...this.gridColumns.progressStatusCols, this.gridColumns.hiddenCol]\r\n                });\r\n\r\n                // location cols\r\n                let hiddenCol = Object.assign({}, this.gridColumns.hiddenCol)\r\n                hiddenCol.headerClass = 'bg-header-color-2';\r\n                this.columnDefs.push({\r\n                    groupId: `${ColumnVarType.ProgressStatic}`, headerTooltip: 'Double click to expand or collapse this section.',\r\n                    headerName: 'Location', headerClass: 'bg-header-group-color-2',\r\n                    suppressMovable: true,\r\n                    marryChildren: true,\r\n                    openByDefault: !this.groupCollapsed[1].includes(this.selectedVals.profId),\r\n                    children: [...this.gridColumns.commonCols, this.gridColumns.progressCommCol, hiddenCol]\r\n                });\r\n\r\n                // add info and dynamic cols\r\n                let infoCols = [];\r\n                let progCols = [];\r\n                this.dynamicColumns.forEach(col => {\r\n                    // update static cols props\r\n                    if (col.type === ColumnVarType.ProgressStatic) {\r\n                        let staticCol = this.gridColumns.commonCols.concat(this.gridColumns.progressCommCol)\r\n                            .find(sc => sc.field === col.name.toLowerCase());\r\n                        if (staticCol) {\r\n                            if (staticCol.field === 'community')\r\n                                this.gridColumns.progressCommCol.colId = col.id;\r\n                            staticCol.colId = col.id;\r\n                            staticCol.headerName = col.displayName || col.name;\r\n                            staticCol.headerTooltip = col.description;\r\n                                                    staticCol.headerComponentParams = {\r\n                            enableEdit: this.isAdmin,\r\n                            isRequired: col.isRequired,\r\n                            colType: col.fieldType,  // ✅ Use the user's configured field type\r\n                            showInfoIcon: col.description?.length > 0,\r\n                            onColumnEdit: (e) => {\r\n                                this.colModalComponent.column = Object.assign({}, col);\r\n                                // provide cols\r\n                                this.colModalComponent.columns = this.dynamicColumns.filter(c => c.type === ColumnVarType.ProgressStatic);\r\n                                this.colModalComponent.open(e, 'static');\r\n                            }\r\n                        }\r\n                        \r\n                        // Note: Removed automatic override to SelectMultiple - respects user configuration\r\n                        }\r\n\r\n                        // add national value to Region col if it's selected\r\n                        if (col.name === 'Region' && col.conditionsApplied?.indexOf('showNational') > -1) {\r\n                            // do not add if region for the project is restricted\r\n                            if (!filters.project?.regions?.length) {\r\n                                staticCol.cellEditorParams.values = [{ id: 0, name: 'All', },\r\n                                ...staticCol.cellEditorParams.values];\r\n                            }\r\n                        }\r\n\r\n                        // add to conditions columns\r\n                        //this.conditionsCols.push(col);\r\n                        return;\r\n                    }\r\n\r\n                    // add dynamic column\r\n                    let colDef: ColDef = {\r\n                        colId: `${col.id}`,\r\n                        field: 'colVals.dCol' + col.id,\r\n                        headerName: col.displayName || col.name,\r\n                        headerTooltip: col.description,\r\n                        headerComponent: GridCustomHeader,\r\n                        headerComponentParams: {\r\n                            enableEdit: this.isAdmin,\r\n                            isRequired: col.isRequired,\r\n                            type: col.type,\r\n                            colType: col.fieldType,\r\n                            colOrder: col.order,\r\n                            showInfoIcon: col.description?.length > 0,\r\n                            onColumnEdit: (e, showMenu?: boolean) => {\r\n                                // set column on child\r\n                                this.colModalComponent.column = Object.assign({}, col);\r\n                                // provide cols for condition's component\r\n                                this.colModalComponent.columns = this.conditionsCols\r\n                                    .filter(c => [ColumnVarType.ProgressInfo, ColumnVarType.Progress].includes(c.type));\r\n                                this.colModalComponent.open(e, `${col.id}`, showMenu);\r\n                            }\r\n                        },\r\n                        cellEditor: this.gridService.getCellEditor(col.fieldType),\r\n                        cellEditorParams: {\r\n                            ...this.gridService.getCellEditorParams(col),\r\n                            onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {\r\n                                this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\r\n                            }, onCellLink: (colId: string, node: IRowNode) => {\r\n                                if (this.selectedVals.dataType === 'Progress')\r\n                                    this.generateLink({ activityId: node.data.id, activityProgressId: node.data.progressId, columnId: colId });\r\n                                else\r\n                                    this.generateLink({ targetId: node.data.id, columnId: colId });\r\n                            }\r\n                        },\r\n                        editable: (params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\r\n                        valueFormatter: this.gridService.getValueFormatter, // add formatting for col if set\r\n                        cellRenderer: this.gridService.getCellRenderer(col.fieldType),\r\n                        //cellRendererParams: { footerValueGetter: (params) => 'Text (' + params.value + ')' },\r\n                        cellClassRules: {\r\n                            'text-end': this.gridService.getCellTextAlign, 'text-danger': this.gridService.getCellClassRules,\r\n                            //'text-muted': (params) => col.type === ColumnVarType.Progress && params.data?.draft\r\n                        }, columnGroupShow: 'open',\r\n                        enableRowGroup: this.gridService.getEnableGrouping(col.fieldType),\r\n                        enableValue: this.gridService.getEnableGrouping(col.fieldType), aggFunc: this.gridService.getAggFunc(col.fieldType),\r\n                        lockPinned: true, lockVisible: true, suppressMovable: !this.isAdmin,\r\n                        width: getColWidth(col.name + (col.isRequired ? '*':''), [this.isAdmin, col.description?.length > 0])\r\n                    };\r\n\r\n                    // attach cell click for attachment columns\r\n                    if (col.fieldType === ColDataType.Attachment) {\r\n                        colDef.onCellClicked = (event) => this.onCellClicked(event);\r\n                        colDef.comparator = this.gridService.getNumComparator;\r\n                    }\r\n\r\n                    // attach num parser & comparator, and date comparator\r\n                    if (col.fieldType >= ColDataType.Number && col.fieldType <= ColDataType.GPS) {\r\n                        colDef.comparator = this.gridService.getNumComparator;\r\n                        colDef.valueGetter = (params) => this.gridService.getNumericValue(col.id, params);\r\n                        colDef.valueParser = (params) => parseFloat(params.newValue);\r\n                    }\r\n\r\n                    if (col.fieldType === ColDataType.Date) {\r\n                        //colDef.cellEditorParams.format = 'dd MMM yyyy';\r\n                        colDef.filterParams = { valueFormatter: this.gridService.getFilterValueFormatter };\r\n                        colDef.comparator = this.gridService.getDateComparator;\r\n                    }\r\n\r\n                    if (col.type === ColumnVarType.ProgressInfo) {\r\n                        colDef.headerClass = 'bg-header-color-3';\r\n                        infoCols.push(colDef);\r\n                    } else if (col.type === ColumnVarType.Progress) {\r\n                        colDef.headerClass = 'bg-header-color-4';\r\n                        colDef.cellClassRules = { ...colDef.cellClassRules, 'text-muted': (params) => params.data?.draft }  // draft on\r\n                        colDef.onCellValueChanged = (params) => this.gridService.onCellValueChanged(params)     // draft off\r\n                        progCols.push(colDef);\r\n                    }\r\n                    // add to conditions columns\r\n                    this.conditionsCols.push(col);\r\n                });\r\n\r\n                // sort dynamic cols by order and push to grid's columns\r\n                infoCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);\r\n                progCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);\r\n\r\n                if (infoCols.length)        // make first col always visible in the group\r\n                    infoCols[0].columnGroupShow = null;\r\n\r\n                // add add new column if admin\r\n                if (this.isAdmin) {\r\n                    let fnOnColAdd = (e) => {\r\n                        this.colModalComponent.column = new DynamicColumn(0, filters.profId, '', ColumnVarType.ProgressInfo, infoCols.length, 0);\r\n                        // provide cols for condition's component\r\n                        this.colModalComponent.columns = this.conditionsCols\r\n                            .filter(c => [ColumnVarType.ProgressInfo, ColumnVarType.Progress].includes(c.type));\r\n                        this.colModalComponent.open(e);\r\n                    };\r\n                    let addNewCol = {\r\n                        ...this.addNewColumn,\r\n                        headerComponentParams: { addNewCol: true, onColumnEdit: fnOnColAdd }\r\n                    };\r\n                    addNewCol.headerClass = 'bg-header-color-3';\r\n                    infoCols.push(addNewCol);\r\n\r\n                    fnOnColAdd = (e) => {\r\n                        this.colModalComponent.column = new DynamicColumn(0, filters.profId, '', ColumnVarType.Progress, progCols.length, 0);\r\n                        // provide cols for condition's component\r\n                        this.colModalComponent.columns = this.conditionsCols\r\n                            .filter(c => [ColumnVarType.ProgressInfo, ColumnVarType.Progress].includes(c.type));\r\n                        this.colModalComponent.open(e);\r\n                    };\r\n                    addNewCol = {\r\n                        ...this.addNewColumn,\r\n                        headerComponentParams: { addNewCol: true, onColumnEdit: fnOnColAdd.bind({}) }\r\n                    };\r\n                    addNewCol.headerClass = 'bg-header-color-4';\r\n                    progCols.push(addNewCol);\r\n                }\r\n\r\n                hiddenCol = Object.assign({}, this.gridColumns.hiddenCol)\r\n                hiddenCol.headerClass = 'bg-header-color-3';\r\n                this.columnDefs.push({\r\n                    groupId: `${ColumnVarType.ProgressInfo}`, headerTooltip: 'Double click to expand or collapse this section.',\r\n                    headerName: 'Info', headerClass: 'bg-header-group-color-3',\r\n                    suppressMovable: true,\r\n                    marryChildren: true,\r\n                    openByDefault: !this.groupCollapsed[2].includes(this.selectedVals.profId),\r\n                    children: infoCols.length > (this.isAdmin ? 2 : 1) ? [...infoCols, hiddenCol] : [...infoCols]\r\n                });\r\n\r\n                hiddenCol = Object.assign({}, this.gridColumns.hiddenCol)\r\n                hiddenCol.headerClass = 'bg-header-color-4';\r\n                this.columnDefs.push({\r\n                    groupId: `${ColumnVarType.Progress}`,\r\n                    headerName: 'Cumulative progress', headerClass: 'bg-header-group-color-4 info-icon',\r\n                    headerTooltip: 'Values entered in previous months will show up in grey.\\nDouble click to expand or collapse this section.',\r\n                    suppressMovable: true,\r\n                    marryChildren: true,\r\n                    openByDefault: !this.groupCollapsed[3].includes(this.selectedVals.profId),\r\n                    children: progCols.length > (this.isAdmin ? 2 : 1) ? [this.gridColumns.progressAsOfCol, hiddenCol, ...progCols] : [this.gridColumns.progressAsOfCol, ...progCols]\r\n                });\r\n                // ---------------------------------------------------------------\r\n            } else {      // target\r\n                // ---------------------------------------------------------------\r\n                this.getGroupsState(true);\r\n\r\n                // location cols\r\n                this.gridColumns.commonCols[2].columnGroupShow = null;\r\n                this.gridColumns.hiddenCol.headerClass = 'bg-header-color-2';\r\n                this.columnDefs.push({\r\n                    groupId: `${ColumnVarType.TargetStatic}`, headerTooltip: 'Double click to expand or collapse this section.',\r\n                    headerName: 'Location', headerClass: 'bg-header-group-color-2',\r\n                    suppressMovable: true,\r\n                    marryChildren: true,\r\n                    openByDefault: !this.groupCollapsed[1].includes(this.selectedVals.profId),\r\n                    children: [...this.gridColumns.commonCols, this.gridColumns.hiddenCol]\r\n                });\r\n\r\n                // add info and dynamic cols for target\r\n                let infoCols = [];\r\n                let targetCols = [];\r\n                this.dynamicColumns.forEach(col => {\r\n                    // update static cols props\r\n                    if (col.type === ColumnVarType.TargetStatic) {\r\n                        let staticCol = this.gridColumns.commonCols.find(sc => sc.field === col.name.toLowerCase());\r\n                        if (staticCol) {\r\n                            staticCol.colId = col.id;\r\n                            staticCol.headerName = col.displayName || col.name;\r\n                            staticCol.headerTooltip = col.description;\r\n                                                    staticCol.headerComponentParams = {\r\n                            enableEdit: this.isAdmin,\r\n                            isRequired: col.isRequired,\r\n                            colType: col.fieldType,  // ✅ Use the user's configured field type\r\n                            showInfoIcon: col.description?.length > 0,\r\n                            onColumnEdit: (e) => {\r\n                                this.colModalComponent.column = Object.assign({}, col);\r\n                                this.colModalComponent.columns = this.dynamicColumns.filter(c => c.type === ColumnVarType.TargetStatic);\r\n                                this.colModalComponent.open(e, 'static');\r\n                            }\r\n                        }\r\n                        \r\n                        // Note: Removed automatic override to SelectMultiple - respects user configuration\r\n                        }\r\n\r\n                        // add national value to Region col if it's selected\r\n                        if (col.name === 'Region' && col.conditionsApplied?.indexOf('showNational') > -1) {\r\n                            // do not add if region for the project is restricted\r\n                            if (!filters.project?.regions?.length) {\r\n                                staticCol.cellEditorParams.values = [{ id: 0, name: 'National' },\r\n                                ...staticCol.cellEditorParams.values];\r\n                            }\r\n                        }\r\n\r\n                        // add to conditions columns\r\n                        //this.conditionsCols.push(col);\r\n                        return;\r\n                    }\r\n\r\n                    // add dynamic column\r\n                    let colDef: ColDef = {\r\n                        colId: `${col.id}`,\r\n                        field: 'colVals.dCol' + col.id,     // TODO: On Server\r\n                        headerName: col.displayName || col.name,\r\n                        headerTooltip: col.description,\r\n                        headerComponent: GridCustomHeader,\r\n                        headerComponentParams: {\r\n                            enableEdit: this.isAdmin,\r\n                            isRequired: col.isRequired,\r\n                            type: col.type,\r\n                            colType: col.fieldType,\r\n                            colOrder: col.order,\r\n                            showInfoIcon: col.description?.length > 0,\r\n                            onColumnEdit: (e, showMenu?: boolean) => {\r\n                                // set column on child\r\n                                this.colModalComponent.column = Object.assign({}, col);\r\n                                // provide cosl for condition's component\r\n                                this.colModalComponent.columns = this.conditionsCols\r\n                                    .filter(c => [ColumnVarType.TargetInfo, ColumnVarType.Target].includes(c.type));\r\n                                this.colModalComponent.open(e, `${col.id}`, showMenu);\r\n                            }\r\n                        },\r\n                        cellEditor: this.gridService.getCellEditor(col.fieldType),\r\n                        cellEditorParams: {\r\n                            ...this.gridService.getCellEditorParams(col),\r\n                            onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {\r\n                                this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\r\n                            }, onCellLink: (colId: string, node: IRowNode) => {\r\n                                if (this.selectedVals.dataType === 'Progress')\r\n                                    this.generateLink({ activityId: node.data.id, activityProgressId: node.data.progressId, columnId: colId });\r\n                                else\r\n                                    this.generateLink({ targetId: node.data.id, columnId: colId });\r\n                            }\r\n                        }, columnGroupShow: 'open',\r\n                        editable: (params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\r\n                        valueFormatter: this.gridService.getValueFormatter, // add formatting for col if set\r\n                        cellRenderer: this.gridService.getCellRenderer(col.fieldType),\r\n                        cellClassRules: { 'text-end': this.gridService.getCellTextAlign, 'text-danger': this.gridService.getCellClassRules },\r\n                        enableValue: this.gridService.getEnableGrouping(col.fieldType), enableRowGroup: this.gridService.getEnableGrouping(col.fieldType),\r\n                        aggFunc: this.gridService.getAggFunc(col.fieldType),\r\n                        lockPinned: true, lockVisible: true, suppressMovable: !this.isAdmin,\r\n                        width: getColWidth(col.name + (col.isRequired ? '*' : ''), [this.isAdmin, col.description?.length > 0])\r\n                    };\r\n\r\n                    // attach cell click for attachment columns\r\n                    if (col.fieldType === ColDataType.Attachment) {\r\n                        colDef.onCellClicked = (event) => this.onCellClicked(event);\r\n                        colDef.comparator = this.gridService.getNumComparator;\r\n                    }\r\n\r\n                    // attach num and date comparators\r\n                    if (col.fieldType >= ColDataType.Number && col.fieldType <= ColDataType.GPS) {\r\n                        colDef.comparator = this.gridService.getNumComparator;\r\n                        colDef.valueGetter = (params) => this.gridService.getNumericValue(col.id, params);\r\n                        colDef.valueParser = (params) => parseFloat(params.newValue);\r\n                    }\r\n\r\n                    if (col.fieldType === ColDataType.Date) {\r\n                        //colDef.cellEditorParams.format = 'dd MMM yyyy';\r\n                        colDef.filterParams = { valueFormatter: this.gridService.getFilterValueFormatter };\r\n                        colDef.comparator = this.gridService.getDateComparator;\r\n                    }\r\n\r\n                    if (col.type === ColumnVarType.TargetInfo) {\r\n                        colDef.headerClass = 'bg-header-color-3';\r\n                        infoCols.push(colDef);\r\n                    } else if (col.type === ColumnVarType.Target) {\r\n                        colDef.headerClass = 'bg-header-color-4';\r\n                        targetCols.push(colDef);\r\n                    }\r\n\r\n                    // add to conditions columns\r\n                    this.conditionsCols.push(col);\r\n                });\r\n\r\n                // sort dynamic cols by order and push to grid's columns\r\n                infoCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);\r\n                targetCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);\r\n\r\n                if (targetCols.length)              // make first col always visible in the group\r\n                    targetCols[0].columnGroupShow = null;\r\n\r\n                // add add new column if admin\r\n                if (this.isAdmin) {\r\n                    let fnOnColAdd = (e) => {\r\n                        this.colModalComponent.column = new DynamicColumn(0, filters.profId, '', ColumnVarType.TargetInfo, infoCols.length, 0);\r\n                        // provide cols for condition's component\r\n                        this.colModalComponent.columns = this.conditionsCols\r\n                            .filter(c => [ColumnVarType.TargetInfo, ColumnVarType.Target].includes(c.type));\r\n                        this.colModalComponent.open(e);\r\n                    };\r\n                    let addNewCol = {\r\n                        ...this.addNewColumn,\r\n                        headerComponentParams: { addNewCol: true, onColumnEdit: fnOnColAdd }\r\n                    };\r\n                    addNewCol.headerClass = 'bg-header-color-3';\r\n                    infoCols.push(addNewCol);\r\n\r\n                    fnOnColAdd = (e) => {\r\n                        this.colModalComponent.column = new DynamicColumn(0, filters.profId, '', ColumnVarType.Target, targetCols.length, 0);\r\n                        // provide cols for condition's component\r\n                        this.colModalComponent.columns = this.conditionsCols\r\n                            .filter(c => [ColumnVarType.TargetInfo, ColumnVarType.Target].includes(c.type));\r\n                        this.colModalComponent.open(e);\r\n                    };\r\n                    addNewCol = {\r\n                        ...this.addNewColumn,\r\n                        headerComponentParams: { addNewCol: true, onColumnEdit: fnOnColAdd.bind({}) }\r\n                    };\r\n                    addNewCol.headerClass = 'bg-header-color-4';\r\n                    targetCols.push(addNewCol);\r\n                }\r\n\r\n                // if dynamic info cols are there and info group is collapsed\r\n                if (infoCols.length && this.groupCollapsed[2])\r\n                    this.gridColumns.hiddenCol.hide = false;\r\n\r\n                let hiddenCol = Object.assign({}, this.gridColumns.hiddenCol)\r\n                hiddenCol.headerClass = 'bg-header-color-3';\r\n                this.columnDefs.push({\r\n                    groupId: `${ColumnVarType.TargetInfo}`, headerTooltip: 'Double click to expand or collapse this section.',\r\n                    headerName: 'Info', headerClass: 'bg-header-group-color-3',\r\n                    suppressMovable: true,\r\n                    marryChildren: true,\r\n                    openByDefault: !this.groupCollapsed[2].includes(this.selectedVals.profId),\r\n                    children: [...this.gridColumns.targetInfoCols, hiddenCol, ...infoCols]\r\n                });\r\n\r\n                hiddenCol = Object.assign({}, this.gridColumns.hiddenCol)\r\n                hiddenCol.headerClass = 'bg-header-color-4';\r\n                this.columnDefs.push({\r\n                    groupId: `${ColumnVarType.Target}`, headerTooltip: 'Double click to expand or collapse this section.',\r\n                    headerName: 'Target', headerClass: 'bg-header-group-color-4',\r\n                    suppressMovable: true,\r\n                    marryChildren: true, openByDefault: !this.groupCollapsed[3].includes(this.selectedVals.profId),\r\n                    children: targetCols.length > (this.isAdmin ? 2 : 1) ? [...targetCols, hiddenCol] : [...targetCols]\r\n                });\r\n            }\r\n\r\n            this.gridReady();\r\n            this.working = false;\r\n        } catch (ex) { console.error(ex); this.messageService.error('Something went wrong.'); } \r\n    }\r\n\r\n    // init'ed and ready\r\n    onGridReady(params: GridReadyEvent) {\r\n        this.gridApi = params.api;\r\n        this.gridColumnApi = params.columnApi;\r\n    }\r\n    \r\n    private gridReady(): void {\r\n        // bind to info group expand icon\r\n        setTimeout(() => {\r\n            // register click event on top left header cell\r\n            const headerCell = document.querySelector('.ag-header-cell[col-id=\"rowNum\"]');\r\n            if (headerCell) {\r\n                headerCell.addEventListener('click', (e) => {\r\n                    //this.gridApi.selectAll();\r\n                    this.gridApi.selectAllOnCurrentPage();\r\n                });\r\n            }\r\n            \r\n            // register click event info column group collapase button\r\n            const colGroupRow = document.querySelector('.ag-header-container .ag-header-row-column-group');\r\n            //colGroupRow.setAttribute('title', 'Double click to expand or collapse this section.');\r\n            colGroupRow.addEventListener('click', (e: MouseEvent) => this.storeGroupState(e));\r\n            colGroupRow.addEventListener('dblclick', (e: MouseEvent) => this.storeGroupState(e, true));\r\n\r\n            const groupExpandIcons = document.querySelectorAll('.ag-header-group-cell .ag-header-icon.ag-header-expand-icon');\r\n            if (groupExpandIcons) {\r\n                groupExpandIcons.forEach(icon => {\r\n                    icon.setAttribute('title', icon.classList.contains('ag-header-expand-icon-expanded')\r\n                        ? 'Collapse columns group' : 'Expand columns group');\r\n                });\r\n            }\r\n\r\n            // register an even to capture column id for getting column's position index on the grid when a column start being moved\r\n            if (this.isAdmin) {\r\n                document.querySelector('.ag-header')?.addEventListener('mousedown', (e: any) => {\r\n                    if (e.target.hasAttribute('col-id') || e.target.closest('.ag-header-cell')) {\r\n                        const colId = e.target.getAttribute('col-id') || e.target.closest('.ag-header-cell')?.getAttribute('col-id');\r\n                        if (colId) {\r\n                            this.movingColPos = this.gridColumnApi.getAllGridColumns()\r\n                                .findIndex(c => c.getColId() === colId);\r\n                        }\r\n                    }\r\n                });\r\n            }\r\n\r\n            // scroll grid even if cursor is outide the viewport (i.e. in header)\r\n            const gridHeader = document.querySelector('.ag-header-viewport');\r\n            const gridViewPort = document.querySelector('.ag-body-viewport');\r\n            const gridViewPortCenter = document.querySelector('.ag-center-cols-viewport');\r\n\r\n            gridHeader.addEventListener('wheel', (e: any) => {\r\n                e.preventDefault();\r\n                const deltaY = e.deltaY || e.wheelDeltaY;\r\n                const deltaX = e.deltaX || e.wheelDeltaX;\r\n                gridViewPort.scrollTop += deltaY;\r\n                gridViewPortCenter.scrollLeft += deltaX;\r\n            });\r\n\r\n            this.setExcelExportParams();\r\n        }, 500);\r\n    }\r\n\r\n    private storeGroupState(e: MouseEvent, dblClick?: boolean): void {\r\n        let colId;\r\n        const target = e.target as HTMLElement;\r\n        let group = dblClick ? target.querySelector('.ag-header-group-text') as HTMLSpanElement :\r\n            target.parentElement.parentElement.querySelector('.ag-header-group-text') as HTMLSpanElement;\r\n        \r\n        if (!group)\r\n            return;\r\n\r\n        if (group.innerText.startsWith('Status'))\r\n            colId = 1;\r\n        else if (group.innerText.startsWith('Location'))\r\n            colId = 2;\r\n        else if (group.innerText.startsWith('Info'))\r\n            colId = 3;\r\n        else colId = 4;\r\n\r\n        const storeName = `${this.selectedVals.dataType !== 'Progress' ? 't' : ''}group${colId}Collapsed`;\r\n        let stored = JSON.parse(localStorage.getItem(storeName) || '[]');\r\n        if (stored.includes(this.selectedVals.profId))\r\n            stored = stored.filter(i => i !== this.selectedVals.profId);\r\n        else\r\n            stored.push(this.selectedVals.profId);\r\n        localStorage.setItem(storeName, JSON.stringify(stored));\r\n        this.groupCollapsed[colId - 1] = stored;\r\n\r\n        // expand/collapse group\r\n        //if (!target.classList.contains('ag-icon')) {\r\n        //    console.log('trying to double click')\r\n        //    this.gridApi.column\r\n        //}\r\n    }\r\n\r\n    // *** Other grid's functions --------------------------------\r\n    private movingColPos: number = 0;\r\n    private moveByGrid: boolean = false;\r\n    async onColumnMoved(event: ColumnMovedEvent) {\r\n        if (!event.column) return;\r\n        if (event.finished) {\r\n            if (this.moveByGrid) {\r\n                this.moveByGrid = false;\r\n                return;\r\n            }\r\n\r\n            try {\r\n                const allCols = event.columnApi.getAllGridColumns();\r\n                const colIdAtPos = allCols[event.toIndex + 1]?.getColId();\r\n                const isNewColBefore = event.toIndex === allCols.length - 1 || allCols[event.toIndex - 1]?.getColId() === 'new';\r\n\r\n                const restrictedCols = ['asOf', 'year', 'qtr'];\r\n                // if to the right is a restrict col, or to left is new col\r\n                if (restrictedCols.includes(colIdAtPos) || isNewColBefore) {\r\n                    this.moveByGrid = true;\r\n                    event.columnApi.moveColumnByIndex(event.toIndex, this.movingColPos);\r\n                    this.messageService.info('Not allowed to move here. Moved back to its place.');\r\n                } else {\r\n                    // update column order in arrays here and in parent after updated on the server\r\n                    let colsToOrder: number[] = [];\r\n                    const movedColType = +event.column.getColDef().headerComponentParams.type;\r\n                    \r\n                    // get cols of the same type and intervention in the order it appears in grid after move\r\n                    // update column definitions first\r\n                    let gridGroupCols: ColDef[] = [];\r\n                    for (let i = 0; i < this.columnDefs.length; i++) {\r\n                        if (this.columnDefs[i]['groupId'] && this.columnDefs[i]['groupId'] === `${movedColType}`) {\r\n                            const group = this.columnDefs[i] as ColGroupDef;\r\n                            gridGroupCols = group.children as ColDef[];\r\n                            break;\r\n                        }\r\n                    }\r\n                    // now, update grid columns rendered\r\n                    let gOrder = 0;\r\n                    for (let i = 0; i < allCols.length; i++) {\r\n                        const colDef = allCols[i].getColDef();\r\n                        if (colDef.headerComponentParams?.type === undefined)\r\n                            continue;\r\n                        if (+colDef.headerComponentParams.type === movedColType) {\r\n                            gOrder++;\r\n                            colDef.headerComponentParams.colOrder = gOrder;     // update colDef order on grid\r\n                            gridGroupCols.find(c => c.colId === colDef.colId).headerComponentParams.colOrder = gOrder;\r\n                            colsToOrder.push(+colDef.colId);\r\n                        }\r\n                    }\r\n                    gridGroupCols.sort((x, y) => {\r\n                        if (!x.headerComponentParams || !y.headerComponentParams) return 0;\r\n                        return x.headerComponentParams.colOrder > y.headerComponentParams.colOrder ? 1 : -1;\r\n                    });\r\n                    \r\n                    // update on the server\r\n                    if (colsToOrder.length) {\r\n                        if (!(await this.colModalComponent.saveColumnsOrder(colsToOrder)))\r\n                            return;\r\n\r\n                        // saved on the server, now order all the arrays in the order of colsToOrder by modifying colOrder\r\n                        for (let i = 0; i < colsToOrder.length; i++) {\r\n                            this.dynamicColumns.find(c => c.id === colsToOrder[i]).order = i + 1;\r\n                            this.conditionsCols.find(c => c.id === colsToOrder[i]).order = i + 1;\r\n                        }\r\n\r\n                        // sort it as per the modified order\r\n                        let cols = this.dynamicColumns.filter(c => c.type === movedColType);\r\n                        cols.sort((x, y) => (x.order > y.order) ? 1 : -1);\r\n                        this.conditionsCols.sort((x, y) => x.type === movedColType && y.type === movedColType && (x.order > y.order) ? 1 : -1);\r\n\r\n                        // notify parent to update itself\r\n                        this.colDone.emit(cols);\r\n                    }\r\n                    }\r\n            } catch (ex) { console.error(ex); this.messageService.error('Something went wrong.'); } \r\n        }\r\n    }\r\n\r\n    onCellClicked(event: CellClickedEvent): void {\r\n        const colId = event.column.getColId();\r\n        if (colId === 'rowNum') {                   // if the row number column was clicked\r\n            //event.api.deselectAll();                // Deselect all other cells\r\n            //event.node.setSelected(true);           // Select the entire row\r\n            const cols = event.columnApi.getColumns().map(c => c.getColId());\r\n            let lastVisibleCol = cols[cols.length - 1];\r\n            if (lastVisibleCol.startsWith('cols-hidden')) {\r\n                for (let i = cols.length - 2; i >= 0; i--) {\r\n                    if (!cols[i].startsWith('cols-hidden')) {\r\n                        lastVisibleCol = cols[i];\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n            event.api.clearRangeSelection();\r\n            event.api.addCellRange({\r\n                columnStart: cols[1],               // start from column after rowNum\r\n                columnEnd: lastVisibleCol,\r\n                rowStartIndex: event.rowIndex,\r\n                rowEndIndex: event.rowIndex\r\n            });\r\n        } else {\r\n            if (!event.value && !event.column.isCellEditable(event.node))\r\n                return;\r\n            if (event.data.id > 0 && (event.data.activityId || event.data.uniqueId)) // only when record is saved\r\n                this.attachmentModalComponent.ngOnInit(colId, event);\r\n        }\r\n    }\r\n\r\n    onCellFocused(event: CellFocusedEvent): void {\r\n        this.gridApi.deselectAll();\r\n    }\r\n\r\n    public invalidCells = []; // col field, rowDataId\r\n    onCellValueChanged(event: CellValueChangedEvent): void {\r\n        if (event.data.region?.length || event.data.province?.length ||\r\n            event.data.district?.length || event.data.community?.length) {\r\n            event.data.dirty = true;        // mark row as dirty\r\n        } else event.data.dirty = false;\r\n        \r\n        if (event.newValue && this.invalidCells.length) {   // remove if marked as invalid cells\r\n            if (this.invalidCells.findIndex(invCell => invCell.rowDataId === event.data.id && invCell.field === event.column.getColDef().field) > -1)\r\n                this.invalidCells = this.invalidCells.filter(invCell => invCell.rowDataId !== event.data.id && invCell.field !== event.column.getColDef().field);\r\n            this.gridApi.refreshCells({ rowNodes: [event.node], columns: [event.column] });\r\n        } else {\r\n            // get all columns with conditions related to this cell's column being changed, so cell val is cleared if condition(s) evaluates to disable col\r\n            let relColsWithConds: string[] = [];\r\n            this.gridColumnApi.getColumns().forEach(col => {\r\n                let conds = col.getColDef().cellEditorParams?.conditions as ColumnCondition[];\r\n                if (conds?.length) {\r\n                    conds = conds.filter(c => ['enable', 'disable'].includes(c.type) && c.expLeft == event.colDef.colId);\r\n                    if (conds.length)\r\n                        relColsWithConds.push(col.getColId());\r\n                }\r\n            });\r\n            \r\n            if(relColsWithConds.length)\r\n                this.gridApi.refreshCells({ columns: relColsWithConds, rowNodes: [event.node], force: true, suppressFlash: true });\r\n        }\r\n        this.cellChanged.emit(event.rowIndex);              // emit changes to parent for pending save\r\n    }\r\n\r\n    @Output() filterApplied = new EventEmitter<string>();\r\n    onFilterApplied(event: FilterChangedEvent): void {\r\n        const col = event.columns[0].getColDef().headerName;\r\n        this.filterApplied.emit(event.columns[0].isFilterActive() ? col : '-' + col);\r\n    }\r\n\r\n    @Output() sortApplied = new EventEmitter<number>();\r\n    onSortChanged(event: SortChangedEvent): void {\r\n        let colsOnSort = 0;\r\n        this.gridColumnApi.getColumns()\r\n            .forEach(c => { if (c.getSort()) colsOnSort++ });\r\n        this.sortApplied.emit(colsOnSort);\r\n    }\r\n\r\n    onColumnRowGroupChanged(event: ColumnRowGroupChangedEvent) {\r\n        const colId = event.column?.getColId();\r\n        if(colId)\r\n            event.columnApi.applyColumnState({ state: [{ colId: colId, hide: false }] });\r\n        if (!this.gridColumnApi.getRowGroupColumns().length)\r\n            this.gridApi.refreshHeader();\r\n    }\r\n    // -----------------------------------------------------------\r\n\r\n    // --- DynamicColumn\r\n    onColumnDone(col: DynamicColumn): void {\r\n        try {\r\n            // get reference to grid's columns'\r\n            let gridStaticCol: ColDef;\r\n            let gridGroupCols: ColDef[] = [];\r\n            for (let i = 0; i < this.columnDefs.length; i++) {\r\n                if (this.columnDefs[i]['groupId'] && this.columnDefs[i]['groupId'] === `${col.type}`) {\r\n                    const group = this.columnDefs[i] as ColGroupDef;\r\n                    if (col.type === ColumnVarType.TargetStatic || col.type === ColumnVarType.ProgressStatic) {\r\n                        gridStaticCol = group.children.find(c => c['colId'] === col.id);\r\n                        if (gridStaticCol)\r\n                            break;\r\n                    } else {\r\n                        gridGroupCols = group.children as ColDef[];\r\n                        break;\r\n                    }\r\n                }\r\n            }\r\n        \r\n            // if deleted\r\n            if (col.id < 0) {\r\n                // remove column from grid\r\n                const delInd = gridGroupCols.findIndex(c => c.colId == `${col.id * -1}`);\r\n                gridGroupCols.splice(delInd, 1);\r\n                this.gridApi.setColumnDefs(this.columnDefs);\r\n            \r\n                // remove from cols arrays\r\n                this.dynamicColumns = this.dynamicColumns.filter(c => c.id !== col.id * -1);\r\n                this.conditionsCols = this.conditionsCols.filter(c => c.id !== col.id * -1);\r\n\r\n                // remove from variables in the parent's profile arrays\r\n                this.colDone.emit([col]);   // emitted with -1 as id included\r\n            } else {\r\n                // find the col in columns since it's already in grid\r\n                let colInd = this.dynamicColumns.findIndex(c => c.id === col.id);\r\n                if (colInd > -1) {     // is updated\r\n                    // update the col on the grid\r\n                    if (gridStaticCol) {        // static columns are only getting updated, niether added nor duplicated\r\n                        gridStaticCol.headerName = col.displayName || col.name;\r\n                        gridStaticCol.headerTooltip = col.description;\r\n                        gridStaticCol.headerComponentParams.isRequired = col.isRequired;\r\n                        gridStaticCol.headerComponentParams.colType = col.fieldType;\r\n                        gridStaticCol.headerComponentParams.showInfoIcon = col.description?.length > 0 || false;\r\n                    } else {\r\n                        const gColInd = gridGroupCols.findIndex(gc => gc.colId == `${col.id}`);\r\n                        if (gColInd > -1) {\r\n                            gridGroupCols[gColInd].headerName = col.displayName || col.name;\r\n                            gridGroupCols[gColInd].headerTooltip = col.description;\r\n                            gridGroupCols[gColInd].headerComponentParams.isRequired = col.isRequired;\r\n                            gridGroupCols[gColInd].headerComponentParams.colType = col.fieldType;\r\n                            gridGroupCols[gColInd].headerComponentParams.showInfoIcon = col.description?.length > 0 || false;\r\n                            gridGroupCols[gColInd].cellEditor = this.gridService.getCellEditor(col.fieldType);\r\n                            gridGroupCols[gColInd].cellEditorParams = {\r\n                                ...this.gridService.getCellEditorParams(col),\r\n                                onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {\r\n                                    this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\r\n                                }, onCellLink: (colId: string, node: IRowNode) => {\r\n                                    if (this.selectedVals.dataType === 'Progress')\r\n                                        this.generateLink({ activityId: node.data.id, activityProgressId: node.data.progressId, columnId: colId });\r\n                                    else\r\n                                        this.generateLink({ targetId: node.data.id, columnId: colId });\r\n                                }\r\n                            };\r\n                            gridGroupCols[gColInd].editable = (params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable);\r\n                            gridGroupCols[gColInd].cellRenderer = this.gridService.getCellRenderer(col.fieldType);\r\n                            gridGroupCols[gColInd].enableRowGroup = this.gridService.getEnableGrouping(col.fieldType);\r\n                            gridGroupCols[gColInd].enableValue = this.gridService.getEnableGrouping(col.fieldType);\r\n                            gridGroupCols[gColInd].aggFunc = this.gridService.getAggFunc(col.fieldType);\r\n                            gridGroupCols[gColInd].width = getColWidth(col.name + (col.isRequired ? '*' : ''), [this.isAdmin, col.description?.length > 0]);\r\n\r\n                            // attach cell click for attachment columns\r\n                            if (col.fieldType === ColDataType.Attachment)\r\n                                gridGroupCols[gColInd].onCellClicked = (event) => this.onCellClicked(event);\r\n                        }\r\n                    }\r\n                    \r\n                    // update changed props only\r\n                    this.dynamicColumns[colInd].name = col.name;\r\n                    this.dynamicColumns[colInd].displayName = col.displayName;\r\n                    this.dynamicColumns[colInd].description = col.description;\r\n                    this.dynamicColumns[colInd].fieldType = col.fieldType;\r\n                    this.dynamicColumns[colInd].fieldTypeValues = col.fieldTypeValues;\r\n                    this.dynamicColumns[colInd].isRequired = col.isRequired;\r\n                    this.dynamicColumns[colInd].conditionsApplied = col.conditionsApplied;\r\n\r\n                    // update conditions array as well\r\n                    colInd = this.conditionsCols.findIndex(c => c.id === col.id);\r\n                    if (colInd > -1) {\r\n                        // update only necessary props\r\n                        this.conditionsCols[colInd].name = col.name;\r\n                        this.conditionsCols[colInd].displayName = col.displayName;\r\n                        this.conditionsCols[colInd].fieldType = col.fieldType;\r\n                        this.conditionsCols[colInd].fieldTypeValues = col.fieldTypeValues;\r\n                    }\r\n\r\n                    // set defs to grid and auto-size it\r\n                    this.gridApi.setColumnDefs(this.columnDefs);\r\n                    this.gridApi.refreshHeader();\r\n                    this.gridColumnApi.autoSizeColumn(`${col.id}`);\r\n\r\n                    // update the col in the parent's profile vars\r\n                    this.colDone.emit([col]);\r\n                } else {                // is added or duplicated\r\n                    // fix the col order\r\n                    if (col.order < 0) col.order *= -1;\r\n                    this.dynamicColumns.forEach(c => {\r\n                        if (c.type === col.type && c.order >= col.order)\r\n                            c.order = c.order++;\r\n                    });\r\n\r\n                    // add to the cols arrays\r\n                    this.dynamicColumns.push(col);\r\n                    this.conditionsCols.push(col);\r\n\r\n                    // add to the grid at the position in order\r\n                    let condColTypes = [];\r\n                    if (col.type === ColumnVarType.Target || col.type === ColumnVarType.TargetInfo)\r\n                        condColTypes = [ColumnVarType.TargetInfo, ColumnVarType.Target];\r\n                    else\r\n                        condColTypes = [ColumnVarType.ProgressInfo, ColumnVarType.Progress];\r\n\r\n                    for (let i = 0; i < gridGroupCols.length; i++) {\r\n                        if (gridGroupCols[i].headerComponentParams?.colOrder >= col.order)\r\n                            gridGroupCols[i].headerComponentParams.colOrder++;\r\n                    }\r\n                    // add to the end\r\n                    let colDef: ColDef = {\r\n                        colId: `${col.id}`,\r\n                        field: 'colVals.dCol' + col.id,\r\n                        headerName: col.displayName || col.name,\r\n                        headerTooltip: col.description,\r\n                        headerComponent: GridCustomHeader,\r\n                        headerComponentParams: {\r\n                            enableEdit: true,\r\n                            isRequired: col.isRequired,\r\n                            type: col.type,\r\n                            colType: col.fieldType,\r\n                            colOrder: col.order,\r\n                            showInfoIcon: col.description?.length > 0,\r\n                            onColumnEdit: (e) => {\r\n                                // set column on child\r\n                                this.colModalComponent.column = Object.assign({}, col);\r\n                                // provide cols for condition's component\r\n                                this.colModalComponent.columns = this.conditionsCols.filter(c => condColTypes.includes(c.type));\r\n                                this.colModalComponent.open(e, `${col.id}`);\r\n                            }\r\n                        },\r\n                        cellEditor: this.gridService.getCellEditor(col.fieldType),\r\n                        cellEditorParams: {\r\n                            ...this.gridService.getCellEditorParams(col),\r\n                            onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {\r\n                                this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);\r\n                            }, onCellLink: (colId: string, node: IRowNode) => {\r\n                                if (this.selectedVals.dataType === 'Progress')\r\n                                    this.generateLink({ activityId: node.data.id, activityProgressId: node.data.progressId, columnId: colId });\r\n                                else\r\n                                    this.generateLink({ targetId: node.data.id, columnId: colId });\r\n                            }\r\n                        },\r\n                        editable: (params) => this.gridService.getCellEditable(params, this.defaultColDefs.editable),\r\n                        valueFormatter: this.gridService.getValueFormatter, // add formatting for col if set\r\n                        cellRenderer: this.gridService.getCellRenderer(col.fieldType),\r\n                        cellClassRules: { 'text-end': this.gridService.getCellTextAlign, 'text-danger': this.gridService.getCellClassRules },\r\n                        width: getColWidth(col.name + (col.isRequired ? '*' : ''), [this.isAdmin, col.description?.length > 0]),\r\n                        enableValue: this.gridService.getEnableGrouping(col.fieldType), enableRowGroup: this.gridService.getEnableGrouping(col.fieldType),\r\n                        aggFunc: this.gridService.getAggFunc(col.fieldType),\r\n                        lockPinned: true, lockVisible: true, suppressMovable: false,\r\n                        columnGroupShow: gridGroupCols.length > 0 ? 'open' : null\r\n                    };\r\n\r\n                    if (col.type === ColumnVarType.ProgressInfo || col.type === ColumnVarType.TargetInfo)\r\n                        colDef.headerClass = 'bg-header-color-3';\r\n                    else\r\n                        colDef.headerClass = 'bg-header-color-4';\r\n\r\n                    // attach cell click for attachment columns\r\n                    if (col.fieldType === ColDataType.Attachment)\r\n                        colDef.onCellClicked = (event) => this.onCellClicked(event);\r\n                    gridGroupCols.push(colDef);\r\n\r\n                    // now sort it to maintain order\r\n                    gridGroupCols.sort((x, y) => {\r\n                        if (y.colId.startsWith('new'))\r\n                            return -1; \r\n                        if (!x.headerComponentParams?.colOrder || !y.headerComponentParams?.colOrder)\r\n                            return 1;\r\n                        if (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder)\r\n                            return 1;\r\n                        return -1;\r\n                    });\r\n\r\n                    // set col defs to grid and auto-size it\r\n                    this.gridApi.setColumnDefs(this.columnDefs);\r\n                    this.gridColumnApi.autoSizeColumn(String(col.id));// s(gridGroupCols.map(c => c.colId));\r\n\r\n                    // add to the profile vars in the parent, emit all cols of this type\r\n                    // filter and sort it before emitting to parent\r\n                    let cols = this.dynamicColumns.filter(c => c.type === col.type);\r\n                    cols.sort((x, y) => (x.order > y.order) ? 1 : -1);\r\n\r\n                    this.colDone.emit(cols);\r\n                }\r\n            }\r\n        } catch (ex) { console.error(ex); this.messageService.error('Something went wrong.'); } \r\n    }\r\n\r\n    // *** DATA\r\n    refreshGridRows(data: (ITargetData | IProgressData)[], enableEditing: boolean): void {\r\n        this.defaultColDefs.editable = enableEditing;\r\n        this.gridApi.setDefaultColDef(this.defaultColDefs);\r\n\r\n        // auto fit dynamic cols: NOT(ColVarType.TargetStatic or ColVarType.ProgressStatic)\r\n        // this.gridColumnApi.autoSizeColumns(this.dynamicColumns.filter(c => ![3, 5].includes(c.type)).map(c => `${c.id}`));\r\n\r\n        this.invalidCells = [];\r\n\r\n        this.rowData = [...data];\r\n        for (let i = 1; i <= 20; i++)       // append extra rows\r\n            this.rowData.push({ id: 0, colVals: {}, dirty: false } as IProgressData);\r\n\r\n        // fetch communities for the data\r\n        if (this.selectedVals.dataType === 'Progress') {\r\n            const commCol = this.gridColumnApi.getColumns().find(c => c.getColDef().field === 'community');\r\n            if (data.length) {\r\n                let allSelProvs = data.map(d => d.province).join(';').replace(/,/g, ';');\r\n                const provIds = [...new Set(allSelProvs.split(';'))];\r\n                allSelProvs = provIds.join(';');\r\n\r\n                if (this.gridService.communities.length) {\r\n                    allSelProvs = '';\r\n                    for (let i = 0; i < provIds.length; i++) {\r\n                        if (this.gridService.communities.findIndex(c => c.provinceId === +provIds[i]) === -1)\r\n                            allSelProvs += provIds[i] + ';';\r\n                    }\r\n                    allSelProvs = allSelProvs.length ? allSelProvs.substring(0, allSelProvs.length - 1) : '';\r\n                }\r\n\r\n                if (allSelProvs.length) {\r\n                    this.gridService.getCommunities(allSelProvs).then((res) => {\r\n                        if (res) {      // refresh community column\r\n                            commCol.getColDef().cellEditorParams.values = [...this.gridService.communities];\r\n                            this.gridApi.refreshCells({ columns: [commCol.getColId()], force: true, suppressFlash: true });\r\n                        }\r\n                    });\r\n                    return;\r\n                }\r\n            }\r\n            if (commCol) { // refresh community column\r\n                commCol.getColDef().cellEditorParams.values = [...this.gridService.communities];\r\n                this.gridApi.refreshCells({ columns: [commCol.getColId()], force: true, suppressFlash: true });\r\n            }\r\n        }\r\n    }\r\n\r\n    private setExcelExportParams(): void {\r\n        let fName = `AIMS3_${this.selectedVals.org}_${this.selectedVals.prof}_`;\r\n        fName += this.selectedVals.proj.replace(/[<>:\"\\/\\\\|?*]+/g, '');\r\n        fName += this.selectedVals.dataType === 'Progress' ? '_Progress.xlsx' : '_Target.xlsx';\r\n        let cols = this.gridColumnApi.getColumns().map(c => c.getColId());\r\n        cols = cols.filter(c => !c.startsWith('new') && !c.startsWith('cols-hidden'));\r\n\r\n        // set excel export params\r\n        this.gridOptions.defaultExcelExportParams = {\r\n            author: 'AIMS 3.0', fileName: fName, allColumns: true,\r\n            sheetName: this.selectedVals.prof.replace(/[<>:\"\\/\\\\|?*]+/g, ''),\r\n            columnKeys: cols\r\n        };\r\n    }\r\n\r\n    onAttached(e: any) {\r\n        const rowData = this.rowData.find(r => r.id === e.id);\r\n        if (e.colId > 0)\r\n            rowData.colVals['dCol' + e.colId] += e.emitVal;\r\n        else\r\n            rowData['file'] += e.emitVal;\r\n        this.gridApi.refreshCells();\r\n    }\r\n\r\n    onComment(e: any): void {\r\n        const node = this.gridApi.getRowNode(e.node.id);\r\n        if (!node.data.comments)\r\n            node.data.comments = [];\r\n\r\n        if (e.updated) {\r\n            const comment = node.data.comments.find(c => c.commentId === e.commentId);\r\n            if(comment) comment.isResolved = !comment.isResolved;\r\n        } else if (e.deleted)\r\n            node.data.comments = node.data.comments.filter(c => c.commentId !== e.commentId);\r\n        else {\r\n            node.data.comments = [...node.data.comments, {\r\n                id: e.isProgress ? node.data.progressId : node.data.id,\r\n                commentId: e.commentId, colId: e.colId, isNote: e.isNote, isResolved: e.isResolved\r\n            }];\r\n        }\r\n        this.gridApi.refreshCells({ rowNodes: [node], force: true, suppressFlash: true });\r\n    }\r\n\r\n    generateLink(params: Comment | any): void {\r\n        // format: {OrgId}S{ProfId}a{ProjId}b{ActOrTarId}a{Month-Year}n{ColId}\r\n        let link = `${this.selectedVals.orgId}S${this.selectedVals.profId}`;\r\n        link += `a${this.selectedVals.projId}`;\r\n\r\n        if (!params.commentOn) {\r\n            const colMap = this.commentModalComponent.staticColMap\r\n                .find(c => c.colId === params.columnId);\r\n            if (colMap)\r\n                params.columnId = colMap.dColId;\r\n            else params.columnId = +params.columnId;\r\n        }\r\n\r\n        if (params.activityId > 0) {\r\n            link += `b${params.activityId}`;\r\n            if (params.activityProgressId > 0)\r\n                link += `a${this.selectedVals.month}-${this.selectedVals.year}`;\r\n            else link += `aMY`;\r\n        } else link += `b${params.targetId}a0`;\r\n\r\n        link += `n${params.columnId}`;\r\n        link = (params.commentOn ? '#comment=' : '#cell=') + link;\r\n\r\n        navigator.clipboard.writeText(location.origin + location.pathname + link);\r\n        this.messageService.info('Link copied to your clipboard.', 'Link Created');\r\n    }\r\n\r\n    ngOnDestroy() {\r\n        this.subscriptions.forEach((sb) => sb.unsubscribe());\r\n    }\r\n}", "<aims-working *ngIf=\"working\"></aims-working>\r\n\r\n<ag-grid-angular style=\"width: 100%; height: 100%\" #aimsGrid\r\n                 [ngClass]=\"{ 'ag-theme-alpine': !isDarkMode, 'ag-theme-alpine-dark': isDarkMode }\"\r\n                 [gridOptions]=\"gridOptions\"\r\n                 [columnDefs]=\"columnDefs\"\r\n                 [defaultColDef]=\"defaultColDefs\"\r\n                 [components]=\"components\"\r\n                 [rowData]=\"rowData\"\r\n                 [animateRows]=\"true\"\r\n                 [tooltipShowDelay]=\"tooltipShowDelay\"\r\n                 (cellValueChanged)=\"onCellValueChanged($event)\"\r\n                 (gridReady)=\"onGridReady($event)\"\r\n                 (columnMoved)=\"onColumnMoved($event)\"\r\n                 (cellFocused)=\"onCellFocused($event)\"\r\n                 (filterChanged)=\"onFilterApplied($event)\"\r\n                 (sortChanged)=\"onSortChanged($event)\"\r\n                 (columnRowGroupChanged)=\"onColumnRowGroupChanged($event)\">\r\n</ag-grid-angular>\r\n\r\n<!-- Dropdown menu modal form: Actions Menu to Add/Edit Dynamic Column -->\r\n<column-form-modal id=\"columnForm\" (done)=\"onColumnDone($event)\"></column-form-modal>\r\n\r\n<!-- Attachment/upload modal -->\r\n<attachments-modal id=\"attachmentModal\" (done)=\"onAttached($event)\"></attachments-modal>\r\n\r\n<!-- Dropdown menu modal form: Add/Edit Comment/Note -->\r\n<comment-modal id=\"commentForm\" (done)=\"onComment($event)\" (genLink)=\"generateLink($event)\" *ngIf=\"rowData.length\"></comment-modal>"], "mappings": ";AAAA,SAAoBA,YAAY,QAA8C,eAAe;AAM7F,OAAO,oBAAoB;AAC3B,SAASC,cAAc,QAAQ,oBAAoB;AAEnD,SAASC,WAAW,QAAQ,yCAAyC;AACrE,SAASC,cAAc,EAAEC,WAAW,EAAEC,aAAa,QAAQ,0BAA0B;AAErF,SAASC,WAAW,QAAQ,8BAA8B;AAC1D,SAA0BC,aAAa,QAAQ,gDAAgD;AAC/F,SAASC,gBAAgB,QAAQ,gDAAgD;AAGjF,SAASC,wBAAwB,QAAQ,+DAA+D;AACxG,SAASC,cAAc,QAAQ,+CAA+C;AAC9E,SAASC,eAAe,QAAQ,iDAAiD;AACjF,SAASC,cAAc,QAAQ,+CAA+C;AAC9E,SAASC,gBAAgB,QAAQ,mDAAmD;AACpF,SAASC,cAAc,QAAQ,+CAA+C;AAC9E,SAASC,sBAAsB,QAAQ,6DAA6D;AACpG,SAASC,oBAAoB,QAAQ,yDAAyD;AAC9F,SAASC,uBAAuB,QAAQ,2DAA2D;AACnG,SAASC,gBAAgB,QAAQ,gDAAgD;AAEjF,SAASC,0BAA0B,QAAQ,qDAAqD;;;;;;;;;;;;IC5BhGC,EAAA,CAAAC,SAAA,mBAA6C;;;;;;IA2B7CD,EAAA,CAAAE,cAAA,uBAAmH;IAAnFF,EAAA,CAAAG,UAAA,kBAAAC,qEAAAC,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAQT,EAAA,CAAAU,WAAA,CAAAF,MAAA,CAAAG,SAAA,CAAAN,MAAA,CAAiB;IAAA,EAAC,qBAAAO,wEAAAP,MAAA;MAAAL,EAAA,CAAAM,aAAA,CAAAC,GAAA;MAAA,MAAAM,MAAA,GAAAb,EAAA,CAAAS,aAAA;MAAA,OAAYT,EAAA,CAAAU,WAAA,CAAAG,MAAA,CAAAC,YAAA,CAAAT,MAAA,CAAoB;IAAA,EAAhC;IAAyDL,EAAA,CAAAe,YAAA,EAAgB;;;;;;;ADQnI,OAAM,MAAOC,aAAa;EAwDtBC,YACYC,WAAwB,EACxBC,cAA8B;IAD9B,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,cAAc,GAAdA,cAAc;IAzD1B,KAAAC,OAAO,GAAY,KAAK;IACjB,KAAAC,UAAU,GAAY,KAAK;IAC3B,KAAAC,OAAO,GAAY,KAAK;IAQxB,KAAAC,UAAU,GAAG;MAChB,UAAU,EAAE/B,cAAc;MAC1B,iBAAiB,EAAED,eAAe;MAClC,gBAAgB,EAAEG,cAAc;MAChC,gBAAgB,EAAEJ,cAAc;MAChC,sBAAsB,EAAEM,oBAAoB;MAC5C,wBAAwB,EAAED,sBAAsB;MAChD,uBAAuB,EAAEE,uBAAuB;MAChD,kBAAkB,EAAEJ;KACvB;IACM,KAAA+B,gBAAgB,GAAW,GAAG;IAC7B,KAAAC,cAAc,GAAG,CAAC,EAAE,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE,CAAC;IAG9B,KAAAC,WAAW,GAAG;MAClBC,UAAU,EAAE,EAAE;MACdC,gBAAgB,EAAE,IAAI;MACtBC,kBAAkB,EAAE,EAAE;MACtBC,eAAe,EAAE,IAAI;MACrBC,eAAe,EAAE,IAAI;MACrBC,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,IAAI,CAAS;KAC3B;IAKD;IACA,KAAAC,cAAc,GAAoB,EAAE;IACpC,KAAAC,cAAc,GAAoB,EAAE;IAC1B,KAAAC,OAAO,GAAG,IAAIxD,YAAY,EAAmB;IAC7C,KAAAyD,WAAW,GAAG,IAAIzD,YAAY,EAAU;IAElD;IACO,KAAA0D,OAAO,GAAoC,EAAE;IAC7C,KAAAC,aAAa,GAAa,EAAE;IAC5B,KAAAC,YAAY,GAAG;MAAEC,QAAQ,EAAE,UAAU;MAAEC,EAAE,EAAE,CAAC;MAAEC,KAAK,EAAE,CAAC;MAAEC,GAAG,EAAE,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,MAAM,EAAE,CAAC;MAAEC,IAAI,EAAE,EAAE;MAAEC,KAAK,EAAE,CAAC;MAAEC,IAAI,EAAE;IAAC,CAAE;IAQ7H,KAAAC,aAAa,GAAmB,EAAE;IA0mC1C;IACQ,KAAAC,YAAY,GAAW,CAAC;IACxB,KAAAC,UAAU,GAAY,KAAK;IA+G5B,KAAAC,YAAY,GAAG,EAAE,CAAC,CAAC;IA6BhB,KAAAC,aAAa,GAAG,IAAI3E,YAAY,EAAU;IAM1C,KAAA4E,WAAW,GAAG,IAAI5E,YAAY,EAAU;IAzvC9C,IAAI6E,QAAQ,CAACC,aAAa,CAAC,oBAAoB,CAAC,EAC5C,IAAI,CAACrC,UAAU,GAAG,IAAI;IAE1B;IACAxC,cAAc,CAAC8E,aAAa,CAAC7E,WAAW,CAAC8E,YAAY,CAACC,UAAU,CAAC;IAEjE;IACA;IACA,IAAI,CAACC,WAAW,GAAG;MACfC,iBAAiB,EAAE,EAAE;MACrBC,YAAY,EAAE,EAAE;MAChBC,SAAS,EAAE,EAAE;MAAEC,cAAc,EAAE,IAAI;MACnCC,YAAY,EAAE,UAAU;MACxBC,oBAAoB,EAAE,IAAI;MAAEC,gBAAgB,EAAE,IAAI;MAClDC,mBAAmB,EAAE,IAAI;MAAEC,wBAAwB,EAAE,EAAE;MACvDC,cAAc,EAAE,EAAE;MAClBC,yBAAyB,EAAE,IAAI;MAC/BC,qBAAqB,EAAE,IAAI;MAC3BC,gBAAgB,EAAE,WAAW;MAC7BC,oBAAoB,EAAE,CAAC,CAAC;MACxBC,kBAAkB,EAAE,IAAI;MACxBC,kBAAkB,EAAE,IAAI;MACxBC,oBAAoB,EAAE,IAAI;MAC1BC,aAAa,EAAE;QACX,YAAY,EAAGC,MAAM,IAAKA,MAAM,CAACC,IAAI,EAAEC,MAAM,IAAIpG,cAAc,CAACqG,SAAS;QACzE,+BAA+B,EAAGH,MAAM,IAAKA,MAAM,CAACC,IAAI,EAAEC,MAAM,IAAIpG,cAAc,CAACsG,QAAQ;QAC3F,6BAA6B,EAAGJ,MAAM,IAAKA,MAAM,CAACC,IAAI,EAAEC,MAAM,IAAIpG,cAAc,CAACuG,SAAS;QAC1F,eAAe,EAAGL,MAAM,IAAK,CAACA,MAAM,CAACC,IAAI,EAAEC,MAAM,IAAIF,MAAM,CAACC,IAAI,EAAEK,YAAY,CAAM;OACvF;MAAEC,SAAS,EAAE;QACVC,YAAY,EAAE,CACV;UAAEC,WAAW,EAAE,qCAAqC;UAAEC,KAAK,EAAE;QAAM,CAAE;QACrE;QACA;UAAED,WAAW,EAAE;QAA6B,CAAE,EAC9C;UAAEA,WAAW,EAAE;QAA6B,CAAE,EAC9C;UAAEA,WAAW,EAAE;QAAwB,CAAE;OAEhD;MAAEE,wBAAwB,EAAE,IAAI,CAAC1E,WAAW,CAAC2E,OAAO;MAAEC,uBAAuB,EAAGb,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAAC4E,uBAAuB,CAACb,MAAM,CAAC;MAC5Ic,cAAc,EAAGC,CAAoB,IAAK,IAAI,CAAC9E,WAAW,CAAC+E,yBAAyB,CAACD,CAAC,EAAE,IAAI,CAACE,cAAc,CAACC,QAAQ,EAAE,IAAI,CAAChE,cAAc,CAAC;MAC1IiE,mBAAmB,EAAGJ,CAAyB,IAAK,IAAI,CAAC9E,WAAW,CAAC+E,yBAAyB,CAACD,CAAC,EAAE,IAAI,CAACE,cAAc,CAACC,QAAQ,EAAE,IAAI,CAAChE,cAAc,CAAC;MACpJkE,aAAa,EAAE,IAAI,CAACnF,WAAW,CAACoF,iBAAiB;MAAEC,mBAAmB,EAAGP,CAAC,IAAK,IAAI,CAAC9E,WAAW,CAACsF,cAAc,CAACR,CAAC,EAAE,IAAI,CAACE,cAAc,CAACC,QAAQ;KACjJ;IAED;IACA,IAAI,CAACD,cAAc,GAAG;MAClBO,QAAQ,EAAE,KAAK;MACfC,SAAS,EAAE,IAAI;MACfC,MAAM,EAAE,IAAI;MACZR,QAAQ,EAAE,KAAK;MACfS,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,CAAC,eAAe,EAAE,gBAAgB,CAAC;MAC7CC,cAAc,EAAE;QACZ,iBAAiB,EAAG7B,MAAM,IAAK,IAAI,CAAC3B,YAAY,CAACyD,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACC,SAAS,KAAKhC,MAAM,CAACC,IAAI,EAAEgC,EAAE,IAAIF,CAAC,CAACG,KAAK,KAAKlC,MAAM,CAACmC,MAAM,CAACC,SAAS,EAAE,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC;QACpJ,aAAa,EAAGlC,MAAM,IAAI;UAAG,IAAI,CAACA,MAAM,CAACC,IAAI,EAAE,OAAO,KAAK;UACvD,OAAO,CAACD,MAAM,CAACC,IAAI,CAACoC,UAAU,IAAIrC,MAAM,CAACC,IAAI,CAACqC,QAAQ,KAAK,CAACtC,MAAM,CAACC,IAAI,CAACC,MAAM,IAAI,CAACF,MAAM,CAACmC,MAAM,CAACI,QAAQ,EAAE,CAACC,UAAU,CAAC,KAAK,CAAC,IACzH,CAACxC,MAAM,CAACmC,MAAM,CAACI,QAAQ,EAAE,CAACC,UAAU,CAAC,aAAa,CAAC,IAAI,CAACxC,MAAM,CAACmC,MAAM,CAACM,cAAc,CAACzC,MAAM,CAAC0C,IAAI,CAAC;QACzG,CAAC;QAAE,aAAa,EAAG1C,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAAC0G,mBAAmB,CAAC3C,MAAM,CAAC;QAC1E,SAAS,EAAGA,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAAC0G,mBAAmB,CAAC3C,MAAM,EAAE,MAAM,CAAC;QAC3E,YAAY,EAAGA,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAAC0G,mBAAmB,CAAC3C,MAAM,EAAE,YAAY;OACtF;MAAE4C,aAAa,EAAGC,KAAK,IAAI;QACxB,MAAMC,MAAM,GAAID,KAAK,CAACA,KAAK,CAACC,MAAsB,CAACC,OAAO,CAAC,UAAU,CAAgB;QACrF,IAAID,MAAM,EAAEE,SAAS,CAACC,QAAQ,CAAC,aAAa,CAAC,EAAE;UAC3C,MAAMC,IAAI,GAAGJ,MAAM,CAACK,qBAAqB,EAAE;UAC3C,MAAMpC,CAAC,GAAI8B,KAAK,CAACA,KAAoB;UACrC,IAAG9B,CAAC,CAACqC,CAAC,IAAIF,IAAI,CAACG,GAAG,GAAC,CAAC,IAAItC,CAAC,CAACuC,CAAC,IAAIJ,IAAI,CAACK,KAAK,GAAC,CAAC,EACvC,IAAI,CAACC,qBAAqB,CAACC,IAAI,CAACZ,KAAK,CAACa,MAAM,EAAEb,KAAK,CAACH,IAAI,EAAEQ,IAAI,EAAE,KAAK,EAAE,IAAI,EAAEJ,MAAM,CAAC;;MAEhG,CAAC;MACDa,UAAU,EAAE,IAAI,CAAC1H,WAAW,CAAC2H;KAChC;IAED;IACA,IAAI,CAACC,YAAY,GAAG;MAChBC,KAAK,EAAE,KAAK;MACZC,UAAU,EAAE,KAAK;MACjBC,aAAa,EAAE,gBAAgB;MAC/BC,WAAW,EAAE,mCAAmC;MAChDC,eAAe,EAAErJ,gBAAgB;MACjCsJ,SAAS,EAAE,qBAAqB;MAChCC,UAAU,EAAE,IAAI;MAChBC,eAAe,EAAE,IAAI;MACrBC,KAAK,EAAE,EAAE;MACT3C,QAAQ,EAAE,EAAE;MACZ4C,QAAQ,EAAE,EAAE;MAAEC,kBAAkB,EAAE,IAAI;MACtCtD,QAAQ,EAAE,KAAK;MAAEuD,cAAc,EAAE,KAAK;MACtCC,iBAAiB,EAAE,IAAI;MACvBhD,MAAM,EAAE,KAAK;MACbiD,YAAY,EAAE,IAAI;MAClBnD,QAAQ,EAAE,KAAK;MACfoD,iBAAiB,EAAE,IAAI;MACvBC,gBAAgB,EAAE,IAAI;MACtBC,WAAW,EAAE;KAChB;EACL;EAEA;EACAC,QAAQA,CAAA;IAAA,IAAAC,KAAA;IACJ,IAAI;MACA,IAAI,CAAC/D,cAAc,CAACC,QAAQ,GAAG,IAAI,CAAC7E,OAAO;MAE3C;MACA,IAAI,CAAC4I,UAAU,GAAG,CAAC;QACfnB,KAAK,EAAE,QAAQ;QACfC,UAAU,EAAE,EAAE;QACdpC,QAAQ,EAAE,EAAE;QAAE2C,KAAK,EAAE,EAAE;QAAEC,QAAQ,EAAE,GAAG;QACtCF,eAAe,EAAE,IAAI;QACrB3C,MAAM,EAAE,KAAK;QAAEF,QAAQ,EAAE,KAAK;QAC9B0D,MAAM,EAAE,MAAM;QACdhE,QAAQ,EAAE,KAAK;QAAEuD,cAAc,EAAE,KAAK;QACtCE,YAAY,EAAE,IAAI;QAAEQ,YAAY,EAAE,IAAI;QACtCX,kBAAkB,EAAE,IAAI;QACxB5B,aAAa,EAAE,IAAI,CAACA,aAAa;QACjCwC,YAAY,EAAGpF,MAA2B,IAAI;UAAG,OAAOA,MAAM,CAAC0C,IAAI,CAAC2C,MAAM,GAAG,OAAO,GAAGrF,MAAM,CAAC0C,IAAI,CAAC4C,QAAQ,GAAG,CAAC;QAAE;OACpH,CAAC;MAEF;MACA,IAAI,CAAC7I,WAAW,CAACE,gBAAgB,GAAG;QAChCuF,KAAK,EAAE,YAAY;QACnB6B,UAAU,EAAE,aAAa;QACzBE,WAAW,EAAE,UAAU;QACvB/C,QAAQ,EAAE,KAAK;QAAEsD,kBAAkB,EAAE,IAAI;QACzCH,eAAe,EAAE,IAAI;QAAEI,cAAc,EAAE,KAAK;QAC5CS,MAAM,EAAE,IAAI;QAAEd,UAAU,EAAE,IAAI;QAAEU,WAAW,EAAE,IAAI;QACjDR,KAAK,EAAE,GAAG;QACVH,SAAS,EAAE;OACd;MAED,IAAI,CAAC1H,WAAW,CAACG,kBAAkB,GAAG,CAAC;QACnCsF,KAAK,EAAE,MAAM;QAAE4B,KAAK,EAAE,MAAM;QAC5BC,UAAU,EAAE,MAAM;QAAEE,WAAW,EAAE,mBAAmB;QACpDD,aAAa,EAAE,sDAAsD;QACrEE,eAAe,EAAErJ,gBAAgB;QACjC0K,qBAAqB,EAAE;UAAEC,OAAO,EAAEzL,WAAW,CAAC0L,UAAU;UAAEC,YAAY,EAAE;QAAI,CAAE;QAC9EC,UAAU,EAAE,UAAU;QAAEC,gBAAgB,EAAE;UACtClK,SAAS,EAAEA,CAACgI,MAAc,EAAEhB,IAAc,EAAEmD,OAAgB,EAAEC,MAAgB,EAAEC,QAAkB,KAAI;YAClG,IAAI,CAACvC,qBAAqB,CAACC,IAAI,CAACC,MAAM,EAAEhB,IAAI,EAAEmD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,CAAC;UAC5E,CAAC;UAAEC,UAAU,EAAEA,CAAClC,KAAa,EAAEpB,IAAc,KAAI;YAC7C,IAAI,IAAI,CAACnF,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAAC3B,YAAY,CAAC;cAAEwG,UAAU,EAAEK,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEgE,kBAAkB,EAAEvD,IAAI,CAACzC,IAAI,CAACiG,UAAU;cAAEC,QAAQ,EAAErC;YAAK,CAAE,CAAC,CAAC,KAE3G,IAAI,CAACjI,YAAY,CAAC;cAAEuK,QAAQ,EAAE1D,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEkE,QAAQ,EAAErC;YAAK,CAAE,CAAC;UACtE;SACH;QACD5C,QAAQ,EAAGlB,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAACoK,eAAe,CAACrG,MAAM,EAAE,IAAI,CAACiB,cAAc,CAACC,QAAQ,CAAC;QAC5FkE,YAAY,EAAE,wBAAwB;QACtCf,eAAe,EAAE,IAAI;QAAEI,cAAc,EAAE,KAAK;QAC5CH,KAAK,EAAE,EAAE;QAAEgC,eAAe,EAAE,MAAM;QAAE3C,UAAU,EAAE,IAAI,CAAC1H,WAAW,CAACsK,gBAAgB;QACjF/E,QAAQ,EAAE,KAAK;QAAEoB,aAAa,EAAGC,KAAK,IAAK,IAAI,CAACD,aAAa,CAACC,KAAK,CAAC;QACpE8B,YAAY,EAAE,IAAI;QAAEH,kBAAkB,EAAE;OAC3C,EACD;QACIV,KAAK,EAAE,QAAQ;QACf5B,KAAK,EAAE,QAAQ;QACf6B,UAAU,EAAE,QAAQ;QAAEE,WAAW,EAAE,mBAAmB;QACtDC,eAAe,EAAErJ,gBAAgB;QACjC0K,qBAAqB,EAAE;UACnBiB,UAAU,EAAE,IAAI;UAChBhB,OAAO,EAAEzL,WAAW,CAAC0M;SACxB;QACDnC,KAAK,EAAE,GAAG;QACVqB,UAAU,EAAE,kBAAkB;QAC9BC,gBAAgB,EAAE;UACdc,MAAM,EAAE,CACJ;YAAEzE,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAS,CAAE,EAC1B;YAAE1E,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAW,CAAE,EAC5B;YAAE1E,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE,UAAU;YAAEC,QAAQ,EAAE;UAAI,CAAE,EAC3C;YAAE3E,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAW,CAAE,CAC/B;UAAEjL,SAAS,EAAEA,CAACgI,MAAc,EAAEhB,IAAc,EAAEmD,OAAgB,EAAEC,MAAgB,EAAEC,QAAkB,KAAI;YACrG,IAAI,CAACvC,qBAAqB,CAACC,IAAI,CAACC,MAAM,EAAEhB,IAAI,EAAEmD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,CAAC;UAC5E,CAAC;UAAEC,UAAU,EAAEA,CAAClC,KAAa,EAAEpB,IAAc,KAAI;YAC7C,IAAI,IAAI,CAACnF,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAAC3B,YAAY,CAAC;cAAEwG,UAAU,EAAEK,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEgE,kBAAkB,EAAEvD,IAAI,CAACzC,IAAI,CAACiG,UAAU;cAAEC,QAAQ,EAAErC;YAAK,CAAE,CAAC,CAAC,KAE3G,IAAI,CAACjI,YAAY,CAAC;cAAEuK,QAAQ,EAAE1D,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEkE,QAAQ,EAAErC;YAAK,CAAE,CAAC;UACtE;SACH;QACD5C,QAAQ,EAAGlB,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAACoK,eAAe,CAACrG,MAAM,EAAE,IAAI,CAACiB,cAAc,CAACC,QAAQ,CAAC;QAC5F2F,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC6K,iBAAiB;QAClDC,YAAY,EAAE;UAAEF,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC+K;QAAuB,CAAE;QAC1E5B,YAAY,EAAE,uBAAuB;QACrCvD,cAAc,EAAE;UACZ,cAAc,EAAG7B,MAAM,IAAKA,MAAM,CAACiH,KAAK,IAAInN,cAAc,CAACsG,QAAQ;UACnE,UAAU,EAAGJ,MAAM,IAAKA,MAAM,CAACiH,KAAK,IAAInN,cAAc,CAACuG;SAC1D;QAAEyE,WAAW,EAAE,IAAI;QACpBT,eAAe,EAAE,IAAI;QAAE6C,WAAW,EAAGlH,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAACkL,UAAU,CAACnH,MAAM;OACrF,EACD;QACI8D,KAAK,EAAE,QAAQ;QACf5B,KAAK,EAAE,QAAQ;QACf6B,UAAU,EAAE,YAAY;QAAEE,WAAW,EAAE,mBAAmB;QAC1DC,eAAe,EAAErJ,gBAAgB;QACjC0K,qBAAqB,EAAE;UACnBiB,UAAU,EAAE,IAAI;UAChBhB,OAAO,EAAEzL,WAAW,CAACqN,IAAI,CAAE;SAC9B;;QACDzB,UAAU,EAAE,gBAAgB;QAC5BC,gBAAgB,EAAE;UAAEyB,EAAE,EAAE,UAAU;UAC9B3L,SAAS,EAAEA,CAACgI,MAAc,EAAEhB,IAAc,EAAEmD,OAAgB,EAAEC,MAAgB,EAAEC,QAAkB,KAAI;YAClG,IAAI,CAACvC,qBAAqB,CAACC,IAAI,CAACC,MAAM,EAAEhB,IAAI,EAAEmD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,CAAC;UAC5E,CAAC;UAAEC,UAAU,EAAEA,CAAClC,KAAa,EAAEpB,IAAc,KAAI;YAC7C,IAAI,IAAI,CAACnF,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAAC3B,YAAY,CAAC;cAAEwG,UAAU,EAAEK,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEgE,kBAAkB,EAAEvD,IAAI,CAACzC,IAAI,CAACiG,UAAU;cAAEC,QAAQ,EAAErC;YAAK,CAAE,CAAC,CAAC,KAE3G,IAAI,CAACjI,YAAY,CAAC;cAAEuK,QAAQ,EAAE1D,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEkE,QAAQ,EAAErC;YAAK,CAAE,CAAC;UACtE;SACH;QACD5C,QAAQ,EAAGlB,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAACoK,eAAe,CAACrG,MAAM,EAAE,IAAI,CAACiB,cAAc,CAACC,QAAQ,CAAC;QAC5F2F,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC6K,iBAAiB;QAClDC,YAAY,EAAE;UAAEF,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC+K;QAAuB,CAAE;QAC1E5C,UAAU,EAAE,IAAI;QAAEU,WAAW,EAAE,IAAI;QAAEnB,UAAU,EAAE,IAAI,CAAC1H,WAAW,CAACqL,iBAAiB;QACnFhD,KAAK,EAAE,GAAG;QAAEgC,eAAe,EAAE,MAAM;QACnCjC,eAAe,EAAE;OACpB,EACD;QACIP,KAAK,EAAE,QAAQ;QACf5B,KAAK,EAAE,QAAQ;QACf6B,UAAU,EAAE,UAAU;QAAEE,WAAW,EAAE,mBAAmB;QACxDD,aAAa,EAAE,sEAAsE;QACrFE,eAAe,EAAErJ,gBAAgB;QACjC0K,qBAAqB,EAAE;UACnBiB,UAAU,EAAE,KAAK;UACjBhB,OAAO,EAAEzL,WAAW,CAACqN,IAAI;UACzB1B,YAAY,EAAE;SACjB;QACDC,UAAU,EAAE,gBAAgB;QAC5BC,gBAAgB,EAAE;UAAEyB,EAAE,EAAE,UAAU;UAAEE,OAAO,EAAE,IAAI;UAC7C7L,SAAS,EAAEA,CAACgI,MAAc,EAAEhB,IAAc,EAAEmD,OAAgB,EAAEC,MAAgB,EAAEC,QAAkB,KAAI;YAClG,IAAI,CAACvC,qBAAqB,CAACC,IAAI,CAACC,MAAM,EAAEhB,IAAI,EAAEmD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,CAAC;UAC5E,CAAC;UAAEC,UAAU,EAAEA,CAAClC,KAAa,EAAEpB,IAAc,KAAI;YAC7C,IAAI,IAAI,CAACnF,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAAC3B,YAAY,CAAC;cAAEwG,UAAU,EAAEK,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEgE,kBAAkB,EAAEvD,IAAI,CAACzC,IAAI,CAACiG,UAAU;cAAEC,QAAQ,EAAErC;YAAK,CAAE,CAAC,CAAC,KAE3G,IAAI,CAACjI,YAAY,CAAC;cAAEuK,QAAQ,EAAE1D,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEkE,QAAQ,EAAErC;YAAK,CAAE,CAAC;UACtE;SACH;QACD5C,QAAQ,EAAGlB,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAACoK,eAAe,CAACrG,MAAM,EAAE,IAAI,CAACiB,cAAc,CAACC,QAAQ,CAAC;QAC5F2F,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC6K,iBAAiB;QAClDC,YAAY,EAAE;UAAEF,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC+K;QAAuB,CAAE;QAC1E5C,UAAU,EAAE,IAAI;QAAEU,WAAW,EAAE,IAAI;QAAEnB,UAAU,EAAE,IAAI,CAAC1H,WAAW,CAACqL,iBAAiB;QACnFhD,KAAK,EAAE,GAAG;QAAEgC,eAAe,EAAE,MAAM;QACnCjC,eAAe,EAAE,IAAI;QAAE6C,WAAW,EAAE,IAAI,CAACjL,WAAW,CAACkL;OACxD,CAAC;MAEF,IAAI,CAACjK,cAAc,CAACsK,IAAI,CAAC,IAAItN,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAEF,aAAa,CAACyN,UAAU,EAAE,CAAC,CAAC,EAAE1N,WAAW,CAAC2N,MAAM,CAAC,CAAC;MAC/G,IAAI,CAACxK,cAAc,CAACsK,IAAI,CAAC,IAAItN,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,KAAK,EAAEF,aAAa,CAACyN,UAAU,EAAE,CAAC,EAAE1N,WAAW,CAAC2N,MAAM,CAAC,CAAC;MAC7G,IAAI,CAACxK,cAAc,CAACsK,IAAI,CAAC,IAAItN,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAEF,aAAa,CAAC2N,YAAY,EAAE,CAAC,CAAC,EAAE5N,WAAW,CAAC0M,YAAY,EACnH,IAAI,CAAChK,WAAW,CAACG,kBAAkB,CAAC,CAAC,CAAC,CAACgJ,gBAAgB,CAACc,MAAM,CAAC,CAAC;MACpE,IAAI,CAACxJ,cAAc,CAACsK,IAAI,CAAC,IAAItN,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,EAAEF,aAAa,CAAC2N,YAAY,EAAE,CAAC,CAAC,EAAE5N,WAAW,CAACqN,IAAI,EAAE,IAAI,EAAE,aAAa,CAAC,CAAC;MAC1I,IAAI,CAAClK,cAAc,CAACsK,IAAI,CAAC,IAAItN,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,UAAU,EAAEF,aAAa,CAAC2N,YAAY,EAAE,CAAC,CAAC,EAAE5N,WAAW,CAACqN,IAAI,EAAE,IAAI,EAAE,gBAAgB,CAAC,CAAC;MAE3I;MACA,IAAI,CAAC3K,WAAW,CAACO,SAAS,GAAG;QACzB8G,KAAK,EAAE,aAAa;QACpBC,UAAU,EAAE,KAAK;QACjBC,aAAa,EAAE,iDAAiD;QAChEG,SAAS,EAAE,qBAAqB;QAChCC,UAAU,EAAE,IAAI;QAChBC,eAAe,EAAE,IAAI;QACrBC,KAAK,EAAE,EAAE;QACT3C,QAAQ,EAAE,EAAE;QACZ4C,QAAQ,EAAE,EAAE;QACZ9C,SAAS,EAAE,KAAK;QAChBP,QAAQ,EAAE,KAAK;QACfwD,iBAAiB,EAAE,IAAI;QACvBhD,MAAM,EAAE,KAAK;QACbiD,YAAY,EAAE,IAAI;QAClBnD,QAAQ,EAAE,KAAK;QACfoD,iBAAiB,EAAE,IAAI;QACvBC,gBAAgB,EAAE,IAAI;QAAEL,kBAAkB,EAAE,IAAI;QAChDM,WAAW,EAAE,IAAI;QAAEL,cAAc,EAAE,KAAK;QACxC6B,eAAe,EAAE;OACpB;MAED;MACA,IAAI,CAAC7J,WAAW,CAACC,UAAU,GAAG,CAAC;QAC3BwF,KAAK,EAAE,QAAQ;QACf6B,UAAU,EAAE,QAAQ;QAAEE,WAAW,EAAE,mBAAmB;QACtDC,eAAe,EAAErJ,gBAAgB;QACjCyJ,KAAK,EAAE,GAAG;QACVD,eAAe,EAAE,IAAI;QACrBsB,UAAU,EAAE,kBAAkB;QAC9BC,gBAAgB,EAAE;UACdc,MAAM,EAAE,CACJ;YAAEzE,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAS,CAAE,EAC1B;YAAE1E,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAkB,CAAE,EACnC;YAAE1E,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAS,CAAE,EAC1B;YAAE1E,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAe,CAAE,EAChC;YAAE1E,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAe,CAAE,EAChC;YAAE1E,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAS,CAAE,EAC1B;YAAE1E,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAU,CAAE,EAC3B;YAAE1E,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAU,CAAE,CAC9B;UACDiB,OAAO;YAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAE,WAAO9H,MAAM,EAAI;cAAG,aAAagF,KAAI,CAAC/I,WAAW,CAAC8L,WAAW,CAAC,MAAM,EAAE/H,MAAM,EAAEgF,KAAI,CAAC1H,aAAa,CAAC;YAAC,CAAC;YAAA,gBAAAsK,QAAAI,EAAA;cAAA,OAAAH,IAAA,CAAAI,KAAA,OAAAC,SAAA;YAAA;UAAA;UAC5GxM,SAAS,EAAEA,CAACgI,MAAc,EAAEhB,IAAc,EAAEmD,OAAgB,EAAEC,MAAgB,EAAEC,QAAkB,KAAI;YAClG,IAAI,CAACvC,qBAAqB,CAACC,IAAI,CAACC,MAAM,EAAEhB,IAAI,EAAEmD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,CAAC;UAC5E,CAAC;UAAEC,UAAU,EAAEA,CAAClC,KAAa,EAAEpB,IAAc,KAAI;YAC7C,IAAI,IAAI,CAACnF,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAAC3B,YAAY,CAAC;cAAEwG,UAAU,EAAEK,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEgE,kBAAkB,EAAEvD,IAAI,CAACzC,IAAI,CAACiG,UAAU;cAAEC,QAAQ,EAAErC;YAAK,CAAE,CAAC,CAAC,KAE3G,IAAI,CAACjI,YAAY,CAAC;cAAEuK,QAAQ,EAAE1D,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEkE,QAAQ,EAAErC;YAAK,CAAE,CAAC;UACtE;SACH;QACD5C,QAAQ,EAAGlB,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAACoK,eAAe,CAACrG,MAAM,EAAE,IAAI,CAACiB,cAAc,CAACC,QAAQ,CAAC;QAC5FkE,YAAY,EAAE,uBAAuB;QACrC+C,WAAW,EAAGnI,MAAM,IAAI;UACpB,MAAMoI,SAAS,GAAGpI,MAAM,CAACC,IAAI,EAAEoI,MAAM,EAAEC,KAAK,CAAC,GAAG,CAAC,IAAI,EAAE;UACvD,IAAI,CAACF,SAAS,CAACG,MAAM,IAAI,CAACH,SAAS,CAAC,CAAC,CAAC,EAAE,OAAO,EAAE;UACjD,MAAMI,MAAM,GAAG,EAAE;UACjB,MAAMC,WAAW,GAAG,CAAC,UAAU,EAAE,SAAS,EAAE,kBAAkB,EAAE,SAAS,EAAE,eAAe,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,EAAE,UAAU,CAAC;UAC/IL,SAAS,CAACM,OAAO,CAACzG,EAAE,IAAG;YACnB,IAAIwG,WAAW,CAACxG,EAAE,CAAC,EAAEuG,MAAM,CAAChB,IAAI,CAACiB,WAAW,CAACxG,EAAE,CAAC,CAAC;UACrD,CAAC,CAAC;UACF,OAAOuG,MAAM,CAACG,IAAI,CAAC,IAAI,CAAC;QAC5B,CAAC;QACD9B,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC6K,iBAAiB;QAClDC,YAAY,EAAE;UAAEF,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC+K;QAAuB,CAAE;QAC1E4B,kBAAkB,EAAG5I,MAAsC,IAAK,IAAI,CAAC6I,qBAAqB,CAAC7I,MAAM,CAAC;QAClG8E,WAAW,EAAE,IAAI;QAAEwB,eAAe,EAAE,MAAM;QAC1C3C,UAAU,EAAEA,CAACmF,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,IAAI,KAAK,IAAI,CAACjN,WAAW,CAACkN,mBAAmB,CAACL,EAAE,EAACC,EAAE,EAACC,EAAE,EAACC,EAAE,EAACC,IAAI,CAAC;QAC5FhC,WAAW,EAAE,IAAI,CAACjL,WAAW,CAACmN;OACjC,EACD;QACIlH,KAAK,EAAE,UAAU;QACjB6B,UAAU,EAAE,UAAU;QAAEE,WAAW,EAAE,mBAAmB;QACxDC,eAAe,EAAErJ,gBAAgB;QACjC0K,qBAAqB,EAAE;UACnBC,OAAO,EAAE,IAAI,CAACjI,YAAY,CAACC,QAAQ,KAAK,SAAS,GAAGzD,WAAW,CAAC0M,YAAY,GAAG1M,WAAW,CAACsP;SAC9F;QACD/E,KAAK,EAAE,GAAG;QACVD,eAAe,EAAE,IAAI;QACrBsB,UAAU,EAAE,kBAAkB;QAC9BC,gBAAgB,EAAE;UACdc,MAAM,EAAE,EAAE;UAAEkB,OAAO;YAAA,IAAA0B,KAAA,GAAAxB,iBAAA,CAAE,WAAO9H,MAAM,EAAI;cAAG,aAAagF,KAAI,CAAC/I,WAAW,CAAC8L,WAAW,CAAC,OAAO,EAAE/H,MAAM,EAAEgF,KAAI,CAAC1H,aAAa,CAAC;YAAC,CAAC;YAAA,gBAAAsK,QAAA2B,GAAA;cAAA,OAAAD,KAAA,CAAArB,KAAA,OAAAC,SAAA;YAAA;UAAA;UACzHxM,SAAS,EAAEA,CAACgI,MAAc,EAAEhB,IAAc,EAAEmD,OAAgB,EAAEC,MAAgB,EAAEC,QAAkB,KAAI;YAClG,IAAI,CAACvC,qBAAqB,CAACC,IAAI,CAACC,MAAM,EAAEhB,IAAI,EAAEmD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,CAAC;UAC5E,CAAC;UAAEC,UAAU,EAAEA,CAAClC,KAAa,EAAEpB,IAAc,KAAI;YAC7C,IAAI,IAAI,CAACnF,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAAC3B,YAAY,CAAC;cAAEwG,UAAU,EAAEK,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEgE,kBAAkB,EAAEvD,IAAI,CAACzC,IAAI,CAACiG,UAAU;cAAEC,QAAQ,EAAErC;YAAK,CAAE,CAAC,CAAC,KAE3G,IAAI,CAACjI,YAAY,CAAC;cAAEuK,QAAQ,EAAE1D,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEkE,QAAQ,EAAErC;YAAK,CAAE,CAAC;UACtE;SACH;QACD5C,QAAQ,EAAGlB,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAACoK,eAAe,CAACrG,MAAM,EAAE,IAAI,CAACiB,cAAc,CAACC,QAAQ,CAAC;QAC5FkE,YAAY,EAAE,uBAAuB;QAErCyB,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC6K,iBAAiB,CAAC0C,IAAI,CAAC,IAAI,CAACvN,WAAW,CAAC;QACzE8K,YAAY,EAAE;UAAEF,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC+K,uBAAuB,CAACwC,IAAI,CAAC,IAAI,CAACvN,WAAW;QAAC,CAAE;QACjG2M,kBAAkB,EAAG5I,MAAuC,IAAK,IAAI,CAAC6I,qBAAqB,CAAC7I,MAAM,CAAC;QACnG8E,WAAW,EAAE,IAAI;QAAEwB,eAAe,EAAE,MAAM;QAC1C3C,UAAU,EAAEA,CAACmF,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,IAAI,KAAK,IAAI,CAACjN,WAAW,CAACwN,qBAAqB,CAACX,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,IAAI,CAAC;QAClGhC,WAAW,EAAE,IAAI,CAACjL,WAAW,CAACmN;OACjC,EACD;QACIlH,KAAK,EAAE,UAAU;QACjB6B,UAAU,EAAE,UAAU;QAAEE,WAAW,EAAE,mBAAmB;QACxDC,eAAe,EAAErJ,gBAAgB;QACjC0K,qBAAqB,EAAE;UACnBC,OAAO,EAAE,IAAI,CAACjI,YAAY,CAACC,QAAQ,KAAK,SAAS,GAAGzD,WAAW,CAAC0M,YAAY,GAAG1M,WAAW,CAACsP;SAC9F;QACD/E,KAAK,EAAE,GAAG;QACVD,eAAe,EAAE,IAAI;QACrBsB,UAAU,EAAE,kBAAkB;QAC9BC,gBAAgB,EAAE;UACdc,MAAM,EAAE,EAAE;UAAEgD,WAAW,EAAE,IAAI;UAAE9B,OAAO;YAAA,IAAA+B,KAAA,GAAA7B,iBAAA,CAAE,WAAO9H,MAAM,EAAI;cAAG,aAAagF,KAAI,CAAC/I,WAAW,CAAC8L,WAAW,CAAC,OAAO,EAAE/H,MAAM,EAAEgF,KAAI,CAAC1H,aAAa,CAAC;YAAC,CAAC;YAAA,gBAAAsK,QAAAgC,GAAA;cAAA,OAAAD,KAAA,CAAA1B,KAAA,OAAAC,SAAA;YAAA;UAAA;UAC5IxM,SAAS,EAAEA,CAACgI,MAAc,EAAEhB,IAAc,EAAEmD,OAAgB,EAAEC,MAAgB,EAAEC,QAAkB,KAAI;YAClG,IAAI,CAACvC,qBAAqB,CAACC,IAAI,CAACC,MAAM,EAAEhB,IAAI,EAAEmD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,CAAC;UAC5E,CAAC;UAAEC,UAAU,EAAEA,CAAClC,KAAa,EAAEpB,IAAc,KAAI;YAC7C,IAAI,IAAI,CAACnF,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAAC3B,YAAY,CAAC;cAAEwG,UAAU,EAAEK,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEgE,kBAAkB,EAAEvD,IAAI,CAACzC,IAAI,CAACiG,UAAU;cAAEC,QAAQ,EAAErC;YAAK,CAAE,CAAC,CAAC,KAE3G,IAAI,CAACjI,YAAY,CAAC;cAAEuK,QAAQ,EAAE1D,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEkE,QAAQ,EAAErC;YAAK,CAAE,CAAC;UACtE;SACH;QACD5C,QAAQ,EAAGlB,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAACoK,eAAe,CAACrG,MAAM,EAAE,IAAI,CAACiB,cAAc,CAACC,QAAQ,CAAC;QAC5FkE,YAAY,EAAE,uBAAuB;QAErCyB,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC6K,iBAAiB,CAAC0C,IAAI,CAAC,IAAI,CAACvN,WAAW,CAAC;QACzE8K,YAAY,EAAE;UAAEF,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC+K,uBAAuB,CAACwC,IAAI,CAAC,IAAI,CAACvN,WAAW;QAAC,CAAE;QACjG2M,kBAAkB,EAAG5I,MAAuC,IAAK,IAAI,CAAC6I,qBAAqB,CAAC7I,MAAM,CAAC;QACnG8E,WAAW,EAAE,IAAI;QAAEwB,eAAe,EAAE,MAAM;QAC1C3C,UAAU,EAAEA,CAACmF,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,IAAI,KAAK,IAAI,CAACjN,WAAW,CAAC4N,qBAAqB,CAACf,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,IAAI,CAAC;QAClGhC,WAAW,EAAE,IAAI,CAACjL,WAAW,CAACmN;OACjC,CAAC;MAEF;MACA,IAAI,CAACnN,WAAW,CAAC8L,WAAW,CAAC,OAAO,CAAC,CAAC+B,IAAI,CAAEC,IAAI,IAAI;QAChD,IAAI,CAACtN,WAAW,CAACC,UAAU,CAAC,CAAC,CAAC,CAACkJ,gBAAgB,CAACc,MAAM,GAAGqD,IAAI;QAC7D;QACAC,UAAU,CAAC,MAAK;UACZ,IAAI,IAAI,CAACC,OAAO,EAAE;YACd,IAAI,CAACA,OAAO,CAACC,YAAY,CAAC;cAAEC,OAAO,EAAE,CAAC,UAAU,CAAC;cAAEC,KAAK,EAAE;YAAI,CAAE,CAAC;;QAEzE,CAAC,EAAE,GAAG,CAAC;MACX,CAAC,CAAC;MACF,IAAI,CAACnO,WAAW,CAAC8L,WAAW,CAAC,OAAO,CAAC,CAAC+B,IAAI,CAAEC,IAAI,IAAI;QAChD,IAAI,CAACtN,WAAW,CAACC,UAAU,CAAC,CAAC,CAAC,CAACkJ,gBAAgB,CAACc,MAAM,GAAGqD,IAAI;QAC7D,IAAI,CAACM,0BAA0B,EAAE;QACjC;QACAL,UAAU,CAAC,MAAK;UACZ,IAAI,IAAI,CAACC,OAAO,EAAE;YACd,IAAI,CAACA,OAAO,CAACC,YAAY,CAAC;cAAEC,OAAO,EAAE,CAAC,UAAU,CAAC;cAAEC,KAAK,EAAE;YAAI,CAAE,CAAC;;QAEzE,CAAC,EAAE,GAAG,CAAC;MACX,CAAC,CAAC;MAEF;MACA,IAAI,CAAC3N,WAAW,CAACI,eAAe,GAAG;QAC/BqF,KAAK,EAAE,WAAW;QAClB6B,UAAU,EAAE,WAAW;QAAEE,WAAW,EAAE,mBAAmB;QACzDC,eAAe,EAAErJ,gBAAgB;QACjCyJ,KAAK,EAAE,GAAG;QACVD,eAAe,EAAE,IAAI;QACrBsB,UAAU,EAAE,kBAAkB;QAC9BC,gBAAgB,EAAE;UACdc,MAAM,EAAE,EAAE;UAAEgD,WAAW,EAAE,IAAI;UAAEY,UAAU,EAAE,uBAAuB;UAClE1C,OAAO;YAAA,IAAA2C,KAAA,GAAAzC,iBAAA,CAAE,WAAO9H,MAAM,EAAI;cAAG,aAAagF,KAAI,CAAC/I,WAAW,CAAC8L,WAAW,CAAC,OAAO,EAAE/H,MAAM,CAAC;YAAC,CAAC;YAAA,gBAAA4H,QAAA4C,GAAA;cAAA,OAAAD,KAAA,CAAAtC,KAAA,OAAAC,SAAA;YAAA;UAAA;UACzFxM,SAAS,EAAEA,CAACgI,MAAc,EAAEhB,IAAc,EAAEmD,OAAgB,EAAEC,MAAgB,EAAEC,QAAkB,KAAI;YAClG,IAAI,CAACvC,qBAAqB,CAACC,IAAI,CAACC,MAAM,EAAEhB,IAAI,EAAEmD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,CAAC;UAC5E,CAAC;UAAEC,UAAU,EAAEA,CAAClC,KAAa,EAAEpB,IAAc,KAAI;YAC7C,IAAI,IAAI,CAACnF,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAAC3B,YAAY,CAAC;cAAEwG,UAAU,EAAEK,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEgE,kBAAkB,EAAEvD,IAAI,CAACzC,IAAI,CAACiG,UAAU;cAAEC,QAAQ,EAAErC;YAAK,CAAE,CAAC,CAAC,KAE3G,IAAI,CAACjI,YAAY,CAAC;cAAEuK,QAAQ,EAAE1D,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEkE,QAAQ,EAAErC;YAAK,CAAE,CAAC;UACtE;SACH;QACD5C,QAAQ,EAAGlB,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAACoK,eAAe,CAACrG,MAAM,EAAE,IAAI,CAACiB,cAAc,CAACC,QAAQ,CAAC;QAC5FkE,YAAY,EAAE,uBAAuB;QACrCyB,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC6K,iBAAiB,CAAC0C,IAAI,CAAC,IAAI,CAACvN,WAAW,CAAC;QACzE8K,YAAY,EAAE;UAAEF,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC+K,uBAAuB,CAACwC,IAAI,CAAC,IAAI,CAACvN,WAAW;QAAC,CAAE;QACjG2M,kBAAkB,EAAG5I,MAAuC,IAAK,IAAI,CAAC6I,qBAAqB,CAAC7I,MAAM,CAAC;QACnG8E,WAAW,EAAE,IAAI;QACjBnB,UAAU,EAAEA,CAACmF,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,IAAI,KAAK,IAAI,CAACjN,WAAW,CAACwO,sBAAsB,CAAC3B,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,IAAI,CAAC;QACnGhC,WAAW,EAAE,IAAI,CAACjL,WAAW,CAACmN;OACjC;MAED;MACA,IAAI,CAAC3M,WAAW,CAACK,eAAe,GAAG;QAC/BgH,KAAK,EAAE,MAAM;QACb5B,KAAK,EAAE,MAAM;QACb6B,UAAU,EAAE,OAAO;QAAEC,aAAa,EAAE,oDAAoD;QAAEC,WAAW,EAAE,mBAAmB;QAC1HC,eAAe,EAAErJ,gBAAgB;QACjC0K,qBAAqB,EAAE;UACnBiB,UAAU,EAAE,IAAI;UAChBhB,OAAO,EAAEzL,WAAW,CAACqN,IAAI;UACzB1B,YAAY,EAAE;SACjB;QACDC,UAAU,EAAE,gBAAgB;QAC5BC,gBAAgB,EAAE;UAAEyB,EAAE,EAAE,UAAU;UAAEE,OAAO,EAAE,IAAI;UAC7C7L,SAAS,EAAEA,CAACgI,MAAc,EAAEhB,IAAc,EAAEmD,OAAgB,EAAEC,MAAgB,EAAEC,QAAkB,KAAI;YAClG,IAAI,CAACvC,qBAAqB,CAACC,IAAI,CAACC,MAAM,EAAEhB,IAAI,EAAEmD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,CAAC;UAC5E,CAAC;UAAEC,UAAU,EAAEA,CAAClC,KAAa,EAAEpB,IAAc,KAAI;YAC7C,IAAI,IAAI,CAACnF,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAAC3B,YAAY,CAAC;cAAEwG,UAAU,EAAEK,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEgE,kBAAkB,EAAEvD,IAAI,CAACzC,IAAI,CAACiG,UAAU;cAAEC,QAAQ,EAAErC;YAAK,CAAE,CAAC,CAAC,KAE3G,IAAI,CAACjI,YAAY,CAAC;cAAEuK,QAAQ,EAAE1D,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEkE,QAAQ,EAAErC;YAAK,CAAE,CAAC;UACtE;SACH;QACD;QACA+C,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAACyO,kBAAkB;QACnD3D,YAAY,EAAE;UAAEF,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAACyO;QAAkB,CAAE;QACrE9B,kBAAkB,EAAE,IAAI,CAAC3M,WAAW,CAAC2M,kBAAkB;QACvDxE,UAAU,EAAE,IAAI;QAAET,UAAU,EAAE,IAAI,CAAC1H,WAAW,CAACqL,iBAAiB;QAChEhD,KAAK,EAAE,GAAG;QAAEQ,WAAW,EAAE,IAAI;QAAEjD,cAAc,EAAE;UAAE,YAAY,EAAE,IAAI,CAAC5F,WAAW,CAAC0O;QAAiB,CAAE;QACnGtG,eAAe,EAAE;OACpB;MACD,IAAI,CAACnH,cAAc,CAACsK,IAAI,CAAC,IAAItN,aAAa,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,MAAM,EAAEF,aAAa,CAAC4Q,QAAQ,EAAE,CAAC,EAAE7Q,WAAW,CAACqN,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;MAEzH;MACA,IAAIyD,KAAK,GAAG,EAAE;MACd,MAAMC,QAAQ,GAAG,IAAI1D,IAAI,EAAE,CAAC2D,WAAW,EAAE;MAEzC,KAAK,IAAIC,CAAC,GAAG,IAAI,EAAEA,CAAC,IAAIF,QAAQ,GAAG,CAAC,EAAEE,CAAC,EAAE,EACrCH,KAAK,CAACrD,IAAI,CAAC;QAAEvF,EAAE,EAAE+I,CAAC;QAAErE,IAAI,EAAE,GAAGqE,CAAC;MAAE,CAAE,CAAC;MAEvC,IAAI,CAACvO,WAAW,CAACM,cAAc,GAAG,CAAC;QAC/B+G,KAAK,EAAE,MAAM;QACb5B,KAAK,EAAE,MAAM;QACb6B,UAAU,EAAE,MAAM;QAAEE,WAAW,EAAE,mBAAmB;QACpDC,eAAe,EAAErJ,gBAAgB;QACjC0K,qBAAqB,EAAE;UACnBiB,UAAU,EAAE,IAAI;UAChBhB,OAAO,EAAEzL,WAAW,CAAC0M,YAAY;UACjCf,YAAY,EAAE;SACjB;QACDC,UAAU,EAAE,kBAAkB;QAC9BC,gBAAgB,EAAE;UACdc,MAAM,EAAE,CAAC;YAAEzE,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAW,CAAE,EAAE,GAAGkE,KAAK,CAAC;UAChDnP,SAAS,EAAEA,CAACgI,MAAc,EAAEhB,IAAc,EAAEmD,OAAgB,EAAEC,MAAgB,EAAEC,QAAkB,KAAI;YAClG,IAAI,CAACvC,qBAAqB,CAACC,IAAI,CAACC,MAAM,EAAEhB,IAAI,EAAEmD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,CAAC;UAC5E,CAAC;UAAEC,UAAU,EAAEA,CAAClC,KAAa,EAAEpB,IAAc,KAAI;YAC7C,IAAI,IAAI,CAACnF,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAAC3B,YAAY,CAAC;cAAEwG,UAAU,EAAEK,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEgE,kBAAkB,EAAEvD,IAAI,CAACzC,IAAI,CAACiG,UAAU;cAAEC,QAAQ,EAAErC;YAAK,CAAE,CAAC,CAAC,KAE3G,IAAI,CAACjI,YAAY,CAAC;cAAEuK,QAAQ,EAAE1D,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEkE,QAAQ,EAAErC;YAAK,CAAE,CAAC;UACtE;SACH;QACD5C,QAAQ,EAAGlB,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAACoK,eAAe,CAACrG,MAAM,EAAE,IAAI,CAACiB,cAAc,CAACC,QAAQ,CAAC;QAC5FkE,YAAY,EAAE,uBAAuB;QACrCyB,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC6K,iBAAiB;QAClDC,YAAY,EAAE;UAAEF,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC+K;QAAuB,CAAE;QAC1E5C,UAAU,EAAE,IAAI;QAAET,UAAU,EAAE,IAAI,CAAC1H,WAAW,CAACsK,gBAAgB;QAC/DjC,KAAK,EAAE,EAAE;QAAEQ,WAAW,EAAE,IAAI;QAC5BT,eAAe,EAAE;OACpB,EACD;QACIP,KAAK,EAAE,KAAK;QACZ5B,KAAK,EAAE,KAAK;QACZ6B,UAAU,EAAE,KAAK;QAAEE,WAAW,EAAE,mBAAmB;QACnDC,eAAe,EAAErJ,gBAAgB;QACjC0K,qBAAqB,EAAE;UACnBiB,UAAU,EAAE,IAAI;UAChBhB,OAAO,EAAEzL,WAAW,CAAC0M,YAAY;UACjCf,YAAY,EAAE;SACjB;QACDC,UAAU,EAAE,kBAAkB;QAC9BC,gBAAgB,EAAE;UACdc,MAAM,EAAE;UACJ;UACA;YAAEzE,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAO,CAAE,EACxB;YAAE1E,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAO,CAAE,EACxB;YAAE1E,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAO,CAAE,EACxB;YAAE1E,EAAE,EAAE,CAAC;YAAE0E,IAAI,EAAE;UAAO,CAAE,CAC3B;UACDjL,SAAS,EAAEA,CAACgI,MAAc,EAAEhB,IAAc,EAAEmD,OAAgB,EAAEC,MAAgB,EAAEC,QAAkB,KAAI;YAClG,IAAI,CAACvC,qBAAqB,CAACC,IAAI,CAACC,MAAM,EAAEhB,IAAI,EAAEmD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,CAAC;UAC5E,CAAC;UAAEC,UAAU,EAAEA,CAAClC,KAAa,EAAEpB,IAAc,KAAI;YAC7C,IAAI,IAAI,CAACnF,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAAC3B,YAAY,CAAC;cAAEwG,UAAU,EAAEK,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEgE,kBAAkB,EAAEvD,IAAI,CAACzC,IAAI,CAACiG,UAAU;cAAEC,QAAQ,EAAErC;YAAK,CAAE,CAAC,CAAC,KAE3G,IAAI,CAACjI,YAAY,CAAC;cAAEuK,QAAQ,EAAE1D,IAAI,CAACzC,IAAI,CAACgC,EAAE;cAAEkE,QAAQ,EAAErC;YAAK,CAAE,CAAC;UACtE;SACH;QACD5C,QAAQ,EAAGlB,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAACoK,eAAe,CAACrG,MAAM,EAAE,IAAI,CAACiB,cAAc,CAACC,QAAQ,CAAC;QAC5FkE,YAAY,EAAE,uBAAuB;QACrCyB,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC6K,iBAAiB;QAClDC,YAAY,EAAE;UAAEF,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC+K;QAAuB,CAAE;QAC1EV,eAAe,EAAE,MAAM;QACvBlC,UAAU,EAAE,IAAI;QAAEU,WAAW,EAAE,IAAI;QACnCR,KAAK,EAAE,EAAE;QACTD,eAAe,EAAE;OACpB,CAAC;MAEF;MACA,KAAK,IAAI2G,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEA,CAAC,EAAE,EACxB,IAAI,CAAC3N,OAAO,CAACmK,IAAI,CAAC;QAAEvF,EAAE,EAAE,CAAC;QAAEgJ,OAAO,EAAE,EAAE;QAAEC,KAAK,EAAE;MAAK,CAAmB,CAAC;KAC/E,CAAC,OAAOC,EAAE,EAAE;MAAEC,OAAO,CAACC,KAAK,CAACF,EAAE,CAAC;MAAE,IAAI,CAACjP,cAAc,CAACmP,KAAK,CAAC,uBAAuB,CAAC;;EACxF;EAEA;EACQxC,qBAAqBA,CAAC7I,MAAuC;IACjE,IAAI,IAAI,CAACzC,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAACD,YAAY,CAACE,EAAE,GAAG,IAAI,CAACJ,OAAO,CAACqE,MAAM,CAAE4J,CAAgB,IAAKA,CAAC,CAACjJ,UAAU,CAAC,CAACkG,MAAM,CAAC,KAEtF,IAAI,CAAChL,YAAY,CAACE,EAAE,GAAG,IAAI,CAACJ,OAAO,CAACqE,MAAM,CAAE4J,CAAc,IAAKA,CAAC,CAAChJ,QAAQ,CAAC,CAACiG,MAAM;IAErF;IACA,IAAI,CAAChL,YAAY,CAACE,EAAE,EAAE;IAEtB,KAAK,IAAIuN,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC3N,OAAO,CAACkL,MAAM,EAAEyC,CAAC,EAAE,EAAE;MAC1C,MAAMO,GAAG,GAAG,IAAI,CAAClO,OAAO,CAAC2N,CAAC,CAAC,CAAC,YAAY,CAAC,IAAI,IAAI,CAAC3N,OAAO,CAAC2N,CAAC,CAAC,CAAC,UAAU,CAAC;MAExE,IAAIO,GAAG,EAAE/I,UAAU,CAAC,GAAG,IAAI,CAACjF,YAAY,CAACE,EAAE,GAAG,CAAC,EAC3C,IAAI,CAACF,YAAY,CAACE,EAAE,EAAE;;IAG9B,IAAI,CAACxB,WAAW,CAACuP,iBAAiB,CAACxL,MAAM,EAAE,IAAI,CAACzC,YAAY,CAAC;EACjE;EAEQ8M,0BAA0BA,CAAA;IAC9B;IACA,IAAI,IAAI,CAAChO,OAAO,IAAI,IAAI,CAACoP,aAAa,EAAE;MACpC,MAAMC,IAAI,GAAG,IAAI,CAACD,aAAa,CAACE,UAAU,EAAE;MAC5C,KAAK,IAAIX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,EAAE,EAAEA,CAAC,EAAE,EAAE;QACzB,MAAMtH,MAAM,GAAGgI,IAAI,CAACV,CAAC,CAAC,EAAE5I,SAAS,EAAE;QACnC,IAAI,CAACsB,MAAM,EAAE;QACb,IAAIA,MAAM,CAACxB,KAAK,KAAK,UAAU,EAAE;UAC7B,IAAI,CAACwB,MAAM,CAACkC,gBAAgB,CAACc,MAAM,EAAE6B,MAAM,EAAE;YACzC7E,MAAM,CAACkC,gBAAgB,CAACc,MAAM,GAAG,IAAI,CAACjK,WAAW,CAACC,UAAU,CAAC,CAAC,CAAC,CAACkJ,gBAAgB,CAACc,MAAM;YACvFgF,IAAI,CAACV,CAAC,CAAC,CAACY,SAAS,CAAClI,MAAM,EAAEgI,IAAI,CAACV,CAAC,CAAC,CAAC5I,SAAS,EAAE,CAAC;;SAErD,MAAM,IAAIsB,MAAM,CAACxB,KAAK,KAAK,UAAU,EAAE;UACpC,IAAI,CAACwB,MAAM,CAACkC,gBAAgB,CAACc,MAAM,EAAE6B,MAAM,EAAE;YACzC7E,MAAM,CAACkC,gBAAgB,CAACc,MAAM,GAAG,IAAI,CAACjK,WAAW,CAACC,UAAU,CAAC,CAAC,CAAC,CAACkJ,gBAAgB,CAACc,MAAM;YACvFgF,IAAI,CAACV,CAAC,CAAC,CAACY,SAAS,CAAClI,MAAM,EAAEgI,IAAI,CAACV,CAAC,CAAC,CAAC5I,SAAS,EAAE,CAAC;;UAElD;;;;EAIhB;EAEQyJ,cAAcA,CAAC/I,MAAgB;IACnC,IAAIgJ,SAAS,GAAGhJ,MAAM,GAAG,kBAAkB,GAAG,iBAAiB;IAC/D,IAAI,CAACtG,cAAc,CAAC,CAAC,CAAC,GAAGuP,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAACJ,SAAS,CAAC,IAAI,IAAI,CAAC;IAC5EA,SAAS,GAAGA,SAAS,CAACK,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IACvC,IAAI,CAAC3P,cAAc,CAAC,CAAC,CAAC,GAAGuP,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAACJ,SAAS,CAAC,IAAI,IAAI,CAAC;IAC5EA,SAAS,GAAGA,SAAS,CAACK,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IACvC,IAAI,CAAC3P,cAAc,CAAC,CAAC,CAAC,GAAGuP,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAACJ,SAAS,CAAC,IAAI,IAAI,CAAC;IAC5EA,SAAS,GAAGA,SAAS,CAACK,OAAO,CAAC,GAAG,EAAE,GAAG,CAAC;IACvC,IAAI,CAAC3P,cAAc,CAAC,CAAC,CAAC,GAAGuP,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAACJ,SAAS,CAAC,IAAI,IAAI,CAAC;EAChF;EAEA;EACAM,QAAQA,CAACC,OAAY;IACjB,IAAI;MACA;MACA,IAAI,CAAClQ,OAAO,GAAG,IAAI;MACnB,IAAI,CAAC8N,OAAO,CAACqC,WAAW,CAAC,IAAI,CAAC;MAC9B,IAAI,CAACrH,UAAU,GAAG,CAAC,IAAI,CAACA,UAAU,CAAC,CAAC,CAAC,CAAC,CAAC,CAA6B;MACpE,IAAI,CAAC/H,cAAc,GAAG,IAAI,CAACA,cAAc,CAACwE,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACE,EAAE,GAAG,CAAC,CAAC,CAAC,CAAI;MACpE,IAAI,CAACxF,WAAW,CAACC,UAAU,CAAC,CAAC,CAAC,CAACkJ,gBAAgB,CAACc,MAAM;MAAc;MAChE,IAAI,CAACjK,WAAW,CAACC,UAAU,CAAC,CAAC,CAAC,CAACkJ,gBAAgB,CAACc,MAAM,CAAChF,MAAM,CAAC6K,CAAC,IAAIA,CAAC,CAACtK,EAAE,GAAG,CAAC,CAAC;MAEhF,IAAI,CAACgI,OAAO,CAACuC,aAAa,CAAC,IAAI,CAACvH,UAAU,CAAC,CAAC,CAAQ;MAEpD;MACA,IAAIoH,OAAO,CAACI,IAAI,KAAK,UAAU,EAAE;QAC7B,IAAI,CAACxH,UAAU,GAAG,IAAI,CAACA,UAAU,CAACyH,MAAM,CAAC,IAAI,CAACjQ,WAAW,CAACE,gBAAgB,CAAC;QAC3E,IAAI,CAACkP,cAAc,EAAE;QAErB;QACA,IAAI,CAACpP,WAAW,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC4J,eAAe,GAAG,MAAM;QACvD,IAAI,CAAC7J,WAAW,CAACO,SAAS,CAACiH,WAAW,GAAG,mBAAmB;QAC5D,IAAI,CAACgB,UAAU,CAACuC,IAAI,CAAC;UACjBmF,OAAO,EAAE,gBAAgB;UAAE3I,aAAa,EAAE,kDAAkD;UAC5FD,UAAU,EAAE,QAAQ;UAAEE,WAAW,EAAE,yBAAyB;UAC5DI,eAAe,EAAE,IAAI;UACrBuI,aAAa,EAAE,IAAI;UACnBC,aAAa,EAAE,CAAC,IAAI,CAACrQ,cAAc,CAAC,CAAC,CAAC,CAACsQ,QAAQ,CAAC,IAAI,CAACvP,YAAY,CAACK,MAAM,CAAC;UACzEmP,QAAQ,EAAE,CAAC,GAAG,IAAI,CAACtQ,WAAW,CAACG,kBAAkB,EAAE,IAAI,CAACH,WAAW,CAACO,SAAS;SAChF,CAAC;QAEF;QACA,IAAIA,SAAS,GAAGgQ,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE,IAAI,CAACxQ,WAAW,CAACO,SAAS,CAAC;QAC7DA,SAAS,CAACiH,WAAW,GAAG,mBAAmB;QAC3C,IAAI,CAACgB,UAAU,CAACuC,IAAI,CAAC;UACjBmF,OAAO,EAAE,GAAG3S,aAAa,CAACkT,cAAc,EAAE;UAAElJ,aAAa,EAAE,kDAAkD;UAC7GD,UAAU,EAAE,UAAU;UAAEE,WAAW,EAAE,yBAAyB;UAC9DI,eAAe,EAAE,IAAI;UACrBuI,aAAa,EAAE,IAAI;UACnBC,aAAa,EAAE,CAAC,IAAI,CAACrQ,cAAc,CAAC,CAAC,CAAC,CAACsQ,QAAQ,CAAC,IAAI,CAACvP,YAAY,CAACK,MAAM,CAAC;UACzEmP,QAAQ,EAAE,CAAC,GAAG,IAAI,CAACtQ,WAAW,CAACC,UAAU,EAAE,IAAI,CAACD,WAAW,CAACI,eAAe,EAAEG,SAAS;SACzF,CAAC;QAEF;QACA,IAAImQ,QAAQ,GAAG,EAAE;QACjB,IAAIC,QAAQ,GAAG,EAAE;QACjB,IAAI,CAACnQ,cAAc,CAACyL,OAAO,CAAC2E,GAAG,IAAG;UAC9B;UACA,IAAIA,GAAG,CAACZ,IAAI,KAAKzS,aAAa,CAACkT,cAAc,EAAE;YAC3C,IAAII,SAAS,GAAG,IAAI,CAAC7Q,WAAW,CAACC,UAAU,CAACgQ,MAAM,CAAC,IAAI,CAACjQ,WAAW,CAACI,eAAe,CAAC,CAC/E0Q,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACtL,KAAK,KAAKmL,GAAG,CAAC1G,IAAI,CAAC8G,WAAW,EAAE,CAAC;YACpD,IAAIH,SAAS,EAAE;cACX,IAAIA,SAAS,CAACpL,KAAK,KAAK,WAAW,EAC/B,IAAI,CAACzF,WAAW,CAACI,eAAe,CAACiH,KAAK,GAAGuJ,GAAG,CAACpL,EAAE;cACnDqL,SAAS,CAACxJ,KAAK,GAAGuJ,GAAG,CAACpL,EAAE;cACxBqL,SAAS,CAACvJ,UAAU,GAAGsJ,GAAG,CAACK,WAAW,IAAIL,GAAG,CAAC1G,IAAI;cAClD2G,SAAS,CAACtJ,aAAa,GAAGqJ,GAAG,CAACM,WAAW;cACjBL,SAAS,CAAC/H,qBAAqB,GAAG;gBAC1DqI,UAAU,EAAE,IAAI,CAACvR,OAAO;gBACxBmK,UAAU,EAAE6G,GAAG,CAAC7G,UAAU;gBAC1BhB,OAAO,EAAE6H,GAAG,CAACQ,SAAS;gBACtBnI,YAAY,EAAE2H,GAAG,CAACM,WAAW,EAAEpF,MAAM,GAAG,CAAC;gBACzCuF,YAAY,EAAG/M,CAAC,IAAI;kBAChB,IAAI,CAACgN,iBAAiB,CAAC5L,MAAM,GAAG6K,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEI,GAAG,CAAC;kBACtD;kBACA,IAAI,CAACU,iBAAiB,CAAC5D,OAAO,GAAG,IAAI,CAAClN,cAAc,CAACyE,MAAM,CAACK,CAAC,IAAIA,CAAC,CAAC0K,IAAI,KAAKzS,aAAa,CAACkT,cAAc,CAAC;kBACzG,IAAI,CAACa,iBAAiB,CAACtK,IAAI,CAAC1C,CAAC,EAAE,QAAQ,CAAC;gBAC5C;eACH;cAED;;YAGA;YACA,IAAIsM,GAAG,CAAC1G,IAAI,KAAK,QAAQ,IAAI0G,GAAG,CAACW,iBAAiB,EAAEC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE;cAC9E;cACA,IAAI,CAAC5B,OAAO,CAAC6B,OAAO,EAAEC,OAAO,EAAE5F,MAAM,EAAE;gBACnC+E,SAAS,CAAC1H,gBAAgB,CAACc,MAAM,GAAG,CAAC;kBAAEzE,EAAE,EAAE,CAAC;kBAAE0E,IAAI,EAAE;gBAAK,CAAG,EAC5D,GAAG2G,SAAS,CAAC1H,gBAAgB,CAACc,MAAM,CAAC;;;YAI7C;YACA;YACA;;UAGJ;UACA,IAAIhD,MAAM,GAAW;YACjBI,KAAK,EAAE,GAAGuJ,GAAG,CAACpL,EAAE,EAAE;YAClBC,KAAK,EAAE,cAAc,GAAGmL,GAAG,CAACpL,EAAE;YAC9B8B,UAAU,EAAEsJ,GAAG,CAACK,WAAW,IAAIL,GAAG,CAAC1G,IAAI;YACvC3C,aAAa,EAAEqJ,GAAG,CAACM,WAAW;YAC9BzJ,eAAe,EAAErJ,gBAAgB;YACjC0K,qBAAqB,EAAE;cACnBqI,UAAU,EAAE,IAAI,CAACvR,OAAO;cACxBmK,UAAU,EAAE6G,GAAG,CAAC7G,UAAU;cAC1BiG,IAAI,EAAEY,GAAG,CAACZ,IAAI;cACdjH,OAAO,EAAE6H,GAAG,CAACQ,SAAS;cACtBO,QAAQ,EAAEf,GAAG,CAACgB,KAAK;cACnB3I,YAAY,EAAE2H,GAAG,CAACM,WAAW,EAAEpF,MAAM,GAAG,CAAC;cACzCuF,YAAY,EAAEA,CAAC/M,CAAC,EAAEuN,QAAkB,KAAI;gBACpC;gBACA,IAAI,CAACP,iBAAiB,CAAC5L,MAAM,GAAG6K,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEI,GAAG,CAAC;gBACtD;gBACA,IAAI,CAACU,iBAAiB,CAAC5D,OAAO,GAAG,IAAI,CAACjN,cAAc,CAC/CwE,MAAM,CAACK,CAAC,IAAI,CAAC/H,aAAa,CAAC2N,YAAY,EAAE3N,aAAa,CAAC4Q,QAAQ,CAAC,CAACkC,QAAQ,CAAC/K,CAAC,CAAC0K,IAAI,CAAC,CAAC;gBACvF,IAAI,CAACsB,iBAAiB,CAACtK,IAAI,CAAC1C,CAAC,EAAE,GAAGsM,GAAG,CAACpL,EAAE,EAAE,EAAEqM,QAAQ,CAAC;cACzD;aACH;YACD3I,UAAU,EAAE,IAAI,CAAC1J,WAAW,CAACsS,aAAa,CAAClB,GAAG,CAACQ,SAAS,CAAC;YACzDjI,gBAAgB,EAAE;cACd,GAAG,IAAI,CAAC3J,WAAW,CAACuS,mBAAmB,CAACnB,GAAG,CAAC;cAC5C3R,SAAS,EAAEA,CAACgI,MAAc,EAAEhB,IAAc,EAAEmD,OAAgB,EAAEC,MAAgB,EAAEC,QAAkB,KAAI;gBAClG,IAAI,CAACvC,qBAAqB,CAACC,IAAI,CAACC,MAAM,EAAEhB,IAAI,EAAEmD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,CAAC;cAC5E,CAAC;cAAEC,UAAU,EAAEA,CAAClC,KAAa,EAAEpB,IAAc,KAAI;gBAC7C,IAAI,IAAI,CAACnF,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAAC3B,YAAY,CAAC;kBAAEwG,UAAU,EAAEK,IAAI,CAACzC,IAAI,CAACgC,EAAE;kBAAEgE,kBAAkB,EAAEvD,IAAI,CAACzC,IAAI,CAACiG,UAAU;kBAAEC,QAAQ,EAAErC;gBAAK,CAAE,CAAC,CAAC,KAE3G,IAAI,CAACjI,YAAY,CAAC;kBAAEuK,QAAQ,EAAE1D,IAAI,CAACzC,IAAI,CAACgC,EAAE;kBAAEkE,QAAQ,EAAErC;gBAAK,CAAE,CAAC;cACtE;aACH;YACD5C,QAAQ,EAAGlB,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAACoK,eAAe,CAACrG,MAAM,EAAE,IAAI,CAACiB,cAAc,CAACC,QAAQ,CAAC;YAC5F2F,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC6K,iBAAiB;YAClD1B,YAAY,EAAE,IAAI,CAACnJ,WAAW,CAACwS,eAAe,CAACpB,GAAG,CAACQ,SAAS,CAAC;YAC7D;YACAhM,cAAc,EAAE;cACZ,UAAU,EAAE,IAAI,CAAC5F,WAAW,CAACyS,gBAAgB;cAAE,aAAa,EAAE,IAAI,CAACzS,WAAW,CAAC0O;cAC/E;aACH;YAAErE,eAAe,EAAE,MAAM;YAC1B7B,cAAc,EAAE,IAAI,CAACxI,WAAW,CAAC0S,iBAAiB,CAACtB,GAAG,CAACQ,SAAS,CAAC;YACjEe,WAAW,EAAE,IAAI,CAAC3S,WAAW,CAAC0S,iBAAiB,CAACtB,GAAG,CAACQ,SAAS,CAAC;YAAEgB,OAAO,EAAE,IAAI,CAAC5S,WAAW,CAAC6S,UAAU,CAACzB,GAAG,CAACQ,SAAS,CAAC;YACnHzJ,UAAU,EAAE,IAAI;YAAEU,WAAW,EAAE,IAAI;YAAET,eAAe,EAAE,CAAC,IAAI,CAAChI,OAAO;YACnEiI,KAAK,EAAErK,WAAW,CAACoT,GAAG,CAAC1G,IAAI,IAAI0G,GAAG,CAAC7G,UAAU,GAAG,GAAG,GAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAACnK,OAAO,EAAEgR,GAAG,CAACM,WAAW,EAAEpF,MAAM,GAAG,CAAC,CAAC;WACvG;UAED;UACA,IAAI8E,GAAG,CAACQ,SAAS,KAAK9T,WAAW,CAAC0L,UAAU,EAAE;YAC1C/B,MAAM,CAACd,aAAa,GAAIC,KAAK,IAAK,IAAI,CAACD,aAAa,CAACC,KAAK,CAAC;YAC3Da,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC1H,WAAW,CAACsK,gBAAgB;;UAGzD;UACA,IAAI8G,GAAG,CAACQ,SAAS,IAAI9T,WAAW,CAAC2N,MAAM,IAAI2F,GAAG,CAACQ,SAAS,IAAI9T,WAAW,CAACgV,GAAG,EAAE;YACzErL,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC1H,WAAW,CAACsK,gBAAgB;YACrD7C,MAAM,CAACyE,WAAW,GAAInI,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAAC+S,eAAe,CAAC3B,GAAG,CAACpL,EAAE,EAAEjC,MAAM,CAAC;YACjF0D,MAAM,CAACuL,WAAW,GAAIjP,MAAM,IAAKkP,UAAU,CAAClP,MAAM,CAACmP,QAAQ,CAAC;;UAGhE,IAAI9B,GAAG,CAACQ,SAAS,KAAK9T,WAAW,CAACqN,IAAI,EAAE;YACpC;YACA1D,MAAM,CAACqD,YAAY,GAAG;cAAEF,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC+K;YAAuB,CAAE;YAClFtD,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC1H,WAAW,CAACqL,iBAAiB;;UAG1D,IAAI+F,GAAG,CAACZ,IAAI,KAAKzS,aAAa,CAAC2N,YAAY,EAAE;YACzCjE,MAAM,CAACO,WAAW,GAAG,mBAAmB;YACxCkJ,QAAQ,CAAC3F,IAAI,CAAC9D,MAAM,CAAC;WACxB,MAAM,IAAI2J,GAAG,CAACZ,IAAI,KAAKzS,aAAa,CAAC4Q,QAAQ,EAAE;YAC5ClH,MAAM,CAACO,WAAW,GAAG,mBAAmB;YACxCP,MAAM,CAAC7B,cAAc,GAAG;cAAE,GAAG6B,MAAM,CAAC7B,cAAc;cAAE,YAAY,EAAG7B,MAAM,IAAKA,MAAM,CAACC,IAAI,EAAEmP;YAAK,CAAE,EAAE;YACpG1L,MAAM,CAACkF,kBAAkB,GAAI5I,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAAC2M,kBAAkB,CAAC5I,MAAM,CAAC,EAAK;YACxFoN,QAAQ,CAAC5F,IAAI,CAAC9D,MAAM,CAAC;;UAEzB;UACA,IAAI,CAACxG,cAAc,CAACsK,IAAI,CAAC6F,GAAG,CAAC;QACjC,CAAC,CAAC;QAEF;QACAF,QAAQ,CAACkC,IAAI,CAAC,CAAC/L,CAAC,EAAEF,CAAC,KAAME,CAAC,CAACiC,qBAAqB,CAAC6I,QAAQ,GAAGhL,CAAC,CAACmC,qBAAqB,CAAC6I,QAAQ,GAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACvGhB,QAAQ,CAACiC,IAAI,CAAC,CAAC/L,CAAC,EAAEF,CAAC,KAAME,CAAC,CAACiC,qBAAqB,CAAC6I,QAAQ,GAAGhL,CAAC,CAACmC,qBAAqB,CAAC6I,QAAQ,GAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAEvG,IAAIjB,QAAQ,CAAC5E,MAAM;UAAS;UACxB4E,QAAQ,CAAC,CAAC,CAAC,CAAC7G,eAAe,GAAG,IAAI;QAEtC;QACA,IAAI,IAAI,CAACjK,OAAO,EAAE;UACd,IAAIiT,UAAU,GAAIvO,CAAC,IAAI;YACnB,IAAI,CAACgN,iBAAiB,CAAC5L,MAAM,GAAG,IAAIjI,aAAa,CAAC,CAAC,EAAEmS,OAAO,CAACzO,MAAM,EAAE,EAAE,EAAE5D,aAAa,CAAC2N,YAAY,EAAEwF,QAAQ,CAAC5E,MAAM,EAAE,CAAC,CAAC;YACxH;YACA,IAAI,CAACwF,iBAAiB,CAAC5D,OAAO,GAAG,IAAI,CAACjN,cAAc,CAC/CwE,MAAM,CAACK,CAAC,IAAI,CAAC/H,aAAa,CAAC2N,YAAY,EAAE3N,aAAa,CAAC4Q,QAAQ,CAAC,CAACkC,QAAQ,CAAC/K,CAAC,CAAC0K,IAAI,CAAC,CAAC;YACvF,IAAI,CAACsB,iBAAiB,CAACtK,IAAI,CAAC1C,CAAC,CAAC;UAClC,CAAC;UACD,IAAIwO,SAAS,GAAG;YACZ,GAAG,IAAI,CAAC1L,YAAY;YACpB0B,qBAAqB,EAAE;cAAEgK,SAAS,EAAE,IAAI;cAAEzB,YAAY,EAAEwB;YAAU;WACrE;UACDC,SAAS,CAACtL,WAAW,GAAG,mBAAmB;UAC3CkJ,QAAQ,CAAC3F,IAAI,CAAC+H,SAAS,CAAC;UAExBD,UAAU,GAAIvO,CAAC,IAAI;YACf,IAAI,CAACgN,iBAAiB,CAAC5L,MAAM,GAAG,IAAIjI,aAAa,CAAC,CAAC,EAAEmS,OAAO,CAACzO,MAAM,EAAE,EAAE,EAAE5D,aAAa,CAAC4Q,QAAQ,EAAEwC,QAAQ,CAAC7E,MAAM,EAAE,CAAC,CAAC;YACpH;YACA,IAAI,CAACwF,iBAAiB,CAAC5D,OAAO,GAAG,IAAI,CAACjN,cAAc,CAC/CwE,MAAM,CAACK,CAAC,IAAI,CAAC/H,aAAa,CAAC2N,YAAY,EAAE3N,aAAa,CAAC4Q,QAAQ,CAAC,CAACkC,QAAQ,CAAC/K,CAAC,CAAC0K,IAAI,CAAC,CAAC;YACvF,IAAI,CAACsB,iBAAiB,CAACtK,IAAI,CAAC1C,CAAC,CAAC;UAClC,CAAC;UACDwO,SAAS,GAAG;YACR,GAAG,IAAI,CAAC1L,YAAY;YACpB0B,qBAAqB,EAAE;cAAEgK,SAAS,EAAE,IAAI;cAAEzB,YAAY,EAAEwB,UAAU,CAAC9F,IAAI,CAAC,EAAE;YAAC;WAC9E;UACD+F,SAAS,CAACtL,WAAW,GAAG,mBAAmB;UAC3CmJ,QAAQ,CAAC5F,IAAI,CAAC+H,SAAS,CAAC;;QAG5BvS,SAAS,GAAGgQ,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE,IAAI,CAACxQ,WAAW,CAACO,SAAS,CAAC;QACzDA,SAAS,CAACiH,WAAW,GAAG,mBAAmB;QAC3C,IAAI,CAACgB,UAAU,CAACuC,IAAI,CAAC;UACjBmF,OAAO,EAAE,GAAG3S,aAAa,CAAC2N,YAAY,EAAE;UAAE3D,aAAa,EAAE,kDAAkD;UAC3GD,UAAU,EAAE,MAAM;UAAEE,WAAW,EAAE,yBAAyB;UAC1DI,eAAe,EAAE,IAAI;UACrBuI,aAAa,EAAE,IAAI;UACnBC,aAAa,EAAE,CAAC,IAAI,CAACrQ,cAAc,CAAC,CAAC,CAAC,CAACsQ,QAAQ,CAAC,IAAI,CAACvP,YAAY,CAACK,MAAM,CAAC;UACzEmP,QAAQ,EAAEI,QAAQ,CAAC5E,MAAM,IAAI,IAAI,CAAClM,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG8Q,QAAQ,EAAEnQ,SAAS,CAAC,GAAG,CAAC,GAAGmQ,QAAQ;SAC/F,CAAC;QAEFnQ,SAAS,GAAGgQ,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE,IAAI,CAACxQ,WAAW,CAACO,SAAS,CAAC;QACzDA,SAAS,CAACiH,WAAW,GAAG,mBAAmB;QAC3C,IAAI,CAACgB,UAAU,CAACuC,IAAI,CAAC;UACjBmF,OAAO,EAAE,GAAG3S,aAAa,CAAC4Q,QAAQ,EAAE;UACpC7G,UAAU,EAAE,qBAAqB;UAAEE,WAAW,EAAE,mCAAmC;UACnFD,aAAa,EAAE,2GAA2G;UAC1HK,eAAe,EAAE,IAAI;UACrBuI,aAAa,EAAE,IAAI;UACnBC,aAAa,EAAE,CAAC,IAAI,CAACrQ,cAAc,CAAC,CAAC,CAAC,CAACsQ,QAAQ,CAAC,IAAI,CAACvP,YAAY,CAACK,MAAM,CAAC;UACzEmP,QAAQ,EAAEK,QAAQ,CAAC7E,MAAM,IAAI,IAAI,CAAClM,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAACI,WAAW,CAACK,eAAe,EAAEE,SAAS,EAAE,GAAGoQ,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC3Q,WAAW,CAACK,eAAe,EAAE,GAAGsQ,QAAQ;SACnK,CAAC;QACF;OACH,MAAM;QAAO;QACV;QACA,IAAI,CAACvB,cAAc,CAAC,IAAI,CAAC;QAEzB;QACA,IAAI,CAACpP,WAAW,CAACC,UAAU,CAAC,CAAC,CAAC,CAAC4J,eAAe,GAAG,IAAI;QACrD,IAAI,CAAC7J,WAAW,CAACO,SAAS,CAACiH,WAAW,GAAG,mBAAmB;QAC5D,IAAI,CAACgB,UAAU,CAACuC,IAAI,CAAC;UACjBmF,OAAO,EAAE,GAAG3S,aAAa,CAACwV,YAAY,EAAE;UAAExL,aAAa,EAAE,kDAAkD;UAC3GD,UAAU,EAAE,UAAU;UAAEE,WAAW,EAAE,yBAAyB;UAC9DI,eAAe,EAAE,IAAI;UACrBuI,aAAa,EAAE,IAAI;UACnBC,aAAa,EAAE,CAAC,IAAI,CAACrQ,cAAc,CAAC,CAAC,CAAC,CAACsQ,QAAQ,CAAC,IAAI,CAACvP,YAAY,CAACK,MAAM,CAAC;UACzEmP,QAAQ,EAAE,CAAC,GAAG,IAAI,CAACtQ,WAAW,CAACC,UAAU,EAAE,IAAI,CAACD,WAAW,CAACO,SAAS;SACxE,CAAC;QAEF;QACA,IAAImQ,QAAQ,GAAG,EAAE;QACjB,IAAIsC,UAAU,GAAG,EAAE;QACnB,IAAI,CAACxS,cAAc,CAACyL,OAAO,CAAC2E,GAAG,IAAG;UAC9B;UACA,IAAIA,GAAG,CAACZ,IAAI,KAAKzS,aAAa,CAACwV,YAAY,EAAE;YACzC,IAAIlC,SAAS,GAAG,IAAI,CAAC7Q,WAAW,CAACC,UAAU,CAAC6Q,IAAI,CAACC,EAAE,IAAIA,EAAE,CAACtL,KAAK,KAAKmL,GAAG,CAAC1G,IAAI,CAAC8G,WAAW,EAAE,CAAC;YAC3F,IAAIH,SAAS,EAAE;cACXA,SAAS,CAACxJ,KAAK,GAAGuJ,GAAG,CAACpL,EAAE;cACxBqL,SAAS,CAACvJ,UAAU,GAAGsJ,GAAG,CAACK,WAAW,IAAIL,GAAG,CAAC1G,IAAI;cAClD2G,SAAS,CAACtJ,aAAa,GAAGqJ,GAAG,CAACM,WAAW;cACjBL,SAAS,CAAC/H,qBAAqB,GAAG;gBAC1DqI,UAAU,EAAE,IAAI,CAACvR,OAAO;gBACxBmK,UAAU,EAAE6G,GAAG,CAAC7G,UAAU;gBAC1BhB,OAAO,EAAE6H,GAAG,CAACQ,SAAS;gBACtBnI,YAAY,EAAE2H,GAAG,CAACM,WAAW,EAAEpF,MAAM,GAAG,CAAC;gBACzCuF,YAAY,EAAG/M,CAAC,IAAI;kBAChB,IAAI,CAACgN,iBAAiB,CAAC5L,MAAM,GAAG6K,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEI,GAAG,CAAC;kBACtD,IAAI,CAACU,iBAAiB,CAAC5D,OAAO,GAAG,IAAI,CAAClN,cAAc,CAACyE,MAAM,CAACK,CAAC,IAAIA,CAAC,CAAC0K,IAAI,KAAKzS,aAAa,CAACwV,YAAY,CAAC;kBACvG,IAAI,CAACzB,iBAAiB,CAACtK,IAAI,CAAC1C,CAAC,EAAE,QAAQ,CAAC;gBAC5C;eACH;cAED;;YAGA;YACA,IAAIsM,GAAG,CAAC1G,IAAI,KAAK,QAAQ,IAAI0G,GAAG,CAACW,iBAAiB,EAAEC,OAAO,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,EAAE;cAC9E;cACA,IAAI,CAAC5B,OAAO,CAAC6B,OAAO,EAAEC,OAAO,EAAE5F,MAAM,EAAE;gBACnC+E,SAAS,CAAC1H,gBAAgB,CAACc,MAAM,GAAG,CAAC;kBAAEzE,EAAE,EAAE,CAAC;kBAAE0E,IAAI,EAAE;gBAAU,CAAE,EAChE,GAAG2G,SAAS,CAAC1H,gBAAgB,CAACc,MAAM,CAAC;;;YAI7C;YACA;YACA;;UAGJ;UACA,IAAIhD,MAAM,GAAW;YACjBI,KAAK,EAAE,GAAGuJ,GAAG,CAACpL,EAAE,EAAE;YAClBC,KAAK,EAAE,cAAc,GAAGmL,GAAG,CAACpL,EAAE;YAC9B8B,UAAU,EAAEsJ,GAAG,CAACK,WAAW,IAAIL,GAAG,CAAC1G,IAAI;YACvC3C,aAAa,EAAEqJ,GAAG,CAACM,WAAW;YAC9BzJ,eAAe,EAAErJ,gBAAgB;YACjC0K,qBAAqB,EAAE;cACnBqI,UAAU,EAAE,IAAI,CAACvR,OAAO;cACxBmK,UAAU,EAAE6G,GAAG,CAAC7G,UAAU;cAC1BiG,IAAI,EAAEY,GAAG,CAACZ,IAAI;cACdjH,OAAO,EAAE6H,GAAG,CAACQ,SAAS;cACtBO,QAAQ,EAAEf,GAAG,CAACgB,KAAK;cACnB3I,YAAY,EAAE2H,GAAG,CAACM,WAAW,EAAEpF,MAAM,GAAG,CAAC;cACzCuF,YAAY,EAAEA,CAAC/M,CAAC,EAAEuN,QAAkB,KAAI;gBACpC;gBACA,IAAI,CAACP,iBAAiB,CAAC5L,MAAM,GAAG6K,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEI,GAAG,CAAC;gBACtD;gBACA,IAAI,CAACU,iBAAiB,CAAC5D,OAAO,GAAG,IAAI,CAACjN,cAAc,CAC/CwE,MAAM,CAACK,CAAC,IAAI,CAAC/H,aAAa,CAACyN,UAAU,EAAEzN,aAAa,CAAC0V,MAAM,CAAC,CAAC5C,QAAQ,CAAC/K,CAAC,CAAC0K,IAAI,CAAC,CAAC;gBACnF,IAAI,CAACsB,iBAAiB,CAACtK,IAAI,CAAC1C,CAAC,EAAE,GAAGsM,GAAG,CAACpL,EAAE,EAAE,EAAEqM,QAAQ,CAAC;cACzD;aACH;YACD3I,UAAU,EAAE,IAAI,CAAC1J,WAAW,CAACsS,aAAa,CAAClB,GAAG,CAACQ,SAAS,CAAC;YACzDjI,gBAAgB,EAAE;cACd,GAAG,IAAI,CAAC3J,WAAW,CAACuS,mBAAmB,CAACnB,GAAG,CAAC;cAC5C3R,SAAS,EAAEA,CAACgI,MAAc,EAAEhB,IAAc,EAAEmD,OAAgB,EAAEC,MAAgB,EAAEC,QAAkB,KAAI;gBAClG,IAAI,CAACvC,qBAAqB,CAACC,IAAI,CAACC,MAAM,EAAEhB,IAAI,EAAEmD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,CAAC;cAC5E,CAAC;cAAEC,UAAU,EAAEA,CAAClC,KAAa,EAAEpB,IAAc,KAAI;gBAC7C,IAAI,IAAI,CAACnF,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAAC3B,YAAY,CAAC;kBAAEwG,UAAU,EAAEK,IAAI,CAACzC,IAAI,CAACgC,EAAE;kBAAEgE,kBAAkB,EAAEvD,IAAI,CAACzC,IAAI,CAACiG,UAAU;kBAAEC,QAAQ,EAAErC;gBAAK,CAAE,CAAC,CAAC,KAE3G,IAAI,CAACjI,YAAY,CAAC;kBAAEuK,QAAQ,EAAE1D,IAAI,CAACzC,IAAI,CAACgC,EAAE;kBAAEkE,QAAQ,EAAErC;gBAAK,CAAE,CAAC;cACtE;aACH;YAAEwC,eAAe,EAAE,MAAM;YAC1BpF,QAAQ,EAAGlB,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAACoK,eAAe,CAACrG,MAAM,EAAE,IAAI,CAACiB,cAAc,CAACC,QAAQ,CAAC;YAC5F2F,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC6K,iBAAiB;YAClD1B,YAAY,EAAE,IAAI,CAACnJ,WAAW,CAACwS,eAAe,CAACpB,GAAG,CAACQ,SAAS,CAAC;YAC7DhM,cAAc,EAAE;cAAE,UAAU,EAAE,IAAI,CAAC5F,WAAW,CAACyS,gBAAgB;cAAE,aAAa,EAAE,IAAI,CAACzS,WAAW,CAAC0O;YAAiB,CAAE;YACpHiE,WAAW,EAAE,IAAI,CAAC3S,WAAW,CAAC0S,iBAAiB,CAACtB,GAAG,CAACQ,SAAS,CAAC;YAAEpJ,cAAc,EAAE,IAAI,CAACxI,WAAW,CAAC0S,iBAAiB,CAACtB,GAAG,CAACQ,SAAS,CAAC;YACjIgB,OAAO,EAAE,IAAI,CAAC5S,WAAW,CAAC6S,UAAU,CAACzB,GAAG,CAACQ,SAAS,CAAC;YACnDzJ,UAAU,EAAE,IAAI;YAAEU,WAAW,EAAE,IAAI;YAAET,eAAe,EAAE,CAAC,IAAI,CAAChI,OAAO;YACnEiI,KAAK,EAAErK,WAAW,CAACoT,GAAG,CAAC1G,IAAI,IAAI0G,GAAG,CAAC7G,UAAU,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAACnK,OAAO,EAAEgR,GAAG,CAACM,WAAW,EAAEpF,MAAM,GAAG,CAAC,CAAC;WACzG;UAED;UACA,IAAI8E,GAAG,CAACQ,SAAS,KAAK9T,WAAW,CAAC0L,UAAU,EAAE;YAC1C/B,MAAM,CAACd,aAAa,GAAIC,KAAK,IAAK,IAAI,CAACD,aAAa,CAACC,KAAK,CAAC;YAC3Da,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC1H,WAAW,CAACsK,gBAAgB;;UAGzD;UACA,IAAI8G,GAAG,CAACQ,SAAS,IAAI9T,WAAW,CAAC2N,MAAM,IAAI2F,GAAG,CAACQ,SAAS,IAAI9T,WAAW,CAACgV,GAAG,EAAE;YACzErL,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC1H,WAAW,CAACsK,gBAAgB;YACrD7C,MAAM,CAACyE,WAAW,GAAInI,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAAC+S,eAAe,CAAC3B,GAAG,CAACpL,EAAE,EAAEjC,MAAM,CAAC;YACjF0D,MAAM,CAACuL,WAAW,GAAIjP,MAAM,IAAKkP,UAAU,CAAClP,MAAM,CAACmP,QAAQ,CAAC;;UAGhE,IAAI9B,GAAG,CAACQ,SAAS,KAAK9T,WAAW,CAACqN,IAAI,EAAE;YACpC;YACA1D,MAAM,CAACqD,YAAY,GAAG;cAAEF,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC+K;YAAuB,CAAE;YAClFtD,MAAM,CAACC,UAAU,GAAG,IAAI,CAAC1H,WAAW,CAACqL,iBAAiB;;UAG1D,IAAI+F,GAAG,CAACZ,IAAI,KAAKzS,aAAa,CAACyN,UAAU,EAAE;YACvC/D,MAAM,CAACO,WAAW,GAAG,mBAAmB;YACxCkJ,QAAQ,CAAC3F,IAAI,CAAC9D,MAAM,CAAC;WACxB,MAAM,IAAI2J,GAAG,CAACZ,IAAI,KAAKzS,aAAa,CAAC0V,MAAM,EAAE;YAC1ChM,MAAM,CAACO,WAAW,GAAG,mBAAmB;YACxCwL,UAAU,CAACjI,IAAI,CAAC9D,MAAM,CAAC;;UAG3B;UACA,IAAI,CAACxG,cAAc,CAACsK,IAAI,CAAC6F,GAAG,CAAC;QACjC,CAAC,CAAC;QAEF;QACAF,QAAQ,CAACkC,IAAI,CAAC,CAAC/L,CAAC,EAAEF,CAAC,KAAME,CAAC,CAACiC,qBAAqB,CAAC6I,QAAQ,GAAGhL,CAAC,CAACmC,qBAAqB,CAAC6I,QAAQ,GAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QACvGqB,UAAU,CAACJ,IAAI,CAAC,CAAC/L,CAAC,EAAEF,CAAC,KAAME,CAAC,CAACiC,qBAAqB,CAAC6I,QAAQ,GAAGhL,CAAC,CAACmC,qBAAqB,CAAC6I,QAAQ,GAAI,CAAC,GAAG,CAAC,CAAC,CAAC;QAEzG,IAAIqB,UAAU,CAAClH,MAAM;UAAe;UAChCkH,UAAU,CAAC,CAAC,CAAC,CAACnJ,eAAe,GAAG,IAAI;QAExC;QACA,IAAI,IAAI,CAACjK,OAAO,EAAE;UACd,IAAIiT,UAAU,GAAIvO,CAAC,IAAI;YACnB,IAAI,CAACgN,iBAAiB,CAAC5L,MAAM,GAAG,IAAIjI,aAAa,CAAC,CAAC,EAAEmS,OAAO,CAACzO,MAAM,EAAE,EAAE,EAAE5D,aAAa,CAACyN,UAAU,EAAE0F,QAAQ,CAAC5E,MAAM,EAAE,CAAC,CAAC;YACtH;YACA,IAAI,CAACwF,iBAAiB,CAAC5D,OAAO,GAAG,IAAI,CAACjN,cAAc,CAC/CwE,MAAM,CAACK,CAAC,IAAI,CAAC/H,aAAa,CAACyN,UAAU,EAAEzN,aAAa,CAAC0V,MAAM,CAAC,CAAC5C,QAAQ,CAAC/K,CAAC,CAAC0K,IAAI,CAAC,CAAC;YACnF,IAAI,CAACsB,iBAAiB,CAACtK,IAAI,CAAC1C,CAAC,CAAC;UAClC,CAAC;UACD,IAAIwO,SAAS,GAAG;YACZ,GAAG,IAAI,CAAC1L,YAAY;YACpB0B,qBAAqB,EAAE;cAAEgK,SAAS,EAAE,IAAI;cAAEzB,YAAY,EAAEwB;YAAU;WACrE;UACDC,SAAS,CAACtL,WAAW,GAAG,mBAAmB;UAC3CkJ,QAAQ,CAAC3F,IAAI,CAAC+H,SAAS,CAAC;UAExBD,UAAU,GAAIvO,CAAC,IAAI;YACf,IAAI,CAACgN,iBAAiB,CAAC5L,MAAM,GAAG,IAAIjI,aAAa,CAAC,CAAC,EAAEmS,OAAO,CAACzO,MAAM,EAAE,EAAE,EAAE5D,aAAa,CAAC0V,MAAM,EAAED,UAAU,CAAClH,MAAM,EAAE,CAAC,CAAC;YACpH;YACA,IAAI,CAACwF,iBAAiB,CAAC5D,OAAO,GAAG,IAAI,CAACjN,cAAc,CAC/CwE,MAAM,CAACK,CAAC,IAAI,CAAC/H,aAAa,CAACyN,UAAU,EAAEzN,aAAa,CAAC0V,MAAM,CAAC,CAAC5C,QAAQ,CAAC/K,CAAC,CAAC0K,IAAI,CAAC,CAAC;YACnF,IAAI,CAACsB,iBAAiB,CAACtK,IAAI,CAAC1C,CAAC,CAAC;UAClC,CAAC;UACDwO,SAAS,GAAG;YACR,GAAG,IAAI,CAAC1L,YAAY;YACpB0B,qBAAqB,EAAE;cAAEgK,SAAS,EAAE,IAAI;cAAEzB,YAAY,EAAEwB,UAAU,CAAC9F,IAAI,CAAC,EAAE;YAAC;WAC9E;UACD+F,SAAS,CAACtL,WAAW,GAAG,mBAAmB;UAC3CwL,UAAU,CAACjI,IAAI,CAAC+H,SAAS,CAAC;;QAG9B;QACA,IAAIpC,QAAQ,CAAC5E,MAAM,IAAI,IAAI,CAAC/L,cAAc,CAAC,CAAC,CAAC,EACzC,IAAI,CAACC,WAAW,CAACO,SAAS,CAAC2S,IAAI,GAAG,KAAK;QAE3C,IAAI3S,SAAS,GAAGgQ,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE,IAAI,CAACxQ,WAAW,CAACO,SAAS,CAAC;QAC7DA,SAAS,CAACiH,WAAW,GAAG,mBAAmB;QAC3C,IAAI,CAACgB,UAAU,CAACuC,IAAI,CAAC;UACjBmF,OAAO,EAAE,GAAG3S,aAAa,CAACyN,UAAU,EAAE;UAAEzD,aAAa,EAAE,kDAAkD;UACzGD,UAAU,EAAE,MAAM;UAAEE,WAAW,EAAE,yBAAyB;UAC1DI,eAAe,EAAE,IAAI;UACrBuI,aAAa,EAAE,IAAI;UACnBC,aAAa,EAAE,CAAC,IAAI,CAACrQ,cAAc,CAAC,CAAC,CAAC,CAACsQ,QAAQ,CAAC,IAAI,CAACvP,YAAY,CAACK,MAAM,CAAC;UACzEmP,QAAQ,EAAE,CAAC,GAAG,IAAI,CAACtQ,WAAW,CAACM,cAAc,EAAEC,SAAS,EAAE,GAAGmQ,QAAQ;SACxE,CAAC;QAEFnQ,SAAS,GAAGgQ,MAAM,CAACC,MAAM,CAAC,EAAE,EAAE,IAAI,CAACxQ,WAAW,CAACO,SAAS,CAAC;QACzDA,SAAS,CAACiH,WAAW,GAAG,mBAAmB;QAC3C,IAAI,CAACgB,UAAU,CAACuC,IAAI,CAAC;UACjBmF,OAAO,EAAE,GAAG3S,aAAa,CAAC0V,MAAM,EAAE;UAAE1L,aAAa,EAAE,kDAAkD;UACrGD,UAAU,EAAE,QAAQ;UAAEE,WAAW,EAAE,yBAAyB;UAC5DI,eAAe,EAAE,IAAI;UACrBuI,aAAa,EAAE,IAAI;UAAEC,aAAa,EAAE,CAAC,IAAI,CAACrQ,cAAc,CAAC,CAAC,CAAC,CAACsQ,QAAQ,CAAC,IAAI,CAACvP,YAAY,CAACK,MAAM,CAAC;UAC9FmP,QAAQ,EAAE0C,UAAU,CAAClH,MAAM,IAAI,IAAI,CAAClM,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAGoT,UAAU,EAAEzS,SAAS,CAAC,GAAG,CAAC,GAAGyS,UAAU;SACrG,CAAC;;MAGN,IAAI,CAACG,SAAS,EAAE;MAChB,IAAI,CAACzT,OAAO,GAAG,KAAK;KACvB,CAAC,OAAOgP,EAAE,EAAE;MAAEC,OAAO,CAACC,KAAK,CAACF,EAAE,CAAC;MAAE,IAAI,CAACjP,cAAc,CAACmP,KAAK,CAAC,uBAAuB,CAAC;;EACxF;EAEA;EACAwE,WAAWA,CAAC7P,MAAsB;IAC9B,IAAI,CAACiK,OAAO,GAAGjK,MAAM,CAAC8P,GAAG;IACzB,IAAI,CAACrE,aAAa,GAAGzL,MAAM,CAAC+P,SAAS;EACzC;EAEQH,SAASA,CAAA;IACb;IACA5F,UAAU,CAAC,MAAK;MACZ;MACA,MAAMgG,UAAU,GAAGxR,QAAQ,CAACC,aAAa,CAAC,kCAAkC,CAAC;MAC7E,IAAIuR,UAAU,EAAE;QACZA,UAAU,CAACC,gBAAgB,CAAC,OAAO,EAAGlP,CAAC,IAAI;UACvC;UACA,IAAI,CAACkJ,OAAO,CAACiG,sBAAsB,EAAE;QACzC,CAAC,CAAC;;MAGN;MACA,MAAMC,WAAW,GAAG3R,QAAQ,CAACC,aAAa,CAAC,kDAAkD,CAAC;MAC9F;MACA0R,WAAW,CAACF,gBAAgB,CAAC,OAAO,EAAGlP,CAAa,IAAK,IAAI,CAACqP,eAAe,CAACrP,CAAC,CAAC,CAAC;MACjFoP,WAAW,CAACF,gBAAgB,CAAC,UAAU,EAAGlP,CAAa,IAAK,IAAI,CAACqP,eAAe,CAACrP,CAAC,EAAE,IAAI,CAAC,CAAC;MAE1F,MAAMsP,gBAAgB,GAAG7R,QAAQ,CAAC8R,gBAAgB,CAAC,6DAA6D,CAAC;MACjH,IAAID,gBAAgB,EAAE;QAClBA,gBAAgB,CAAC3H,OAAO,CAAC6H,IAAI,IAAG;UAC5BA,IAAI,CAACC,YAAY,CAAC,OAAO,EAAED,IAAI,CAACvN,SAAS,CAACC,QAAQ,CAAC,gCAAgC,CAAC,GAC9E,wBAAwB,GAAG,sBAAsB,CAAC;QAC5D,CAAC,CAAC;;MAGN;MACA,IAAI,IAAI,CAAC5G,OAAO,EAAE;QACdmC,QAAQ,CAACC,aAAa,CAAC,YAAY,CAAC,EAAEwR,gBAAgB,CAAC,WAAW,EAAGlP,CAAM,IAAI;UAC3E,IAAIA,CAAC,CAAC+B,MAAM,CAAC2N,YAAY,CAAC,QAAQ,CAAC,IAAI1P,CAAC,CAAC+B,MAAM,CAACC,OAAO,CAAC,iBAAiB,CAAC,EAAE;YACxE,MAAMe,KAAK,GAAG/C,CAAC,CAAC+B,MAAM,CAAC4N,YAAY,CAAC,QAAQ,CAAC,IAAI3P,CAAC,CAAC+B,MAAM,CAACC,OAAO,CAAC,iBAAiB,CAAC,EAAE2N,YAAY,CAAC,QAAQ,CAAC;YAC5G,IAAI5M,KAAK,EAAE;cACP,IAAI,CAAC3F,YAAY,GAAG,IAAI,CAACsN,aAAa,CAACkF,iBAAiB,EAAE,CACrD7O,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACQ,QAAQ,EAAE,KAAKuB,KAAK,CAAC;;;QAGvD,CAAC,CAAC;;MAGN;MACA,MAAM8M,UAAU,GAAGpS,QAAQ,CAACC,aAAa,CAAC,qBAAqB,CAAC;MAChE,MAAMoS,YAAY,GAAGrS,QAAQ,CAACC,aAAa,CAAC,mBAAmB,CAAC;MAChE,MAAMqS,kBAAkB,GAAGtS,QAAQ,CAACC,aAAa,CAAC,0BAA0B,CAAC;MAE7EmS,UAAU,CAACX,gBAAgB,CAAC,OAAO,EAAGlP,CAAM,IAAI;QAC5CA,CAAC,CAACgQ,cAAc,EAAE;QAClB,MAAMC,MAAM,GAAGjQ,CAAC,CAACiQ,MAAM,IAAIjQ,CAAC,CAACkQ,WAAW;QACxC,MAAMC,MAAM,GAAGnQ,CAAC,CAACmQ,MAAM,IAAInQ,CAAC,CAACoQ,WAAW;QACxCN,YAAY,CAACO,SAAS,IAAIJ,MAAM;QAChCF,kBAAkB,CAACO,UAAU,IAAIH,MAAM;MAC3C,CAAC,CAAC;MAEF,IAAI,CAACI,oBAAoB,EAAE;IAC/B,CAAC,EAAE,GAAG,CAAC;EACX;EAEQlB,eAAeA,CAACrP,CAAa,EAAEwQ,QAAkB;IACrD,IAAIzN,KAAK;IACT,MAAMhB,MAAM,GAAG/B,CAAC,CAAC+B,MAAqB;IACtC,IAAI0O,KAAK,GAAGD,QAAQ,GAAGzO,MAAM,CAACrE,aAAa,CAAC,uBAAuB,CAAoB,GACnFqE,MAAM,CAAC2O,aAAa,CAACA,aAAa,CAAChT,aAAa,CAAC,uBAAuB,CAAoB;IAEhG,IAAI,CAAC+S,KAAK,EACN;IAEJ,IAAIA,KAAK,CAACE,SAAS,CAAClP,UAAU,CAAC,QAAQ,CAAC,EACpCsB,KAAK,GAAG,CAAC,CAAC,KACT,IAAI0N,KAAK,CAACE,SAAS,CAAClP,UAAU,CAAC,UAAU,CAAC,EAC3CsB,KAAK,GAAG,CAAC,CAAC,KACT,IAAI0N,KAAK,CAACE,SAAS,CAAClP,UAAU,CAAC,MAAM,CAAC,EACvCsB,KAAK,GAAG,CAAC,CAAC,KACTA,KAAK,GAAG,CAAC;IAEd,MAAMgI,SAAS,GAAG,GAAG,IAAI,CAACvO,YAAY,CAACC,QAAQ,KAAK,UAAU,GAAG,GAAG,GAAG,EAAE,QAAQsG,KAAK,WAAW;IACjG,IAAI6N,MAAM,GAAG5F,IAAI,CAACC,KAAK,CAACC,YAAY,CAACC,OAAO,CAACJ,SAAS,CAAC,IAAI,IAAI,CAAC;IAChE,IAAI6F,MAAM,CAAC7E,QAAQ,CAAC,IAAI,CAACvP,YAAY,CAACK,MAAM,CAAC,EACzC+T,MAAM,GAAGA,MAAM,CAACjQ,MAAM,CAACsJ,CAAC,IAAIA,CAAC,KAAK,IAAI,CAACzN,YAAY,CAACK,MAAM,CAAC,CAAC,KAE5D+T,MAAM,CAACnK,IAAI,CAAC,IAAI,CAACjK,YAAY,CAACK,MAAM,CAAC;IACzCqO,YAAY,CAAC2F,OAAO,CAAC9F,SAAS,EAAEC,IAAI,CAAC8F,SAAS,CAACF,MAAM,CAAC,CAAC;IACvD,IAAI,CAACnV,cAAc,CAACsH,KAAK,GAAG,CAAC,CAAC,GAAG6N,MAAM;IAEvC;IACA;IACA;IACA;IACA;EACJ;;EAKMG,aAAaA,CAACjP,KAAuB;IAAA,IAAAkP,MAAA;IAAA,OAAAjK,iBAAA;MACvC,IAAI,CAACjF,KAAK,CAACV,MAAM,EAAE;MACnB,IAAIU,KAAK,CAACmP,QAAQ,EAAE;QAChB,IAAID,MAAI,CAAC3T,UAAU,EAAE;UACjB2T,MAAI,CAAC3T,UAAU,GAAG,KAAK;UACvB;;QAGJ,IAAI;UACA,MAAM6T,OAAO,GAAGpP,KAAK,CAACkN,SAAS,CAACY,iBAAiB,EAAE;UACnD,MAAMuB,UAAU,GAAGD,OAAO,CAACpP,KAAK,CAACsP,OAAO,GAAG,CAAC,CAAC,EAAE5P,QAAQ,EAAE;UACzD,MAAM6P,cAAc,GAAGvP,KAAK,CAACsP,OAAO,KAAKF,OAAO,CAAC1J,MAAM,GAAG,CAAC,IAAI0J,OAAO,CAACpP,KAAK,CAACsP,OAAO,GAAG,CAAC,CAAC,EAAE5P,QAAQ,EAAE,KAAK,KAAK;UAE/G,MAAM8P,cAAc,GAAG,CAAC,MAAM,EAAE,MAAM,EAAE,KAAK,CAAC;UAC9C;UACA,IAAIA,cAAc,CAACvF,QAAQ,CAACoF,UAAU,CAAC,IAAIE,cAAc,EAAE;YACvDL,MAAI,CAAC3T,UAAU,GAAG,IAAI;YACtByE,KAAK,CAACkN,SAAS,CAACuC,iBAAiB,CAACzP,KAAK,CAACsP,OAAO,EAAEJ,MAAI,CAAC5T,YAAY,CAAC;YACnE4T,MAAI,CAAC7V,cAAc,CAACqW,IAAI,CAAC,oDAAoD,CAAC;WACjF,MAAM;YACH;YACA,IAAIC,WAAW,GAAa,EAAE;YAC9B,MAAMC,YAAY,GAAG,CAAC5P,KAAK,CAACV,MAAM,CAACC,SAAS,EAAE,CAACmD,qBAAqB,CAACkH,IAAI;YAEzE;YACA;YACA,IAAIiG,aAAa,GAAa,EAAE;YAChC,KAAK,IAAI1H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG+G,MAAI,CAAC9M,UAAU,CAACsD,MAAM,EAAEyC,CAAC,EAAE,EAAE;cAC7C,IAAI+G,MAAI,CAAC9M,UAAU,CAAC+F,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI+G,MAAI,CAAC9M,UAAU,CAAC+F,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,GAAGyH,YAAY,EAAE,EAAE;gBACtF,MAAMjB,KAAK,GAAGO,MAAI,CAAC9M,UAAU,CAAC+F,CAAC,CAAgB;gBAC/C0H,aAAa,GAAGlB,KAAK,CAACzE,QAAoB;gBAC1C;;;YAGR;YACA,IAAI4F,MAAM,GAAG,CAAC;YACd,KAAK,IAAI3H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiH,OAAO,CAAC1J,MAAM,EAAEyC,CAAC,EAAE,EAAE;cACrC,MAAMtH,MAAM,GAAGuO,OAAO,CAACjH,CAAC,CAAC,CAAC5I,SAAS,EAAE;cACrC,IAAIsB,MAAM,CAAC6B,qBAAqB,EAAEkH,IAAI,KAAKmG,SAAS,EAChD;cACJ,IAAI,CAAClP,MAAM,CAAC6B,qBAAqB,CAACkH,IAAI,KAAKgG,YAAY,EAAE;gBACrDE,MAAM,EAAE;gBACRjP,MAAM,CAAC6B,qBAAqB,CAAC6I,QAAQ,GAAGuE,MAAM,CAAC,CAAK;gBACpDD,aAAa,CAACnF,IAAI,CAACxL,CAAC,IAAIA,CAAC,CAAC+B,KAAK,KAAKJ,MAAM,CAACI,KAAK,CAAC,CAACyB,qBAAqB,CAAC6I,QAAQ,GAAGuE,MAAM;gBACzFH,WAAW,CAAChL,IAAI,CAAC,CAAC9D,MAAM,CAACI,KAAK,CAAC;;;YAGvC4O,aAAa,CAACrD,IAAI,CAAC,CAAC/L,CAAC,EAAEF,CAAC,KAAI;cACxB,IAAI,CAACE,CAAC,CAACiC,qBAAqB,IAAI,CAACnC,CAAC,CAACmC,qBAAqB,EAAE,OAAO,CAAC;cAClE,OAAOjC,CAAC,CAACiC,qBAAqB,CAAC6I,QAAQ,GAAGhL,CAAC,CAACmC,qBAAqB,CAAC6I,QAAQ,GAAG,CAAC,GAAG,CAAC,CAAC;YACvF,CAAC,CAAC;YAEF;YACA,IAAIoE,WAAW,CAACjK,MAAM,EAAE;cACpB,IAAI,QAAQwJ,MAAI,CAAChE,iBAAiB,CAAC8E,gBAAgB,CAACL,WAAW,CAAC,CAAC,EAC7D;cAEJ;cACA,KAAK,IAAIxH,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGwH,WAAW,CAACjK,MAAM,EAAEyC,CAAC,EAAE,EAAE;gBACzC+G,MAAI,CAAC9U,cAAc,CAACsQ,IAAI,CAACxL,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAKuQ,WAAW,CAACxH,CAAC,CAAC,CAAC,CAACqD,KAAK,GAAGrD,CAAC,GAAG,CAAC;gBACpE+G,MAAI,CAAC7U,cAAc,CAACqQ,IAAI,CAACxL,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAKuQ,WAAW,CAACxH,CAAC,CAAC,CAAC,CAACqD,KAAK,GAAGrD,CAAC,GAAG,CAAC;;cAGxE;cACA,IAAIU,IAAI,GAAGqG,MAAI,CAAC9U,cAAc,CAACyE,MAAM,CAACK,CAAC,IAAIA,CAAC,CAAC0K,IAAI,KAAKgG,YAAY,CAAC;cACnE/G,IAAI,CAAC2D,IAAI,CAAC,CAAC/L,CAAC,EAAEF,CAAC,KAAME,CAAC,CAAC+K,KAAK,GAAGjL,CAAC,CAACiL,KAAK,GAAI,CAAC,GAAG,CAAC,CAAC,CAAC;cACjD0D,MAAI,CAAC7U,cAAc,CAACmS,IAAI,CAAC,CAAC/L,CAAC,EAAEF,CAAC,KAAKE,CAAC,CAACmJ,IAAI,KAAKgG,YAAY,IAAIrP,CAAC,CAACqJ,IAAI,KAAKgG,YAAY,IAAKnP,CAAC,CAAC+K,KAAK,GAAGjL,CAAC,CAACiL,KAAM,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;cAEtH;cACA0D,MAAI,CAAC5U,OAAO,CAAC2V,IAAI,CAACpH,IAAI,CAAC;;;SAGlC,CAAC,OAAOP,EAAE,EAAE;UAAEC,OAAO,CAACC,KAAK,CAACF,EAAE,CAAC;UAAE4G,MAAI,CAAC7V,cAAc,CAACmP,KAAK,CAAC,uBAAuB,CAAC;;;IACvF;EACL;EAEAzI,aAAaA,CAACC,KAAuB;IACjC,MAAMiB,KAAK,GAAGjB,KAAK,CAACV,MAAM,CAACI,QAAQ,EAAE;IACrC,IAAIuB,KAAK,KAAK,QAAQ,EAAE;MAAoB;MACxC;MACA;MACA,MAAM4H,IAAI,GAAG7I,KAAK,CAACkN,SAAS,CAACpE,UAAU,EAAE,CAACoH,GAAG,CAAChR,CAAC,IAAIA,CAAC,CAACQ,QAAQ,EAAE,CAAC;MAChE,IAAIyQ,cAAc,GAAGtH,IAAI,CAACA,IAAI,CAACnD,MAAM,GAAG,CAAC,CAAC;MAC1C,IAAIyK,cAAc,CAACxQ,UAAU,CAAC,aAAa,CAAC,EAAE;QAC1C,KAAK,IAAIwI,CAAC,GAAGU,IAAI,CAACnD,MAAM,GAAG,CAAC,EAAEyC,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;UACvC,IAAI,CAACU,IAAI,CAACV,CAAC,CAAC,CAACxI,UAAU,CAAC,aAAa,CAAC,EAAE;YACpCwQ,cAAc,GAAGtH,IAAI,CAACV,CAAC,CAAC;YACxB;;;;MAIZnI,KAAK,CAACiN,GAAG,CAACmD,mBAAmB,EAAE;MAC/BpQ,KAAK,CAACiN,GAAG,CAACoD,YAAY,CAAC;QACnBC,WAAW,EAAEzH,IAAI,CAAC,CAAC,CAAC;QACpB0H,SAAS,EAAEJ,cAAc;QACzBK,aAAa,EAAExQ,KAAK,CAACyC,QAAQ;QAC7BgO,WAAW,EAAEzQ,KAAK,CAACyC;OACtB,CAAC;KACL,MAAM;MACH,IAAI,CAACzC,KAAK,CAACoE,KAAK,IAAI,CAACpE,KAAK,CAACV,MAAM,CAACM,cAAc,CAACI,KAAK,CAACH,IAAI,CAAC,EACxD;MACJ,IAAIG,KAAK,CAAC5C,IAAI,CAACgC,EAAE,GAAG,CAAC,KAAKY,KAAK,CAAC5C,IAAI,CAACoC,UAAU,IAAIQ,KAAK,CAAC5C,IAAI,CAACqC,QAAQ,CAAC;QAAE;QACrE,IAAI,CAACiR,wBAAwB,CAACxO,QAAQ,CAACjB,KAAK,EAAEjB,KAAK,CAAC;;EAEhE;EAEA2Q,aAAaA,CAAC3Q,KAAuB;IACjC,IAAI,CAACoH,OAAO,CAACwJ,WAAW,EAAE;EAC9B;EAGA7K,kBAAkBA,CAAC/F,KAA4B;IAC3C,IAAIA,KAAK,CAAC5C,IAAI,CAACoI,MAAM,EAAEE,MAAM,IAAI1F,KAAK,CAAC5C,IAAI,CAACyT,QAAQ,EAAEnL,MAAM,IACxD1F,KAAK,CAAC5C,IAAI,CAAC0T,QAAQ,EAAEpL,MAAM,IAAI1F,KAAK,CAAC5C,IAAI,CAAC2T,SAAS,EAAErL,MAAM,EAAE;MAC7D1F,KAAK,CAAC5C,IAAI,CAACiL,KAAK,GAAG,IAAI,CAAC,CAAQ;KACnC,MAAMrI,KAAK,CAAC5C,IAAI,CAACiL,KAAK,GAAG,KAAK;IAE/B,IAAIrI,KAAK,CAACsM,QAAQ,IAAI,IAAI,CAAC9Q,YAAY,CAACkK,MAAM,EAAE;MAAI;MAChD,IAAI,IAAI,CAAClK,YAAY,CAACyD,SAAS,CAAC+R,OAAO,IAAIA,OAAO,CAAC7R,SAAS,KAAKa,KAAK,CAAC5C,IAAI,CAACgC,EAAE,IAAI4R,OAAO,CAAC3R,KAAK,KAAKW,KAAK,CAACV,MAAM,CAACC,SAAS,EAAE,CAACF,KAAK,CAAC,GAAG,CAAC,CAAC,EACpI,IAAI,CAAC7D,YAAY,GAAG,IAAI,CAACA,YAAY,CAACqD,MAAM,CAACmS,OAAO,IAAIA,OAAO,CAAC7R,SAAS,KAAKa,KAAK,CAAC5C,IAAI,CAACgC,EAAE,IAAI4R,OAAO,CAAC3R,KAAK,KAAKW,KAAK,CAACV,MAAM,CAACC,SAAS,EAAE,CAACF,KAAK,CAAC;MACpJ,IAAI,CAAC+H,OAAO,CAACC,YAAY,CAAC;QAAE4J,QAAQ,EAAE,CAACjR,KAAK,CAACH,IAAI,CAAC;QAAEyH,OAAO,EAAE,CAACtH,KAAK,CAACV,MAAM;MAAC,CAAE,CAAC;KACjF,MAAM;MACH;MACA,IAAI4R,gBAAgB,GAAa,EAAE;MACnC,IAAI,CAACtI,aAAa,CAACE,UAAU,EAAE,CAACjD,OAAO,CAAC2E,GAAG,IAAG;QAC1C,IAAI2G,KAAK,GAAG3G,GAAG,CAACjL,SAAS,EAAE,CAACwD,gBAAgB,EAAEqO,UAA+B;QAC7E,IAAID,KAAK,EAAEzL,MAAM,EAAE;UACfyL,KAAK,GAAGA,KAAK,CAACtS,MAAM,CAACK,CAAC,IAAI,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC+K,QAAQ,CAAC/K,CAAC,CAAC0K,IAAI,CAAC,IAAI1K,CAAC,CAACmS,OAAO,IAAIrR,KAAK,CAACa,MAAM,CAACI,KAAK,CAAC;UACpG,IAAIkQ,KAAK,CAACzL,MAAM,EACZwL,gBAAgB,CAACvM,IAAI,CAAC6F,GAAG,CAAC9K,QAAQ,EAAE,CAAC;;MAEjD,CAAC,CAAC;MAEF,IAAGwR,gBAAgB,CAACxL,MAAM,EACtB,IAAI,CAAC0B,OAAO,CAACC,YAAY,CAAC;QAAEC,OAAO,EAAE4J,gBAAgB;QAAED,QAAQ,EAAE,CAACjR,KAAK,CAACH,IAAI,CAAC;QAAE0H,KAAK,EAAE,IAAI;QAAE+J,aAAa,EAAE;MAAI,CAAE,CAAC;;IAE1H,IAAI,CAAC/W,WAAW,CAAC0V,IAAI,CAACjQ,KAAK,CAACyC,QAAQ,CAAC,CAAC,CAAc;EACxD;;EAGA8O,eAAeA,CAACvR,KAAyB;IACrC,MAAMwK,GAAG,GAAGxK,KAAK,CAACsH,OAAO,CAAC,CAAC,CAAC,CAAC/H,SAAS,EAAE,CAAC2B,UAAU;IACnD,IAAI,CAACzF,aAAa,CAACwU,IAAI,CAACjQ,KAAK,CAACsH,OAAO,CAAC,CAAC,CAAC,CAACkK,cAAc,EAAE,GAAGhH,GAAG,GAAG,GAAG,GAAGA,GAAG,CAAC;EAChF;EAGAiH,aAAaA,CAACzR,KAAuB;IACjC,IAAI0R,UAAU,GAAG,CAAC;IAClB,IAAI,CAAC9I,aAAa,CAACE,UAAU,EAAE,CAC1BjD,OAAO,CAAC3G,CAAC,IAAG;MAAG,IAAIA,CAAC,CAACyS,OAAO,EAAE,EAAED,UAAU,EAAE;IAAC,CAAC,CAAC;IACpD,IAAI,CAAChW,WAAW,CAACuU,IAAI,CAACyB,UAAU,CAAC;EACrC;EAEAE,uBAAuBA,CAAC5R,KAAiC;IACrD,MAAMiB,KAAK,GAAGjB,KAAK,CAACV,MAAM,EAAEI,QAAQ,EAAE;IACtC,IAAGuB,KAAK,EACJjB,KAAK,CAACkN,SAAS,CAAC2E,gBAAgB,CAAC;MAAEC,KAAK,EAAE,CAAC;QAAE7Q,KAAK,EAAEA,KAAK;QAAE6L,IAAI,EAAE;MAAK,CAAE;IAAC,CAAE,CAAC;IAChF,IAAI,CAAC,IAAI,CAAClE,aAAa,CAACmJ,kBAAkB,EAAE,CAACrM,MAAM,EAC/C,IAAI,CAAC0B,OAAO,CAAC4K,aAAa,EAAE;EACpC;EACA;EAEA;EACAC,YAAYA,CAACzH,GAAkB;IAC3B,IAAI;MACA;MACA,IAAI0H,aAAqB;MACzB,IAAIrC,aAAa,GAAa,EAAE;MAChC,KAAK,IAAI1H,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC/F,UAAU,CAACsD,MAAM,EAAEyC,CAAC,EAAE,EAAE;QAC7C,IAAI,IAAI,CAAC/F,UAAU,CAAC+F,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,IAAI,CAAC/F,UAAU,CAAC+F,CAAC,CAAC,CAAC,SAAS,CAAC,KAAK,GAAGqC,GAAG,CAACZ,IAAI,EAAE,EAAE;UAClF,MAAM+E,KAAK,GAAG,IAAI,CAACvM,UAAU,CAAC+F,CAAC,CAAgB;UAC/C,IAAIqC,GAAG,CAACZ,IAAI,KAAKzS,aAAa,CAACwV,YAAY,IAAInC,GAAG,CAACZ,IAAI,KAAKzS,aAAa,CAACkT,cAAc,EAAE;YACtF6H,aAAa,GAAGvD,KAAK,CAACzE,QAAQ,CAACQ,IAAI,CAACxL,CAAC,IAAIA,CAAC,CAAC,OAAO,CAAC,KAAKsL,GAAG,CAACpL,EAAE,CAAC;YAC/D,IAAI8S,aAAa,EACb;WACP,MAAM;YACHrC,aAAa,GAAGlB,KAAK,CAACzE,QAAoB;YAC1C;;;;MAKZ;MACA,IAAIM,GAAG,CAACpL,EAAE,GAAG,CAAC,EAAE;QACZ;QACA,MAAM+S,MAAM,GAAGtC,aAAa,CAAC5Q,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC+B,KAAK,IAAI,GAAGuJ,GAAG,CAACpL,EAAE,GAAG,CAAC,CAAC,EAAE,CAAC;QACxEyQ,aAAa,CAACuC,MAAM,CAACD,MAAM,EAAE,CAAC,CAAC;QAC/B,IAAI,CAAC/K,OAAO,CAACuC,aAAa,CAAC,IAAI,CAACvH,UAAU,CAAC;QAE3C;QACA,IAAI,CAAChI,cAAc,GAAG,IAAI,CAACA,cAAc,CAACyE,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAKoL,GAAG,CAACpL,EAAE,GAAG,CAAC,CAAC,CAAC;QAC3E,IAAI,CAAC/E,cAAc,GAAG,IAAI,CAACA,cAAc,CAACwE,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAKoL,GAAG,CAACpL,EAAE,GAAG,CAAC,CAAC,CAAC;QAE3E;QACA,IAAI,CAAC9E,OAAO,CAAC2V,IAAI,CAAC,CAACzF,GAAG,CAAC,CAAC,CAAC,CAAG;OAC/B,MAAM;QACH;QACA,IAAI6H,MAAM,GAAG,IAAI,CAACjY,cAAc,CAAC6E,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAKoL,GAAG,CAACpL,EAAE,CAAC;QAChE,IAAIiT,MAAM,GAAG,CAAC,CAAC,EAAE;UAAM;UACnB;UACA,IAAIH,aAAa,EAAE;YAAS;YACxBA,aAAa,CAAChR,UAAU,GAAGsJ,GAAG,CAACK,WAAW,IAAIL,GAAG,CAAC1G,IAAI;YACtDoO,aAAa,CAAC/Q,aAAa,GAAGqJ,GAAG,CAACM,WAAW;YAC7CoH,aAAa,CAACxP,qBAAqB,CAACiB,UAAU,GAAG6G,GAAG,CAAC7G,UAAU;YAC/DuO,aAAa,CAACxP,qBAAqB,CAACC,OAAO,GAAG6H,GAAG,CAACQ,SAAS;YAC3DkH,aAAa,CAACxP,qBAAqB,CAACG,YAAY,GAAG2H,GAAG,CAACM,WAAW,EAAEpF,MAAM,GAAG,CAAC,IAAI,KAAK;WAC1F,MAAM;YACH,MAAM4M,OAAO,GAAGzC,aAAa,CAAC5Q,SAAS,CAACsT,EAAE,IAAIA,EAAE,CAACtR,KAAK,IAAI,GAAGuJ,GAAG,CAACpL,EAAE,EAAE,CAAC;YACtE,IAAIkT,OAAO,GAAG,CAAC,CAAC,EAAE;cACdzC,aAAa,CAACyC,OAAO,CAAC,CAACpR,UAAU,GAAGsJ,GAAG,CAACK,WAAW,IAAIL,GAAG,CAAC1G,IAAI;cAC/D+L,aAAa,CAACyC,OAAO,CAAC,CAACnR,aAAa,GAAGqJ,GAAG,CAACM,WAAW;cACtD+E,aAAa,CAACyC,OAAO,CAAC,CAAC5P,qBAAqB,CAACiB,UAAU,GAAG6G,GAAG,CAAC7G,UAAU;cACxEkM,aAAa,CAACyC,OAAO,CAAC,CAAC5P,qBAAqB,CAACC,OAAO,GAAG6H,GAAG,CAACQ,SAAS;cACpE6E,aAAa,CAACyC,OAAO,CAAC,CAAC5P,qBAAqB,CAACG,YAAY,GAAG2H,GAAG,CAACM,WAAW,EAAEpF,MAAM,GAAG,CAAC,IAAI,KAAK;cAChGmK,aAAa,CAACyC,OAAO,CAAC,CAACxP,UAAU,GAAG,IAAI,CAAC1J,WAAW,CAACsS,aAAa,CAAClB,GAAG,CAACQ,SAAS,CAAC;cACjF6E,aAAa,CAACyC,OAAO,CAAC,CAACvP,gBAAgB,GAAG;gBACtC,GAAG,IAAI,CAAC3J,WAAW,CAACuS,mBAAmB,CAACnB,GAAG,CAAC;gBAC5C3R,SAAS,EAAEA,CAACgI,MAAc,EAAEhB,IAAc,EAAEmD,OAAgB,EAAEC,MAAgB,EAAEC,QAAkB,KAAI;kBAClG,IAAI,CAACvC,qBAAqB,CAACC,IAAI,CAACC,MAAM,EAAEhB,IAAI,EAAEmD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,CAAC;gBAC5E,CAAC;gBAAEC,UAAU,EAAEA,CAAClC,KAAa,EAAEpB,IAAc,KAAI;kBAC7C,IAAI,IAAI,CAACnF,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAAC3B,YAAY,CAAC;oBAAEwG,UAAU,EAAEK,IAAI,CAACzC,IAAI,CAACgC,EAAE;oBAAEgE,kBAAkB,EAAEvD,IAAI,CAACzC,IAAI,CAACiG,UAAU;oBAAEC,QAAQ,EAAErC;kBAAK,CAAE,CAAC,CAAC,KAE3G,IAAI,CAACjI,YAAY,CAAC;oBAAEuK,QAAQ,EAAE1D,IAAI,CAACzC,IAAI,CAACgC,EAAE;oBAAEkE,QAAQ,EAAErC;kBAAK,CAAE,CAAC;gBACtE;eACH;cACD4O,aAAa,CAACyC,OAAO,CAAC,CAACjU,QAAQ,GAAIlB,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAACoK,eAAe,CAACrG,MAAM,EAAE,IAAI,CAACiB,cAAc,CAACC,QAAQ,CAAC;cACpHwR,aAAa,CAACyC,OAAO,CAAC,CAAC/P,YAAY,GAAG,IAAI,CAACnJ,WAAW,CAACwS,eAAe,CAACpB,GAAG,CAACQ,SAAS,CAAC;cACrF6E,aAAa,CAACyC,OAAO,CAAC,CAAC1Q,cAAc,GAAG,IAAI,CAACxI,WAAW,CAAC0S,iBAAiB,CAACtB,GAAG,CAACQ,SAAS,CAAC;cACzF6E,aAAa,CAACyC,OAAO,CAAC,CAACvG,WAAW,GAAG,IAAI,CAAC3S,WAAW,CAAC0S,iBAAiB,CAACtB,GAAG,CAACQ,SAAS,CAAC;cACtF6E,aAAa,CAACyC,OAAO,CAAC,CAACtG,OAAO,GAAG,IAAI,CAAC5S,WAAW,CAAC6S,UAAU,CAACzB,GAAG,CAACQ,SAAS,CAAC;cAC3E6E,aAAa,CAACyC,OAAO,CAAC,CAAC7Q,KAAK,GAAGrK,WAAW,CAACoT,GAAG,CAAC1G,IAAI,IAAI0G,GAAG,CAAC7G,UAAU,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAACnK,OAAO,EAAEgR,GAAG,CAACM,WAAW,EAAEpF,MAAM,GAAG,CAAC,CAAC,CAAC;cAE/H;cACA,IAAI8E,GAAG,CAACQ,SAAS,KAAK9T,WAAW,CAAC0L,UAAU,EACxCiN,aAAa,CAACyC,OAAO,CAAC,CAACvS,aAAa,GAAIC,KAAK,IAAK,IAAI,CAACD,aAAa,CAACC,KAAK,CAAC;;;UAIvF;UACA,IAAI,CAAC5F,cAAc,CAACiY,MAAM,CAAC,CAACvO,IAAI,GAAG0G,GAAG,CAAC1G,IAAI;UAC3C,IAAI,CAAC1J,cAAc,CAACiY,MAAM,CAAC,CAACxH,WAAW,GAAGL,GAAG,CAACK,WAAW;UACzD,IAAI,CAACzQ,cAAc,CAACiY,MAAM,CAAC,CAACvH,WAAW,GAAGN,GAAG,CAACM,WAAW;UACzD,IAAI,CAAC1Q,cAAc,CAACiY,MAAM,CAAC,CAACrH,SAAS,GAAGR,GAAG,CAACQ,SAAS;UACrD,IAAI,CAAC5Q,cAAc,CAACiY,MAAM,CAAC,CAACG,eAAe,GAAGhI,GAAG,CAACgI,eAAe;UACjE,IAAI,CAACpY,cAAc,CAACiY,MAAM,CAAC,CAAC1O,UAAU,GAAG6G,GAAG,CAAC7G,UAAU;UACvD,IAAI,CAACvJ,cAAc,CAACiY,MAAM,CAAC,CAAClH,iBAAiB,GAAGX,GAAG,CAACW,iBAAiB;UAErE;UACAkH,MAAM,GAAG,IAAI,CAAChY,cAAc,CAAC4E,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAKoL,GAAG,CAACpL,EAAE,CAAC;UAC5D,IAAIiT,MAAM,GAAG,CAAC,CAAC,EAAE;YACb;YACA,IAAI,CAAChY,cAAc,CAACgY,MAAM,CAAC,CAACvO,IAAI,GAAG0G,GAAG,CAAC1G,IAAI;YAC3C,IAAI,CAACzJ,cAAc,CAACgY,MAAM,CAAC,CAACxH,WAAW,GAAGL,GAAG,CAACK,WAAW;YACzD,IAAI,CAACxQ,cAAc,CAACgY,MAAM,CAAC,CAACrH,SAAS,GAAGR,GAAG,CAACQ,SAAS;YACrD,IAAI,CAAC3Q,cAAc,CAACgY,MAAM,CAAC,CAACG,eAAe,GAAGhI,GAAG,CAACgI,eAAe;;UAGrE;UACA,IAAI,CAACpL,OAAO,CAACuC,aAAa,CAAC,IAAI,CAACvH,UAAU,CAAC;UAC3C,IAAI,CAACgF,OAAO,CAAC4K,aAAa,EAAE;UAC5B,IAAI,CAACpJ,aAAa,CAAC6J,cAAc,CAAC,GAAGjI,GAAG,CAACpL,EAAE,EAAE,CAAC;UAE9C;UACA,IAAI,CAAC9E,OAAO,CAAC2V,IAAI,CAAC,CAACzF,GAAG,CAAC,CAAC;SAC3B,MAAM;UAAiB;UACpB;UACA,IAAIA,GAAG,CAACgB,KAAK,GAAG,CAAC,EAAEhB,GAAG,CAACgB,KAAK,IAAI,CAAC,CAAC;UAClC,IAAI,CAACpR,cAAc,CAACyL,OAAO,CAAC3G,CAAC,IAAG;YAC5B,IAAIA,CAAC,CAAC0K,IAAI,KAAKY,GAAG,CAACZ,IAAI,IAAI1K,CAAC,CAACsM,KAAK,IAAIhB,GAAG,CAACgB,KAAK,EAC3CtM,CAAC,CAACsM,KAAK,GAAGtM,CAAC,CAACsM,KAAK,EAAE;UAC3B,CAAC,CAAC;UAEF;UACA,IAAI,CAACpR,cAAc,CAACuK,IAAI,CAAC6F,GAAG,CAAC;UAC7B,IAAI,CAACnQ,cAAc,CAACsK,IAAI,CAAC6F,GAAG,CAAC;UAE7B;UACA,IAAIkI,YAAY,GAAG,EAAE;UACrB,IAAIlI,GAAG,CAACZ,IAAI,KAAKzS,aAAa,CAAC0V,MAAM,IAAIrC,GAAG,CAACZ,IAAI,KAAKzS,aAAa,CAACyN,UAAU,EAC1E8N,YAAY,GAAG,CAACvb,aAAa,CAACyN,UAAU,EAAEzN,aAAa,CAAC0V,MAAM,CAAC,CAAC,KAEhE6F,YAAY,GAAG,CAACvb,aAAa,CAAC2N,YAAY,EAAE3N,aAAa,CAAC4Q,QAAQ,CAAC;UAEvE,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG0H,aAAa,CAACnK,MAAM,EAAEyC,CAAC,EAAE,EAAE;YAC3C,IAAI0H,aAAa,CAAC1H,CAAC,CAAC,CAACzF,qBAAqB,EAAE6I,QAAQ,IAAIf,GAAG,CAACgB,KAAK,EAC7DqE,aAAa,CAAC1H,CAAC,CAAC,CAACzF,qBAAqB,CAAC6I,QAAQ,EAAE;;UAEzD;UACA,IAAI1K,MAAM,GAAW;YACjBI,KAAK,EAAE,GAAGuJ,GAAG,CAACpL,EAAE,EAAE;YAClBC,KAAK,EAAE,cAAc,GAAGmL,GAAG,CAACpL,EAAE;YAC9B8B,UAAU,EAAEsJ,GAAG,CAACK,WAAW,IAAIL,GAAG,CAAC1G,IAAI;YACvC3C,aAAa,EAAEqJ,GAAG,CAACM,WAAW;YAC9BzJ,eAAe,EAAErJ,gBAAgB;YACjC0K,qBAAqB,EAAE;cACnBqI,UAAU,EAAE,IAAI;cAChBpH,UAAU,EAAE6G,GAAG,CAAC7G,UAAU;cAC1BiG,IAAI,EAAEY,GAAG,CAACZ,IAAI;cACdjH,OAAO,EAAE6H,GAAG,CAACQ,SAAS;cACtBO,QAAQ,EAAEf,GAAG,CAACgB,KAAK;cACnB3I,YAAY,EAAE2H,GAAG,CAACM,WAAW,EAAEpF,MAAM,GAAG,CAAC;cACzCuF,YAAY,EAAG/M,CAAC,IAAI;gBAChB;gBACA,IAAI,CAACgN,iBAAiB,CAAC5L,MAAM,GAAG6K,MAAM,CAACC,MAAM,CAAC,EAAE,EAAEI,GAAG,CAAC;gBACtD;gBACA,IAAI,CAACU,iBAAiB,CAAC5D,OAAO,GAAG,IAAI,CAACjN,cAAc,CAACwE,MAAM,CAACK,CAAC,IAAIwT,YAAY,CAACzI,QAAQ,CAAC/K,CAAC,CAAC0K,IAAI,CAAC,CAAC;gBAC/F,IAAI,CAACsB,iBAAiB,CAACtK,IAAI,CAAC1C,CAAC,EAAE,GAAGsM,GAAG,CAACpL,EAAE,EAAE,CAAC;cAC/C;aACH;YACD0D,UAAU,EAAE,IAAI,CAAC1J,WAAW,CAACsS,aAAa,CAAClB,GAAG,CAACQ,SAAS,CAAC;YACzDjI,gBAAgB,EAAE;cACd,GAAG,IAAI,CAAC3J,WAAW,CAACuS,mBAAmB,CAACnB,GAAG,CAAC;cAC5C3R,SAAS,EAAEA,CAACgI,MAAc,EAAEhB,IAAc,EAAEmD,OAAgB,EAAEC,MAAgB,EAAEC,QAAkB,KAAI;gBAClG,IAAI,CAACvC,qBAAqB,CAACC,IAAI,CAACC,MAAM,EAAEhB,IAAI,EAAEmD,OAAO,EAAEC,MAAM,EAAEC,QAAQ,CAAC;cAC5E,CAAC;cAAEC,UAAU,EAAEA,CAAClC,KAAa,EAAEpB,IAAc,KAAI;gBAC7C,IAAI,IAAI,CAACnF,YAAY,CAACC,QAAQ,KAAK,UAAU,EACzC,IAAI,CAAC3B,YAAY,CAAC;kBAAEwG,UAAU,EAAEK,IAAI,CAACzC,IAAI,CAACgC,EAAE;kBAAEgE,kBAAkB,EAAEvD,IAAI,CAACzC,IAAI,CAACiG,UAAU;kBAAEC,QAAQ,EAAErC;gBAAK,CAAE,CAAC,CAAC,KAE3G,IAAI,CAACjI,YAAY,CAAC;kBAAEuK,QAAQ,EAAE1D,IAAI,CAACzC,IAAI,CAACgC,EAAE;kBAAEkE,QAAQ,EAAErC;gBAAK,CAAE,CAAC;cACtE;aACH;YACD5C,QAAQ,EAAGlB,MAAM,IAAK,IAAI,CAAC/D,WAAW,CAACoK,eAAe,CAACrG,MAAM,EAAE,IAAI,CAACiB,cAAc,CAACC,QAAQ,CAAC;YAC5F2F,cAAc,EAAE,IAAI,CAAC5K,WAAW,CAAC6K,iBAAiB;YAClD1B,YAAY,EAAE,IAAI,CAACnJ,WAAW,CAACwS,eAAe,CAACpB,GAAG,CAACQ,SAAS,CAAC;YAC7DhM,cAAc,EAAE;cAAE,UAAU,EAAE,IAAI,CAAC5F,WAAW,CAACyS,gBAAgB;cAAE,aAAa,EAAE,IAAI,CAACzS,WAAW,CAAC0O;YAAiB,CAAE;YACpHrG,KAAK,EAAErK,WAAW,CAACoT,GAAG,CAAC1G,IAAI,IAAI0G,GAAG,CAAC7G,UAAU,GAAG,GAAG,GAAG,EAAE,CAAC,EAAE,CAAC,IAAI,CAACnK,OAAO,EAAEgR,GAAG,CAACM,WAAW,EAAEpF,MAAM,GAAG,CAAC,CAAC,CAAC;YACvGqG,WAAW,EAAE,IAAI,CAAC3S,WAAW,CAAC0S,iBAAiB,CAACtB,GAAG,CAACQ,SAAS,CAAC;YAAEpJ,cAAc,EAAE,IAAI,CAACxI,WAAW,CAAC0S,iBAAiB,CAACtB,GAAG,CAACQ,SAAS,CAAC;YACjIgB,OAAO,EAAE,IAAI,CAAC5S,WAAW,CAAC6S,UAAU,CAACzB,GAAG,CAACQ,SAAS,CAAC;YACnDzJ,UAAU,EAAE,IAAI;YAAEU,WAAW,EAAE,IAAI;YAAET,eAAe,EAAE,KAAK;YAC3DiC,eAAe,EAAEoM,aAAa,CAACnK,MAAM,GAAG,CAAC,GAAG,MAAM,GAAG;WACxD;UAED,IAAI8E,GAAG,CAACZ,IAAI,KAAKzS,aAAa,CAAC2N,YAAY,IAAI0F,GAAG,CAACZ,IAAI,KAAKzS,aAAa,CAACyN,UAAU,EAChF/D,MAAM,CAACO,WAAW,GAAG,mBAAmB,CAAC,KAEzCP,MAAM,CAACO,WAAW,GAAG,mBAAmB;UAE5C;UACA,IAAIoJ,GAAG,CAACQ,SAAS,KAAK9T,WAAW,CAAC0L,UAAU,EACxC/B,MAAM,CAACd,aAAa,GAAIC,KAAK,IAAK,IAAI,CAACD,aAAa,CAACC,KAAK,CAAC;UAC/D6P,aAAa,CAAClL,IAAI,CAAC9D,MAAM,CAAC;UAE1B;UACAgP,aAAa,CAACrD,IAAI,CAAC,CAAC/L,CAAC,EAAEF,CAAC,KAAI;YACxB,IAAIA,CAAC,CAACU,KAAK,CAACtB,UAAU,CAAC,KAAK,CAAC,EACzB,OAAO,CAAC,CAAC;YACb,IAAI,CAACc,CAAC,CAACiC,qBAAqB,EAAE6I,QAAQ,IAAI,CAAChL,CAAC,CAACmC,qBAAqB,EAAE6I,QAAQ,EACxE,OAAO,CAAC;YACZ,IAAI9K,CAAC,CAACiC,qBAAqB,CAAC6I,QAAQ,GAAGhL,CAAC,CAACmC,qBAAqB,CAAC6I,QAAQ,EACnE,OAAO,CAAC;YACZ,OAAO,CAAC,CAAC;UACb,CAAC,CAAC;UAEF;UACA,IAAI,CAACnE,OAAO,CAACuC,aAAa,CAAC,IAAI,CAACvH,UAAU,CAAC;UAC3C,IAAI,CAACwG,aAAa,CAAC6J,cAAc,CAACE,MAAM,CAACnI,GAAG,CAACpL,EAAE,CAAC,CAAC,CAAC;UAElD;UACA;UACA,IAAIyJ,IAAI,GAAG,IAAI,CAACzO,cAAc,CAACyE,MAAM,CAACK,CAAC,IAAIA,CAAC,CAAC0K,IAAI,KAAKY,GAAG,CAACZ,IAAI,CAAC;UAC/Df,IAAI,CAAC2D,IAAI,CAAC,CAAC/L,CAAC,EAAEF,CAAC,KAAME,CAAC,CAAC+K,KAAK,GAAGjL,CAAC,CAACiL,KAAK,GAAI,CAAC,GAAG,CAAC,CAAC,CAAC;UAEjD,IAAI,CAAClR,OAAO,CAAC2V,IAAI,CAACpH,IAAI,CAAC;;;KAGlC,CAAC,OAAOP,EAAE,EAAE;MAAEC,OAAO,CAACC,KAAK,CAACF,EAAE,CAAC;MAAE,IAAI,CAACjP,cAAc,CAACmP,KAAK,CAAC,uBAAuB,CAAC;;EACxF;EAEA;EACAoK,eAAeA,CAACxV,IAAqC,EAAEyV,aAAsB;IACzE,IAAI,CAACzU,cAAc,CAACC,QAAQ,GAAGwU,aAAa;IAC5C,IAAI,CAACzL,OAAO,CAAC0L,gBAAgB,CAAC,IAAI,CAAC1U,cAAc,CAAC;IAElD;IACA;IAEA,IAAI,CAAC5C,YAAY,GAAG,EAAE;IAEtB,IAAI,CAAChB,OAAO,GAAG,CAAC,GAAG4C,IAAI,CAAC;IACxB,KAAK,IAAI+K,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,EAAE,EAAEA,CAAC,EAAE;IAAQ;IAChC,IAAI,CAAC3N,OAAO,CAACmK,IAAI,CAAC;MAAEvF,EAAE,EAAE,CAAC;MAAEgJ,OAAO,EAAE,EAAE;MAAEC,KAAK,EAAE;IAAK,CAAmB,CAAC;IAE5E;IACA,IAAI,IAAI,CAAC3N,YAAY,CAACC,QAAQ,KAAK,UAAU,EAAE;MAC3C,MAAMoY,OAAO,GAAG,IAAI,CAACnK,aAAa,CAACE,UAAU,EAAE,CAAC4B,IAAI,CAACxL,CAAC,IAAIA,CAAC,CAACK,SAAS,EAAE,CAACF,KAAK,KAAK,WAAW,CAAC;MAC9F,IAAIjC,IAAI,CAACsI,MAAM,EAAE;QACb,IAAIsN,WAAW,GAAG5V,IAAI,CAAC8S,GAAG,CAACzH,CAAC,IAAIA,CAAC,CAACoI,QAAQ,CAAC,CAAC/K,IAAI,CAAC,GAAG,CAAC,CAACwD,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC;QACxE,MAAM2J,OAAO,GAAG,CAAC,GAAG,IAAIC,GAAG,CAACF,WAAW,CAACvN,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;QACpDuN,WAAW,GAAGC,OAAO,CAACnN,IAAI,CAAC,GAAG,CAAC;QAE/B,IAAI,IAAI,CAAC1M,WAAW,CAAC+Z,WAAW,CAACzN,MAAM,EAAE;UACrCsN,WAAW,GAAG,EAAE;UAChB,KAAK,IAAI7K,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG8K,OAAO,CAACvN,MAAM,EAAEyC,CAAC,EAAE,EAAE;YACrC,IAAI,IAAI,CAAC/O,WAAW,CAAC+Z,WAAW,CAAClU,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACkU,UAAU,KAAK,CAACH,OAAO,CAAC9K,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAChF6K,WAAW,IAAIC,OAAO,CAAC9K,CAAC,CAAC,GAAG,GAAG;;UAEvC6K,WAAW,GAAGA,WAAW,CAACtN,MAAM,GAAGsN,WAAW,CAACK,SAAS,CAAC,CAAC,EAAEL,WAAW,CAACtN,MAAM,GAAG,CAAC,CAAC,GAAG,EAAE;;QAG5F,IAAIsN,WAAW,CAACtN,MAAM,EAAE;UACpB,IAAI,CAACtM,WAAW,CAACka,cAAc,CAACN,WAAW,CAAC,CAAC/L,IAAI,CAAEsM,GAAG,IAAI;YACtD,IAAIA,GAAG,EAAE;cAAO;cACZR,OAAO,CAACxT,SAAS,EAAE,CAACwD,gBAAgB,CAACc,MAAM,GAAG,CAAC,GAAG,IAAI,CAACzK,WAAW,CAAC+Z,WAAW,CAAC;cAC/E,IAAI,CAAC/L,OAAO,CAACC,YAAY,CAAC;gBAAEC,OAAO,EAAE,CAACyL,OAAO,CAACrT,QAAQ,EAAE,CAAC;gBAAE6H,KAAK,EAAE,IAAI;gBAAE+J,aAAa,EAAE;cAAI,CAAE,CAAC;;UAEtG,CAAC,CAAC;UACF;;;MAGR,IAAIyB,OAAO,EAAE;QAAE;QACXA,OAAO,CAACxT,SAAS,EAAE,CAACwD,gBAAgB,CAACc,MAAM,GAAG,CAAC,GAAG,IAAI,CAACzK,WAAW,CAAC+Z,WAAW,CAAC;QAC/E,IAAI,CAAC/L,OAAO,CAACC,YAAY,CAAC;UAAEC,OAAO,EAAE,CAACyL,OAAO,CAACrT,QAAQ,EAAE,CAAC;UAAE6H,KAAK,EAAE,IAAI;UAAE+J,aAAa,EAAE;QAAI,CAAE,CAAC;;;EAG1G;EAEQ7C,oBAAoBA,CAAA;IACxB,IAAI+E,KAAK,GAAG,SAAS,IAAI,CAAC9Y,YAAY,CAACI,GAAG,IAAI,IAAI,CAACJ,YAAY,CAACM,IAAI,GAAG;IACvEwY,KAAK,IAAI,IAAI,CAAC9Y,YAAY,CAACQ,IAAI,CAACoO,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;IAC9DkK,KAAK,IAAI,IAAI,CAAC9Y,YAAY,CAACC,QAAQ,KAAK,UAAU,GAAG,gBAAgB,GAAG,cAAc;IACtF,IAAIkO,IAAI,GAAG,IAAI,CAACD,aAAa,CAACE,UAAU,EAAE,CAACoH,GAAG,CAAChR,CAAC,IAAIA,CAAC,CAACQ,QAAQ,EAAE,CAAC;IACjEmJ,IAAI,GAAGA,IAAI,CAAChK,MAAM,CAACK,CAAC,IAAI,CAACA,CAAC,CAACS,UAAU,CAAC,KAAK,CAAC,IAAI,CAACT,CAAC,CAACS,UAAU,CAAC,aAAa,CAAC,CAAC;IAE7E;IACA,IAAI,CAAC3D,WAAW,CAACyX,wBAAwB,GAAG;MACxCC,MAAM,EAAE,UAAU;MAAEC,QAAQ,EAAEH,KAAK;MAAEI,UAAU,EAAE,IAAI;MACrDC,SAAS,EAAE,IAAI,CAACnZ,YAAY,CAACM,IAAI,CAACsO,OAAO,CAAC,iBAAiB,EAAE,EAAE,CAAC;MAChEwK,UAAU,EAAEjL;KACf;EACL;EAEAkL,UAAUA,CAAC7V,CAAM;IACb,MAAM1D,OAAO,GAAG,IAAI,CAACA,OAAO,CAACkQ,IAAI,CAACsJ,CAAC,IAAIA,CAAC,CAAC5U,EAAE,KAAKlB,CAAC,CAACkB,EAAE,CAAC;IACrD,IAAIlB,CAAC,CAAC+C,KAAK,GAAG,CAAC,EACXzG,OAAO,CAAC4N,OAAO,CAAC,MAAM,GAAGlK,CAAC,CAAC+C,KAAK,CAAC,IAAI/C,CAAC,CAAC+V,OAAO,CAAC,KAE/CzZ,OAAO,CAAC,MAAM,CAAC,IAAI0D,CAAC,CAAC+V,OAAO;IAChC,IAAI,CAAC7M,OAAO,CAACC,YAAY,EAAE;EAC/B;EAEAxO,SAASA,CAACqF,CAAM;IACZ,MAAM2B,IAAI,GAAG,IAAI,CAACuH,OAAO,CAAC8M,UAAU,CAAChW,CAAC,CAAC2B,IAAI,CAACT,EAAE,CAAC;IAC/C,IAAI,CAACS,IAAI,CAACzC,IAAI,CAAC+W,QAAQ,EACnBtU,IAAI,CAACzC,IAAI,CAAC+W,QAAQ,GAAG,EAAE;IAE3B,IAAIjW,CAAC,CAACkW,OAAO,EAAE;MACX,MAAMC,OAAO,GAAGxU,IAAI,CAACzC,IAAI,CAAC+W,QAAQ,CAACzJ,IAAI,CAACxL,CAAC,IAAIA,CAAC,CAACoV,SAAS,KAAKpW,CAAC,CAACoW,SAAS,CAAC;MACzE,IAAGD,OAAO,EAAEA,OAAO,CAACE,UAAU,GAAG,CAACF,OAAO,CAACE,UAAU;KACvD,MAAM,IAAIrW,CAAC,CAACsW,OAAO,EAChB3U,IAAI,CAACzC,IAAI,CAAC+W,QAAQ,GAAGtU,IAAI,CAACzC,IAAI,CAAC+W,QAAQ,CAACtV,MAAM,CAACK,CAAC,IAAIA,CAAC,CAACoV,SAAS,KAAKpW,CAAC,CAACoW,SAAS,CAAC,CAAC,KAChF;MACDzU,IAAI,CAACzC,IAAI,CAAC+W,QAAQ,GAAG,CAAC,GAAGtU,IAAI,CAACzC,IAAI,CAAC+W,QAAQ,EAAE;QACzC/U,EAAE,EAAElB,CAAC,CAACuW,UAAU,GAAG5U,IAAI,CAACzC,IAAI,CAACiG,UAAU,GAAGxD,IAAI,CAACzC,IAAI,CAACgC,EAAE;QACtDkV,SAAS,EAAEpW,CAAC,CAACoW,SAAS;QAAErT,KAAK,EAAE/C,CAAC,CAAC+C,KAAK;QAAEgC,MAAM,EAAE/E,CAAC,CAAC+E,MAAM;QAAEsR,UAAU,EAAErW,CAAC,CAACqW;OAC3E,CAAC;;IAEN,IAAI,CAACnN,OAAO,CAACC,YAAY,CAAC;MAAE4J,QAAQ,EAAE,CAACpR,IAAI,CAAC;MAAE0H,KAAK,EAAE,IAAI;MAAE+J,aAAa,EAAE;IAAI,CAAE,CAAC;EACrF;EAEAtY,YAAYA,CAACmE,MAAqB;IAC9B;IACA,IAAIuX,IAAI,GAAG,GAAG,IAAI,CAACha,YAAY,CAACG,KAAK,IAAI,IAAI,CAACH,YAAY,CAACK,MAAM,EAAE;IACnE2Z,IAAI,IAAI,IAAI,IAAI,CAACha,YAAY,CAACO,MAAM,EAAE;IAEtC,IAAI,CAACkC,MAAM,CAACwX,SAAS,EAAE;MACnB,MAAMC,MAAM,GAAG,IAAI,CAACjU,qBAAqB,CAACkU,YAAY,CACjDnK,IAAI,CAACxL,CAAC,IAAIA,CAAC,CAAC+B,KAAK,KAAK9D,MAAM,CAACmG,QAAQ,CAAC;MAC3C,IAAIsR,MAAM,EACNzX,MAAM,CAACmG,QAAQ,GAAGsR,MAAM,CAACE,MAAM,CAAC,KAC/B3X,MAAM,CAACmG,QAAQ,GAAG,CAACnG,MAAM,CAACmG,QAAQ;;IAG3C,IAAInG,MAAM,CAACqC,UAAU,GAAG,CAAC,EAAE;MACvBkV,IAAI,IAAI,IAAIvX,MAAM,CAACqC,UAAU,EAAE;MAC/B,IAAIrC,MAAM,CAACiG,kBAAkB,GAAG,CAAC,EAC7BsR,IAAI,IAAI,IAAI,IAAI,CAACha,YAAY,CAACS,KAAK,IAAI,IAAI,CAACT,YAAY,CAACU,IAAI,EAAE,CAAC,KAC/DsZ,IAAI,IAAI,KAAK;KACrB,MAAMA,IAAI,IAAI,IAAIvX,MAAM,CAACoG,QAAQ,IAAI;IAEtCmR,IAAI,IAAI,IAAIvX,MAAM,CAACmG,QAAQ,EAAE;IAC7BoR,IAAI,GAAG,CAACvX,MAAM,CAACwX,SAAS,GAAG,WAAW,GAAG,QAAQ,IAAID,IAAI;IAEzDK,SAAS,CAACC,SAAS,CAACC,SAAS,CAACC,QAAQ,CAACC,MAAM,GAAGD,QAAQ,CAACE,QAAQ,GAAGV,IAAI,CAAC;IACzE,IAAI,CAACrb,cAAc,CAACqW,IAAI,CAAC,gCAAgC,EAAE,cAAc,CAAC;EAC9E;EAEA2F,WAAWA,CAAA;IACP,IAAI,CAACha,aAAa,CAACwK,OAAO,CAAEyP,EAAE,IAAKA,EAAE,CAACC,WAAW,EAAE,CAAC;EACxD;EAAC,QAAAC,CAAA,G;qBA7oDQtc,aAAa,EAAAhB,EAAA,CAAAud,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAzd,EAAA,CAAAud,iBAAA,CAAAG,EAAA,CAAAC,cAAA;EAAA;EAAA,QAAAC,EAAA,G;UAAb5c,aAAa;IAAA6c,SAAA;IAAAC,SAAA,WAAAC,oBAAAC,EAAA,EAAAC,GAAA;MAAA,IAAAD,EAAA;uBAmCXje,0BAA0B;uBAc1BV,wBAAwB;uBAGxBD,gBAAgB;;;;;;;;;;;;;;;;;;;;QCvF/BY,EAAA,CAAAke,UAAA,IAAAC,qCAAA,0BAA6C;QAE7Cne,EAAA,CAAAE,cAAA,4BAe2E;QAN1DF,EAAA,CAAAG,UAAA,8BAAAie,mEAAA/d,MAAA;UAAA,OAAoB4d,GAAA,CAAApQ,kBAAA,CAAAxN,MAAA,CAA0B;QAAA,EAAC,uBAAAge,4DAAAhe,MAAA;UAAA,OAClC4d,GAAA,CAAAnJ,WAAA,CAAAzU,MAAA,CAAmB;QAAA,EADe,yBAAAie,8DAAAje,MAAA;UAAA,OAEhC4d,GAAA,CAAAlH,aAAA,CAAA1W,MAAA,CAAqB;QAAA,EAFW,yBAAAke,8DAAAle,MAAA;UAAA,OAGhC4d,GAAA,CAAAxF,aAAA,CAAApY,MAAA,CAAqB;QAAA,EAHW,2BAAAme,gEAAAne,MAAA;UAAA,OAI9B4d,GAAA,CAAA5E,eAAA,CAAAhZ,MAAA,CAAuB;QAAA,EAJO,yBAAAoe,8DAAApe,MAAA;UAAA,OAKhC4d,GAAA,CAAA1E,aAAA,CAAAlZ,MAAA,CAAqB;QAAA,EALW,mCAAAqe,wEAAAre,MAAA;UAAA,OAMtB4d,GAAA,CAAAvE,uBAAA,CAAArZ,MAAA,CAA+B;QAAA,EANT;QAOhEL,EAAA,CAAAe,YAAA,EAAkB;QAGlBf,EAAA,CAAAE,cAAA,2BAAiE;QAA9BF,EAAA,CAAAG,UAAA,kBAAAwe,yDAAAte,MAAA;UAAA,OAAQ4d,GAAA,CAAAlE,YAAA,CAAA1Z,MAAA,CAAoB;QAAA,EAAC;QAACL,EAAA,CAAAe,YAAA,EAAoB;QAGrFf,EAAA,CAAAE,cAAA,2BAAoE;QAA5BF,EAAA,CAAAG,UAAA,kBAAAye,yDAAAve,MAAA;UAAA,OAAQ4d,GAAA,CAAApC,UAAA,CAAAxb,MAAA,CAAkB;QAAA,EAAC;QAACL,EAAA,CAAAe,YAAA,EAAoB;QAGxFf,EAAA,CAAAke,UAAA,IAAAW,sCAAA,2BAAmI;;;QA3BpH7e,EAAA,CAAA8e,UAAA,SAAAb,GAAA,CAAA7c,OAAA,CAAa;QAGXpB,EAAA,CAAA+e,SAAA,GAAkF;QAAlF/e,EAAA,CAAA8e,UAAA,YAAA9e,EAAA,CAAAgf,eAAA,KAAAC,GAAA,GAAAhB,GAAA,CAAA5c,UAAA,EAAA4c,GAAA,CAAA5c,UAAA,EAAkF,gBAAA4c,GAAA,CAAAna,WAAA,gBAAAma,GAAA,CAAA/T,UAAA,mBAAA+T,GAAA,CAAA/X,cAAA,gBAAA+X,GAAA,CAAA1c,UAAA,aAAA0c,GAAA,CAAA3b,OAAA,2CAAA2b,GAAA,CAAAzc,gBAAA;QAwBNxB,EAAA,CAAA+e,SAAA,GAAoB;QAApB/e,EAAA,CAAA8e,UAAA,SAAAb,GAAA,CAAA3b,OAAA,CAAAkL,MAAA,CAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}