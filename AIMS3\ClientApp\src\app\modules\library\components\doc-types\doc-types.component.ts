import { ChangeDetector<PERSON><PERSON>, Component, <PERSON>E<PERSON>ter, On<PERSON><PERSON>roy, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { lastValueFrom, Subject, Subscription } from 'rxjs';
import { ColDataType, UploadLocation } from '../../../../shared/enums';
import { MessageService } from '../../../../shared/services/message.service';
import { MenuComponent } from '../../../../_theme/core/components';
import { ModalComponent, ModalConfig } from '../../../../_theme/partials';
import { DocType, DocTypeField } from '../../models/doc-type.model';
import { DocumentTypeService } from '../../services/doc-type.service';

@Component({
    selector: 'doc-types-modal',
    templateUrl: './doc-types.component.html',
    styleUrls: ['./doc-types.component.scss']
})
export class DocumentTypesComponent implements OnInit, OnDestroy {
    working: boolean = false;

    documentTypes: DocType[] = [];
    docType: DocType;
    form: FormGroup;
    
    @ViewChild('modal') private modalComponent: ModalComponent;
    modalConfig: ModalConfig = {
        modalTitle: 'Document upload settings',
        cancelButtonLabel: 'Close',
        disableDoneButton: true,
        hideDoneButton: true,
        options: { size: 'lg' },
        shouldDo: () => this.save(),
        shouldCancel: () => {
            this.done.emit(
                this.documentTypes.filter(dt => dt.uploadLocation <= UploadLocation.Library)
            );

            this.ngOnDestroy();
            return true;
        }
    };
    @Output() done = new EventEmitter<DocType[]>();

    fieldTypes = [];
    field: DocTypeField;
    uploadLocs: string[] = ['Any location', 'Library only', 'Progress & Target only'];

    subscriptions: Subscription[] = [];
    constructor(
        private docTypeService: DocumentTypeService,
        private cdr: ChangeDetectorRef,
        private messageService: MessageService
    ) {
        this.docType = new DocType(0, '', UploadLocation.Any, false);

        Object.values(ColDataType).filter(v => typeof v === 'string' && v !== 'Formula' && v !== 'Attachment').forEach(v => {
            this.fieldTypes.push({ id: ColDataType[v], name: v });
        });
    }

    ngOnInit(open?: boolean) {
        this.initForm();

        if (open) {
            this.modalComponent?.open().then();

            if ($.fn.dataTable.isDataTable('#table_docTypes'))
                $('#table_docTypes').DataTable({ retrieve: true }).destroy();

            this.editMode = false;
            this.getDocTypes();
        }
    }

    private getDocTypes(): void {
        this.working = true;
        this.subscriptions.push(
            this.docTypeService.getAllDocumentTypes().subscribe({
                next: (types) => {
                    types.forEach(fType => {
                        fType.fields?.forEach(field => {
                            const ftype = this.fieldTypes.find(f => f.id == field.fieldType);
                            field.fTypeInfo = [ftype.name[0], ftype.name];
                        });
                    });

                    this.documentTypes = types;
                },
                error: (e) => {
                    console.log(e);
                    this.working = false;
                },
                complete: () => {
                    this.cdr.detectChanges();
                    this.bindDataTable();
                    this.working = false;
                }
            }));
    }

    private bindDataTable(): void {
        $('#table_docTypes').DataTable({
            retrieve: false,
            dom: '<f<t>p>',
            pageLength: 10,
            autoWidth: false,
            rowCallback: (row: Node, data: any, index: number, indexFull?: number) => {
                row.childNodes[0].textContent = `${indexFull + 1}`;
            }
        } as DataTables.Settings).on('draw.dt', () => MenuComponent.reinitialization());
        $('#table_docTypes th').on('click', (e) => this.onSort(e));

        MenuComponent.reinitialization();
    }

    private onSort(e: any): void {
        const target = e.target as HTMLElement;
        if (target.hasAttribute('data-orderable'))
            return;

        if (target.classList.contains('sorting_desc') && !target.classList.contains('noSort')) {
            target.classList.add('noSort');
            return;
        }

        if (target.classList.contains('noSort')) {
            setTimeout(() => {
                $('#table_docTypes').DataTable({
                    retrieve: true
                }).order([1, 'asc']).draw();
                target.classList.remove('noSort');
            }, this.documentTypes.length * 2);
        }
    }

    editMode: boolean = false;
    onAddEdit(typeId?: number): void {
        if (typeId > 0) {
            const type = this.documentTypes.find(dt => dt.id === typeId);
            this.docType = new DocType(type.id, type.typeName, type.uploadLocation, type.isActRequired, type.fields);
        } else
            this.docType = new DocType(0, '', UploadLocation.Any, false, []);

        this.field = new DocTypeField(0, this.docType.id, '', ColDataType.Text, true);
        this.initForm();
        this.editMode = true;
    }

    // convenient getter for easy access to form fields
    get f() {
        return this.form.controls;
    }

    private initForm() {
        this.form = new FormGroup({
            id: new FormControl({ value: this.docType.id, disabled: true }),
            name: new FormControl(this.docType.typeName, Validators.required),
            upLoc: new FormControl(this.docType.uploadLocation, Validators.required),
            isActReq: new FormControl(this.docType.isActRequired, Validators.required) 
        });

        this.form.controls['name'].valueChanges.subscribe((val) => {
            this.exists = false;
        });
    }

    // ** Fields ---------------------------------------------
    fieldsNotFilled = [false];
    onAddField(e?: any): void {
        if ((!e || e.keyCode === 13)) {
            // validate field
            this.field.fieldType = +this.field.fieldType || ColDataType.Text;
            this.field.isRequired = this.field.isRequired || false;

            this.fieldsNotFilled[0] = false;
            if (!this.field.fieldName) {
                this.fieldsNotFilled = [true];
                return;
            }

            if (this.field.fieldType === ColDataType.Text || this.field.fieldType === ColDataType.Date ||
                this.field.fieldType === ColDataType.Checkbox) {
                this.field.fieldTypeValues = null;
            }

            this.docType.fields = [...this.docType.fields, this.field];
            this.field = new DocTypeField(0, this.docType.id, '', ColDataType.Text, this.field.isRequired);
        }
    }

    onDeleteField(ind: number): void {
        this.docType.fields.splice(ind, 1);
    }
    // -------------------------------------------------------

    exists: boolean = false;
    async save(): Promise<boolean> {
        const result = new Subject<boolean>();

        // validate fields
        this.fieldsNotFilled = [false];
        if (this.docType.fields.length) {
            this.docType.fields.forEach(field => {
                field.fieldType = +field.fieldType || ColDataType.Text;

                if (field.fieldType === ColDataType.Text || field.fieldType === ColDataType.Date ||
                    field.fieldType === ColDataType.Checkbox) {
                    field.fieldTypeValues = null;
                }

                if (!field.fieldName)
                    this.fieldsNotFilled.push(true);
                else
                    this.fieldsNotFilled.push(false);
            });
        }

        if (this.fieldsNotFilled.includes(true)) {
            this.messageService.error('Please fill in the required fields.');
            return;
        }

        this.docType = new DocType(this.docType.id, this.f.name.value,
            +this.f.upLoc.value, this.f.isActReq.value, this.docType.fields);
        //console.log(this.docType); return await lastValueFrom(result.asObservable());
        try {
            this.modalConfig.working = true;

            const isEdit = this.docType.id > 0 ? true : false;
            const operation = isEdit
                ? this.docTypeService.updateDocumentType(this.docType)
                : this.docTypeService.addDocumentType(this.docType);

            this.subscriptions.push(operation.subscribe({
                next: (retType: DocType) => {
                    this.docType = retType;
                },
                error: (err) => {
                    this.exists = err.error.srExists;

                    this.modalConfig.working = false;
                    console.log(err);
                    result.next(false);
                    result.complete();
                },
                complete: () => {
                    if ($.fn.dataTable.isDataTable('#table_docTypes'))
                        $('#table_docTypes').DataTable({ retrieve: true }).destroy();

                    this.docType.fields?.forEach(field => {
                        const ftype = this.fieldTypes.find(f => f.id == field.fieldType);
                        field.fTypeInfo = [ftype.name[0], ftype.name];
                    });

                    if (isEdit) {
                        const ind = this.documentTypes.findIndex(dt => dt.id === this.docType.id);
                        this.documentTypes[ind] = this.docType;
                        this.messageService.success('The file type has been updated successfully.');
                    } else {
                        this.documentTypes.push(this.docType);
                        this.messageService.success('The file type has been added successfully.');
                    }

                    this.cdr.detectChanges();
                    this.bindDataTable();

                    this.fieldsNotFilled = [false];
                    this.editMode = false;
                    this.modalConfig.working = false;

                    result.next(true);
                    result.complete();
                }
            }));
        } catch (e) {
            this.working = false;
            this.messageService.error('Something went wrong.');
            console.log(e);
            result.next(false);
            result.complete();
        }

        return await lastValueFrom(result.asObservable());
    }

    onDelete(docTypeId: number, typeName: string): void {
        this.messageService.confirmMessage('Confirm Delete',
            `Are you sure you want to delete this file type: <span class="text-primary">'${typeName}'</span>?`,
            () => {
                this.working = true;
                if ($.fn.dataTable.isDataTable('#table_docTypes'))
                    $('#table_docTypes').DataTable({ retrieve: true }).destroy();

                this.subscriptions.push(this.docTypeService.deleteDocType(docTypeId).subscribe({
                    next: () => {
                        this.documentTypes = this.documentTypes.filter(dt => dt.id !== docTypeId);
                    },
                    error: (err) => {
                        this.working = false;
                        console.error(err);
                    },
                    complete: () => {
                        this.cdr.detectChanges();
                        this.bindDataTable();
                        this.messageService.success('The file type has been deleted successfully.');
                        this.working = false;
                    }
                }));
            }, true, 'Delete');
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}