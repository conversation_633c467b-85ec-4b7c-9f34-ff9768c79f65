﻿using AIMS3.Business.Models;
using AIMS3.Data;
using AIMS3.Data.Models;
using AIMS3.Repository.Repositories;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace AIMS3.Business.Services.Implementations;

public partial class ViewDataService : IViewDataService
{
    private readonly IDapperRepository _dapperRepository;

    private readonly CultureInfo culture = new("en-GB");
    DateTime periodDate;
    DateTime? fromDateFilter = null;
    DateTime? toDateFilter = null;
    List<ProgressAsOfDateModel> activitiesInPeriodStart = [];

    public ViewDataService(IDapperRepository dapperRepository)
    {
        //_actRepository = actRepository;
        //_targetService = targetService;
        _dapperRepository = dapperRepository;
    }

    public async Task<DataInterventionModel> GetData(DataFilterModel filters)
    {
        // get interventions - WITHOUT data status filtering to ensure all organizations appear
        var query = "SELECT DISTINCT InterventionId AS Value, CategoryCode, Intervention FROM ";
        if (filters.IsTarget)
            query += "vTargetsWithDetails";
        else
            query += "vActivitiesWithProgressDetails";

        // Get conditions for intervention selection (exclude data status filtering)
        var interventionConditions = GetConditionsForInterventionSelection(filters);
        if (interventionConditions.Count > 0)
            query += $" WHERE {string.Join(" AND ", interventionConditions)}";

        query += " ORDER BY CategoryCode, Intervention;";

        var profiles = await _dapperRepository.ExecuteRawAsync<SingeValueModel>(query);
        if (!profiles.Any())
        {
            return new DataInterventionModel()
            {
                Interventions = [],
                Filters = filters,
                Data = Array.Empty<ProgressDataViewModel>(),
            };
        }

        var profIds = profiles.Select(p => Convert.ToInt32(p.Value)).ToList();

        // get data for the first intervention - WITH full filtering including data status
        var dataConditions = GetConditions(filters);
        return new DataInterventionModel()
        {
            Interventions = profIds,
            Filters = filters,
            Data = filters.IsTarget ? await GetTargetData(profIds[0], filters, dataConditions)
                                    : await GetProgressData(profIds[0], filters, dataConditions)
        };
    }

    public async Task<IEnumerable<TargetDataViewModel>> GetTargetData(int profId, DataFilterModel filters, List<string> conditions = null)
    {
        if (conditions is null)
        {
            filters.ProfIds = new int[] { profId };
            conditions = GetConditions(filters);
        }
        else
        {
            conditions = conditions.Where(c => !c.StartsWith("InterventionId IN")).ToList();
            conditions.Add("InterventionId = " + profId);
        }

        if (filters.Period > 0)
            conditions.Add($"Period = {filters.Period}");
        if (filters.Year > 0)
            conditions.Add($"Year = {filters.Year}");

        var query = @"SELECT t.Id,UniqueId,Partner,OrganizationId,Project,ProjectId,InterventionId,
                      Region,Province,District,Period,Year,MarkedSubmitted,DateSubmitted,DateApproved,
                      dcv.DynamicColumnId, dcv.Value AS ColumnValue
                      FROM vTargetsWithDetails t INNER JOIN DynamicColumnsValues dcv ON dcv.TargetId = t.Id";

        if (conditions?.Count > 0)
            query += " WHERE " + string.Join(" AND ", conditions);

        query += " ORDER BY Partner,Project,Region";

        var targets = await _dapperRepository.ExecuteRawAsync<DataResultModel>(query);

        var targetsWithCols = targets.GroupBy(t => new
        {
            t.Id, //t.UniqueId,
            t.Partner, t.Project,
            //t.Region, t.Province, t.District,
            //t.Period, t.Year,
            //t.MarkedSubmitted, t.DateSubmitted, t.DateApproved
        }).Select(g => new
        {
            g.Key.Id,
            g.Key.Project,
            g.Key.Partner,
            g.First().ProjectId,
            g.First().OrganizationId,
            g.First().InterventionId,
            g.First().UniqueId,
            g.First().Region,
            g.First().Province,
            g.First().District,
            g.First().Year,
            g.First().Period,
            g.First().MarkedSubmitted,
            g.First().DateSubmitted,
            g.First().DateApproved,
            ColVals = g.Select(t =>
            {
                if (t.DynamicColumnId != null)
                {
                    return new DynamicColumnValue()
                    {
                        DynamicColumnId = (int)t.DynamicColumnId,
                        Value = t.ColumnValue
                    };
                }
                return null;
            }).ToList()
        });

        // attach comments if any
        query = @$"SELECT Id, TargetId, dbo.GetColumnID(ColumnId) AS ColumnName, IsNote, IsResolved
                   FROM Comments WHERE CommentId IS NULL AND 
                   TargetId IN (${String.Join(",", targets.Select(t => t.Id))})";
        var comments = await _dapperRepository.ExecuteRawAsync<CommentModel>(query);

        return targetsWithCols.Select(t => new TargetDataViewModel()
        {
            Id = t.Id,
            ProjId = t.ProjectId,
            OrgId = t.OrganizationId,
            ProfId = t.InterventionId,
            Partner = t.Partner,
            Project = t.Project,
            UniqueId = t.UniqueId,
            Region = t.Region,
            Province = t.Province,
            District = t.District,
            Year = t.Year,
            Qtr = t.Period,
            MarkedSubmitted = t.MarkedSubmitted,
            DateSubmitted = t.DateSubmitted,
            DateApproved = t.DateApproved,
            ColVals = ActivityService.GetDynamicColumnsAsObject(t.ColVals.Where(c => c != null).ToList()),
            Comments = comments.Where(c => c.TargetId == t.Id)
                            .Select(c => new CommentInfoModel()
                            {
                                Id = (int)c.TargetId,
                                ColId = c.ColumnName,
                                CommentId = c.Id,
                                IsNote = c.IsNote,
                                IsResolved = c.IsResolved
                            }).ToList()
        });
    }

    public async Task<IEnumerable<ProgressDataViewModel>> GetProgressData(int profId, DataFilterModel filters, List<string> conditions = null)
    {
        try
        {
            if (conditions is null)
            {
                filters.ProfIds = [profId];
                conditions = GetConditions(filters);
            }
            else
            {
                conditions = conditions.Where(c => !c.StartsWith("InterventionId IN")).ToList();
                conditions.Add("InterventionId = " + profId);
            }

            var query = @"SELECT act.Id, ProgressId, UniqueId, Partner, OrganizationId, Project, ProjectId, InterventionId,
                          Status, SDate, EDate, Region, Province, District, Community,
                          AsOfDate, Period, Year, MarkedSubmitted, DateSubmitted, DateApproved
                          FROM vActivitiesWithProgressDetails act";

            if (filters.ApprovalMode)
            {
                if (filters.PeriodEnd > 0)
                    conditions.Add($"Period = {filters.PeriodEnd}");
                if (filters.YearEnd > 0)
                    conditions.Add($"Year = {filters.YearEnd}");
            }
            else
            {
                // 1. If both start and end periods are selected
                if (filters.Period > 0 && filters.PeriodEnd > 0)
                {
                    // go one month backwards to include the progress of the current month
                    int periodStart = (int)filters.Period - 1;
                    int yearStart = (int)filters.Year;
                    if (periodStart <= 0)
                    {
                        periodStart = 12;
                        yearStart--;
                    }

                    int periodEnd = (int)filters.PeriodEnd + 1;
                    int yearEnd = (int)filters.YearEnd;
                    if (periodEnd > 12)
                    {
                        periodEnd = 1;
                        yearEnd++;
                    }
                    periodDate = new DateTime(yearStart, periodStart, 1);
                    var periodEndDate = new DateTime(yearEnd, periodEnd, 1).AddDays(-1);

                    fromDateFilter = periodDate.AddMonths(1);
                    toDateFilter = periodEndDate;

                    // Use culture-invariant date formatting to avoid regional differences
                    var periodEndDateString = periodEndDate.ToString("yyyy-MM-dd HH:mm:ss.fff", System.Globalization.CultureInfo.InvariantCulture);

                    query = query.Replace("AsOfDate", $@"(CASE WHEN AsOfDate >= SDate AND
                                          (EDate IS NULL OR DATEDIFF(MONTH, EDate, AsOfDate) <= 0) AND
                                          (AsOfDate <= '{periodEndDateString}') THEN AsOfDate ELSE NULL
		                               END) AS AsOfDate");
                    // Step a1: filter progress data outside activities start and end dates
                    //conditions.Add($"AsOfDate >= SDate AND (EDate IS NULL OR DATEDIFF(MONTH, EDate, AsOfDate) <= 0)");

                    // Step a2: filter progress data after the (selected) period end date
                    //conditions.Add($"(AsOfDate <= '{ periodEndDate }' OR AsOfDate IS NULL)");
                }
                // 2. If start or end period is selected, take it as of that period,year
                else if (filters.Period > 0 && (filters.PeriodEnd is null || filters.PeriodEnd == 0))
                {
                    var periodDate = new DateTime((int)filters.Year, (int)filters.Period, 1);
                    var periodDateString = periodDate.ToString("yyyy-MM-dd HH:mm:ss.fff", System.Globalization.CultureInfo.InvariantCulture);
                    conditions.Add($"(SDate >= '{periodDateString}' OR EDate >= '{periodDateString}' OR EDate IS NULL OR AsOfDate >= '{periodDateString}' OR AsOfDate IS NULL)");
                }
                else if (filters.PeriodEnd > 0 && (filters.Period is null || filters.Period == 0))
                {
                    int periodEnd = (int)filters.PeriodEnd + 1;
                    int yearEnd = (int)filters.YearEnd;
                    if (periodEnd > 12)
                    {
                        periodEnd = 1;
                        yearEnd++;
                    }
                    var periodEndDate = new DateTime(yearEnd, periodEnd, 1).AddDays(-1);
                    var periodEndDateString = periodEndDate.ToString("yyyy-MM-dd HH:mm:ss.fff", System.Globalization.CultureInfo.InvariantCulture);
                    conditions.Add($"(SDate <= '{periodEndDateString}' OR EDate <= '{periodEndDateString}' OR EDate IS NULL OR AsOfDate <= '{periodEndDateString}' OR AsOfDate IS NULL)");
                }
            }

            if (conditions?.Count > 0)
                query += " WHERE " + string.Join(" AND ", conditions);

            query += " ORDER BY Partner,Project,Region";

            var activities = await _dapperRepository.ExecuteRawAsync<DataResultModel>(query);

            var dynColsData = new List<DynColValueModel>();
            var actIds = new List<int>();

            if (filters.ApprovalMode)
            {
                if (filters.PeriodEnd == 0 || filters.PeriodEnd is null)
                {
                    activities = activities.GroupBy(a => new
                    {
                        a.Id, //a.ProgressId,
                        //a.UniqueId,
                        a.Partner,
                        a.Project
                        //a.Status,
                        //a.SDate, a.EDate,
                        //a.Region, a.Province, a.District, a.Community,
                        ////a.Period, a.Year, a.AsOfDate,
                        //a.MarkedSubmitted, a.DateSubmitted, a.DateApproved
                    }).Select(g => g.MaxBy(a => a.AsOfDate));
                }

                // get columns values
                actIds = activities.Select(a => a.Id).Distinct().ToList();
                var progIds = activities.Select(a => a.ProgressId).Distinct().ToList();
                dynColsData = await GetDynamicColumnValues(profId, actIds, progIds);
            }
            else
            {
                // 1. if no period (start and end) is selected, group and take max of period,year
                if ((filters.Period is null || filters.Period == 0) &&
                    (filters.PeriodEnd is null || filters.PeriodEnd == 0))
                {
                    activities = activities.GroupBy(a => new {
                        a.Id,
                        a.Partner, a.Project
                    }).Select(g => g.MaxBy(a => a.AsOfDate));

                    // get columns values
                    var progIds = new List<int?>();
                    foreach (var act in activities)
                    {
                        if (!actIds.Contains(act.Id))
                            actIds.Add(act.Id);
                        if (!progIds.Contains(act.ProgressId))
                            progIds.Add(act.ProgressId);
                    }
                    dynColsData = await GetDynamicColumnValues(profId, actIds, progIds);
                }
                // 2. if both start and end periods are selected, subtract periodEnd progress data from periodStart data
                else {
                    // 2.1 if both start and end periods are selected
                    if (filters.Period > 0 && filters.PeriodEnd > 0)
                    {
                        /*
                            - 2 P(T/R) => MaxBy(AsOfDate ?? Now) & by SDate & EDate between F(from) and F(to)
                            - 3 Exclude (2) from Activities
                            - 4 P(F/R) InitialProg => Filter Activities <= F(from) & AsOfD!=null, then MaxBy(AsOfDate)
                            - 5 Calculate Progress => P(T/R) - P(F/R)
                         */
                        // Step b: filter activities by Max AsOfDate to get the activities for end of the period,
                        //         but, first exclude activities that ends before From Month or Starts after To Month
                        var activitiesToReturn = activities.Where(a => ((a.EDate is null && a.SDate < toDateFilter) ||
                                                                        (a.EDate >= fromDateFilter && a.SDate < toDateFilter)) &&
                                                                        a.AsOfDate is not null)
                                                           .GroupBy(a => new {
                                                               a.Id,
                                                               a.Partner,
                                                               a.Project
                                                           })
                                                           .Select(g => g.MaxBy(a => a.AsOfDate))
                                                           .Distinct().ToList();
                        // Step bb:
                        actIds = activitiesToReturn.Select(a => a.Id).ToList();
                        activitiesToReturn = [.. activitiesToReturn,
                            .. activities.Where(a => !actIds.Contains(a.Id) && a.AsOfDate is null && (
                                               (a.EDate is null && a.SDate < toDateFilter) ||
                                               (a.EDate >= fromDateFilter && a.SDate < toDateFilter)))
                                .GroupBy(a => new { a.Id, a.Partner, a.Project })
                                .Select(g => g.First()).Distinct()
                        ];

                        // Step c: exclude activities in step b from activities
                        activities = activities.Except(activitiesToReturn).ToList();

                        // Step d1: filter activities to exclude activities with no progress,
                        //          and those with AsOfDate in or after filter start date
                        activitiesInPeriodStart = activities.Where(a => a.ProgressId is not null && a.AsOfDate < fromDateFilter)
                                                            .GroupBy(a => new
                                                            {
                                                                a.Id,
                                                                a.Partner,
                                                                a.Project
                                                            })
                                                            .Select(g => g.MaxBy(a => a.AsOfDate))
                                                            .Select(a => new ProgressAsOfDateModel(a.Id, a.ProgressId,
                                                                         new DateTime(a.AsOfDate.Value.Year, a.AsOfDate.Value.Month, 1)))
                                                            .Distinct().ToList();

                        // Step d2: get only progress columns values for initial progress
                        var progressInPeriodStart = new List<DynColValueModel>();
                        if (activitiesInPeriodStart.Count > 0)
                        {
                            query = @$"SELECT ActivityId, ProgressId, DynamicColumnId, Value
                                       FROM vDynColsWithValues
                                       WHERE ProgressId IN ({string.Join(",", activitiesInPeriodStart.Select(p => p.ProgressId))})
                                       AND FieldType IN (1,2,3);";  // exclude non-numeric fields/columns

                            progressInPeriodStart = (await _dapperRepository.ExecuteRawAsync<DynColValueModel>(query))
                                                                    .ToList();
                        }

                        // get columns data for the activities to return
                        var progIds = new List<int?>();
                        foreach(var act in activitiesToReturn)
                        {
                            if (!actIds.Contains(act.Id))
                                actIds.Add(act.Id);

                            // skip progress data for activities when
                            if (act.AsOfDate is null || act.AsOfDate < fromDateFilter)
                                continue;

                            if (!progIds.Contains(act.ProgressId))
                                progIds.Add(act.ProgressId);
                        }
                        dynColsData = await GetDynamicColumnValues(profId, actIds, progIds);

                        var numRegex = NumRegex();

                        // calculate progress
                        foreach (var col in dynColsData)
                        {
                            foreach (var prog in progressInPeriodStart)
                            {
                                if (col.ProgressId == prog.ProgressId)
                                    continue;

                                if (col.ActivityId == prog.ActivityId &&
                                    col.DynamicColumnId == prog.DynamicColumnId)
                                {
                                    bool fVal = double.TryParse(numRegex.Replace(col.Value ?? "", ""), out double fromVal);
                                    bool tVal = double.TryParse(numRegex.Replace(prog.Value ?? "", ""), out double toVal);
                                    col.Value = (fromVal - toVal).ToString();
                                }
                            }
                        }

                        activities = activitiesToReturn;
                    }
                    // 2.2 if only start or end period is selected
                    else
                    {
                        activities = activities.GroupBy(a => new {
                            a.Id,
                            a.Partner,
                            a.Project
                        }).Select(g => g.MaxBy(a => a.AsOfDate ?? DateTime.Now))
                          .ToList();

                        // get columns values
                        var progIds = new List<int?>();
                        foreach (var act in activities)
                        {
                            if (!actIds.Contains(act.Id))
                                actIds.Add(act.Id);
                            if (!progIds.Contains(act.ProgressId))
                                progIds.Add(act.ProgressId);
                        }
                        dynColsData = await GetDynamicColumnValues(profId, actIds, progIds);
                    }
                }
            }

            // assign column values to activities
            var activitiesWithCols = activities.Select(a => new
            {
                a.Id,
                a.ProgressId,
                a.ProjectId,
                a.Project,
                a.OrganizationId,
                a.Partner,
                a.InterventionId,
                a.UniqueId,
                a.Status,
                a.SDate,
                a.EDate,
                a.Region,
                a.Province,
                a.District,
                a.Community,
                a.Period,
                a.Year,
                a.AsOfDate,
                a.MarkedSubmitted,
                a.DateSubmitted,
                a.DateApproved,
                ColVals = dynColsData.Where(c => c.ActivityId == a.Id || c.ProgressId == a.ProgressId)
                                     .Select(c => new DynamicColumnValue()
                                     {
                                         DynamicColumnId = c.DynamicColumnId,
                                         Value = c.Value
                                     }).ToList()
            });

            // attach documents
            query = @$"SELECT ActivityId, COUNT(DocumentId) AS DocumentId
                       FROM ActivitiesDocuments
                       WHERE ActivityId IN (${String.Join(",", actIds)})
                       GROUP BY ActivityId;";
            var docs = await _dapperRepository.ExecuteRawAsync<ActivityDocument>(query);

            // Create final result, but filter out activities without meaningful progress data
            // However, ensure each intervention has at least one activity to prevent empty interventions
            var activitiesWithProgress = new List<ProgressDataViewModel>();
            
            // Group activities by intervention to ensure each intervention has at least one activity
            var activitiesByIntervention = activitiesWithCols.GroupBy(a => a.InterventionId).ToList();
            
            foreach (var interventionGroup in activitiesByIntervention)
            {
                var interventionActivities = interventionGroup.ToList();
                var validActivities = new List<dynamic>();
                
                // For activities with time series filtering, apply strict validation
                if ((filters.Period > 0 || filters.PeriodEnd > 0) && !filters.ApprovalMode)
                {
                    foreach (var activity in interventionActivities)
                    {
                        // Check if this is an ongoing activity - only apply strict filtering to ongoing activities
                        bool isOngoingActivity = activity.Status == 0; // ActivityStatus.Ongoing = 0
                        
                        // STRICT VALIDATION: Only for ongoing activities, others get more lenient treatment
                        bool hasValidProgress = false;
                        
                        if (isOngoingActivity)
                        {
                            // STEP 1: Check if there's an actual progress record (not just info-level data)
                            if (activity.ProgressId != null && activity.ProgressId > 0)
                            {
                                // STEP 2: Validate AsOfDate is within the filtered time period
                                bool asOfDateInPeriod = false;
                                if (activity.AsOfDate != null)
                                {
                                    // Check if AsOfDate falls within the filtered time series
                                    if (fromDateFilter != null && toDateFilter != null)
                                    {
                                        asOfDateInPeriod = activity.AsOfDate >= fromDateFilter && activity.AsOfDate <= toDateFilter;
                                    }
                                    else if (filters.Period > 0 && filters.Year > 0)
                                    {
                                        var filterStart = new DateTime((int)filters.Year, (int)filters.Period, 1);
                                        var filterEnd = filterStart.AddMonths(1).AddDays(-1);
                                        asOfDateInPeriod = activity.AsOfDate >= filterStart && activity.AsOfDate <= filterEnd;
                                    }
                                }
                                
                                // STEP 3: Only proceed if AsOfDate is within the time period
                                if (asOfDateInPeriod)
                                {
                                    // STEP 4: Check for meaningful cumulative progress data
                                    var meaningfulProgressValues = activity.ColVals?.Where(cv => cv != null && 
                                        cv.Value != null &&
                                        !string.IsNullOrEmpty(cv.Value.ToString()) &&
                                        cv.Value.ToString().Trim() != "" &&
                                        cv.Value.ToString().Trim() != "0" &&
                                        cv.Value.ToString().Trim() != "0.00" &&
                                        cv.Value.ToString().Trim() != "0.0" &&
                                        cv.Value.ToString().Trim() != "N/A" &&
                                        cv.Value.ToString().Trim() != "-" &&
                                        cv.Value.ToString().Trim() != "null" &&
                                        cv.Value.ToString().Trim() != "undefined").ToList();
                                    
                                    // STEP 5: Validate that progress values are actually meaningful
                                    if (meaningfulProgressValues?.Any() == true)
                                    {
                                        bool hasActualProgressData = meaningfulProgressValues.Any(cv => {
                                            var stringValue = cv.Value?.ToString()?.Trim();
                                            if (string.IsNullOrEmpty(stringValue)) return false;
                                            
                                            // For numeric values, ensure they're greater than 0
                                            if (double.TryParse(stringValue.Replace(",", ""), out double numValue))
                                            {
                                                return numValue > 0; // Must be greater than 0 to be meaningful
                                            }
                                            
                                            // For non-numeric values, they should be meaningful strings
                                            return stringValue.Length > 0 && 
                                                   stringValue != "0" && 
                                                   stringValue != "N/A" &&
                                                   stringValue != "-" &&
                                                   stringValue != "null" &&
                                                   stringValue != "undefined";
                                        });
                                        
                                        hasValidProgress = hasActualProgressData;
                                    }
                                }
                            }
                            
                            // STEP 6: Additional validation - ensure GetAsOfDateString doesn't return "N/A"
                            if (hasValidProgress)
                            {
                                var asOfString = GetAsOfDateString(activity.Id, activity.SDate, activity.EDate, activity.AsOfDate);
                                if (asOfString == "N/A" || string.IsNullOrEmpty(asOfString))
                                {
                                    hasValidProgress = false;
                                }
                            }
                        }
                        else
                        {
                            // For non-ongoing activities (Completed, Archived, Cancelled), apply more lenient filtering
                            // Just check basic criteria: valid progressId and not completely empty
                            if (activity.ProgressId != null && activity.ProgressId > 0)
                            {
                                var asOfString = GetAsOfDateString(activity.Id, activity.SDate, activity.EDate, activity.AsOfDate);
                                if (asOfString != "N/A" && !string.IsNullOrEmpty(asOfString))
                                {
                                    hasValidProgress = true; // More lenient for completed/archived activities
                                }
                            }
                        }
                        
                        if (hasValidProgress)
                        {
                            validActivities.Add(activity);
                        }
                    }
                    
                    // If no activities passed strict validation, include the most recent activity
                    // to ensure the intervention doesn't disappear completely
                    if (!validActivities.Any() && interventionActivities.Any())
                    {
                        // Get the most recent activity (by AsOfDate or creation date)
                        var mostRecentActivity = interventionActivities
                            .Where(a => a.AsOfDate != null)
                            .OrderByDescending(a => a.AsOfDate)
                            .FirstOrDefault() ?? interventionActivities.First();
                            
                        validActivities.Add(mostRecentActivity);
                    }
                }
                else
                {
                    // For non-time-series filtering or approval mode, keep all activities
                    validActivities.AddRange(interventionActivities);
                }
                
                // Convert valid activities to ProgressDataViewModel
                foreach (var activity in validActivities)
                {
                    activitiesWithProgress.Add(new ProgressDataViewModel()
                    {
                        Id = activity.Id,
                        ProjId = activity.ProjectId,
                        OrgId = activity.OrganizationId,
                        ProfId = activity.InterventionId,
                        ProgressId = activity.ProgressId,
                        Partner = activity.Partner,
                        Project = activity.Project,
                        ActivityId = activity.UniqueId,
                        File = docs.SingleOrDefault(d => d.ActivityId == activity.Id)?.DocumentId,
                        Status = activity.Status,
                        SMonth = activity.SDate?.ToString("dd/MM/yyyy", culture),
                        EMonth = activity.EDate?.ToString("dd/MM/yyyy", culture),
                        Region = activity.Region,
                        Province = activity.Province,
                        District = activity.District,
                        Community = activity.Community,
                        Month = activity.Period,
                        Year = activity.Year,
                        AsOf = GetAsOfDateString(activity.Id, activity.SDate, activity.EDate, activity.AsOfDate),
                        MarkedSubmitted = activity.MarkedSubmitted,
                        DateSubmitted = activity.DateSubmitted,
                        DateApproved = activity.DateApproved,
                        ColVals = ActivityService.GetDynamicColumnsAsObject(activity.ColVals)
                    });
                }
            }

            // attach comments if any
            query = @$"SELECT DISTINCT Id, ActivityId, ActivityProgressId AS ProgressId,
                       dbo.GetColumnID(ColumnId) AS ColumnName, IsNote, IsResolved
                       FROM Comments WHERE CommentId IS NULL AND ActivityId IN (${String.Join(",", actIds)})";
            var comments = await _dapperRepository.ExecuteRawAsync<CommentModel>(query);

            if (comments.Any()) {
                foreach (var activity in activitiesWithProgress)
                {
                    var actComments = comments.Where(c => c.ActivityId == activity.Id);
                    activity.Comments = actComments.Where(c => !c.IsResolved)
                                                   .Select(c => new CommentInfoModel()
                                                   {
                                                       Id = (int)(c.ProgressId ?? c.ActivityId),
                                                       ColId = c.ColumnName,
                                                       CommentId = c.Id,
                                                       IsNote = c.IsNote,
                                                       IsResolved = c.IsResolved
                                                   }).ToList();

                    // add at least one resolved comment to indicate activity is commented
                    var resolvedComments = actComments.Where(c => c.IsResolved).Select(c => new
                    {
                        Id = (int)(c.ProgressId ?? c.ActivityId),
                        ColId = c.ColumnName
                    }).Distinct();

                    foreach (var rc in resolvedComments) {
                        activity.Comments.Add(new CommentInfoModel()
                        {
                            Id = rc.Id,
                            ColId = rc.ColId,
                            CommentId = 0, // unresolved comments
                            IsNote = false,
                            IsResolved = true
                        });
                    }
                }
            }

            return activitiesWithProgress;
        }
        catch (Exception ex)
        {
            // Log the error for debugging
            Console.WriteLine($"Error in GetProgressData: {ex.Message}");
            return Array.Empty<ProgressDataViewModel>();
        }
    }

    public async Task<OperationResult> ApproveTargetData(int[] ids, string voidApproval, string byUser)
    {
        try
        {
            string query = "";
            if (string.IsNullOrEmpty(voidApproval))
            {
                query = @$"UPDATE Targets SET DateApproved = GETDATE(), ApprovedBy = N'{byUser}'
                           WHERE Id IN ({string.Join(",", ids)}) AND DateSubmitted IS NOT NULL;";
            }
            else
            {
                query = @$"UPDATE Targets SET DateApproved = NULL, ApprovedBy = NULL
                           WHERE Id IN ({string.Join(",", ids)}) AND DateSubmitted IS NOT NULL;";
            }

            await _dapperRepository.ExecuteScalarAsync(query);
            return OperationResult.Succeeded;
        }
        catch (Exception)
        {
            return OperationResult.Failed;
        }
    }

    public async Task<OperationResult> ApproveProgressData(int[] progIds, string voidApproval, string byUser)
    {
        try
        { 
            string query = "";
            
                if (string.IsNullOrEmpty(voidApproval))
                {
                    // update and set status to 'Archived' == 2
                    query = @$"UPDATE ActivitiesProgress SET DateApproved = GETDATE(), ApprovedBy = N'{byUser}',
                           Status = IIF(Status = 1, 2, Status)
                           WHERE Id IN ({string.Join(",", progIds)}) AND DateSubmitted IS NOT NULL;";
                }
                else
                {
                    // update and reverse status from 'Archived'
                    query = @$"UPDATE ActivitiesProgress SET DateApproved = NULL, ApprovedBy = NULL,
                           Status = (SELECT IIF(EndDate IS NULL, Status, 1) FROM Activities WHERE Id = ActivityId)
                           WHERE Id IN ({string.Join(",", progIds)}) AND DateSubmitted IS NOT NULL;";
                }
            await _dapperRepository.ExecuteScalarAsync(query);
            return OperationResult.Succeeded;
        }
        catch (Exception)
        {
            return OperationResult.Failed;
        }
    }


    #region Private Helper Methods
    
    private static List<string> GetConditions(DataFilterModel filters)
    {
        var conditions = new List<string>();

        if (filters.OrgIds?.Length > 0)
            conditions.Add($"OrganizationId IN ({string.Join(",", filters.OrgIds)})");

        if (filters.ProjIds?.Length > 0)
            conditions.Add($"ProjectId IN ({string.Join(",", filters.ProjIds)})");

        if (filters.ProfIds?.Length > 0)
            conditions.Add($"InterventionId IN ({string.Join(",", filters.ProfIds)})");
        else if (filters.CatIds?.Length > 0)
            conditions.Add($"CategoryId IN ({string.Join(",", filters.CatIds)})");
        if (filters.Outputs?.Length > 0)
            conditions.Add($"Output IN (N'{string.Join("',N'", filters.Outputs)}')");

        if (filters.Regions?.Length > 0)
            conditions.Add($"Region IN ('{string.Join("','", filters.Regions)}')");

        if (filters.DataStatus?.Length > 0 && filters.DataStatus.Length < 3)
        {
            if (filters.DataStatus.Contains(0) && filters.DataStatus.Contains(1))           // Draft or submitted
                conditions.Add("(DateSubmitted IS NULL OR (DateSubmitted IS NOT NULL AND DateApproved IS NULL))");
            else if (filters.DataStatus.Contains(0) && filters.DataStatus.Contains(2))      // Draft or approved
                conditions.Add("(DateSubmitted IS NULL OR DateApproved IS NOT NULL)");
            else if (filters.DataStatus.Contains(1) && filters.DataStatus.Contains(2))      // Submitted or approved
                conditions.Add("((DateSubmitted IS NOT NULL AND DateApproved IS NULL) OR DateApproved IS NOT NULL)");
            else if (filters.DataStatus.Contains(0))
                conditions.Add("DateSubmitted IS NULL");  // Draft items (marked for submission but not yet submitted)
            else if (filters.DataStatus.Contains(1))
                conditions.Add("DateSubmitted IS NOT NULL AND DateApproved IS NULL");
            else if (filters.DataStatus.Contains(2))
                conditions.Add("DateApproved IS NOT NULL");
        }

        return conditions;
    }

    private static List<string> GetConditionsForInterventionSelection(DataFilterModel filters)
    {
        var conditions = new List<string>();

        if (filters.OrgIds?.Length > 0)
            conditions.Add($"OrganizationId IN ({string.Join(",", filters.OrgIds)})");

        if (filters.ProjIds?.Length > 0)
            conditions.Add($"ProjectId IN ({string.Join(",", filters.ProjIds)})");

        if (filters.ProfIds?.Length > 0)
            conditions.Add($"InterventionId IN ({string.Join(",", filters.ProfIds)})");
        else if (filters.CatIds?.Length > 0)
            conditions.Add($"CategoryId IN ({string.Join(",", filters.CatIds)})");
        if (filters.Outputs?.Length > 0)
            conditions.Add($"Output IN (N'{string.Join("',N'", filters.Outputs)}')");

        if (filters.Regions?.Length > 0)
            conditions.Add($"Region IN ('{string.Join("','", filters.Regions)}')");

        // NOTE: DataStatus filtering is intentionally excluded here to ensure
        // all organizations appear regardless of their data status

        return conditions;
    }

    private async Task<List<DynColValueModel>> GetDynamicColumnValues(int profId, List<int> actIds, List<int?> progIds)
    {
        if (!actIds.Any())
            return new List<DynColValueModel>();

        var query = @$"SELECT ActivityId, ProgressId, DynamicColumnId, Value
                       FROM vDynColsWithValues
                       WHERE InterventionProfileId = {profId}
                       AND ((ActivityId IN ({string.Join(",", actIds)}) AND Type <> 2)";

        progIds = progIds.Where(id => id > 0).ToList();
        if (progIds.Count > 0)
            query += $" OR ProgressId IN ({string.Join(",", progIds).TrimEnd(',')}));";
        else
            query += ")";

        return
            (await _dapperRepository.ExecuteRawAsync<DynColValueModel>(query))
        .ToList();
    }

    private string GetAsOfDateString(int activityId, DateTime? SDate, DateTime? EDate, DateTime? asOfDate)
    {
        if (asOfDate is null)
            return "N/A";

        var strAsOfDate = asOfDate?.ToString("dd MMM yyyy", culture);

        if (fromDateFilter is null || toDateFilter is null)
            return strAsOfDate;

        /*
        - SDate < From & EDate > To Or no EDate
          > AsOfD == F(to) & InitProg.AsOfD == F(from-1=P) => Fully Available
          > AsOfD < F(to) & InitPog.AsOfD == F(from-1=P) => InitProg.AsOfD/SDate - AsOfD
          > InitProg.AsOfD < F(from-1=P) => SDate - AsOfD
          > else: All Missing => N/A
        */
        if (SDate < fromDateFilter && (EDate is null || EDate > toDateFilter))
        {
            var startProgAsOfDate = activitiesInPeriodStart.Find(p => p.activityId == activityId)?.AsOfDate;
            if (asOfDate == toDateFilter)
            {
                if (startProgAsOfDate == periodDate)
                    return "Fully Available";
                if (startProgAsOfDate is null)
                    return GetFormattedDateRange((DateTime)SDate, (DateTime)asOfDate);
                if (startProgAsOfDate <= periodDate)
                    return GetFormattedDateRange((DateTime)startProgAsOfDate?.AddMonths(1), (DateTime)asOfDate);
            }
            if (asOfDate < toDateFilter)
            {
                if(startProgAsOfDate is null)
                    return GetFormattedDateRange((DateTime)SDate, (DateTime)asOfDate);
                if (startProgAsOfDate <= periodDate)
                    return GetFormattedDateRange((DateTime)startProgAsOfDate?.AddMonths(1), (DateTime)asOfDate);
            }
            return "N/A";
        }

        /*
        - SDate >= From & SDate <= To & EDate >= To
          > AsOfD == F(to) => Fully Available
          > AsOfD < F(to) => As of / SDate - AsOfD
          > else: All Missing => N/A
        */
        if (SDate >= fromDateFilter && SDate <= toDateFilter && (EDate is null || EDate >= toDateFilter))
        {
            if (asOfDate == toDateFilter)
                return "Fully Available";
            if (asOfDate < toDateFilter)
                return GetFormattedDateRange((DateTime)SDate, (DateTime)asOfDate);
            return "N/A";
        }

        /*
        - SDate >= From & EDate < To
          > AsOfD.MonthYear == EDate.MonthYear => Fully Available
          > AsOfD.MY < EDate.MY => As of / SDate - AsOfD
          > else: All Missing => N/A
        */
        if (SDate >= fromDateFilter && (EDate is null || EDate < toDateFilter))
        {
            if (EDate.HasValue && asOfDate.Value.Month == EDate.Value.Month &&
                asOfDate.Value.Year == EDate.Value.Year)
                return "Fully Available";
            else if (EDate is null || (new DateTime(asOfDate.Value.Year, asOfDate.Value.Month, 1) <
                                       new DateTime(EDate.Value.Year, EDate.Value.Month,1)))
                return GetFormattedDateRange((DateTime)SDate, (DateTime)asOfDate);
            return "N/A";
        }

        /*
        - SDate < From & EDate < To
          > AsOfD.MonthYear == EDate.MonthYear & InitPog.AsOfD == F(from-1=P) => Fully Avail.
          > AsOfD < F(to) & InitPog.AsOfD == F(from-1=P) => InitProg.AsOfD/SDate - AsOfD
          > InitProg.AsOfD < F(from-1=P) => SDate - AsOfD
          > else: All Missing => N/A
        */
        if (SDate < fromDateFilter && (EDate is null || EDate <= toDateFilter))
        {
            var startProgAsOfDate = activitiesInPeriodStart.Find(p => p.activityId == activityId)?.AsOfDate;
            if (EDate.HasValue && asOfDate.Value.Month == EDate.Value.Month &&
                asOfDate.Value.Year == EDate.Value.Year)
            {
                if (startProgAsOfDate == periodDate)
                    return "Fully Available";
                if (startProgAsOfDate is null)
                    return GetFormattedDateRange((DateTime)SDate, (DateTime)asOfDate);
                if (startProgAsOfDate <= periodDate)
                    return GetFormattedDateRange((DateTime)startProgAsOfDate?.AddMonths(1), (DateTime)asOfDate);
            }
            if (asOfDate < toDateFilter)
            {
                if (startProgAsOfDate is null)
                    return GetFormattedDateRange((DateTime)SDate, (DateTime)asOfDate);
                if (startProgAsOfDate <= periodDate)
                    return GetFormattedDateRange((DateTime)startProgAsOfDate?.AddMonths(1), (DateTime)asOfDate);
            }
            return "N/A";
        }

        return "N/A";
    }

    private string GetFormattedDateRange(DateTime fromDate, DateTime toDate)
    {
        if (fromDate.Year == toDate.Year)
        {
            if(fromDate.Month == toDate.Month)
                return $"As of {fromDate.ToString("MMM", culture)} {toDate.Year}";
            return $"{fromDate.ToString("MMM", culture)} - {toDate.ToString("MMM", culture)} {toDate.Year}";
        }
        return $"{fromDate.ToString("MMM yyyy", culture)} - {toDate.ToString("MMM yyyy", culture)}";
    }


    [GeneratedRegex(@"[^-0-9.,]+")]
    private static partial Regex NumRegex();

    #endregion
}