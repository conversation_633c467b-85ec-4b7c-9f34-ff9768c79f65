import { After<PERSON>iewInit, Component, EventE<PERSON>ter, OnDestroy, OnInit, Output } from '@angular/core';
import Graphic from '@arcgis/core/Graphic';
import Map from '@arcgis/core/Map';
import PopupTemplate from '@arcgis/core/PopupTemplate';
import Point from '@arcgis/core/geometry/Point';
import FeatureLayer from '@arcgis/core/layers/FeatureLayer';
import { CustomContent } from '@arcgis/core/popup/content';
import SimpleMarkerSymbol from '@arcgis/core/symbols/SimpleMarkerSymbol';
import MapView from '@arcgis/core/views/MapView';
import Zoom from '@arcgis/core/widgets/Zoom';
import { loadModules } from 'esri-loader';
import { Subject, Subscription, lastValueFrom } from 'rxjs';
import { environment } from '../../../../../environments/environment';
import { ThemeModeService } from '../../../../_theme/partials/layout/theme-mode-switcher/theme-mode.service';
import { ProfileInfo } from '../../../../modules/data-entry/models/profile.model';
import { formatNum } from '../../../../shared/utilities';
import { IGpsDataPoint, IProfActivityStatus } from '../../../models/map-data.model';
import { DashboardResultService } from '../../../services/result.service';

@Component({
    selector: 'aims-map',
    templateUrl: './map.component.html',
    styleUrls: ['./map.component.scss']
})
export class AimsMapComponent implements OnInit, AfterViewInit, OnDestroy {
    loading: boolean = false;

    private mapConfig = environment.arcgisConfig;
    map: Map;
    mapView: MapView;
    interventions: ProfileInfo[] = [];

    @Output() initialized: EventEmitter<AimsMapComponent> = new EventEmitter<AimsMapComponent>();

    centerPosition = { lat: 33.885, lon: 67.434 };

    filters: any = { dataStatus: [1,2] };

    private subscriptions: Subscription[] = [];
    constructor(
        private themeService: ThemeModeService,
        private dbResultService: DashboardResultService
    ) {

    }

    async ngOnInit() {
        this.loading = true;

        let baseMap: string = 'streets-navigation-vector';

        if (this.themeService.mode.value === 'dark') {
            baseMap = 'streets-night-vector';
            this.mapConfig.css = this.mapConfig.cssDark;
        }

        await loadModules(['esri/Map', 'esri/views/MapView', 'esri/Graphic',
            'esri/PopupTemplate', 'esri/widgets/Popup', 'esri/widgets/Zoom'], this.mapConfig);

        this.map = new Map({ basemap: baseMap });

        this.mapView = new MapView({
            container: 'aimsMap',
            map: this.map,
            center: [this.centerPosition.lon, this.centerPosition.lat],
            zoom: 5
        });

        const customZoom = new Zoom({
            view: this.mapView,
            container: 'mapZoom'
        });
        this.mapView.ui.remove('zoom');
        this.mapView.ui.add(customZoom, "top-right");

        // add province and district boundaries
        const provincesLayer = new FeatureLayer({
            url: this.mapConfig.afgBoundaries.provUrl,
            blendMode: 'average'
        });

        const districtsLayer = new FeatureLayer({
            url: this.mapConfig.afgBoundaries.distUrl,
            blendMode: 'soft-light'
        });
        this.map.addMany([provincesLayer, districtsLayer]);

        this.loading = false;
    }

    ngAfterViewInit(): void {
        this.initialized.emit(this);
    }

    private flyTo(point: Point, zoom: number = 10): void {
        // Fly to the point with animation and increase the zoom level
        this.mapView.goTo({
            target: point,
            zoom: zoom
        }, {
            animate: true,
            duration: 2000,
            easing: "ease-in-out" // linear, ease, ease-in, ease-out, ease-in-out
        });
    }

    private markers = [];
    private removeMarkers(): void {
        this.markers.forEach((m) => {
            if (this.mapView.graphics.includes(m))
                this.mapView.graphics.remove(m);
        });
    }
    
    addMarkers(points: IGpsDataPoint[], flyToFirstPoint?: boolean): void {
        if (!this.mapView) {
            setTimeout(() => {
                this.addMarkers(points);
            }, 100);
            return;
        }

        // if any previous markers are there, remove them
        this.removeMarkers();
        const symbolColor = [0, 158, 247, 0.5];

        points.forEach((loc: any) => {
            const _symbol = new SimpleMarkerSymbol({
                size: 8,
                outline: {
                    width: '1px',
                    color: symbolColor
                },
                color: symbolColor
            });

            const point = new Point({
                latitude: loc.gpsLat,
                longitude: loc.gpsLon
            });

            const popup = new PopupTemplate({
                title: '<span class="text-muted">District:</span> {name}',
                content: [
                    new CustomContent({
                        creator: async ({ graphic }) => {
                            const { distId } = graphic.attributes;
                            const element = document.createElement("div");
                            const content = await this.getActivityProgress(distId);
                            element.innerHTML = content;
                            return element;
                        }
                    })
                ]
            });

            const marker = new Graphic({
                geometry: point,
                symbol: _symbol,
                attributes: {
                    distId: loc.districtId,
                    name: `${loc.distName}, ${loc.provName}`,
                    lat: loc.gpsLat,
                    lon: loc.gpsLon
                },
                popupTemplate: popup
            });

            this.markers.push(marker);
            this.mapView.graphics.add(marker);
        });

        if (flyToFirstPoint) {
            const point = new Point({
                latitude: points[0].gpsLat,
                longitude: points[0].gpsLon
            });
            this.flyTo(point, 8);
        }
    }

    private async getActivityProgress(distId: number): Promise<string> {
        const result = new Subject<string>();

        let actWithStatus: IProfActivityStatus[] = [];

        this.subscriptions.push(this.dbResultService.getActivityProgress(distId, this.filters).subscribe({
            next: (res) => {
                actWithStatus = res;
            },
            error: (err) => {
                result.next('');
                result.complete();
            },
            complete: () => {
                let content = '<p class="fs-6">No activities in progress for this district.</p>';

                if (actWithStatus.length) {
                    let totalOngoing: number = 0;
                    let totalCompleted: number = 0;

                    content = `
                        <table class="table table-sm table-hover fs-8 table-row-dashed align-middle m-0">
                                <thead>
                                    <tr class="text-gray-900 fw-bold gs-0" style="border-bottom: 1px solid #eee !important">
                                        <th>Intervention</th>
                                        <th width="10%">Ongoing</th>
                                        <th width="10%">Completed</th>
                                    </tr>
                                </thead>
                                <tbody class="text-gray-800">`;

                    actWithStatus.forEach(actStatus => {
                        const prof = this.interventions.find(i => i.id === actStatus.interventionId);
                        totalOngoing += actStatus.ongoing;
                        totalCompleted += actStatus.completed;

                        content += `<tr><td>
                                        <p class="text-muted fs-9 m-0">
                                            ${ prof.category.output} - ${prof.category.code} ${prof.category.name}
                                        </p>
                                        <p class="text-gray-900 m-0">${ prof.name } (${ prof.abbreviation })</p>
                                    </td>
                                    <td>${ actStatus.ongoing }</td>
                                    <td>${ actStatus.completed } </td>
                                </tr>`;
                    });

                    content += '</tbody></table>';

                    content = `<p class="fs-6">In this district, there are <b>${formatNum(totalOngoing) || 0}</b> ` +
                        `ongoing activities and <b>${formatNum(totalCompleted) || 0}</b> completed activities. ` +
                        'Specifically: </p>' + content;
                }
                
                result.next(content);
                result.complete();
            }
        }));

        return await lastValueFrom(result.asObservable());
    }


    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}