<app-modal #modal [modalConfig]="modalConfig">
    <div class="d-flex flex-stack" *ngIf="!editMode">
        <select class="form-select form-select-sm w-auto max-w-200px mr-3" ngbTooltip="File type" [(ngModel)]="fileTypeId"
                (change)="onSelectFileType()">
            <option selected value="0">All</option>
            <option *ngFor="let fType of fileTypes" [value]="fType.id">{{ fType.typeName }}</option>
        </select>
        <button type="button" class="btn btn-sm btn-icon btn-light-primary px-3 w-auto flex-end"
                ngbTooltip="Upload new attachment" (click)="onUpload()" *ngIf="isCellEditable">
            <i class="fas fa-upload me-2"></i> Upload
        </button>
    </div>
    <!-- Upload -->
    <div *ngIf="editMode">
        <div class="row">
            <div class="col-3">
                <button class="btn btn-sm btn-icon btn-light btn-active-color-primary w-auto px-3"
                        [disabled]="working" (click)="onCancelEditMode()">
                    <i class="fas fa-chevron-left me-3"></i> Cancel {{ editMode === 'upload' ? 'upload' : 'edit' }}
                </button>
            </div>
            <div class="col-9" *ngIf="editMode === 'upload'">
                <input id="fileInput" class="form-control form-control-sm" type="file" [disabled]="working" (change)="onFileSelect($event)" />
                <div class="progress mt-1" *ngIf="uploadFile && working">
                    <div class="progress-bar" role="progressbar" style.width="{{ uploadFile.progress | async }}%"
                         attr.aria-valuenow="{{uploadFile.progress | async}}" aria-valuemin="0" aria-valuemax="100">
                        {{uploadFile.progress | async}}%
                    </div>
                </div>
            </div>
        </div>
        <div class="mt-5">
            <form id="docForm" class="form" [formGroup]="form">
                <div class="row">
                    <div class="col-3"></div>
                    <div class="col-6 form-group">
                        <label class="required">File name</label>
                        <input name="docName" class="form-control" type="text" placeholder="File name" formControlName="name"
                               [ngClass]="{ 'is-invalid': form.controls['name'].dirty && form.controls['name'].invalid }" />
                        <ng-container [ngTemplateOutlet]="formError"
                                      [ngTemplateOutletContext]="{
                        validation: 'required',
                        message: 'File name is required.',
                        control: form.controls['name']
                        }"></ng-container>
                    </div>
                    <div class="col-3 form-group">
                        <label class="required">File type</label>
                        <select class="form-select" placeholder="Select file type" formControlName="fileType" (change)="onSelectFileType()">
                            <option selected></option>
                            <option *ngFor="let fType of fileTypes" [value]="fType.id">{{ fType.typeName }}</option>
                        </select>
                    </div>
                </div>
                <div class="row" *ngIf="fields.length">
                    <div class="col-4 mb-4" *ngFor="let dField of fields">
                        <label [ngClass]="{'required': dField.isRequired}">{{ dField.fieldName }}</label>
                        <!-- Text, Number, Currency, Percentage, GPS -->
                        <div class="input-group input-group-sm" *ngIf="dField.fieldType <= 4">
                            <input class="form-control form-control-sm" [type]="dField.fieldType == 0 ? 'text' : 'number'"
                                   [formControlName]="'field'+dField.id"
                                   [ngClass]="{ 'is-invalid': form.controls['field'+dField.id].dirty && form.controls['field'+dField.id].invalid }" />
                            <div class="input-group-append" *ngIf="dField.fieldTypeValues"><span class="input-group-text">{{ dField.fieldTypeValues }}</span></div>
                        </div>
                        <!-- Date -->
                        <input class="form-control form-control-sm" type="date" [formControlName]="'field'+dField.id" *ngIf="dField.fieldType === 5" />
                        <!-- Checkbox -->
                        <div class="form-check form-check-custom form-check-sm form-check-solid pt-2" *ngIf="dField.fieldType === 6">
                            <input class="form-check-input" type="checkbox" [formControlName]="'field'+dField.id" />
                        </div>
                        <!-- Select -->
                        <select class="form-select form-select-sm" [formControlName]="'field'+dField.id" *ngIf="dField.fieldType == 7"
                                [ngClass]="{ 'is-invalid': form.controls['field'+dField.id]?.dirty && form.controls['field'+dField.id]?.invalid }">
                            <option value=""></option>
                            <option *ngFor="let val of dField.fieldTypeValues" [value]="val.id">{{ val.name }}</option>
                        </select>
                        <!-- SelectMultiple -->
                        <filter-ddl [id]="'field'+dField.id" class="filter-container" [multiple]="true" [showSearch]="false" [minWidth]="130"
                                    (change)="onMultiSelect($event)" [showAll]="false" *ngIf="dField.fieldType == 8"
                                    [options]="dField.fieldTypeValues" [placeholders]="['Option(s)', 'Selected']"
                                    [selectedValues]="f['field'+dField.id]?.value || []">
                        </filter-ddl>
                        <ng-container [ngTemplateOutlet]="formError" *ngIf="dField.isRequired"
                                      [ngTemplateOutletContext]="{ validation: 'required', message: 'Required field.',
                                        control: form.controls['field'+dField.id] }"></ng-container>
                    </div>
                </div>
            </form>
            <div class="modal-footer pb-0 px-0">
                <button type="button" class="btn btn-sm btn-primary" [disabled]="!file || form?.invalid || working"
                        *ngIf="editMode === 'upload'" (click)="save()">
                    <span class="indicator-label" *ngIf="!working; else btnSpinner">Upload</span>
                    <ng-template #btnSpinner>
                        <span class="indicator-progress" style="display: block">
                            Please wait...
                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                        </span>
                    </ng-template>
                </button>

                <button type="button" class="btn btn-sm btn-primary" [disabled]="form?.invalid || working"
                        *ngIf="editMode === 'edit'" (click)="save()">
                    <span class="indicator-label" *ngIf="!working; else btnSpinner">Save</span>
                    <ng-template #btnSpinner>
                        <span class="indicator-progress" style="display: block">
                            Please wait...
                            <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                        </span>
                    </ng-template>
                </button>
            </div>
        </div>
    </div>
    <!-- Documents -->
    <div class="mt-3 blockui" [ngClass]="{'d-none': editMode}">
        <aims-working *ngIf="modalConfig.working"></aims-working>
        <table id="table_documents" class="table table-sm table-hover fs-8 table-row-dashed align-middle">
            <thead>
                <tr class="text-start text-gray-900 fw-bold gs-0">
                    <th class="w-50px" data-orderable="false"></th>
                    <th class="d-none"></th>
                    <th>File name</th>
                    <th width="30%" [ngClass]="{'d-none': !fields?.length}">File information</th>
                    <th width="10%">Size</th>
                    <th width="3%">Approved?</th>
                    <th width="15%" class="text-end" data-orderable="false"></th>
                </tr>
            </thead>
            <tbody>
                <tr *ngFor="let doc of documents; let ind = index;">
                    <td class="text-center"></td>
                    <td class="d-none">{{ ind+1 }}</td>
                    <td><a class="text-primary" (click)="downloadFile(doc.fileName)">{{ doc.docName }}</a></td>
                    <td [ngClass]="{'d-none': !fields?.length}">
                        <ul class="p-0 m-0">
                            <li *ngFor="let f of fields"><span class="fw-semibold">{{ f.fieldName }}:</span> {{ getFieldValue(doc.fieldsWithValues, f.id) }}</li>
                        </ul>
                    </td>
                    <td>{{ doc.docSize | fileSize }}</td>
                    <td>
                        <span [inlineSVG]="'./assets/media/icons/duotune/general/gen026.svg'" class="svg-icon svg-icon-2 text-primary"
                              title="Approved on: {{doc.dateApproved | date:'medium'}}" *ngIf="doc.dateApproved"></span>
                    </td>
                    <td class="text-end fs-6">
                        <button class="btn btn-sm btn-icon h-30px btn-bg-light col-{{ doc.fileName | fileExtColor }}"
                                (click)="downloadFile(doc.fileName)" ngbTooltip="Download">
                            <i class="far {{ doc.fileName | fileExt }}"></i>
                        </button>
                        <ng-container *ngIf="!isCellEditable && (['ga'] | isAuth)">
                            <button class="btn btn-sm btn-icon w-30px h-30px btn-bg-light btn-active-color-primary ms-2" ngbTooltip="More Actions"
                                    data-qs-menu-trigger="click" data-qs-menu-placement="bottom-end" data-qs-menu-flip="top-end">
                                <i class="bi bi-three-dots fs-5"></i>
                            </button>
                            <!-- Dropdown menu -->
                            <div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown py-lg-1 w-lg-225px" data-qs-menu="true">
                                <ng-container *ngIf="doc.orgId > 0">
                                    <div class="menu-item menu-lg-down-accordion" *ngIf="!doc.dateApproved else voidApproval">
                                        <a class="menu-link py-3" (click)="toggleStatus(doc.id, doc.dateApproved)">
                                            <span class="menu-icon">
                                                <span [inlineSVG]="'./assets/media/icons/duotune/general/gen026.svg'" class="svg-icon svg-icon-3"></span>
                                            </span><span class="menu-title">Approve</span>
                                        </a>
                                    </div>
                                    <ng-template #voidApproval>
                                        <div class="menu-item menu-lg-down-accordion">
                                            <a class="menu-link py-3" (click)="toggleStatus(doc.id, doc.dateApproved)">
                                                <span class="menu-icon">
                                                    <span [inlineSVG]="'./assets/media/icons/duotune/general/gen026.svg'" class="svg-icon svg-icon-3 text-muted"></span>
                                                </span><span class="menu-title">Retreat Approval</span>
                                            </a>
                                        </div>
                                    </ng-template>
                                </ng-container>
                            </div>
                        </ng-container>
                        <ng-container *ngIf="isCellEditable && +fileTypeId > 0">
                            <button class="btn btn-sm btn-icon w-30px h-30px btn-bg-light btn-active-color-primary ms-2" ngbTooltip="Delete"
                                    *ngIf="!fileTypeId else moreMenu" (click)="onDelete(doc.id, doc.fileName)">
                                <span [inlineSVG]="'./assets/media/icons/duotune/general/trash.svg'" class="svg-icon svg-icon-3 text-danger"></span>
                            </button>
                            <ng-template #moreMenu>
                                <button class="btn btn-sm btn-icon w-30px h-30px btn-bg-light btn-active-color-primary ms-2" ngbTooltip="More Actions"
                                        data-qs-menu-trigger="click" data-qs-menu-placement="bottom-end" data-qs-menu-flip="top-end">
                                    <i class="bi bi-three-dots fs-5"></i>
                                </button>
                                <!-- Dropdown menu -->
                                <div class="menu-sub menu-sub-lg-down-accordion menu-sub-lg-dropdown py-lg-1 w-lg-225px" data-qs-menu="true">
                                    <div class="menu-item menu-lg-down-accordion" *ngIf="!doc.dateApproved">
                                        <a class="menu-link py-3" (click)="onEdit(doc)">
                                            <span class="menu-icon">
                                                <span [inlineSVG]="'./assets/media/icons/duotune/general/gen055.svg'" class="svg-icon svg-icon-3"></span>
                                            </span><span class="menu-title">Edit</span>
                                        </a>
                                    </div>
                                    <ng-container *ngIf="doc.orgId > 0 && (['ga'] | isAuth)">
                                        <div class="menu-item menu-lg-down-accordion" *ngIf="!doc.dateApproved else voidApproval">
                                            <a class="menu-link py-3" (click)="toggleStatus(doc.id, doc.dateApproved)">
                                                <span class="menu-icon">
                                                    <span [inlineSVG]="'./assets/media/icons/duotune/general/gen026.svg'" class="svg-icon svg-icon-3"></span>
                                                </span><span class="menu-title">Approve</span>
                                            </a>
                                        </div>
                                        <ng-template #voidApproval>
                                            <div class="menu-item menu-lg-down-accordion">
                                                <a class="menu-link py-3" (click)="toggleStatus(doc.id, doc.dateApproved)">
                                                    <span class="menu-icon">
                                                        <span [inlineSVG]="'./assets/media/icons/duotune/general/gen026.svg'" class="svg-icon svg-icon-3 text-muted"></span>
                                                    </span><span class="menu-title">Retreat Approval</span>
                                                </a>
                                            </div>
                                        </ng-template>
                                    </ng-container>
                                    <div class="menu-item separator"></div>
                                    <div class="menu-item menu-lg-down-accordion">
                                        <a class="menu-link py-3" (click)="onDelete(doc.id, doc.fileName)">
                                            <span class="menu-icon">
                                                <span [inlineSVG]="'./assets/media/icons/duotune/general/trash.svg'" class="svg-icon svg-icon-3 text-danger"></span>
                                            </span><span class="menu-title text-danger">Delete</span>
                                        </a>
                                    </div>
                                </div>
                            </ng-template>
                        </ng-container>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <ng-template #formError let-control="control" let-message="message" let-validation="validation">
        <ng-container *ngIf="control.hasError(validation) && control.dirty">
            <div class="fv-plugins-message-container">
                <div class="fv-help-block">
                    <span role="alert">{{ message }}</span>
                </div>
            </div>
        </ng-container>
    </ng-template>
</app-modal>