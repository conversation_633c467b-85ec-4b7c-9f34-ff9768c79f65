<!--begin::Chat-->
<div class="app-navbar-item" [ngClass]="itemClass" ngbTooltip="Comments Notifications">
    <div [ngClass]="btnClass" class="position-relative" data-qs-menu-trigger="click" data-qs-menu-attach="parent"
         data-qs-menu-placement="bottom-end">
        <span [inlineSVG]="'./assets/media/icons/duotune/communication/com012.svg'" class="svg-icon"
              [ngClass]="btnIconClass"></span>
        <span class="bullet bullet-dot bg-danger h-8px w-8px position-absolute"
              *ngIf="alerts?.length">
        </span>
    </div>
    <app-notifications-inner [items]="alerts" [loading]="loading"></app-notifications-inner>
</div>
<!--end::Chat-->

<!--begin::Theme mode-->
<div class="app-navbar-item" [ngClass]="itemClass">
    <app-theme-mode-switcher [toggleBtnClass]="btnClass"
                             toggleBtnClass="{`btn-active-light-primary btn-custom ${toolbarButtonHeightClass}`}">
    </app-theme-mode-switcher>
</div>
<!--end::Theme mode-->

<!--begin::User menu-->
<div class="app-navbar-item" [ngClass]="itemClass">
    <!--begin::Menu wrapper-->
    <div class="cursor-pointer symbol" [ngClass]="userAvatarClass"
         data-qs-menu-trigger="{default: 'click'}" ngbTooltip="User"
         data-qs-menu-attach="parent" data-qs-menu-placement="bottom-end">
        <img *ngIf="user$ | async as _user" alt="User picture" [src]="_user.pic || './assets/media/avatars/blank.png'" />
    </div>
    <app-user-inner data-qs-menu='true'></app-user-inner>
    <!--end::Menu wrapper-->
</div>
<!--end::User menu-->

<!--begin::Header menu toggle-->
<ng-container *ngIf="appHeaderDefaulMenuDisplay">
    <div class="app-navbar-item d-lg-none ms-2 me-n3" title="Show header menu">
        <div class="btn btn-icon btn-active-color-primary w-35px h-35px" id="qs_app_header_menu_toggle">
            <span [inlineSVG]="'./assets/media/icons/duotune/text/txt001.svg'" class="svg-icon"
                  [ngClass]="btnIconClass"></span>
        </div>
    </div>
</ng-container>
<!--end::Header menu toggle-->