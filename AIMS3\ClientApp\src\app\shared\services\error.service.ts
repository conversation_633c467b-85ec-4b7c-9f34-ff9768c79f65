import { Injectable } from '@angular/core';
import { AuthService } from '../../modules/auth';
import { MessageService } from './message.service';

@Injectable()
export class ErrorService {
    constructor(
        private authService: AuthService,
        private messageService: MessageService
    ) { }

    showMessage(data: any): void {
        switch (data.status) {
            case 204:
                this.handleNoContentError(data);
                break;
            case 401:
                this.handleUnauthorizedError(data);
                break;
            case 400:
                this.handleBadRequestError(data);
                break;
            case 404:
                this.handleBadRequestError(data);
                break;
            case 422:
                this.handleUnprocessableEntityError(data);
                break;
            case 403:
                this.handelAccessDenied(data);
                break;
            case 500:
                this.handleServerError(data);
                break;
            default:
                break;
        }
    }

    handleNoContentError(err: any) {
        this.messageService.error('No data received from the server.');
    }

    handleUnauthorizedError(err: any) {
        this.authService.logout();
        this.messageService.error('Your session is expired. Please login again.');
    }

    handleBadRequestError(err: any) {
        let errMsg = '';
        for (let key in err.error) {
            errMsg += `${err.error[key][0]}\n`;
        }

        console.error(errMsg);
        this.messageService.error(errMsg);
    }

    handleUnprocessableEntityError(err: any) {
        this.messageService.error('Something went wrong.');
    }

    handelAccessDenied(err: any) {
        this.messageService.error('Access denied to the resource.');
    }

    handleServerError(err: any) {
        this.messageService.error('Something went wrong on the server.');
    }
}