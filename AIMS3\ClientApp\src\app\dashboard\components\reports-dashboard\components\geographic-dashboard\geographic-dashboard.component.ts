import { Component, Input, OnChanges, OnInit, SimpleChanges, AfterViewInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { NgApexchartsModule } from 'ng-apexcharts';
import { 
  ReportData, 
  ReportFilter,
  Province,
  District
} from '../../models/reports.model';

declare var L: any; // For Leaflet map

@Component({
  selector: 'app-region-dashboard',
  standalone: true,
  imports: [CommonModule, NgApexchartsModule],
  templateUrl: './geographic-dashboard.component.html',
  styleUrls: ['./geographic-dashboard.component.scss']
})
export class RegionDashboardComponent implements OnInit, OnChanges, AfterViewInit {
  @Input() reportData: ReportData;
  @Input() filters: ReportFilter;
  
  // For template use
  Math = Math;
  
  // Chart options
  regionChartOptions: any;
  provinceChartOptions: any;
  
  // Map properties
  map: any;
  mapInitialized = false;
  mapMarkers: any[] = [];
  defaultLatitude = 34.5553; // Afghanistan center latitude
  defaultLongitude = 69.2075; // Afghanistan center longitude
  defaultZoom = 6;
  
  constructor() { }

  ngOnInit(): void {
    this.initializeCharts();
  }
  
  ngAfterViewInit(): void {
    this.initializeMap();
  }

  ngOnChanges(changes: SimpleChanges): void {
    if (changes.reportData?.currentValue) {
      this.initializeCharts();
      
      if (this.mapInitialized) {
        this.updateMapMarkers();
      }
    }
  }

  private initializeCharts(): void {
    this.initRegionChart();
    this.initProvinceChart();
  }
  
  private initRegionChart(): void {
    if (!this.reportData) return;
    
    // Mock data for demonstration - replace with actual data
    const regions = [
      { region: 'Central', value: 68 },
      { region: 'Northern', value: 42 },
      { region: 'Eastern', value: 56 },
      { region: 'Western', value: 35 },
      { region: 'Southern', value: 29 }
    ];
    
    this.regionChartOptions = {
      series: [{
        name: 'Activities',
        data: regions.map(r => r.value)
      }],
      chart: {
        type: 'bar',
        height: 350,
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          borderRadius: 4,
          horizontal: false,
          columnWidth: '60%'
        }
      },
      title: {
        text: 'Activities by Region',
        align: 'left'
      },
      colors: ['#4CAF50'],
      dataLabels: {
        enabled: false
      },
      xaxis: {
        categories: regions.map(r => r.region)
      },
      yaxis: {
        title: {
          text: 'Number of Activities'
        }
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val + ' activities';
          }
        }
      }
    };
  }
  
  private initProvinceChart(): void {
    if (!this.reportData) return;
    
    // Mock data for demonstration - replace with actual data
    const provinces = [
      { province: 'Kabul', value: 43 },
      { province: 'Herat', value: 32 },
      { province: 'Balkh', value: 27 },
      { province: 'Kandahar', value: 21 },
      { province: 'Nangarhar', value: 19 },
      { province: 'Paktia', value: 14 },
      { province: 'Bamyan', value: 12 }
    ];
    
    this.provinceChartOptions = {
      series: [{
        name: 'Activities',
        data: provinces.map(p => p.value)
      }],
      chart: {
        type: 'bar',
        height: 350,
        toolbar: {
          show: false
        }
      },
      plotOptions: {
        bar: {
          borderRadius: 4,
          horizontal: true
        }
      },
      title: {
        text: 'Top Provinces by Activities',
        align: 'left'
      },
      colors: ['#2196F3'],
      dataLabels: {
        enabled: false
      },
      xaxis: {
        categories: provinces.map(p => p.province)
      },
      yaxis: {
        title: {
          text: 'Number of Activities'
        }
      },
      tooltip: {
        y: {
          formatter: function (val) {
            return val + ' activities';
          }
        }
      }
    };
  }

  private initializeMap(): void {
    if (typeof L !== 'undefined') {
      // Create map instance
      this.map = L.map('geographic-map').setView([this.defaultLatitude, this.defaultLongitude], this.defaultZoom);
      
      // Add tile layer (OpenStreetMap)
      L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
        attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
      }).addTo(this.map);
      
      // Add markers for activities
      this.updateMapMarkers();
      
      this.mapInitialized = true;
    } else {
      console.error('Leaflet library not loaded.');
    }
  }

  private updateMapMarkers(): void {
    if (!this.map) return;
    
    // Clear existing markers
    this.mapMarkers.forEach(marker => this.map.removeLayer(marker));
    this.mapMarkers = [];
    
    // Mock activity locations - replace with actual data
    const activityLocations = [
      { lat: 34.5553, lng: 69.2075, name: 'Kabul Activities', status: 'ongoing', count: 15 },
      { lat: 36.7069, lng: 67.1122, name: 'Mazar-e-Sharif Activities', status: 'completed', count: 8 },
      { lat: 34.3380, lng: 62.2041, name: 'Herat Activities', status: 'planned', count: 5 },
      { lat: 31.6201, lng: 65.7160, name: 'Kandahar Activities', status: 'ongoing', count: 12 },
      { lat: 34.4300, lng: 70.4460, name: 'Jalalabad Activities', status: 'completed', count: 7 }
    ];
    
    // Add new markers
    activityLocations.forEach(location => {
      const markerColor = this.getMarkerColorByStatus(location.status);
      const markerSize = this.getMarkerSizeByCount(location.count);
      
      const marker = L.circleMarker([location.lat, location.lng], {
        radius: markerSize,
        fillColor: markerColor,
        color: '#fff',
        weight: 1,
        opacity: 1,
        fillOpacity: 0.8
      }).addTo(this.map);
      
      marker.bindPopup(`
        <div class="map-popup">
          <h5>${location.name}</h5>
          <p><strong>Status:</strong> ${this.capitalizeFirstLetter(location.status)}</p>
          <p><strong>Activities:</strong> ${location.count}</p>
        </div>
      `);
      
      this.mapMarkers.push(marker);
    });
  }
  
  private getMarkerColorByStatus(status: string): string {
    switch (status) {
      case 'completed': return '#4CAF50';
      case 'ongoing': return '#FFC107';
      case 'planned': return '#2196F3';
      default: return '#9E9E9E';
    }
  }
  
  private getMarkerSizeByCount(count: number): number {
    // Scale marker size based on activity count (min 7, max 15)
    return Math.max(7, Math.min(15, 7 + (count / 5)));
  }
  
  private capitalizeFirstLetter(text: string): string {
    return text.charAt(0).toUpperCase() + text.slice(1);
  }
  
  formatNumber(num: number): string {
    return num ? num.toLocaleString() : '0';
  }
} 