import { Component, OnInit } from '@angular/core';
import {
    CellClickedEvent, CellFocusedEvent, ColDef, ColGroupDef,
    ColumnApi, GridApi, GridOptions, GridReadyEvent,
    ICellRendererParams
} from 'ag-grid-community';
import 'ag-grid-enterprise';
import { LicenseManager } from 'ag-grid-enterprise';
import { environment } from '../../../../../environments/environment';
import { ActivityStatus, ColDataType, ColumnVarType, Region, TargetPeriod } from '../../../../shared/enums';
import { MessageService } from '../../../../shared/services/message.service';
import { getColWidth } from '../../../../shared/utilities';
import { SabaAttachmentRenderer } from '../../../aims-grid/controls/cell-renderers/attachment/attachment.control';
import { SabaCheckboxRenderer } from '../../../aims-grid/controls/cell-renderers/checkbox/checkbox.control';
import { SabaSelectValueRenderer } from '../../../aims-grid/controls/cell-renderers/select/select-value.control';
import { GridCustomHeader } from '../../../aims-grid/controls/grid-header/grid-header.control';
import { DynamicColumn } from '../../../aims-grid/models/dynamic-column.model';
import { GridService } from '../../../aims-grid/services/grid.service';
import { IProgressDataView, ITargetDataView } from '../../../data/models/data.model';

@Component({
    selector: 'aims-pivot-grid',
    templateUrl: './pivot-grid.component.html',
    styleUrls: ['./pivot-grid.component.scss']
})
export class PivotGridComponent implements OnInit {
    working: boolean = false;
    public isDarkMode: boolean = false;

    public gridApi: GridApi;
    public gridColumnApi: ColumnApi;

    public gridOptions: GridOptions;
    public columnDefs: (ColDef | ColGroupDef)[];
    public defaultColDefs: ColDef;
    public components = {
        'sabaCheckboxRenderer': SabaCheckboxRenderer,
        'sabaAttachmentRenderer': SabaAttachmentRenderer,
        'sabaSelectValRenderer': SabaSelectValueRenderer
    };
    public tooltipShowDelay: number = 500;
    private groupCollapsed = [[],[],[],[]];

    private gridColumns = {
        commonCols: [],
        progressActIdCol: null, // activity id
        progressStatusCols: [], // file, status, start, end months
        progressCommCol: null,  // community column
        progressAsOfCol: null,  // as of 
        targetInfoCols: [],     // year, qtr
        //hiddenCol: null         // hidden narrow col
    };

    // data from grid's parent
    dynamicColumns: DynamicColumn[] = [];

    // *** DATA -------------
    public rowData: IProgressDataView[] | ITargetDataView[] = [];
    public selectedVals = { isTarget: false, profId: 0, prof: '' };
    //hideAsOfCol: boolean = false;

    constructor(
        private gridService: GridService,
        private messageService: MessageService
    ) {
        if (document.querySelector('[data-theme="dark"'))
            this.isDarkMode = true;

        // AG-GRID
        LicenseManager.setLicenseKey(environment.agGridConfig.licenseKey);

        // set all defaults
        // default grid options
        this.gridOptions = {
            groupHeaderHeight: 25,
            headerHeight: 30,
            rowHeight: 20, rowDragManaged: true,
            rowSelection: 'multiple',
            enableRangeSelection: true, enableFillHandle: false,
            scrollbarWidth: 5,
            suppressRowClickSelection: true,
            groupDisplayType: 'groupRows',
            groupDefaultExpanded: -1,
            groupIncludeFooter: true,
            groupMaintainOrder: true,
            groupAllowUnbalanced: true, sideBar: ['columns', 'filters'],
            groupIncludeTotalFooter: true,
            pivotRowTotals: 'after',
            pivotColumnGroupTotals: 'after',
            autoGroupColumnDef: { headerName: 'Group' },
            onColumnRowGroupChanged: (params) => this.onRowGroupChanged(),
            //processSecondaryColDef: (colDef) => {
            //    console.log(colDef .headerName, 'secondary')
            //},
            //processPivotResultColDef: (colDef) => {
            //    console.log(colDef.pivotValueColumn.getDefinition().headerName)
            //},
            onColumnValueChanged: (params) => { params.api.refreshHeader() },
            processCellForClipboard: (params) => this.gridService.processCellForClipboard(params)
        };

        // default column configurations
        this.defaultColDefs = {
            sortable: false,
            resizable: true,
            filter: true,
            editable: false,
            minWidth: 50,
            suppressFillHandle: true,
            lockVisible: true,
            enablePivot: true,
            enableRowGroup: true,
            enableValue: true,
            menuTabs: ['filterMenuTab', 'generalMenuTab'],
            comparator: this.gridService.getComparator,
            //headerValueGetter: (params) => {
            //    let colHeader = params.colDef.headerName;
            //    //if (params.columnApi.isPivotMode()) {
            //    //    params.
            //        return colHeader + '@';
            //    //}
            //    //return colHeader;
            //}
        };
    }

    // init the default readonly pivot grid
    ngOnInit() {
        try {
            // add default common column definitions
            this.columnDefs = [{        // row num
                colId: 'rowNum',
                headerName: '',
                minWidth: 50, width: 50, maxWidth: 100,
                filter: false, sortable: false,
                pinned: 'left',
                enableRowGroup: false, enablePivot: false, enableValue: false,
                suppressMovable: true,
                suppressMenu: true, lockPosition: true,
                onCellClicked: this.onCellClicked,
                cellRenderer: (params: ICellRendererParams) => {
                    return params.node.footer ? (params.node.level === -1 ? 'GTotal' : 'Total')
                        : params.node.rowIndex + 1;
                }
            }];
            /*    {  // Project
                colId: 'project',
                field: 'project',
                headerName: 'Project', cellClass: 'fs-7',
                enableRowGroup: true,
                pinned: true,
                width: 100
            }, {  // IP
                colId: 'partner',
                field: 'partner',
                headerName: 'IP', cellClass: 'fs-7',
                enableRowGroup: true,
                pinned: true,
                width: 70,
                hide: !this.isGlobalUser
            }];*/

            // progress first few columns
            this.gridColumns.progressActIdCol = {  // activity id
                field: 'activityId',
                headerName: 'Activity ID',
                headerClass: 'required',
                enableRowGroup: false, enablePivot: false, enableValue: false,
                pinned: true, lockPinned: true,
                width: 110,
                cellClass: 'fs-7',
                suppressMovable: true
            };

            this.gridColumns.progressStatusCols = [{   // file
                field: 'file', colId: 'file',
                headerName: 'File', headerClass: 'bg-header-color-1',
                headerTooltip: 'All files linked to an activity.',
                headerComponent: GridCustomHeader,
                headerComponentParams: { colType: ColDataType.Attachment, showInfoIcon: true },
                //cellRenderer: 'sabaAttachmentRenderer',
                enableRowGroup: false, enablePivot: false, enableValue: true,
                width: 80, columnGroupShow: 'open', comparator: this.gridService.getNumComparator,
                sortable: false,
                suppressMenu: true, suppressMovable: true
            },
            {   // status
                colId: 'status',
                field: 'status',
                headerName: 'Status', headerClass: 'bg-header-color-1',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: true,
                    colType: ColDataType.SelectSingle
                },
                width: 110, suppressMovable: true,
                enableValue: false,
                valueGetter: (params) => ActivityStatus[params.data?.status || 0]
            },
            {  // sMonth: Not required
                colId: 'sMonth',
                field: 'sMonth',
                headerName: 'Start date', headerClass: 'bg-header-color-1',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: true,
                    colType: ColDataType.Date  // MMM/yyyy
                },
                enableValue: false,
                valueFormatter: this.gridService.getValueFormatter,
                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                lockPinned: true, comparator: this.gridService.getDateComparator,
                width: 130, suppressMovable: true, columnGroupShow: 'open'
            },
            {   // eMonth: Not always required
                colId: 'eMonth',
                field: 'eMonth',
                headerName: 'End date', headerClass: 'bg-header-color-1',
                headerTooltip: 'The date when the implementation of the activity has been completed.',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: false,
                    colType: ColDataType.Date,  // MMM/yyyy
                    showInfoIcon: true
                },
                enableValue: false,
                valueFormatter: this.gridService.getValueFormatter,
                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                lockPinned: true, comparator: this.gridService.getDateComparator,
                width: 130, suppressMovable: true, columnGroupShow: 'open'
            }];

            // info static column used for group collapse
            /*this.gridColumns.hiddenCol = {
                colId: 'cols-hidden',
                headerName: '...',
                headerTooltip: 'Column(s) hidden. Click group handle to expand.',
                cellClass: 'non-selectable-cell',
                lockPinned: true,
                width: 28,
                minWidth: 28,
                maxWidth: 28,
                resizable: false,
                filter: false,
                suppressMenu: true,
                sortable: false,
                suppressSizeToFit: true,
                suppressAutoSize: true,
                lockVisible: true, enableRowGroup: false,
                columnGroupShow: 'closed'
            };*/

            // location columns
            this.gridColumns.commonCols = [{  // region
                field: 'region',
                headerName: 'Region', headerClass: 'bg-header-color-2',
                headerComponent: GridCustomHeader,
                width: 130,
                enableValue: false,
                valueGetter: (params) => {
                    const regions = params.data?.region?.split(',') || [];
                    let result = [];
                    regions.forEach(r => result.push(Region[r]));
                    return result.join(',');
                },
                suppressMovable: true, columnGroupShow: 'open'
            },
            {   // province: always required
                field: 'province',
                headerName: 'Province', headerClass: 'bg-header-color-2',
                headerComponent: GridCustomHeader,
                width: 140, enableValue: false,
                columnGroupShow: 'open'
            },
            {   // district: always required
                field: 'district',
                headerName: 'District', headerClass: 'bg-header-color-2',
                headerComponent: GridCustomHeader,
                width: 145, enableValue: false,
                suppressMovable: true, columnGroupShow: 'open'
            }];

            // progress community column
            this.gridColumns.progressCommCol = {   // community
                field: 'community',
                headerName: 'Community', headerClass: 'bg-header-color-2',
                headerComponent: GridCustomHeader,
                width: 170, suppressMovable: true, enableValue: false,
                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter }
            };

            // progress static columns
            this.gridColumns.progressAsOfCol = {  // as of
                colId: 'asOf',
                field: 'asOf',
                headerName: 'As of', headerTooltip: 'As of the end of this month.', headerClass: 'bg-header-color-4',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: true,
                    colType: ColDataType.Date,  // MMM/yyyy
                    showInfoIcon: true
                },
                enableValue: false,
                valueFormatter: this.gridService.asOfValueFormatter,
                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                comparator: this.gridService.getDateComparator,
                width: 110, suppressMovable: true
            };

            // target info static columns
            let years = [{ id: new Date().getFullYear(), name: new Date().getFullYear().toString() }];
            for (let i = 1; i <= 3; i++)
                years.push({ id: years[0].id + i, name: `${years[0].id + i}` });

            this.gridColumns.targetInfoCols = [{    // year
                colId: 'year',
                field: 'year',
                headerName: 'Year', headerClass: 'bg-header-color-3',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: true,
                    colType: ColDataType.SelectSingle,
                    showInfoIcon: false
                },
                cellEditorParams: {
                    values: [{ id: 0, name: 'All years' }, ...years]
                },
                cellRenderer: 'sabaSelectValRenderer',
                enableValue: false,
                valueFormatter: this.gridService.getValueFormatter,
                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                comparator: this.gridService.getNumComparator,
                width: 95, suppressMovable: true
            },
            {   // quarter
                coldId: 'qtr',
                field: 'qtr',
                headerName: 'Qtr', headerClass: 'bg-header-color-3',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: true,
                    colType: ColDataType.SelectSingle,
                    showInfoIcon: false
                },
                enableValue: false,
                valueGetter: (params) => TargetPeriod[params.data?.qtr || 1],
                //cellEditorParams: {
                //    values: [
                //        { id: 1, name: 'Qtr-1' },
                //        { id: 2, name: 'Qtr-2' },
                //        { id: 3, name: 'Qtr-3' },
                //        { id: 4, name: 'Qtr-4' }
                //    ]
                //},
                //cellRenderer: 'sabaSelectValRenderer',
                //valueFormatter: this.gridService.getValueFormatter,
                //filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                columnGroupShow: 'open',
                lockPinned: true,
                width: 90, suppressMovable: true
            }];

            // push some empty data
            for (let i = 1; i <= 20; i++)
                this.rowData.push({ id: 0, colVals: {}} as IProgressDataView);
        } catch (ex) {
            console.error(ex);
            this.messageService.error('Something went wrong.');
        }
    }

    // init as per profile and project selected
    initGrid(): void {
        try {
            // by reaching this, the gridColumns are already asssigned to by the parent
            this.working = true;

            // reset/refresh grid: keep the first cols
            //if (this.hideAsOfCol)
            //    this.gridColumns.progressAsOfCol.hide = true;
            //else
            //    this.gridColumns.progressAsOfCol.hide = false;

            this.columnDefs = [this.columnDefs[0]];
            this.gridApi.setColumnDefs(this.columnDefs);

            // create columns
            if (!this.selectedVals.isTarget) {
                this.columnDefs = this.columnDefs.concat(this.gridColumns.progressActIdCol);

                // status group and columns
                this.gridColumns.commonCols[2].columnGroupShow = 'open';
                //this.gridColumns.hiddenCol.headerClass = 'bg-header-color-1';
                this.columnDefs.push({
                    groupId: 'ProgressStatus',// headerTooltip: 'Double click to expand or collapse this section.',
                    headerName: 'Status', headerClass: 'bg-header-group-color-1',
                    suppressMovable: true,
                    //marryChildren: true,
                    openByDefault: true, //!this.groupCollapsed[0].includes(this.selectedVals.profId),
                    children: [...this.gridColumns.progressStatusCols]//, this.gridColumns.hiddenCol]
                });

                // location cols
                //let hiddenCol = Object.assign({}, this.gridColumns.hiddenCol)
                //hiddenCol.headerClass = 'bg-header-color-2';
                this.columnDefs.push({
                    groupId: `${ColumnVarType.ProgressStatic}`, //headerTooltip: 'Double click to expand or collapse this section.',
                    headerName: 'Location', headerClass: 'bg-header-group-color-2',
                    suppressMovable: true,
                    //marryChildren: true,
                    openByDefault: true, //!this.groupCollapsed[1].includes(this.selectedVals.profId),
                    children: [...this.gridColumns.commonCols, this.gridColumns.progressCommCol]//, hiddenCol]
                });

                // add info and dynamic cols
                let infoCols = [];
                let progCols = [];
                this.dynamicColumns.forEach(col => {
                    // update static cols props
                    if (col.type === ColumnVarType.ProgressStatic) {
                        let staticCol = this.gridColumns.commonCols.concat(this.gridColumns.progressCommCol)
                            .find(sc => sc.field === col.name.toLowerCase());
                        if (staticCol) {
                            if (staticCol.field === 'community')
                                this.gridColumns.progressCommCol.colId = col.id;
                            staticCol.colId = col.id;
                            staticCol.headerName = col.displayName || col.name;
                            staticCol.headerTooltip = col.description;
                            staticCol.headerComponentParams = {
                                enableEdit: false,
                                isRequired: col.isRequired,
                                colType: col.fieldType,
                                showInfoIcon: col.description?.length > 0
                            }
                        }
                        return;
                    }

                    // add dynamic column
                    let colDef: ColDef = {
                        colId: `${col.id}`,
                        field: 'colVals.dCol' + col.id,
                        headerName: col.displayName || col.name,
                        headerTooltip: col.description,
                        headerComponent: GridCustomHeader,
                        headerComponentParams: {
                            enableEdit: false,
                            isRequired: col.isRequired,
                            type: col.type,
                            colType: col.fieldType,
                            colOrder: col.order,
                            showInfoIcon: col.description?.length > 0,
                            pivot: true
                        },
                        cellEditorParams: this.gridService.getCellEditorParams(col),
                        valueFormatter: this.gridService.getValueFormatter,
                        cellRenderer: this.gridService.getCellRenderer(col.fieldType),
                        cellClassRules: {
                            'text-end': this.gridService.getCellTextAlign, 'text-danger': this.gridService.getCellClassRules
                        }, columnGroupShow: 'open',
                        enableRowGroup: this.gridService.getEnableGrouping(col.fieldType),
                        enableValue: this.gridService.getEnableGrouping(col.fieldType), //aggFunc: this.gridService.getAggFunc(col.fieldType),
                        lockPinned: true,
                        width: getColWidth(col.name + (col.isRequired ? '*':''), [false, col.description?.length > 0])
                    };

                    // attach number comparator for attachment
                    if (col.fieldType === ColDataType.Attachment)
                        colDef.comparator = this.gridService.getNumComparator;

                    // attach num and date comparators
                    if (col.fieldType >= ColDataType.Number && col.fieldType <= ColDataType.GPS) {
                        colDef.comparator = this.gridService.getNumComparator;
                        colDef.valueGetter = (params) => this.gridService.getNumericValue(col.id, params);
                        colDef.valueParser = (params) => parseFloat(params.newValue);
                    }

                    if (col.fieldType === ColDataType.Date) {
                        colDef.filterParams = { valueFormatter: this.gridService.getFilterValueFormatter };
                        colDef.comparator = this.gridService.getDateComparator;
                    }

                    if (col.type === ColumnVarType.ProgressInfo) {
                        colDef.headerClass = 'bg-header-color-3';
                        infoCols.push(colDef);
                    } else if (col.type === ColumnVarType.Progress) {
                        colDef.headerClass = 'bg-header-color-4';
                        progCols.push(colDef);
                    }
                });

                // sort dynamic cols by order and push to grid's columns
                infoCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);
                progCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);

                // make first col always visible in the group
                if (infoCols.length)
                    infoCols[0].columnGroupShow = null;

                if (progCols.length)
                    progCols[0].columnGroupShow = null;

                //hiddenCol = Object.assign({}, this.gridColumns.hiddenCol)
                //hiddenCol.headerClass = 'bg-header-color-3';
                this.columnDefs.push({
                    groupId: `${ColumnVarType.ProgressInfo}`,
                    headerName: 'Info', headerClass: 'bg-header-group-color-3',
                    //headerTooltip: 'Double click to expand or collapse this section.',
                    //marryChildren: true,
                    openByDefault: true, //!this.groupCollapsed[2].includes(this.selectedVals.profId),
                    children: [...infoCols] //infoCols.length > 1 ? [...infoCols, hiddenCol] : [...infoCols]
                });

                //hiddenCol = Object.assign({}, this.gridColumns.hiddenCol)
                //hiddenCol.headerClass = 'bg-header-color-4';
                this.columnDefs.push({
                    groupId: `${ColumnVarType.Progress}`,
                    headerName: 'Cumulative progress', headerClass: 'bg-header-group-color-4',
                    //headerTooltip: 'Double click to expand or collapse this section.',
                    //marryChildren: true,
                    openByDefault: !this.groupCollapsed[3].includes(this.selectedVals.profId),
                    children: [this.gridColumns.progressAsOfCol, ...progCols]//progCols.length > 1 ? [this.gridColumns.progressAsOfCol, ...progCols, hiddenCol] : --
                });
                // ---------------------------------------------------------------
            } else {      // target
                // ---------------------------------------------------------------
                // location cols
                this.gridColumns.commonCols[2].columnGroupShow = null;
                //this.gridColumns.hiddenCol.headerClass = 'bg-header-color-2';
                this.columnDefs.push({
                    groupId: `${ColumnVarType.TargetStatic}`, //headerTooltip: 'Double click to expand or collapse this section.',
                    headerName: 'Location', headerClass: 'bg-header-group-color-2',
                    suppressMovable: true,
                    //marryChildren: true,
                    openByDefault: !this.groupCollapsed[1].includes(this.selectedVals.profId),
                    children: [...this.gridColumns.commonCols]//, this.gridColumns.hiddenCol]
                });

                // add info and dynamic cols for target
                let infoCols = [];
                let targetCols = [];
                this.dynamicColumns.forEach(col => {
                    // update static cols props
                    if (col.type === ColumnVarType.TargetStatic) {
                        let staticCol = this.gridColumns.commonCols.find(sc => sc.field === col.name.toLowerCase());
                        if (staticCol) {
                            staticCol.colId = col.id;
                            staticCol.headerName = col.displayName || col.name;
                            staticCol.headerTooltip = col.description;
                            staticCol.headerComponentParams = {
                                enableEdit: false,
                                isRequired: col.isRequired,
                                colType: col.fieldType,
                                showInfoIcon: col.description?.length > 0
                            }
                        }
                        return;
                    }

                    // add dynamic column
                    let colDef: ColDef = {
                        colId: `${col.id}`,
                        field: 'colVals.dCol' + col.id,
                        headerName: col.displayName || col.name,
                        headerTooltip: col.description,
                        headerComponent: GridCustomHeader,
                        headerComponentParams: {
                            enableEdit: false,
                            isRequired: col.isRequired,
                            type: col.type,
                            colType: col.fieldType,
                            colOrder: col.order,
                            showInfoIcon: col.description?.length > 0,
                            pivot: true
                        },
                        cellEditorParams: this.gridService.getCellEditorParams(col),
                        valueFormatter: this.gridService.getValueFormatter, // add formatting for col if set
                        cellRenderer: this.gridService.getCellRenderer(col.fieldType),
                        cellClassRules: { 'text-end': this.gridService.getCellTextAlign, 'text-danger': this.gridService.getCellClassRules },
                        enableRowGroup: this.gridService.getEnableGrouping(col.fieldType),
                        enableValue: this.gridService.getEnableGrouping(col.fieldType),
                        //aggFunc: this.gridService.getAggFunc(col.fieldType),
                        width: getColWidth(col.name + (col.isRequired ? '*' : ''), [false, col.description?.length > 0]),
                        lockPinned: true, columnGroupShow: 'open'
                    };

                    // attach comparator for attachment columns
                    if (col.fieldType === ColDataType.Attachment)
                        colDef.comparator = this.gridService.getNumComparator;

                    // attach num and date comparators
                    if (col.fieldType >= ColDataType.Number && col.fieldType <= ColDataType.GPS) {
                        colDef.comparator = this.gridService.getNumComparator;
                        colDef.valueGetter = (params) => this.gridService.getNumericValue(col.id, params);
                        colDef.valueParser = (params) => parseFloat(params.newValue);
                    }

                    if (col.fieldType === ColDataType.Date) {
                        colDef.filterParams = { valueFormatter: this.gridService.getFilterValueFormatter };
                        colDef.comparator = this.gridService.getDateComparator;
                    }

                    if (col.type === ColumnVarType.TargetInfo) {
                        colDef.headerClass = 'bg-header-color-3';
                        infoCols.push(colDef);
                    } else if (col.type === ColumnVarType.Target) {
                        colDef.headerClass = 'bg-header-color-4';
                        targetCols.push(colDef);
                    }
                });

                // sort dynamic cols by order and push to grid's columns
                infoCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);
                targetCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);

                // make first col always visible in the group
                if (targetCols.length)
                    targetCols[0].columnGroupShow = null;

                // if dynamic info cols are there and info group is collapsed
                //if (infoCols.length && this.groupCollapsed[2])
                //    this.gridColumns.hiddenCol.hide = false;

                //let hiddenCol = Object.assign({}, this.gridColumns.hiddenCol)
                //hiddenCol.headerClass = 'bg-header-color-3';
                this.columnDefs.push({
                    groupId: `${ColumnVarType.TargetInfo}`, //headerTooltip: 'Double click to expand or collapse this section.',
                    headerName: 'Info', headerClass: 'bg-header-group-color-3',
                    //marryChildren: true,
                    openByDefault: true, // !this.groupCollapsed[2].includes(this.selectedVals.profId),
                    children: [...this.gridColumns.targetInfoCols, /*hiddenCol,*/ ...infoCols]
                });

                //hiddenCol = Object.assign({}, this.gridColumns.hiddenCol)
                //hiddenCol.headerClass = 'bg-header-color-4';
                this.columnDefs.push({
                    groupId: `${ColumnVarType.Target}`,
                    headerName: 'Target', headerClass: 'bg-header-group-color-4',
                    //headerTooltip: 'Double click to expand or collapse this section.',
                    //marryChildren: true,
                    openByDefault: true,// !this.groupCollapsed[3].includes(this.selectedVals.profId),
                    children: [...targetCols] //targetCols.length > 1 ? [...targetCols, hiddenCol] : [...targetCols]
                });
            }
            
            this.gridReady();
            this.working = false;
        } catch (ex) {
            console.error(ex);
            this.messageService.error('Something went wrong.');
        } 
    }

    // init'ed and ready
    onGridReady(params: GridReadyEvent) {
        this.gridApi = params.api;
        this.gridColumnApi = params.columnApi;
    }

    private gridReady(): void {
        setTimeout(() => {
            // register click event on top left header cell
            const headerCell = document.querySelector('#aims-pivot-grid .ag-header-cell[col-id="rowNum"]');
            if (headerCell) {
                headerCell.addEventListener('click', (e) => {
                    this.gridApi.selectAllOnCurrentPage();
                });
            }

            // scroll grid even if cursor is outide the viewport (i.e. in header)
            const gridHeader = document.querySelector('#aims-pivot-grid .ag-header-viewport');
            const gridViewPort = document.querySelector('#aims-pivot-grid .ag-body-viewport');
            const gridViewPortCenter = document.querySelector('#aims-pivot-grid .ag-center-cols-viewport');

            gridHeader.addEventListener('wheel', (e: any) => {
                e.preventDefault();
                const deltaY = e.deltaY || e.wheelDeltaY;
                const deltaX = e.deltaX || e.wheelDeltaX;
                gridViewPort.scrollTop += deltaY;
                gridViewPortCenter.scrollLeft += deltaX;
            });

            // autosize cols
            this.gridColumnApi.autoSizeAllColumns();

            this.setExcelExportParams();
        }, 500);
    }

    // *** Other grid's functions --------------------------------
    onCellClicked(event: CellClickedEvent): void {
        const colId = event.column.getColId();
        if (colId === 'rowNum') {                   // if the row number column was clicked
            const cols = event.columnApi.getColumns().map(c => c.getColId());
            let lastVisibleCol = cols[cols.length - 1];
            if (lastVisibleCol.startsWith('cols-hidden')) {
                for (let i = cols.length - 2; i >= 0; i--) {
                    if (!cols[i].startsWith('cols-hidden')) {
                        lastVisibleCol = cols[i];
                        break;
                    }
                }
            }
            event.api.clearRangeSelection();
            event.api.addCellRange({
                columnStart: cols[2],               // start from column after rowNum and approval status
                columnEnd: lastVisibleCol,
                rowStartIndex: event.rowIndex,
                rowEndIndex: event.rowIndex
            });
        }
    }

    onCellFocused(event: CellFocusedEvent): void {
        this.gridApi.deselectAll();
    }

    onRowGroupChanged() {
        if (!this.gridColumnApi)
            return;
        
        const rowGroupColumns = this.gridColumnApi.getRowGroupColumns();
        
        if (rowGroupColumns.length > 0) {
            const firstRowGroupColumn = rowGroupColumns[0];

            this.gridOptions.autoGroupColumnDef.headerName =
                firstRowGroupColumn.getColDef().headerName;
        }

        this.gridApi.setAutoGroupColumnDef(this.gridOptions.autoGroupColumnDef);
        this.gridApi.refreshHeader();
    }
    // -----------------------------------------------------------

    // *** DATA
    refreshGridRows(data: IProgressDataView[] | ITargetDataView[]): void {
        this.gridApi.setDefaultColDef(this.defaultColDefs);
        this.rowData = [...data];
    }

    private setExcelExportParams(): void {
        let fName = 'AIMS3_' + this.selectedVals.prof;
        fName += this.selectedVals.isTarget ? '_Target.xlsx' : '_Progress.xlsx';
        let cols = this.gridColumnApi.getColumns().map(c => c.getColId());
        cols = cols.filter(c => !c.startsWith('cols-hidden'));

        // set excel export params
        this.gridOptions.defaultExcelExportParams = {
            author: 'AIMS 3.0', fileName: fName, allColumns: true,
            sheetName: this.selectedVals.prof.replace(/[<>:"\/\\|?*]+/g, ''),
            columnKeys: cols
        };
    }
}