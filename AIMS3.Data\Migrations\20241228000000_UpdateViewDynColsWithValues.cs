using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace AIMS3.Data.Migrations
{
    /// <inheritdoc />
    public partial class UpdateViewDynColsWithValues : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Drop existing view if it exists
            migrationBuilder.Sql("IF OBJECT_ID('vDynColsWithValues', 'V') IS NOT NULL DROP VIEW vDynColsWithValues");
            
            // Create updated view that properly unions both data sources
            migrationBuilder.Sql(@"
                CREATE VIEW vDynColsWithValues AS
                SELECT 
                    dcv.ActivityId,
                    NULL as ProgressId,
                    dcv.DynamicColumnId,
                    dcv.Value,
                    dc.InterventionProfileId,
                    dc.Type,
                    dc.FieldType
                FROM DynamicColumnsValues dcv
                INNER JOIN DynamicColumns dc ON dcv.DynamicColumnId = dc.Id
                WHERE dcv.ActivityId IS NOT NULL
                
                UNION ALL
                
                SELECT 
                    ap.ActivityId,
                    ap.Id as ProgressId,
                    dcpv.DynamicColumnId,
                    dcpv.Value,
                    dc.InterventionProfileId,
                    dc.Type,
                    dc.FieldType
                FROM DynColActProgValues dcpv
                INNER JOIN ActivitiesProgress ap ON dcpv.ActivityProgressId = ap.Id
                INNER JOIN DynamicColumns dc ON dcpv.DynamicColumnId = dc.Id
                
                UNION ALL
                
                SELECT 
                    NULL as ActivityId,
                    NULL as ProgressId,
                    dcv.DynamicColumnId,
                    dcv.Value,
                    dc.InterventionProfileId,
                    dc.Type,
                    dc.FieldType
                FROM DynamicColumnsValues dcv
                INNER JOIN DynamicColumns dc ON dcv.DynamicColumnId = dc.Id
                WHERE dcv.TargetId IS NOT NULL
            ");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // Drop the updated view
            migrationBuilder.Sql("IF OBJECT_ID('vDynColsWithValues', 'V') IS NOT NULL DROP VIEW vDynColsWithValues");
            
            // Restore original view (you may need to adjust this based on the original definition)
            migrationBuilder.Sql(@"
                CREATE VIEW vDynColsWithValues AS
                SELECT 
                    dcv.ActivityId,
                    NULL as ProgressId,
                    dcv.DynamicColumnId,
                    dcv.Value,
                    dc.InterventionProfileId,
                    dc.Type,
                    dc.FieldType
                FROM DynamicColumnsValues dcv
                INNER JOIN DynamicColumns dc ON dcv.DynamicColumnId = dc.Id
            ");
        }
    }
}
