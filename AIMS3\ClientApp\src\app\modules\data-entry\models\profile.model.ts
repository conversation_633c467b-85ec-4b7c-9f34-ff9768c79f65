import { Category } from "../../admin/models/category.model";
import { DynamicColumn } from "../../aims-grid/models/dynamic-column.model";

export interface IProfile {
    id: number;
    categoryId: number;
    name: string;
}

export class Profile implements IProfile {
    constructor(
        public id: number,
        public categoryId: number,
        public name: string,
        public abbreviation: string,
        public description?: string
    ) { }
}

export class ProfileInfo implements IProfile {
    constructor(
        public id: number,
        public categoryId: number,
        public category: Category,
        public name: string,
        public abbreviation: string,
        public description?: string,
        public variables?: DynamicColumn[]
    ) { }
}