import { TargetPeriod } from "../../../shared/enums";
import { ICommentInfo } from "../../comments/models/comment.model";

export interface IProgressData {
    id: number;             // activityId
    progressId?: number;    // activityProgressId
    activityId: string;     // uniqueId
    file?: number;
    status?: number;
    sMonth?: string;
    eMonth?: string;
    region?: string;
    province?: string;
    district?: string;
    community?: string;
    asOf?: string;
    colVals?: any;
    markedSubmitted: boolean;
    dateSubmitted?: Date;
    dateApproved?: Date;
    dirty: boolean;
    draft: boolean;         // for progress column prev. values
    comments?: ICommentInfo[]        // change to comment model
}

export interface IActivityAdded {
    id: number;         // new id from server
    activityId: string;
}

export interface ITargetData {
    id: number;
    uniqueId: string;
    region?: string;
    province?: string;
    district?: string;
    year?: number;
    qtr?: TargetPeriod;
    colVals?: any;
    markedSubmitted: boolean;
    dateSubmitted?: Date;
    dateApproved?: Date;
    dirty: boolean;
    comments?: any[]        // change to comment model
}

export interface ITargetAdded {
    id: number;         // new id from server
    uniqueId: string;   // client-side unique id
}