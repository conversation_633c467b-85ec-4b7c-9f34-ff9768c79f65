import { Component, OnInit } from '@angular/core';
import { DashboardService } from '../../../services/dashboard.service';

@Component({
  selector: 'app-category-statistics',
  template: `
    <div class="row">
      <div class="col-md-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">Total Outputs</h5>
            <h2 class="card-text">{{ statistics?.TotalOutputs || 0 }}</h2>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">Total Categories</h5>
            <h2 class="card-text">{{ statistics?.TotalCategories || 0 }}</h2>
          </div>
        </div>
      </div>
      <div class="col-md-4">
        <div class="card">
          <div class="card-body">
            <h5 class="card-title">Total Beneficiaries</h5>
            <h2 class="card-text">{{ statistics?.TotalBeneficiaries || 0 }}</h2>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [`
    .card {
      margin-bottom: 20px;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .card-title {
      color: #666;
      font-size: 1.1rem;
      margin-bottom: 10px;
    }
    .card-text {
      color: #333;
      font-size: 2rem;
      font-weight: bold;
    }
  `]
})
export class CategoryStatisticsComponent implements OnInit {
  statistics: any;

  constructor(private dashboardService: DashboardService) {}

  ngOnInit() {
    this.loadStatistics();
  }

  loadStatistics() {
    this.dashboardService.getCategoryStatistics().subscribe(
      (data) => {
        this.statistics = data;
      },
      (error) => {
        console.error('Error loading category statistics:', error);
      }
    );
  }
} 