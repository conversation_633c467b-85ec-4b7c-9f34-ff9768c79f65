import { Project } from "../../admin/models/project.model";
import { OrgInfo } from "../../auth/models/org-info.model";
import { ProfileInfo } from "./profile.model";

export interface IInventory {
    id: number;
    interventionProfileId: number;
    organizationId: number;
    projectId: number;
}

export class Inventory implements IInventory {
    constructor(
        public id: number,
        public interventionProfileId: number,
        public organizationId: number,
        public projectId: number,
        public details?: string,
        public isActive: boolean = false
    ) { }
}

export class InventoryList implements IInventory {
    constructor(
        public id: number,
        public interventionProfileId: number,
        public profile: ProfileInfo,
        public organizationId: number,
        public organization: OrgInfo,
        public projectId: number,
        public project: Project,
        public details?: string,
        public isActive: boolean = false
    ) { }
}

export class InvFilter {
    status?: boolean;
    orgIds?: number[];
    outputs?: string[];
    catIds?: number[];
    profIds?: number[];
    projIds?: number[];
}