import { ChangeDete<PERSON><PERSON><PERSON>, <PERSON>mpo<PERSON>, <PERSON>E<PERSON>ter, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { CellClickedEvent } from 'ag-grid-community';
import { lastValueFrom, Subject, Subscription } from 'rxjs';
import { FilterDropdownList } from '../../../../shared/components/filter-dropdown/filter-ddl.control';
import { ColDataType } from '../../../../shared/enums';
import { MessageService } from '../../../../shared/services/message.service';
import { SharedService } from '../../../../shared/services/shared.service';
import { UploadService } from '../../../../shared/services/upload.service';
import { MenuComponent } from '../../../../_theme/core/components';
import { ModalComponent, ModalConfig } from '../../../../_theme/partials';
import { DocType, DocTypeField } from '../../../library/models/doc-type.model';
import { Doc, FieldValue, IDoc } from '../../../library/models/document.model';
import { DocumentService } from '../../../library/services/document.service';

@Component({
    selector: 'attachments-modal',
    templateUrl: './attachments.component.html',
    styleUrls: ['./attachments.component.scss']
})
export class AttachmentsFormComponent implements OnInit, OnDestroy {
    working: boolean = false;
    isCellEditable: boolean = false;

    editMode: 'upload' | 'edit' | null;
    form: FormGroup;
    documents: IDoc[] = [];
    file: File;
    uploadFile: any;

    recordId: number;
    isTarget: boolean = false;
    colId: number;

    fileTypes: DocType[] = [];
    attachment: Doc;

    @ViewChild('modal') private modalComponent: ModalComponent;
    modalConfig: ModalConfig = {
        modalTitle: 'Attachments',
        cancelButtonLabel: 'Close',
        hideDoneButton: true,
        disableDoneButton: true,
        options: { size: 'lg' },
        shouldDo: () => this.save(),
        shouldCancel: () => { this.ngOnDestroy(); return true; }
    };
    @Output() done = new EventEmitter();

    subscriptions: Subscription[] = [];
    constructor(
        private docService: DocumentService,
        private uploadService: UploadService,
        private sharedService: SharedService,
        private cdr: ChangeDetectorRef,
        private messageService: MessageService
    ) {
        this.attachment = new Doc(0, '', '');
    }

    ngOnInit(colId?: string, event?: CellClickedEvent) {
        if (colId) {
            this.recordId = event.data.id;
            this.isTarget = event.data.uniqueId || false;

            if (event.data.activityId)
                this.modalConfig.modalTitle = `Attachments <span class="text-primary fs-6 fw-semibold px-2" title="Activity ID">${event.data.activityId}</span>`;
            if (colId !== 'file') {
                this.modalConfig.modalTitle += `<span class="text-info fs-6 ms-3" title="Column">${event.colDef.headerName}</span>`;
                this.colId = +colId;
            } else this.colId = null;

            this.isCellEditable = event.column.isCellEditable(event.node);
            this.modalComponent?.open().then();
            this.fileTypeId = 0;
            this.fields = [];
            this.editMode = null;

            if (this.fileTypes.length > 0)
                $('#table_documents').DataTable({ retrieve: true }).destroy();

            this.getDocuments();

            if (!this.fileTypes.length)
                this.getDocTypes();
        }
    }

    getDocTypes(): void {
        this.working = true;
        this.subscriptions.push(
            this.docService.getDocumentTypes().subscribe({
                next: (docTypes) => {
                    this.fileTypes = docTypes || [];
                },
                error: (e) => {
                    console.log(e);
                    this.working = false;
                },
                complete: () => {
                    this.working = false;
                }
            }));
    }

    getDocuments(dtRetrieve?: boolean): void {
        this.modalConfig.working = true;
        this.subscriptions.push(
            this.docService.getDocuments(this.recordId, this.isTarget, this.colId, this.fileTypeId)
                .subscribe({
                    next: (docs) => {
                        this.documents = docs || [];
                    },
                    error: (e) => {
                        console.log(e);
                        this.modalConfig.working = false;
                    },
                    complete: () => {
                        if (dtRetrieve)
                            $('#table_documents').DataTable({ retrieve: true }).destroy();
                        this.cdr.detectChanges();
                        this.bindDataTable();
                        this.modalConfig.working = false;
                    }
            }));
    }

    bindDataTable(): void {
        $('#table_documents').DataTable({
            retrieve: false,
            dom: '<f<t>p>',
            pageLength: 15,
            rowCallback: (row: Node, data: any, index: number, indexFull?: number) => {
                row.childNodes[0].textContent = `${indexFull + 1}`;
            }
        } as DataTables.Settings).on('draw.dt', () => MenuComponent.reinitialization());
        $('#table_documents th').on('click', (e) => this.onSort(e));
        MenuComponent.reinitialization();
    }

    onSort(e: any): void {
        const target = e.target as HTMLElement;
        if (target.hasAttribute('data-orderable'))
            return;

        if (target.classList.contains('sorting_desc') && !target.classList.contains('noSort')) {
            target.classList.add('noSort');
            return;
        }

        if (target.classList.contains('noSort')) {
            setTimeout(() => {
                $('#table_documents').DataTable({
                    retrieve: true
                }).order([1, 'asc']).draw();
                target.classList.remove('noSort');
            }, this.documents.length * 2);
        }
    }

    // convenient getter for easy access to form fields
    get f() {
        return this.form.controls;
    }

    fields: DocTypeField[] = [];
    fileTypeId: number = 0;
    initForm(fileTypeId?: number) {
        this.form = new FormGroup({
            id: new FormControl({ value: this.attachment.id, disabled: true }),
            name: new FormControl(this.attachment.docName, Validators.required),
            fileType: new FormControl(fileTypeId || +this.fileTypeId, [Validators.required, Validators.min(1)])
        });
    }

    onUpload(): void {
        this.attachment = new Doc(0, '', '');
        this.initForm();
        this.editMode = 'upload';
        this.onSelectFileType();

        // click on file select
        setTimeout(() => {
            const fileInput = document.querySelector('#fileInput') as HTMLInputElement;
            fileInput.click();
        }, 50);
    }

    onFileSelect(e): void {
        try {
            if (!e.target.files?.length) {
                this.file = null;
                this.editMode = null;
            } else {
                // check file type
                let isValid: boolean = false;

                const acceptedTypes: string[] = ["application/pdf", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", "application/msword",
                    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "application/vnd.openxmlformats-officedocument.presentationml.presentation",
                    "application/vnd.openxmlformats-officedocument.presentationml.slideshow", "application/vnd.msword", "application/vnd.ms-excel",
                    "application/vnd.ms-powerpoint", "image/bmp", "image/jpeg", "image/pjpeg", "image/png", "image/gif", "application/zip",
                    "application/x-zip-compressed"];

                acceptedTypes.forEach(t => {
                    if (e.target.files[0].type === t) {
                        isValid = true;
                    }
                });

                if (isValid) {
                    this.file = e.target.files[0];
                    this.form.controls['name'].setValue(this.file.name.substring(0,
                        this.file.name.lastIndexOf('.')).replace(/\s/g, ' '));
                    this.attachment.docName = this.f.name.value;
                } else {
                    this.file = null;
                    this.messageService.error('Invalid file type.');
                }
            }
        } catch (e) {
            this.file = null;
            this.editMode = null;
            this.messageService.error('Something went wrong.');
            console.log(e);
        }
    }

    onSelectFileType(): void {
        if (this.editMode) {
            const fTypeId = +this.f.fileType.value;
            this.fields = [];
            this.initForm(fTypeId);

            if (!fTypeId)
                return;

            const fields = this.fileTypes.find(ft => ft.id === fTypeId)?.fields;
            if (fields) {
                fields.forEach(field => {
                    let validators = [];
                    if (field.isRequired && field.fieldType !== ColDataType.Checkbox)
                        validators.push(Validators.required);

                    if (field.fieldType === ColDataType.Percentage) {
                        validators.push(Validators.min(-100));
                        validators.push(Validators.max(100));
                    } else if (field.fieldType === ColDataType.GPS) {
                        validators.push(Validators.min(29.00000001));
                        validators.push(Validators.max(74.99999999));
                    } else if (field.fieldType >= ColDataType.SelectSingle && field.fieldTypeValues) {
                        if (!Array.isArray(field.fieldTypeValues)) {
                            field.fieldTypeValues = field.fieldTypeValues.split(',');
                            let fieldVals = [];

                            // make array of dropdown items from csv string
                            for (let i = 0; i < field.fieldTypeValues.length; i++) {
                                fieldVals.push({
                                    id: field.fieldTypeValues[i],
                                    name: field.fieldTypeValues[i]
                                });
                            }

                            field.fieldTypeValues = fieldVals;
                        }
                    }

                    let fieldValue: any = this.getFieldValue(this.attachment.fieldsWithValues, field.id);
                    if (fieldValue) {
                        if (field.fieldType === ColDataType.Checkbox)
                            fieldValue = fieldValue == 'true' || fieldValue == 'True';
                        else if (field.fieldType >= ColDataType.SelectSingle)
                            fieldValue = fieldValue.split(',');
                    }
                    this.form.addControl('field' + field.id, new FormControl(fieldValue || null, validators));
                });

                this.fields = [...fields];
            }
        } else {
            this.fields = this.fileTypes.find(ft => ft.id === +this.fileTypeId)?.fields;
            this.getDocuments(true);
        }
    }

    getFieldValue(docValues: FieldValue[], fId: number): string {
        if (!docValues?.length)
            return '';

        return docValues.find(fv => fv.fieldId === fId)?.value;
    }

    onMultiSelect(ctrl: FilterDropdownList): void {
        this.form.controls[ctrl.id].setValue(ctrl.selectedValues || ctrl['selVals']);
    }

    onEdit(doc: IDoc): void {
        this.attachment = new Doc(doc.id, doc.docName, doc.fileName);
        this.attachment.fieldsWithValues = doc.fieldsWithValues;
        this.initForm();
        this.editMode = 'edit';
        // generate rest of fields
        this.onSelectFileType();
    }

    onCancelEditMode(): void {
        this.editMode = null;
        //this.fileTypeId = 0;
        //this.fields = [];
        //this.subscriptions = [];
        //this.getDocuments(true);
    }

    async save(): Promise<boolean> {
        const result = new Subject<boolean>();

        try {
            this.working = true;

            let doc = new Doc(this.attachment.id, this.f.name.value, this.file?.name);
            doc.organizationId = 1;

            if (this.isTarget)
                doc.targetId = this.recordId;
            else
                doc.activityIds = [this.recordId];

            if (this.colId > 0)
                doc.dynamicColumnId = this.colId;

            doc.docTypeId = +this.f.fileType.value;
            doc.fieldsWithValues = [];
            this.fields.forEach(field => {
                let val = this.form.controls['field' + field.id].value;
                if (field.fieldType >= ColDataType.SelectSingle)
                    val = val.join(',');
                else val = '' + val;
                doc.fieldsWithValues.push(new FieldValue(field.id, val));
            });

            // upload and add new file
            if (this.attachment.id === 0) {
                this.uploadFile = this.uploadService.uploadFile('documents', this.file, doc);
                this.uploadFile.uploadStatus.subscribe(returnDocId => {
                    //if (returnDocId < 0) {
                    //    this.messageService.error('Error uploading the file');
                    //    this.working = false;
                    //    this.file = null;
                    //} else
                    //    doc.id = +returnDocId;
                }, error => {
                    this.messageService.error('Error uploading the file.');
                    this.working = false;
                    this.file = null;

                    result.next(false);
                    result.complete();

                    console.log(error);
                }, () => { // on upload complete
                    //doc.fileName = this.file.name;
                    //this.documents.push(doc);
                    //this.cdr.detectChanges();
                    this.done.emit({
                        id: this.recordId,
                        colId: this.colId,
                        emitVal: 1
                    });
                    this.messageService.success('The file has been uploaded successfully.', 'Uploaded');
                    this.file = null;
                    //this.bindDataTable(true);
                    this.working = false;

                    this.onCancelEditMode();
                    this.getDocuments(true);
                    result.next(true);
                    result.complete();
                });
            } else {
                this.subscriptions.push(this.docService.updateDocument(doc).subscribe({
                    error: (err) => {
                        this.working = false;
                        console.log(err);
                        result.next(false);
                        result.complete();
                    },
                    complete: () => {
                        this.messageService.success('The file attachment information have been updated successfully.', 'Saved');
                        this.working = false;
                        this.onCancelEditMode();
                        this.getDocuments(true);
                        result.next(true);
                        result.complete();
                    }
                }));
            }
        } catch (e) {
            this.working = false;
            this.messageService.error('Something went wrong.');
            console.log(e);
            result.next(false);
            result.complete();
        }

        return await lastValueFrom(result.asObservable());
    }

    onDelete(docId: number, docName: string): void {
        this.messageService.confirmMessage('Confirm Delete', `Are you sure you want to delete this attachment: '${docName}'?`,
            () => {
                this.working = true;
                if ($.fn.dataTable.isDataTable('#table_documents'))
                    $('#table_documents').DataTable({ retrieve: true }).destroy();

                let operation = this.docService.deleteDocument(docId);

                if (!this.colId && !this.isTarget)
                    operation = this.docService.deleteDocument(docId, this.recordId);

                this.subscriptions.push(operation.subscribe({
                    next: () => {
                        this.documents = this.documents.filter(d => d.id !== docId);
                    },
                    error: (err) => {
                        this.working = false;
                        console.error(err);
                    },
                    complete: () => {
                        this.cdr.detectChanges();
                        this.bindDataTable();
                        this.done.emit({
                            id: this.recordId,
                            colId: this.colId,
                            emitVal: -1
                        });
                        this.messageService.success('The attachment has been deleted successfully.');
                        this.working = false;
                    }
                })
                );
            }, true, 'Delete');
    }

    /** Approve status */
    toggleStatus(docId: number, isApproved: Date): void {
        this.messageService.confirmMessage('Change Status', `Status for this document will be changed.`,
            () => {
                this.working = true;
                this.subscriptions.push(this.docService.changeStatus(docId).subscribe({
                    error: (e) => {
                        console.log(e);
                        this.working = false;
                    },
                    complete: () => {
                        let doc = this.documents.find(d => d.id === docId);
                        if (doc)
                            doc.dateApproved = doc.dateApproved ? null : new Date();

                        this.messageService.success('The status for the document has been changed.');
                        this.working = false;
                    }
                }));
            }, false, isApproved ? 'Retreat Approval' : 'Approve');
    }

    downloadFile(fileName: string): void {
        this.sharedService.downloadFile(fileName).subscribe({
            next: (fileBlob) => {
                this.messageService.info('The file is being downloaded.');

                let file = document.createElement("a");
                file.href = URL.createObjectURL(fileBlob);
                file.download = fileName;
                file.click(); // start download
            }, error: (err) => {
                this.messageService.error('Sorry, there was an error downloading the file.');
                console.log(err);
            }
        });
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
        this.subscriptions = [];
    }
}