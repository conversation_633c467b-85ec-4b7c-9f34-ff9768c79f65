import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, ViewChild } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { lastValueFrom, Subject, Subscription } from 'rxjs';
import { MessageService } from '../../../../../shared/services/message.service';
import { ModalComponent, ModalConfig } from '../../../../../_theme/partials';
import { Project } from '../../../../admin/models/project.model';
import { Inventory } from '../../../models/inventory.model';
import { InventoryService } from '../../../services/inventory.service';

@Component({
    selector: 'inventory-form-modal',
    templateUrl: './inventory-form.component.html',
    styleUrls: ['./inventory-form.component.scss']
})
export class InventoryFormComponent implements OnInit, OnDestroy {
    working: boolean = false;

    @Input() inventory: Inventory;
    @Input() interventions: any[] = [];
    projects: Project[] = [];
    @Input() orgs: any[] = [];

    form: FormGroup;

    @ViewChild('modal') private modalComponent: ModalComponent;
    modalConfig: ModalConfig = {
        modalTitle: 'Add new inventory',
        cancelButtonLabel: 'Cancel',
        doneButtonLabel: 'Add',
        disableDoneButton: true,
        options: { size: 'md' },
        shouldDo: () => this.save(),
        shouldCancel: () => { this.ngOnDestroy(); return true; }
    };
    @Output() done = new EventEmitter();

    private subscriptions: Subscription[] = [];
    constructor(
        private invService: InventoryService,
        private messageService: MessageService
    ) {
        this.inventory = new Inventory(0, 0, -1, 0);
    }

    ngOnInit(fetchProjects: boolean = false) {
        this.initForm();

        if (!fetchProjects)
            return;

        // sort interventions by category
        this.interventions.sort((x, y) => (x.category > y.category) ? 1 : ((x.category < y.category) ? -1 : 0));

        if (!this.projects.length)
            this.getProjects();
        else
            this.bindDropdowns();

        this.modalComponent?.open(true).then();

        // reset dropdowns
        if(!this.inventory.id)
            $('#org,#proj,#profile').val('').trigger('change');
    }

    // convenient getter for easy access to form fields
    get f() {
        return this.form.controls;
    }

    initForm() {
        this.form = new FormGroup({
            id: new FormControl({ value: this.inventory.id, disabled: true }),
            profId: new FormControl(this.inventory.interventionProfileId, Validators.required),
            orgId: new FormControl(this.inventory.organizationId, Validators.required),
            projId: new FormControl(this.inventory.projectId, Validators.required),
            details: new FormControl(this.inventory.details)
        });

        if (this.inventory?.id > 0) {
            this.modalConfig.modalTitle = 'Edit inventory';
            this.modalConfig.doneButtonLabel = 'Save changes';
        }

        this.subscriptions.push(
            this.form.valueChanges.subscribe((res) => {
                this.modalConfig.disableDoneButton = !this.form.valid;
            })
        );
    }

    // get a list of all projects
    getProjects(): void {
        this.working = true;
        
        this.subscriptions.push(
            this.invService.getProjectsList().subscribe({
                next: (projs: Project[]) => {
                    this.projects = projs;
                },
                error: (e) => {
                    console.log(e);
                    this.working = false;
                },
                complete: () => {
                    this.bindDropdowns();
                    this.working = false;
                }
            })
        );
    }

    bindDropdowns(): void {
        setTimeout(() => {
            $('#org').val(this.f.orgId.value).trigger('change');
            $('#profile').val(this.f.profId.value).trigger('change');
            $('#proj').val(this.f.projId.value).trigger('change');
        }, 200);

        // listen to dropdown changes
        $('#org').on('change', (e: any) => {
            const selVal = $(e.target).val() || '';
            this.f.orgId.setValue(selVal);
        });
        $('#profile').on('change', (e: any) => {
            const selVal = $(e.target).val() || '';
            this.f.profId.setValue(selVal);
        });
        $('#proj').on('change', (e: any) => {
            const selVal = $(e.target).val() || '';
            this.f.projId.setValue(selVal);
        });
    }

    async save(): Promise<boolean> {
        const result = new Subject<boolean>();
        this.modalConfig.working = true;

        this.inventory = new Inventory(this.inventory.id, this.f.profId.value, this.f.orgId.value,
            this.f.projId.value, this.f.details.value, false);

        // update inventory info
        let operation = this.inventory.id > 0 ?
            this.invService.updateInventory(this.inventory) :
            this.invService.addInventory(this.inventory);

        this.subscriptions.push(operation.subscribe({
                error: (err) => {
                    this.modalConfig.working = false;
                    console.log(err);

                    result.next(false);
                    result.complete();
                },
                complete: () => {
                    let successMsg = 'The inventory record has been added successfully.';
                    if (this.inventory.id > 0)
                        successMsg = "The inventory record has been updated successfully.";

                    this.messageService.success(successMsg);

                    // notify to refresh inventory table
                    this.done.emit();

                    this.modalConfig.working = false;
                    result.next(true);
                    result.complete();
                }
            })
        );

        return await lastValueFrom(result.asObservable());
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}