.region-dashboard {
  .stats-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    .card-body {
      padding: 1.25rem;
    }
    
    h2 {
      font-weight: 600;
      font-size: 1.75rem;
    }
    
    .icon-bg {
      font-size: 1.5rem;
      opacity: 0.8;
      padding: 10px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    .progress-sm {
      height: 6px;
      border-radius: 3px;
    }
  }
  
  .card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    
    .card-body {
      padding: 1.5rem;
    }
  }
  
  .map-container {
    height: 500px;
    width: 100%;
    border-radius: 0.475rem;
    overflow: hidden;
    position: relative;
  }
  
  .map-loading,
  .map-error {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.9);
    z-index: 1000;
    
    p {
      margin: 0;
      color: #5e6278;
      font-size: 0.875rem;
    }
    
    i {
      font-size: 2rem;
    }
  }
  
  .map-legend {
    display: flex;
    flex-wrap: wrap;
    gap: 1rem;
    padding: 0.5rem;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 0.475rem;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
  
  .legend-item {
    display: flex;
    align-items: center;
    font-size: 0.875rem;
    color: #3f4254;
  }
  
  .legend-marker {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 0.5rem;
    border: 1px solid #e4e6ef;
  }
  
  .map-popup {
    padding: 0.5rem;
    
    h5 {
      margin: 0 0 0.5rem 0;
      font-size: 1rem;
      font-weight: 600;
      color: #181c32;
    }
    
    p {
      margin: 0;
      font-size: 0.875rem;
      color: #5e6278;
    }
  }
}

// Leaflet popup customization
:host ::ng-deep {
  .leaflet-popup-content-wrapper {
    border-radius: 0.475rem;
    box-shadow: 0 0 50px 0 rgba(82, 63, 105, 0.15);
  }
  
  .leaflet-popup-content {
    margin: 0.5rem 0.75rem;
  }
  
  .leaflet-popup-tip {
    background: white;
  }
}

// Responsive adjustments
@media (max-width: 767.98px) {
  .region-dashboard {
    .stats-card {
      h2 {
        font-size: 1.5rem;
      }
    }
    
    .map-container {
      height: 300px;
    }
  }
} 