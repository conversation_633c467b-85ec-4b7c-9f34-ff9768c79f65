/* Custom styles for reports dashboard */
:host {
  display: block;
  padding: 0rem;
  height: 100%;
  width: 100%;
  overflow-x: hidden;
  max-width: 100%;
}

/* Core container class */
.reports-container {
  width: 100%;
  max-width: 100%;
}

/* Card styling */
.card {
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  margin-bottom: 1.5rem;
  width: 100%;
  max-width: 100%;
  overflow: hidden; /* Prevent content overflow */
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 0.5rem 1.5rem 0.5rem rgba(0, 0, 0, 0.075);
  }
}

/* Clickable elements */
.cursor-pointer {
  cursor: pointer;
}

/* Card hover shadow effect */
.card-hover-shadow {
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1.5rem rgba(0, 0, 0, 0.1) !important;
  }
}

/* Progress bar styling */
.progress {
  height: 6px;
  background-color: #e9ecef;
  max-width: 100%;
}

/* Table styling */
.table {
  width: 100%;
  max-width: 100%;
  
  tr {
    transition: background-color 0.2s ease;
    
    &:hover {
      background-color: #f8f9fa;
    }
  }
}

/* Handle table overflow properly */
.table-responsive {
  width: 100%;
  max-width: 100%;
  overflow-x: auto;
}

/* Badge styling */
.badge-circle {
  width: 22px;
  height: 22px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}

/* Chart container styling */
[id$="Chart"] {
  width: 100%;
  max-width: 100%;
  min-height: 350px;
  border-radius: 4px;
}

/* Navigation tabs */
.nav-tabs {
  display: flex;
  flex-wrap: wrap;
  width: 100%;
  max-width: 100%;
  margin-bottom: 1rem;
  
  .nav-link {
    cursor: pointer;
    
    &.active {
      border-bottom-width: 2px;
    }
  }
}

/* Responsive layout adjustments */
.row {
  margin-right: 0;
  margin-left: 0;
  width: 100%;
  max-width: 100%;
}

/* Media queries for responsive design */
@media (max-width: 992px) {
  :host {
    padding: 1rem;
  }
  
  .card-body {
    padding: 1rem;
  }
  
  .mb-5 {
    margin-bottom: 1rem !important;
  }
  
  .p-5 {
    padding: 0.75rem !important;
  }
}

@media (max-width: 768px) {
  .symbol-50px {
    width: 40px !important;
    height: 40px !important;
  }
  
  .fs-4 {
    font-size: 1.1rem !important;
  }
  
  .d-flex {
    flex-wrap: wrap;
  }
  
  .me-5 {
    margin-right: 0.5rem !important;
  }
  
  .me-4 {
    margin-right: 0.25rem !important;
  }
}

/* Text truncation and wrapping */
.text-truncate {
  max-width: 100%;
}

.fw-bold.text-dark.fs-5 {
  white-space: normal;
  word-break: break-word;
}

/* Material Tabs Styling */
.reports-tab-group {
  margin-bottom: 1.5rem;

  ::ng-deep {
    .mat-mdc-tab-header {
      border-bottom: 1px solid #ebedf3;
    }

    .mat-mdc-tab-labels {
      .mdc-tab {
        height: 45px;
        min-width: 120px;
        padding: 0 1.25rem;
        opacity: 1;
        
        &.mdc-tab--active {
          .mdc-tab__text-label {
            color: #3699ff;
            font-weight: 600;
          }
        }
        
        .mdc-tab__text-label {
          font-size: 1rem;
          color: #6c7293;
        }
      }
    }

    .mat-mdc-tab-body-wrapper {
      border-radius: 8px;
      background: #fff;
    }
  }
}

.mat-tab-content {
  min-height: 400px;
}

/* Ensure cards within tabs have consistent spacing */
.tab-content-card {
  margin-bottom: 1.5rem;
}

/* General Dashboard Styles */

/* Fix for Material Tabs display issues */
::ng-deep .mat-mdc-tab-body-wrapper {
  width: 100%;
}

::ng-deep .mat-mdc-tab-header {
  background-color: #ffffff;
}

/* Fix spacing in tab content */
::ng-deep .mat-tab-body.mat-tab-body-active {
  overflow-y: visible;
  overflow-x: hidden;
}

/* Hide scrollbars but keep functionality */
::ng-deep .mat-tab-body-content {
  overflow-x: hidden;
  overflow-y: visible;
  height: auto !important;
}

/* Enhanced date input styling */
input[type="date"] {
  border-radius: 6px;
  border: 1px solid #e1e3ea;
  transition: border-color 0.2s ease;
  
  &:focus {
    border-color: #3699ff;
    box-shadow: 0 0 0 0.2rem rgba(54, 153, 255, 0.25);
    outline: 0;
  }
}

/* Quarterly Filter System Styling */
.quarterly-filter-section {
  background: linear-gradient(135deg, #f8f9ff 0%, #f1f3ff 100%);
  border-radius: 12px;
  padding: 1.5rem;
  border: 1px solid #e8eafd;
}

.quarter-selector {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.quarter-card {
  flex: 1;
  min-width: 120px;
  background: #ffffff;
  border: 2px solid #e1e3ea;
  border-radius: 12px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: transparent;
    transition: background 0.3s ease;
  }
  
  &:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border-color: #3699ff;
  }
  
  &.active {
    border-color: #3699ff;
    background: linear-gradient(135deg, #3699ff 0%, #4a9eff 100%);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(54, 153, 255, 0.3);
    
    &::before {
      background: linear-gradient(90deg, #fff, rgba(255,255,255,0.8), #fff);
    }
    
    .quarter-icon i {
      color: white !important;
    }
    
    .quarter-months,
    .quarter-stats small {
      color: rgba(255, 255, 255, 0.9) !important;
    }
  }
  
  .quarter-icon {
    font-size: 1.5rem;
    margin-bottom: 0.5rem;
    
    i {
      transition: color 0.3s ease;
    }
  }
  
  .quarter-label {
    font-size: 1.25rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
  }
  
  .quarter-months {
    font-size: 0.875rem;
    color: #6c7293;
    margin-bottom: 0.5rem;
    font-weight: 500;
  }
  
  .quarter-stats {
    font-size: 0.75rem;
  }
}

/* Quarter Comparison and Analytics Styling */
.quarter-comparison {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 1rem;
  border-left: 4px solid #3699ff;
}

.comparison-card {
  background: #ffffff;
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid #e1e3ea;
  height: 100%;
  
  h6 {
    border-bottom: 2px solid #e1e3ea;
    padding-bottom: 0.5rem;
    margin-bottom: 1rem;
  }
}

.quarter-navigation .btn-group {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border-radius: 6px;
  
  .btn.quarter-display-btn {
    background-color: #f8f9fa;
    color: #495057;
    font-weight: 600;
    min-width: 100px;
  }
}

/* Quick filter buttons styling */
.btn-light-info {
  background-color: #e8f4fd;
  border-color: #bee5eb;
  color: #0c5460;
  
  &:hover {
    background-color: #d1ecf1;
    border-color: #abdde5;
    color: #0a4047;
  }
}

/* Auto-refresh button styling */
.btn-light-warning {
  &.auto-refresh-active {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      top: 2px;
      right: 2px;
      width: 8px;
      height: 8px;
      background-color: #28a745;
      border-radius: 50%;
      animation: pulse 2s infinite;
    }
  }
}

@keyframes pulse {
  0% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
  }
  
  70% {
    transform: scale(1);
    box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
  }
  
  100% {
    transform: scale(0.95);
    box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
  }
}

/* Filter count badge styling */
.badge-circle {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Analytics section styling */
.analytics-card {
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.1);
  }
}

.predictive-metric {
  padding: 1.5rem;
  border-radius: 8px;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  
  .symbol {
    margin-bottom: 1rem;
  }
  
  h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
  }
  
  p {
    font-size: 1rem;
    margin-bottom: 0;
  }
}

/* Live data badge */
.badge-light-success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
  animation: blink 3s infinite;
}

@keyframes blink {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

/* Export dropdown enhancements */
.dropdown-menu {
  border-radius: 8px;
  box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
  border: 0;
  
  .dropdown-item {
    padding: 0.75rem 1rem;
    display: flex;
    align-items: center;
    
    &:hover {
      background-color: #f8f9fa;
      color: #495057;
    }
    
    i {
      width: 20px;
    }
  }
}
