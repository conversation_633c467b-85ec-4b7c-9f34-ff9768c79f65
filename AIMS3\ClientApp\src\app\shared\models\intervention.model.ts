export interface Intervention {
  id: number | string;
  name: string;
  category: string | any;
  region: string;
  progress?: number;
  beneficiaries?: number;
  activities?: number;
  startDate?: string;
  endDate?: string;
  description?: string;
  variables?: any[];
  output?: string;
  categoryColor?: string;
  progressColor?: string;
  icon?: string;
  budget?: number;
  type?: 'public' | 'productive';
}

export interface InterventionFilter {
  sector?: string;
  sdg?: string;
  region?: string;
  status?: string;
  startDate?: Date;
  endDate?: Date;
} 