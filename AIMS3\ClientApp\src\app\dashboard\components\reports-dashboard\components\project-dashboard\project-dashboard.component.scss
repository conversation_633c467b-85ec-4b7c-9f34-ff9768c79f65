.project-dashboard {
  .stats-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    .card-body {
      padding: 1.25rem;
    }
    
    h2 {
      font-weight: 600;
      font-size: 1.75rem;
    }
    
    .icon-bg {
      font-size: 1.5rem;
      opacity: 0.8;
      padding: 10px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    .progress-sm {
      height: 6px;
      border-radius: 3px;
    }
  }
  
  .card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    
    .card-body {
      padding: 1.5rem;
    }
    
    .card-title {
      margin-bottom: 1.25rem;
      font-weight: 600;
    }
  }
  
  .table {
    th {
      font-weight: 600;
      border-top: none;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      padding: 0.75rem 1rem;
      background-color: rgba(0, 0, 0, 0.02);
    }
    
    td {
      vertical-align: middle;
      padding: 0.75rem 1rem;
      
      .progress {
        margin-bottom: 0.25rem;
      }
      
      .small {
        font-size: 0.8rem;
        color: #666;
      }
    }
  }
}

// Responsive adjustments
@media (max-width: 767.98px) {
  .project-dashboard {
    .stats-card {
      h2 {
        font-size: 1.5rem;
      }
    }
    
    .table-responsive {
      font-size: 0.85rem;
    }
  }
} 