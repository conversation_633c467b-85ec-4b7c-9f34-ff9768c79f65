<aims-working *ngIf="working"></aims-working>
<h5 class="modal-title fw-semibold">{{ modalConfig.modalTitle }}</h5>
<div class="separator"></div>
<div class="modal-content">
    <form class="form" [formGroup]="form" *ngIf="form">
        <div class="row form-group">
            <label class="col-3 mt-1 required">Category</label>
            <div class="col-9">
                <select class="form-select form-select-sm" placeholder="Select a category" formControlName="catId"
                        [ngClass]="{ 'is-invalid': form.controls['catId'].dirty && form.controls['catId'].invalid }">
                    <optgroup *ngFor="let output of outputs" [label]="output.name">
                        <option *ngFor="let cat of output.categories" [value]="cat.id">{{ cat.name }}</option>
                    </optgroup>
                </select>
                <ng-container [ngTemplateOutlet]="formError"
                              [ngTemplateOutletContext]="{ validation: 'required', message: 'Category is required.',
                        control: form.controls['catId'] }"></ng-container>
            </div>
        </div>
        <div class="row form-group">
            <div class="col-8">
                <label class="required">Intervention name</label>
                <input name="profName" class="form-control form-control-sm" type="text" placeholder="Profile name" formControlName="name"
                       [ngClass]="{ 'is-invalid': form.controls['name'].dirty && form.controls['name'].invalid }" minlength="3" />
                <ng-container [ngTemplateOutlet]="formError"
                              [ngTemplateOutletContext]="{ validation: 'required', message: 'Internvention name is required.',
                        control: form.controls['name'] }"></ng-container>
                <ng-container [ngTemplateOutlet]="formError"
                              [ngTemplateOutletContext]="{ validation: 'required', message: 'Abbreviation is required.',
                    control: form.controls['abbrv'] }"></ng-container>
            </div>
            <div class="col-4">
                <label class="fs-7 required">Abbreviation</label>
                <input name="profName" class="form-control form-control-sm" type="text" formControlName="abbrv" minlength="3"
                       [ngClass]="{ 'is-invalid': form.controls['abbrv'].dirty && form.controls['abbrv'].invalid }" maxlength="10" />
            </div>
            <div class="fv-plugins-message-container mb-3" *ngIf="exists">
                <div class="fv-help-block">
                    <span role="alert">An intervention with this name or abbreviation exists.</span>
                </div>
            </div>
        </div>
        <div class="form-group mb-0">
            <label>Description</label>
            <textarea class="form-control form-control-sm" type="text" formControlName="desc" rows="2"></textarea>
        </div>
    </form>
</div>
<div class="separator"></div>
<div class="modal-footer" [ngClass]="{ 'd-flex flex-stack': profile.id > 0 }">
    <div class="text-start" *ngIf="profile.id > 0">
        <button type="button" class="btn btn-sm btn-icon btn-light-danger" ngbTooltip="Delete Profile"
                [disabled]="working || modalConfig.working" (click)="deleteProfile()">
            <span [inlineSVG]="'./assets/media/icons/duotune/general/trash.svg'"
                  class="svg-icon svg-icon-3"></span>
        </button>
        <button type="button" class="btn btn-sm btn-icon btn-light-primary ms-2" ngbTooltip="Duplicate Profile"
                [disabled]="working || modalConfig.working" (click)="duplicateProfile()">
            <span [inlineSVG]="'./assets/media/icons/duotune/general/gen054.svg'"
                  class="svg-icon svg-icon-3"></span>
        </button>
    </div>
    <div class="text-end">
        <button type="button" class="btn btn-sm btn-outline-secondary" *ngIf="!modalConfig.hideCancelButton"
                [disabled]="modalConfig.disableCancelButton" (click)="close()">
            {{ modalConfig.cancelButtonLabel || 'Cancel' }}
        </button>
        <button type="button" class="btn btn-sm btn-primary ms-2" *ngIf="!modalConfig.hideDoneButton" (click)="save()"
                [disabled]="modalConfig.disableDoneButton || modalConfig.working">
            <span class="indicator-label" *ngIf="!modalConfig.working; else btnSpinner">
                {{ modalConfig.doneButtonLabel || 'Submit' }}
            </span>
            <ng-template #btnSpinner>
                <span class="indicator-progress" style="display: block">
                    Please wait...
                    <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                </span>
            </ng-template>
        </button>
    </div>
</div>

<ng-template #formError let-control="control" let-message="message" let-validation="validation">
    <ng-container *ngIf="control.hasError(validation) && control.dirty">
        <div class="fv-plugins-message-container mb-3">
            <div class="fv-help-block">
                <span role="alert">{{ message }}</span>
            </div>
        </div>
    </ng-container>
</ng-template>