// Basic styling for the report filter component
.filter-section {
  margin-bottom: 1.5rem;
}

.filter-header {
  margin-bottom: 0.75rem;
  font-weight: 600;
  color: var(--bs-gray-700);
}

.filter-row {
  margin-bottom: 1rem;
}

// Time period filter styles
.time-period-container {
  padding: 0.5rem 0;
}

// Demographic filter styles
.demographic-filters {
  .form-check {
    margin-bottom: 0.5rem;
  }
}

// Project filter styles
.project-filters {
  select {
    width: 100%;
  }
}

// Make small screens stack the form fields
@media (max-width: 768px) {
  .row > div {
    margin-bottom: 1rem;
  }
} 