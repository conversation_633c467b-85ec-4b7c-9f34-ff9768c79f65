import { Component, Input, OnDestroy, OnInit } from '@angular/core';
import { GuardsCheckStart, NavigationCancel, NavigationEnd, NavigationSkipped, NavigationStart, Router } from '@angular/router';
import { Subscription } from 'rxjs/internal/Subscription';
// import { DrawerComponent } from '../../../core/components';
@Component({
    selector: 'app-content',
    templateUrl: './content.component.html',
    styleUrls: ['./content.component.scss'],
})
export class ContentComponent implements OnInit, OnDestroy {
    @Input() contentContainerCSSClass: string = '';
    @Input() appContentContiner?: 'fixed' | 'fluid';
    @Input() appContentContainerClass: string = '';

    loading: boolean = false;
    private unsubscribe: Subscription[] = [];

    constructor(private router: Router) { }

    ngOnInit(): void {
        // by Sabawoon
        this.contentContainerCSSClass = this.contentContainerCSSClass || '';
        this.appContentContainerClass = this.appContentContainerClass || '';
        this.contentContainerCSSClass = this.appContentContainerClass + ' ' + this.contentContainerCSSClass;
        this.contentContainerCSSClass = this.contentContainerCSSClass.trim();

        this.routingChanges();
    }

    routingChanges() {
        const routerSubscription = this.router.events.subscribe((event) => {
            if (event instanceof NavigationStart) {
                this.loading = true;
            } else if (event instanceof NavigationEnd || event instanceof NavigationCancel) {
                // DrawerComponent.hideAll();
                
                // by Sabawoon
                this.loading = false;
                this.contentContainerCSSClass = this.contentContainerCSSClass || '';
                this.appContentContainerClass = this.appContentContainerClass || '';
                this.contentContainerCSSClass = this.appContentContainerClass + ' ' + this.contentContainerCSSClass;
                this.contentContainerCSSClass = this.contentContainerCSSClass.trim();
            } else if (event instanceof GuardsCheckStart)
                this.loading = false;
        });
        this.unsubscribe.push(routerSubscription);
    }

    ngOnDestroy() {
        this.unsubscribe.forEach((sb) => sb.unsubscribe());
    }
}