<!--begin::Menu wrapper-->
<div id="qs_app_sidebar_menu_wrapper" class="app-sidebar-wrapper hover-scroll-overlay-y my-5" data-qs-scroll="true"
     data-qs-scroll-activate="true" data-qs-scroll-height="auto"
     data-qs-scroll-dependencies="#qs_app_sidebar_logo, #qs_app_sidebar_footer"
     data-qs-scroll-wrappers="#qs_app_sidebar_menu" data-qs-scroll-offset="5px" data-qs-scroll-save-state="true">
    <!--begin::Menu-->
    <div id="#qs_app_sidebar_menu" class="menu menu-column menu-rounded menu-sub-indention px-3" data-qs-menu="true"
         data-qs-menu-expand="false">
        <!-- Dashboard -->
        <!-- Separator -->
        <div class="menu-item">
            <div class="menu-content pb-2">
                <span class="menu-section text-info text-uppercase fs-8 ls-1">Dashboard</span>
            </div>
        </div>
        <!-- Map -->
        <div class="menu-item">
            <a class="menu-link without-sub" routerLink="/dashboard" routerLinkActive="active" [routerLinkActiveOptions]="{exact: true}">
                <span class="menu-icon">
                    <span [inlineSVG]="'./assets/media/icons/duotune/maps/map002.svg'" class="svg-icon svg-icon-2"></span>
                </span><span class="menu-title">Map</span>
            </a>
        </div>
        <ng-container *ngIf="(['admin'] | isAuth)">
            <!-- Analytics -->
            <div class="menu-item">
                <a class="menu-link without-sub" routerLink="/dashboard/results" routerLinkActive="active">
                    <span class="menu-icon">
                        <span [inlineSVG]="'./assets/media/icons/duotune/graphs/gra001.svg'" class="svg-icon svg-icon-2"></span>
                    </span><span class="menu-title" data-page-title="Results">Result</span>
                </a>
            </div>
            <!-- Indicators -->
            <div class="menu-item">
                <a class="menu-link without-sub" routerLink="/dashboard/indicators" routerLinkActive="active">
                    <span class="menu-icon">
                        <span [inlineSVG]="'./assets/media/icons/duotune/general/scale.svg'" class="svg-icon svg-icon-2"></span>
                    </span><span class="menu-title" data-page-title="Indicators">Indicator</span>
                </a>
            </div>
        </ng-container>
        <!-- Reports -->
        <div class="menu-item">
            <a class="menu-link without-sub" routerLink="/dashboard/reports" routerLinkActive="active">
                <span class="menu-icon">
                    <span [inlineSVG]="'./assets/media/icons/duotune/files/fil003.svg'" class="svg-icon svg-icon-2"></span>
                </span><span class="menu-title" data-page-title="Reports">Reports</span>
            </a>
        </div>

        <!-- DataEntry -->
        <!-- Separator -->
        <div class="menu-item">
            <div class="menu-content pt-8 pb-2">
                <span class="menu-section text-primary text-uppercase fs-8 ls-1">Data Entry</span>
            </div>
        </div>
        <!-- Location -->
        <div class="menu-item">
            <a class="menu-link without-sub" routerLink="/locations" routerLinkActive="active">
                <span class="menu-icon">
                    <span [inlineSVG]="'./assets/media/icons/duotune/general/gen018.svg'" class="svg-icon svg-icon-2"></span>
                </span><span class="menu-title" data-page-title="Locations">Location</span>
            </a>
        </div>
        <!-- Inventory -->
        <div class="menu-item">
            <a class="menu-link without-sub" routerLink="/data-entry/inventory" routerLinkActive="active">
                <span class="menu-icon">
                    <span [inlineSVG]="'./assets/media/icons/duotune/general/box.svg'" class="svg-icon svg-icon-2"></span>
                </span><span class="menu-title">Inventory</span>
            </a>
        </div>
        <!-- Targets and Progress-->
        <div class="menu-item">
            <a class="menu-link without-sub" routerLink="/data-entry/targets-progress" routerLinkActive="active">
                <span class="menu-icon">
                    <span [inlineSVG]="'./assets/media/icons/duotune/general/gen055.svg'" class="svg-icon svg-icon-2"></span>
                </span><span class="menu-title" data-page-title="Targets & Progress">Target &amp; Progress</span>
            </a>
        </div>
        <!-- Submit Data -->
        <!-- <div class="menu-item">
            <a class="menu-link without-sub" routerLink="/data/submit" routerLinkActive="active">
                <span class="menu-icon">
                    <span [inlineSVG]="'./assets/media/icons/duotune/general/gen016.svg'" class="svg-icon svg-icon-2"></span>
                </span><span class="menu-title">Submit data</span>
            </a>
        </div> -->
        <!-- All Data -->
        <div class="menu-item">
            <a class="menu-link without-sub" routerLink="/data/view" routerLinkActive="active">
                <span class="menu-icon">
                    <span [inlineSVG]="'./assets/media/icons/duotune/files/fil002.svg'" class="svg-icon svg-icon-2"></span>
                </span><span class="menu-title">View all data</span>
            </a>
        </div>

        <!-- Library -->
        <!-- Separator -->
        <div class="menu-item">
            <div class="menu-content pt-8 pb-2">
                <span class="menu-section text-success text-uppercase fs-8 ls-1">LIBRARY</span>
            </div>
        </div>
        <!-- Documents -->
        <div class="menu-item">
            <a class="menu-link without-sub" routerLink="/library/documents" routerLinkActive="active">
                <span class="menu-icon">
                    <span [inlineSVG]="'./assets/media/icons/duotune/general/library.svg'" class="svg-icon svg-icon-2"></span>
                </span><span class="menu-title">Documents</span>
            </a>
        </div>
        <div class="menu-item d-none">
            <a class="menu-link without-sub" routerLink="/libary/download" routerLinkActive="active">
                <span class="menu-title">Download</span>
            </a>
        </div>

        <!-- ADMIN -->
        <!-- Separator -->
        <div class="menu-item">
            <div class="menu-content pt-8 pb-2">
                <span class="menu-section text-muted text-uppercase fs-8 ls-1">ADMIN</span>
            </div>
        </div>
        <!-- Project Groups -->
        <div class="menu-item" *ngIf="(['admin'] | isAuth)">
            <a class="menu-link without-sub" routerLink="/admin/project-groups" routerLinkActive="active">
                <span class="menu-icon">
                    <span [inlineSVG]="'./assets/media/icons/duotune/general/gen025.svg'" class="svg-icon svg-icon-2"></span>
                </span><span class="menu-title" data-page-title="Project Groups">Project Groups</span>
            </a>
        </div>
        <!-- Projects -->
        <div class="menu-item">
            <a class="menu-link without-sub" routerLink="/admin/projects" routerLinkActive="active">
                <span class="menu-icon">
                    <span [inlineSVG]="'./assets/media/icons/duotune/general/gen022.svg'" class="svg-icon svg-icon-2"></span>
                </span><span class="menu-title" data-page-title="Projects">Project</span>
            </a>
        </div>
        <!-- Categories -->
        <div class="menu-item">
            <a class="menu-link without-sub" routerLink="/admin/categories" routerLinkActive="active">
                <span class="menu-icon">
                    <span [inlineSVG]="'./assets/media/icons/duotune/general/branches.svg'" class="svg-icon svg-icon-2"></span>
                </span><span class="menu-title" data-page-title="Categories">Category</span>
            </a>
        </div>
        <!-- Partners -->
        <div class="menu-item">
            <a class="menu-link without-sub" routerLink="/admin/partners" routerLinkActive="active">
                <span class="menu-icon">
                    <span [inlineSVG]="'./assets/media/icons/duotune/general/building.svg'" class="svg-icon svg-icon-2"></span>
                </span><span class="menu-title" data-page-title="Implementation Partners (IP)">Partner</span>
            </a>
        </div>
        <!-- Users -->
        <div class="menu-item" *ngIf="(['admin'] | isAuth)">
            <a class="menu-link without-sub" routerLink="/admin/users" routerLinkActive="active">
                <span class="menu-icon">
                    <span [inlineSVG]="'./assets/media/icons/duotune/communication/com006.svg'" class="svg-icon svg-icon-2"></span>
                </span><span class="menu-title" data-page-title="Users">User</span>
            </a>
        </div>
        <div class="menu-item d-none">
            <a class="menu-link without-sub" routerLink="/profile" routerLinkActive="active">
                <span class="menu-title" data-page-title="User Profile">User Profile</span>
            </a>
        </div>
        <div class="menu-item d-none">
            <a class="menu-link without-sub" routerLink="/data-entry/intervention-profiles" routerLinkActive="active">
                <span class="menu-title">Intervention Profiles</span>
            </a>
        </div>
    </div>
    <!--end::Menu-->
</div>
<!--end::Menu wrapper-->