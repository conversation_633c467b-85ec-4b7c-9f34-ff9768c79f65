<app-modal #modal [modalConfig]="modalConfig">
    <form id="docForm" class="form" [formGroup]="form">
        <div class="row" *ngIf="!document.id && !document.isLink">
            <div class="col-12 form-group">
                <label class="required">Select a file</label>
                <input id="fileInput" class="form-control" type="file" [disabled]="modalConfig.working" (change)="onFileSelect($event)" />
                <div class="progress mt-1" *ngIf="uploadFile && modalConfig.working">
                    <div class="progress-bar" role="progressbar" style.width="{{ uploadFile.progress | async }}%"
                         attr.aria-valuenow="{{uploadFile.progress | async}}" aria-valuemin="0" aria-valuemax="100">
                        {{uploadFile.progress | async}}%
                    </div>
                </div>
            </div>
        </div>
        <div class="row" *ngIf="document.isLink">
            <div class="col-12 form-group">
                <label class="required">File link</label>
                <input name="fileName" class="form-control" type="url" formControlName="fName"
                       placeholder="File source url including http(s)://" pattern="https?://.+" maxlength="2000"
                       [ngClass]="{ 'is-invalid': form.controls['fName'].dirty && form.controls['fName'].invalid }" />

                <div class="fv-plugins-message-container" *ngIf="form.controls['fName'].dirty && form.controls['fName'].invalid">
                    <div class="fv-help-block">
                        <span role="alert">A valid file link is required.</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-4 form-group">
                <label class="required">File name</label>
                <input name="docName" class="form-control" type="text" placeholder="File name" formControlName="name" maxlength="256"
                       [ngClass]="{ 'is-invalid': form.controls['name'].dirty && form.controls['name'].invalid }" />
                <ng-container [ngTemplateOutlet]="formError"
                              [ngTemplateOutletContext]="{
                        validation: 'required',
                        message: 'File name is required.',
                        control: form.controls['name']
                        }"></ng-container>
            </div>
            <div class="col-3 form-group">
                <label class="required">File type</label>
                <select class="form-select" placeholder="Select file type" formControlName="fileType" (change)="onSelectFileType()">
                    <option selected></option>
                    <option value="-1" *ngIf="(['ga'] | isAuth)">Templates / General</option>
                    <option *ngFor="let fType of fileTypes" [value]="fType.id">{{ fType.typeName }}</option>
                </select>
            </div>
            <div class="col-5" *ngIf="+f.fileType.value > 0 && !document.targetId && !document.dynamicColumnId">
                <label [ngClass]="{'required':docType?.isActRequired}">Activity ID(s)</label>
                <select id="activities" class="form-select" data-placeholder="Select activity(ies)" data-control="select2"
                        multiple [ngClass]="{ 'is-invalid': actIdRequired }">
                    <option *ngFor="let act of activities" [value]="act.fieldId">{{ act.value }}</option>
                </select>
                <div class="fv-plugins-message-container" *ngIf="actIdRequired">
                    <div class="fv-help-block">
                        <span role="alert">Required field. Please select at least 1 ID.</span>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" *ngIf="+f.fileType.value === -1">
            <div class="col-10 form-group">
                <label>Description</label>
                <textarea class="form-control" type="text" formControlName="desc" rows="2"></textarea>
            </div>
            <div class="col-2 form-group">
                <label>Shared with</label>
                <div class="form-check form-check-custom form-check-solid pt-2">
                    <input id="status" type="checkbox" class="form-check-input" formControlName="shared" />
                    <label for="status" class="form-check-label ms-3">All</label>
                </div>
            </div>
        </div>
        <div class="row" *ngIf="fields.length">
            <div class="col-4 mb-4" *ngFor="let dField of fields">
                <label [ngClass]="{'required': dField.isRequired}">{{ dField.fieldName }}</label>
                <!-- Text, Number, Currency, Percentage, GPS -->
                <div class="input-group input-group-sm" *ngIf="dField.fieldType <= 4">
                    <input class="form-control form-control-sm" [type]="dField.fieldType == 0 ? 'text' : 'number'"
                           [formControlName]="'field'+dField.id"
                           [ngClass]="{ 'is-invalid': form.controls['field'+dField.id]?.dirty && form.controls['field'+dField.id]?.invalid }" />
                    <div class="input-group-append" *ngIf="dField.fieldTypeValues"><span class="input-group-text">{{ dField.fieldTypeValues }}</span></div>
                </div>
                <!-- Date -->
                <input class="form-control form-control-sm" type="date" [formControlName]="'field'+dField.id" *ngIf="dField.fieldType === 5"
                       [ngClass]="{ 'is-invalid': form.controls['field'+dField.id]?.dirty && form.controls['field'+dField.id]?.invalid }" />
                <!-- Checkbox -->
                <div class="form-check form-check-custom form-check-sm form-check-solid pt-2" *ngIf="dField.fieldType === 6">
                    <input class="form-check-input" type="checkbox" [formControlName]="'field'+dField.id" />
                </div>
                <!-- Select -->
                <select class="form-select form-select-sm" [formControlName]="'field'+dField.id" *ngIf="dField.fieldType == 7"
                        [ngClass]="{ 'is-invalid': form.controls['field'+dField.id]?.dirty && form.controls['field'+dField.id]?.invalid }">
                    <option value=""></option>
                    <option *ngFor="let val of dField.fieldTypeValues" [value]="val.id">{{ val.name }}</option>
                </select>
                <!-- SelectMultiple -->
                <filter-ddl [id]="'field'+dField.id" class="filter-container" [multiple]="true" [showSearch]="false" [minWidth]="130"
                            (change)="onMultiSelect($event)" [showAll]="false" *ngIf="dField.fieldType == 8"
                            [options]="dField.fieldTypeValues" [placeholders]="['Option(s)', 'Selected']"
                            [selectedValues]="f['field'+dField.id]?.value || []">
                </filter-ddl>
                <ng-container [ngTemplateOutlet]="formError" *ngIf="dField.isRequired"
                              [ngTemplateOutletContext]="{ validation: 'required', message: 'Required field.',
                                        control: form.controls['field'+dField.id] }"></ng-container>
            </div>
        </div>
    </form>

    <ng-template #formError let-control="control" let-message="message" let-validation="validation">
        <ng-container *ngIf="control?.hasError(validation) && control.dirty">
            <div class="fv-plugins-message-container">
                <div class="fv-help-block">
                    <span role="alert">{{ message }}</span>
                </div>
            </div>
        </ng-container>
    </ng-template>
</app-modal>