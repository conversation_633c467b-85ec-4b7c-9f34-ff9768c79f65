import { Component, Injectable, Input, OnDestroy, OnInit } from '@angular/core';
import { Observable, Subscription } from 'rxjs';
import { AuthService, UserType } from '../../../../../modules/auth';
import { INotification } from '../../../../../modules/comments/models/comment.model';
import { CommentService } from '../../../../../modules/comments/services/comment.service';

@Injectable({ providedIn: 'root' })
@Component({
    selector: 'app-navbar',
    templateUrl: './navbar.component.html',
    styleUrls: ['./navbar.component.scss']
})
export class NavbarComponent implements OnInit, OnDestroy {
    @Input() appHeaderDefaulMenuDisplay: boolean;
    @Input() isRtl: boolean;

    itemClass: string = 'ms-1 ms-lg-3';
    btnClass: string =
        'btn btn-icon btn-custom btn-icon-muted btn-active-light btn-active-color-primary'; //w-35px h-35px w-md-40px h-md-40px';
    userAvatarClass: string = 'symbol-circle'; // symbol-35px symbol-md-40px';
    btnIconClass: string = ''; //'svg-icon-1';

    alerts: INotification[] = [];
    user$: Observable<UserType>;
    private subscriptions: Subscription[] = [];

    constructor(
        private auth: AuthService,
        private commentService: CommentService
    ) {
        this.commentService.observeNotifications.subscribe(refresh => {
            if (refresh)
                this.getNotifications();
        });
    }

    ngOnInit(): void {
        this.user$ = this.auth.currentUserSubject.asObservable();
        this.getNotifications();
    }

    loading: boolean = false; count = 1;
    getNotifications(): void {
        this.loading = true;

        this.subscriptions.push(
            this.commentService.getNotifications().subscribe({
                next: (items) => {
                    items.forEach(item => {
                        item.commentOn = item.commentOn.replace(':', '<br/>');
                    });

                    this.alerts = items;
                },
                error: (e) => {
                    console.log(e);
                    this.loading = false;
                }, complete: () => {
                    this.loading = false;
                }
            })
        );

        // refresh every 1hr
        setTimeout(() => this.getNotifications(), 1000 * 60 * 60);
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}