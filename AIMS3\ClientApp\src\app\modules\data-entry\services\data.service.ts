import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { environment } from '../../../../environments/environment';
import { OperationDataResult } from '../../../shared/models/operation-result.model';
import { IProgressData, ITargetData } from '../models/data.model';

const API_URL = `${environment.apiUrl}`;

@Injectable({ providedIn: 'root' })
export class DataService {
    constructor(private http: HttpClient) { }

    getTargets(profId: number, projId: number, orgId?: number): Observable<ITargetData[]> {
        let url = `${API_URL}/targets/${profId}/${projId}`;
        if (orgId > 0)
            url += '/' + orgId;

        return this.http.get<ITargetData[]>(url);
    }

    getActivities(profId: number, projId: number, month: number, year: number, orgId?: number): Observable<IProgressData[]> {
        let url = `${API_URL}/activities/${profId}/${projId}/${month}/${year}`;
        if (orgId > 0)
            url += '/' + orgId;

        return this.http.get<IProgressData[]>(url);
    }

    saveTargets(profId: number, projId: number, data: ITargetData[], deletedIds?: number[], orgId?: number):
        Observable<OperationDataResult> {

        let url = `${API_URL}/targets/${profId}/${projId}`;
        if (orgId > 0)
            url += '/' + orgId;

        return this.http.post<OperationDataResult>(url, { targets: data, deletedIds: deletedIds });
    }

    saveActivities(profId: number, projId: number, month: number, year: number, data: IProgressData[], deletedIds?: number[], orgId?: number):
        Observable<OperationDataResult> {

        let url = `${API_URL}/activities/${profId}/${projId}`;
        if (orgId > 0)
            url += '/' + orgId;

        return this.http.post<OperationDataResult>(url, { activities: data, month: month, year: year, deletedIds: deletedIds });
    }

    markTargets(targetIds: number[], voidSubmission?: boolean, orgId?: number): Observable<void> {
        let url = `${API_URL}/targets/submit`;
        if (orgId > 0)
            url += '/' + orgId;

        if(voidSubmission)
            url += '/' + 'void-submission';

        return this.http.patch<void>(url, targetIds);
    }

    markAtivities(actIds: number[], month: number, year: number, voidSubmission?: boolean, orgId?: number): Observable<any[]> {
        let url = `${API_URL}/activities/submit/${month}/${year}`;
        if (orgId > 0)
            url += '/' + orgId;

        if (voidSubmission)
            url += '/' + 'void-submission';

        return this.http.patch<any>(url, actIds);
    }

    submitTargetsData(model: any): Observable<any> {
        const url = `${API_URL}/targets/submit/data`;
        return this.http.patch<any>(url, model);
    }
}