import { Component, EventE<PERSON>ter, OnDestroy, OnInit, Output, ViewChild, Input } from '@angular/core';
import {
    CellClickedEvent, CellFocusedEvent, ColDef, ColGroupDef,
    ColumnApi, ColumnRowGroupChangedEvent, FilterChangedEvent, GridApi, GridOptions, GridReadyEvent,
    ICellRendererParams, IRowNode, SortChangedEvent
} from 'ag-grid-community';
import 'ag-grid-enterprise';
import { LicenseManager } from 'ag-grid-enterprise';
import { Subscription } from 'rxjs';
import { environment } from '../../../../../../environments/environment';
import { ActivityStatus, ColDataType, ColumnVarType, Region } from '../../../../../shared/enums';
import { MessageService } from '../../../../../shared/services/message.service';
import { getColWidth } from '../../../../../shared/utilities';
import { SabaAttachmentRenderer } from '../../../../aims-grid/controls/cell-renderers/attachment/attachment.control';
import { SabaCheckboxRenderer } from '../../../../aims-grid/controls/cell-renderers/checkbox/checkbox.control';
import { SabaSelectValueRenderer } from '../../../../aims-grid/controls/cell-renderers/select/select-value.control';
import { GridCustomHeader } from '../../../../aims-grid/controls/grid-header/grid-header.control';
import { DynamicColumn } from '../../../../aims-grid/models/dynamic-column.model';
import { GridService } from '../../../../aims-grid/services/grid.service';
import { CommentComponent } from '../../../../comments/components/comment.component';
import { AttachmentsFormComponent } from '../../../../library/components/attachments/attachments.component';
import { BtnApproveAllHeader } from '../../../controls/btn-approve-all/approve-all.control';
import { SabaBtnApproveRenderer } from '../../../controls/btn-approve/btn-approve.control';
import { IProgressDataView, ITargetDataView } from '../../../models/data.model';

@Component({
    selector: 'aims-readonly-grid',
    templateUrl: './readonly-grid.component.html',
    styleUrls: ['./readonly-grid.component.scss']
})
export class ReadOnlyGridComponent implements OnInit, OnDestroy {
    working: boolean = false;
    public isDarkMode: boolean = false;
    isApprover: boolean = false; isGlobalUser: boolean = false;

    public gridApi: GridApi;
    public gridColumnApi: ColumnApi;

    public gridOptions: GridOptions;
    public columnDefs: (ColDef | ColGroupDef)[];
    public defaultColDefs: ColDef;
    public components = {
        'sabaCheckboxRenderer': SabaCheckboxRenderer,
        'sabaAttachmentRenderer': SabaAttachmentRenderer,
        'sabaSelectValRenderer': SabaSelectValueRenderer,
        'sabaBtnApproveRenderer': SabaBtnApproveRenderer
    };
    public tooltipShowDelay: number = 500;
    private groupCollapsed = [[],[],[],[]];

    private gridColumns = { //: { [key: string]: ColDef | ColDef[] } = {
        commonCols: [],
        progressActIdCol: null, // activity id
        progressStatusCols: [], // file, status, start, end months
        progressCommCol: null,  // community column
        progressAsOfCol: null,  // as of 
        targetInfoCols: [],     // year, qtr
        hiddenCol: null         // hidden narrow col
    };

    // data from grid's parent
    dynamicColumns: DynamicColumn[] = [];

    // *** DATA -------------
    public rowData: IProgressDataView[] | ITargetDataView[] = [];
    public selectedVals = { isTarget: false, profId: 0, prof: '' };
    approvalMode: boolean = false;
    renameAsOfCol: boolean = false;
    
    // Add input property to control N/A period filtering
    @Input() hideNAPeriods: boolean = true;

    @ViewChild(AttachmentsFormComponent, { static: false })
    private attachmentModalComponent: AttachmentsFormComponent;

    @ViewChild(CommentComponent, { static: false })
    private commentModalComponent: CommentComponent;

    private subscriptions: Subscription[] = [];
    constructor(
        private gridService: GridService,
        private messageService: MessageService
    ) {
        if (document.querySelector('[data-theme="dark"'))
            this.isDarkMode = true;

        // AG-GRID
        LicenseManager.setLicenseKey(environment.agGridConfig.licenseKey);

        // set all defaults
        // default grid options
        this.gridOptions = {
            groupHeaderHeight: 30,
            headerHeight: 35,
            rowHeight: 24, rowDragManaged: true,
            rowSelection: 'multiple',
            enableRangeSelection: true, //enableFillHandle: true,
            //undoRedoCellEditing: true, undoRedoCellEditingLimit: 20,
            scrollbarWidth: 10,
            suppressRowClickSelection: true,
            groupDisplayType: 'groupRows',
            groupDefaultExpanded: -1,
            groupIncludeFooter: true,
            groupMaintainOrder: true,
            groupAllowUnbalanced: true,
            pivotRowTotals: 'after',
            pivotColumnGroupTotals: 'after',
            autoGroupColumnDef: { headerName: 'Group' },
            rowClassRules: {
                'bg-success': (params) => params.data?.status == ActivityStatus.Completed,
                'bg-gray-300 text-gray-500': (params) => params.data?.status == ActivityStatus.Archived,
                'bg-gray-400 text-gray-600': (params) => params.data?.status == ActivityStatus.Cancelled,
                'text-gray-600': (params) => !params.data?.status && params.data?.dateApproved      // for target data when approved
            }, statusBar: {
                statusPanels: [
                    { statusPanel: 'agTotalAndFilteredRowCountComponent', align: 'left' },
                    //{ statusPanel: 'agTotalRowCountComponent', align: 'center' },
                    { statusPanel: 'agFilteredRowCountComponent' },
                    { statusPanel: 'agSelectedRowCountComponent' },
                    { statusPanel: 'agAggregationComponent' },
                ]
            },
            onColumnRowGroupChanged: (params) => this.onRowGroupChanged(),
            onColumnValueChanged: (params) => { params.api.refreshHeader() },
            processCellForClipboard: (params) => this.gridService.processCellForClipboard(params),
            getContextMenuItems: (e) => this.gridService.getContextMenu(e, false)
        };

        // default column configurations
        this.defaultColDefs = {
            sortable: false,
            resizable: true,
            filter: true,
            editable: false,
            minWidth: 50,
            suppressFillHandle: true,
            suppressMovable: true,
            lockVisible: true,
            enablePivot: true,
            enableRowGroup: true,
            enableValue: true,
            menuTabs: ['filterMenuTab', 'generalMenuTab'],
            cellClassRules: {
                'has-comment': (params) => this.gridService.getCellCommentClass(params),
                'is-note': (params) => this.gridService.getCellCommentClass(params, 'note'),
                'unresolved': (params) => this.gridService.getCellCommentClass(params, 'unresolved')
            }, onCellClicked: (event) => {  // show comments on clicking cell triangle
                const target = (event.event.target as HTMLElement).closest('.ag-cell') as HTMLElement;
                if (target?.classList.contains('has-comment')) {
                    const cell = target.getBoundingClientRect();
                    const e = (event.event as MouseEvent);
                    if(e.y <= cell.top+8 && e.x >= cell.right-8)
                        this.commentModalComponent.open(event.colDef, event.node, cell, false, true, target);
                }
            },
            comparator: this.gridService.getComparator
        };
    }

    // init the default readonly grid
    ngOnInit() {
        try {
            // add default common column definitions
            this.columnDefs = [{        // row num
                colId: 'rowNum',
                headerName: '',
                minWidth: 50, width: 50, maxWidth: 100,
                filter: false, sortable: false,
                pinned: 'left',
                enableRowGroup: false, enablePivot: false, enableValue: false,
                suppressMenu: true, lockPosition: true,
                onCellClicked: this.onCellClicked,
                cellRenderer: (params: ICellRendererParams) => { return params.node.footer ? 'Total' : params.node.rowIndex + 1; }
            }, {  // Approve
                colId: 'dataStatus',
                headerName: '', cellClass: 'p-0',
                headerComponent: BtnApproveAllHeader,
                cellRenderer: 'sabaBtnApproveRenderer',
                enableRowGroup: false, enablePivot: false, enableValue: false,
                suppressMenu: true, lockPosition: true,
                resizable: false,
                pinned: true, lockPinned: true,
                width: 30,minWidth: 30,maxWidth: 30,
                filter: false,
                hide: !this.approvalMode,
                onCellClicked: (event) => this.approveData(event)
            }, {  // Project
                colId: 'project',
                field: 'project',
                headerName: 'Project', cellClass: 'fs-7',
                enableRowGroup: true,
                pinned: true,
                width: 100
            }, {  // IP
                colId: 'partner',
                field: 'partner',
                headerName: 'IP', cellClass: 'fs-7',
                enableRowGroup: true,
                pinned: true,
                width: 70,
                hide: !this.isGlobalUser
            }];

            // progress first few columns
            this.gridColumns.progressActIdCol = {  // activity id
                field: 'activityId',
                headerName: 'Activity ID',
                headerClass: 'required',
                enableRowGroup: false,
                pinned: true, lockPinned: true,
                width: 110,
                cellClass: 'fs-7'
            };

            this.gridColumns.progressStatusCols = [{   // file
                field: 'file', colId: 'file',
                headerName: 'File', headerClass: 'bg-header-color-1',
                headerTooltip: 'All files linked to an activity.',
                headerComponent: GridCustomHeader,
                headerComponentParams: { colType: ColDataType.Attachment, showInfoIcon: true },
                cellEditorParams: {
                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {
                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);
                    }, onCellLink: (colId: string, node: IRowNode, gotoCell?: boolean) => {
                        let params: any = {
                            projId: node.data.projId, profId: node.data.profId, orgId: node.data.orgId,
                            month: node.data.month, year: node.data.year
                        };
                        if (!this.selectedVals.isTarget) {
                            params = {...params, comment: { columnId: colId, activityId: node.data.id,
                                activityProgressId: node.data.progressId, month: node.data.month, year: node.data.year } }
                        } else
                            params = { ...params, comment: { targetId: node.data.id, columnId: colId } };
                        this.generateLink(params, gotoCell);
                    }
                },
                cellRenderer: 'sabaAttachmentRenderer',
                enableRowGroup: false,
                width: 80, columnGroupShow: 'open', comparator: this.gridService.getNumComparator,
                sortable: false, onCellClicked: (event) => this.onCellClicked(event),
                suppressMenu: true
            },
            {   // status
                colId: 'status',
                field: 'status',
                headerName: 'Status', headerClass: 'bg-header-color-1',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: true,
                    colType: ColDataType.SelectSingle
                },
                width: 110,
                cellEditorParams: {
                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {
                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);
                    }, onCellLink: (colId: string, node: IRowNode, gotoCell?: boolean) => {
                        let params: any = {
                            projId: node.data.projId, profId: node.data.profId, orgId: node.data.orgId,
                            month: node.data.month, year: node.data.year
                        };
                        if (!this.selectedVals.isTarget) {
                            params = {...params, comment: { columnId: colId, activityId: node.data.id,
                                activityProgressId: node.data.progressId, month: node.data.month, year: node.data.year } }
                        } else
                            params = { ...params, comment: { targetId: node.data.id, columnId: colId } };
                        this.generateLink(params, gotoCell);
                    }
                },
                valueGetter: (params) => params.data?.status > -1 ? ActivityStatus[params.data?.status] : '', //this.gridService.getValueFormatter,
                //filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                //cellRenderer: 'sabaSelectValRenderer',
                cellClassRules: {
                    'text-primary': (params) => params.value == 'Archived',
                    'text-red': (params) => params.value == 'Cancelled'
                }
            },
            {  // sMonth: Not required
                colId: 'sMonth',
                field: 'sMonth',
                headerName: 'Start date', headerClass: 'bg-header-color-1',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: true,
                    colType: ColDataType.Date  // MMM/yyyy
                },
                cellEditorParams: {
                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {
                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);
                    }, onCellLink: (colId: string, node: IRowNode, gotoCell?: boolean) => {
                        let params: any = {
                            projId: node.data.projId, profId: node.data.profId, orgId: node.data.orgId,
                            month: node.data.month, year: node.data.year
                        };
                        if (!this.selectedVals.isTarget) {
                            params = {...params, comment: { columnId: colId, activityId: node.data.id,
                                activityProgressId: node.data.progressId, month: node.data.month, year: node.data.year } }
                        } else
                            params = { ...params, comment: { targetId: node.data.id, columnId: colId } };
                        this.generateLink(params, gotoCell);
                    }
                },
                valueFormatter: this.gridService.getValueFormatter,
                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                lockPinned: true, comparator: this.gridService.getDateComparator,
                width: 130, columnGroupShow: 'open'
            },
            {   // eMonth: Not always required
                colId: 'eMonth',
                field: 'eMonth',
                headerName: 'End date', headerClass: 'bg-header-color-1',
                headerTooltip: 'The date when the implementation of the activity has been completed.',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: false,
                    colType: ColDataType.Date,  // MMM/yyyy
                    showInfoIcon: true
                },
                cellEditorParams: {
                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {
                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);
                    }, onCellLink: (colId: string, node: IRowNode, gotoCell?: boolean) => {
                        let params: any = {
                            projId: node.data.projId, profId: node.data.profId, orgId: node.data.orgId,
                            month: node.data.month, year: node.data.year
                        };
                        if (!this.selectedVals.isTarget) {
                            params = {...params, comment: { columnId: colId, activityId: node.data.id,
                                activityProgressId: node.data.progressId, month: node.data.month, year: node.data.year } }
                        } else
                            params = { ...params, comment: { targetId: node.data.id, columnId: colId } };
                        this.generateLink(params, gotoCell);
                    }
                },
                valueFormatter: this.gridService.getValueFormatter,
                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                lockPinned: true, comparator: this.gridService.getDateComparator,
                width: 130, columnGroupShow: 'open'
            }];

            // info static column used for group collapse
            this.gridColumns.hiddenCol = {
                colId: 'cols-hidden',
                headerName: '...',
                headerTooltip: 'Column(s) hidden. Click group handle to expand.',
                cellClass: 'non-selectable-cell',
                lockPinned: true,
                width: 28,
                minWidth: 28,
                maxWidth: 28,
                resizable: false,
                filter: false,
                suppressMenu: true,
                sortable: false,
                suppressSizeToFit: true,
                suppressAutoSize: true,
                lockVisible: true, enableRowGroup: false,
                columnGroupShow: 'closed'
            };

            // location columns
            this.gridColumns.commonCols = [{  // region
                field: 'region',
                headerName: 'Region', headerClass: 'bg-header-color-2',
                headerComponent: GridCustomHeader,
                width: 130,
                cellEditorParams: {
                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {
                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);
                    }, onCellLink: (colId: string, node: IRowNode, gotoCell?: boolean) => {
                        let params: any = {
                            projId: node.data.projId, profId: node.data.profId, orgId: node.data.orgId,
                            month: node.data.month, year: node.data.year
                        };
                        if (!this.selectedVals.isTarget) {
                            params = {...params, comment: { columnId: colId, activityId: node.data.id,
                                activityProgressId: node.data.progressId, month: node.data.month, year: node.data.year } }
                        } else
                            params = { ...params, comment: { targetId: node.data.id, columnId: colId } };
                        this.generateLink(params, gotoCell);
                    }
                },
                valueGetter: (params) => {
                    const regions = params.data?.region?.split(',') || [];
                    let result = [];
                    regions.forEach(r => result.push(Region[r]));
                    return result.join(',');
                },
                //cellRenderer: 'sabaSelectValRenderer',
                //valueFormatter: this.gridService.getValueFormatter,
                //filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                //comparator: (vA, vB, nA, nB, desc) => this.gridService.getRegionComparator(vA,vB,nA,nB,desc),
                columnGroupShow: 'open'
            },
            {   // province: always required
                field: 'province',
                headerName: 'Province', headerClass: 'bg-header-color-2',
                headerComponent: GridCustomHeader,
                width: 140,
                cellEditorParams: {
                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {
                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);
                    }, onCellLink: (colId: string, node: IRowNode, gotoCell?: boolean) => {
                        let params: any = {
                            projId: node.data.projId, profId: node.data.profId, orgId: node.data.orgId,
                            month: node.data.month, year: node.data.year
                        };
                        if (!this.selectedVals.isTarget) {
                            params = {...params, comment: { columnId: colId, activityId: node.data.id,
                                activityProgressId: node.data.progressId, month: node.data.month, year: node.data.year } }
                        } else
                            params = { ...params, comment: { targetId: node.data.id, columnId: colId } };
                        this.generateLink(params, gotoCell);
                    }
                },
                //cellRenderer: 'sabaSelectValRenderer',
                //valueFormatter: this.gridService.getValueFormatter,
                //filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                columnGroupShow: 'open',
                //comparator: (vA, vB, nA, nB, desc) => this.gridService.getProvinceComparator(vA, vB, nA, nB, desc)
            },
            {   // district: always required
                field: 'district',
                headerName: 'District', headerClass: 'bg-header-color-2',
                headerComponent: GridCustomHeader,
                width: 145,
                cellEditorParams: {
                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {
                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);
                    }, onCellLink: (colId: string, node: IRowNode, gotoCell?: boolean) => {
                        let params: any = {
                            projId: node.data.projId, profId: node.data.profId, orgId: node.data.orgId,
                            month: node.data.month, year: node.data.year
                        };
                        if (!this.selectedVals.isTarget) {
                            params = {...params, comment: { columnId: colId, activityId: node.data.id,
                                activityProgressId: node.data.progressId, month: node.data.month, year: node.data.year } }
                        } else
                            params = { ...params, comment: { targetId: node.data.id, columnId: colId } };
                        this.generateLink(params, gotoCell);
                    }
                },
                //cellRenderer: 'sabaSelectValRenderer',
                //valueFormatter: this.gridService.getValueFormatter,
                //filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                columnGroupShow: 'open',
                //comparator: (vA, vB, nA, nB, desc) => this.gridService.getDistrictComparator(vA, vB, nA, nB, desc)
            }];

            // progress community column
            this.gridColumns.progressCommCol = {   // community
                field: 'community',
                headerName: 'Community', headerClass: 'bg-header-color-2',
                headerComponent: GridCustomHeader,
                width: 170,
                cellEditorParams: {
                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {
                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);
                    }, onCellLink: (colId: string, node: IRowNode, gotoCell?: boolean) => {
                        let params: any = {
                            projId: node.data.projId, profId: node.data.profId, orgId: node.data.orgId,
                            month: node.data.month, year: node.data.year
                        };
                        if (!this.selectedVals.isTarget) {
                            params = {...params, comment: { columnId: colId, activityId: node.data.id,
                                activityProgressId: node.data.progressId, month: node.data.month, year: node.data.year } }
                        } else
                            params = { ...params, comment: { targetId: node.data.id, columnId: colId } };
                        this.generateLink(params, gotoCell);
                    }
                },
                //cellRenderer: 'sabaSelectValRenderer',
                //valueFormatter: this.gridService.getValueFormatter,
                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                //comparator: (vA, vB, nA, nB, desc) => this.gridService.getCommunityComparator(vA, vB, nA, nB, desc),
            };

            // progress static columns
            this.gridColumns.progressAsOfCol = {  // as of
                colId: 'asOf',
                field: 'asOf',
                headerName: 'As of', headerTooltip: 'As of the end of this month.', headerClass: 'bg-header-color-4',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: true,
                    colType: ColDataType.Date,  // MMM/yyyy
                    showInfoIcon: true
                },
                cellEditorParams: {
                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {
                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);
                    }, onCellLink: (colId: string, node: IRowNode, gotoCell?: boolean) => {
                        let params: any = {
                            projId: node.data.projId, profId: node.data.profId, orgId: node.data.orgId,
                            month: node.data.month, year: node.data.year
                        };
                        if (!this.selectedVals.isTarget) {
                            params = {...params, comment: { columnId: colId, activityId: node.data.id,
                                activityProgressId: node.data.progressId, month: node.data.month, year: node.data.year } }
                        } else
                            params = { ...params, comment: { targetId: node.data.id, columnId: colId } };
                        this.generateLink(params, gotoCell);
                    }
                },
                valueGetter: (params) => {
                    if (!params.node.footer && (!params.data?.progressId || !params.data?.asOf
                        || !Object.keys(params.data?.colVals).length)) {
                        params.data.asOf = 'N/A';
                        return 'N/A';
                    }
                    return params.data?.asOf;
                },
                valueFormatter: this.gridService.asOfValueFormatter, //getValueFormatter,
                cellClassRules: {
                    'text-pink': (params) => !params.node.footer &&
                        (params.data?.progressId && (params.data?.asOf.includes('-') || params.data?.asOf.startsWith('As'))),
                    'text-na': (params) => !params.node.footer &&
                        (!params.data?.progressId || params.data?.asOf === 'N/A')
                },

                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                comparator: this.gridService.getDateComparator,
                width: 110, /*hide: !this.approvalMode*/
                //cellClassRules: { 'text-muted': this.gridService.getCellClassRules }
            };

            // target info static columns
            let years = [{ id: new Date().getFullYear(), name: new Date().getFullYear().toString() }];
            for (let i = 1; i <= 3; i++)
                years.push({ id: years[0].id + i, name: `${years[0].id + i}` });

            this.gridColumns.targetInfoCols = [{    // year
                colId: 'year',
                field: 'year',
                headerName: 'Year', headerClass: 'bg-header-color-3',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: true,
                    colType: ColDataType.SelectSingle,
                    showInfoIcon: false
                },
                cellEditorParams: {
                    values: [{ id: 0, name: 'All years' }, ...years], // min: 2000, max: 2050 },
                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {
                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);
                    }, onCellLink: (colId: string, node: IRowNode, gotoCell?: boolean) => {
                        let params: any = {
                            projId: node.data.projId, profId: node.data.profId, orgId: node.data.orgId,
                            month: node.data.month, year: node.data.year
                        };
                        if (!this.selectedVals.isTarget) {
                            params = {...params, comment: { columnId: colId, activityId: node.data.id,
                                activityProgressId: node.data.progressId, month: node.data.month, year: node.data.year } }
                        } else
                            params = { ...params, comment: { targetId: node.data.id, columnId: colId } };
                        this.generateLink(params, gotoCell);
                    }
                },
                cellRenderer: 'sabaSelectValRenderer',
                valueFormatter: this.gridService.getValueFormatter,
                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                comparator: this.gridService.getNumComparator,
                width: 95
            },
            {   // quarter
                coldId: 'qtr',
                field: 'qtr',
                headerName: 'Qtr', headerClass: 'bg-header-color-3',
                headerComponent: GridCustomHeader,
                headerComponentParams: {
                    isRequired: true,
                    colType: ColDataType.SelectSingle,
                    showInfoIcon: false
                },
                cellEditorParams: {
                    values: [
                        { id: 1, name: 'Qtr-1' },
                        { id: 2, name: 'Qtr-2' },
                        { id: 3, name: 'Qtr-3' },
                        { id: 4, name: 'Qtr-4' }
                    ],
                    onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {
                        this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);
                    }, onCellLink: (colId: string, node: IRowNode, gotoCell?: boolean) => {
                        let params: any = {
                            projId: node.data.projId, profId: node.data.profId, orgId: node.data.orgId,
                            month: node.data.month, year: node.data.year
                        };
                        if (!this.selectedVals.isTarget) {
                            params = {...params, comment: { columnId: colId, activityId: node.data.id,
                                activityProgressId: node.data.progressId, month: node.data.month, year: node.data.year } }
                        } else
                            params = { ...params, comment: { targetId: node.data.id, columnId: colId } };
                        this.generateLink(params, gotoCell);
                    }
                },
                cellRenderer: 'sabaSelectValRenderer',
                valueFormatter: this.gridService.getValueFormatter,
                filterParams: { valueFormatter: this.gridService.getFilterValueFormatter },
                columnGroupShow: 'open',
                lockPinned: true,
                width: 90
            }];
        } catch (ex) {
            console.error(ex);
            this.messageService.error('Something went wrong.');
        }
    }

    private getGroupsState(target?: boolean): void {
        let storeName = target ? 'tgroup1Collapsed' : 'group1Collapsed';
        this.groupCollapsed[0] = JSON.parse(localStorage.getItem(storeName) || '[]');
        storeName = storeName.replace('1', '2');
        this.groupCollapsed[1] = JSON.parse(localStorage.getItem(storeName) || '[]');
        storeName = storeName.replace('2', '3');
        this.groupCollapsed[2] = JSON.parse(localStorage.getItem(storeName) || '[]');
        storeName = storeName.replace('3', '4');
        this.groupCollapsed[3] = JSON.parse(localStorage.getItem(storeName) || '[]');
    }

    // init as per profile and project selected
    initGrid(isTarget: boolean): void {
        try {
            // by reaching this, the gridColumns are already asssigned to by the parent
            this.working = true;

            // reset/refresh grid: keep the first cols
            this.columnDefs[1]['hide'] = !this.approvalMode;
            if (this.renameAsOfCol) {
                this.gridColumns.progressAsOfCol.headerName = 'Period';
                this.gridColumns.progressAsOfCol.headerTooltip = '';
                this.gridColumns.progressAsOfCol.headerComponentParams.isRequired = false;
                this.gridColumns.progressAsOfCol.headerComponentParams.showInfoIcon = false;
            } else {
                this.gridColumns.progressAsOfCol.headerName = 'As of';
                this.gridColumns.progressAsOfCol.headerTooltip = 'As of the end of this month.';
                this.gridColumns.progressAsOfCol.headerComponentParams.isRequired = true;
                this.gridColumns.progressAsOfCol.headerComponentParams.showInfoIcon = true;
            }

            this.columnDefs = [this.columnDefs[0], this.columnDefs[1],
                               this.columnDefs[2], this.columnDefs[3]];
            this.gridApi.setColumnDefs(this.columnDefs);

            // create columns
            if (!isTarget) {
                this.columnDefs = this.columnDefs.concat(this.gridColumns.progressActIdCol);
                this.getGroupsState();

                // status group and columns
                this.gridColumns.commonCols[2].columnGroupShow = 'open';
                this.gridColumns.hiddenCol.headerClass = 'bg-header-color-1';
                this.columnDefs.push({
                    groupId: 'ProgressStatus', headerTooltip: 'Double click to expand or collapse this section.',
                    headerName: 'Status', headerClass: 'bg-header-group-color-1',
                    suppressMovable: true,
                    marryChildren: true,
                    openByDefault: !this.groupCollapsed[0].includes(this.selectedVals.profId),
                    children: [...this.gridColumns.progressStatusCols, this.gridColumns.hiddenCol]
                });

                // location cols
                let hiddenCol = Object.assign({}, this.gridColumns.hiddenCol)
                hiddenCol.headerClass = 'bg-header-color-2';
                this.columnDefs.push({
                    groupId: `${ColumnVarType.ProgressStatic}`, headerTooltip: 'Double click to expand or collapse this section.',
                    headerName: 'Location', headerClass: 'bg-header-group-color-2',
                    suppressMovable: true,
                    marryChildren: true,
                    openByDefault: !this.groupCollapsed[1].includes(this.selectedVals.profId),
                    children: [...this.gridColumns.commonCols, this.gridColumns.progressCommCol, hiddenCol]
                });

                // add info and dynamic cols
                let infoCols = [];
                let progCols = [];
                this.dynamicColumns.forEach(col => {
                    // update static cols props
                    if (col.type === ColumnVarType.ProgressStatic) {
                        let staticCol = this.gridColumns.commonCols.concat(this.gridColumns.progressCommCol)
                            .find(sc => sc.field === col.name.toLowerCase());
                        if (staticCol) {
                            if (staticCol.field === 'community')
                                this.gridColumns.progressCommCol.colId = col.id;
                            staticCol.colId = col.id;
                            staticCol.headerName = col.displayName || col.name;
                            staticCol.headerTooltip = col.description;
                            staticCol.headerComponentParams = {
                                enableEdit: false,
                                isRequired: col.isRequired,
                                colType: col.fieldType,
                                showInfoIcon: col.description?.length > 0
                            }
                        }
                        return;
                    }

                    // add dynamic column
                    let colDef: ColDef = {
                        colId: `${col.id}`,
                        field: 'colVals.dCol' + col.id,
                        headerName: col.displayName || col.name,
                        headerTooltip: col.description,
                        headerComponent: GridCustomHeader,
                        headerComponentParams: {
                            enableEdit: false,
                            isRequired: col.isRequired,
                            type: col.type,
                            colType: col.fieldType,
                            colOrder: col.order,
                            showInfoIcon: col.description?.length > 0,
                            pivot: true
                        },
                        cellEditorParams: {
                            ...this.gridService.getCellEditorParams(col),
                            onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {
                                this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);
                            }, onCellLink: (colId: string, node: IRowNode, gotoCell?: boolean) => {
                                let params: any = {
                                    projId: node.data.projId, profId: node.data.profId, orgId: node.data.orgId,
                                    month: node.data.month, year: node.data.year
                                };
                                if (!this.selectedVals.isTarget) {
                                    params = {...params, comment: { columnId: colId, activityId: node.data.id,
                                        activityProgressId: node.data.progressId } }
                                } else
                                    params = { ...params, comment: { targetId: node.data.id, columnId: colId } };
                                this.generateLink(params, gotoCell);
                            }
                        },
                        valueFormatter: this.gridService.getValueFormatter, // add formatting for col if set
                        cellRenderer: this.gridService.getCellRenderer(col.fieldType),
                        cellClassRules: {
                            'text-end': this.gridService.getCellTextAlign, 'text-danger': this.gridService.getCellClassRules
                        }, columnGroupShow: 'open', lockPinned: true,
                        enableRowGroup: this.gridService.getEnableGrouping(col.fieldType),
                        //aggFunc: this.gridService.getAggFunc(col.fieldType),
                        width: getColWidth(col.name + (col.isRequired ? '*':''), [false, col.description?.length > 0])
                    };

                    // attach cell click for attachment columns
                    if (col.fieldType === ColDataType.Attachment) {
                        colDef.onCellClicked = (event) => this.onCellClicked(event);
                        colDef.comparator = this.gridService.getNumComparator;
                    }

                    // attach num and date comparators
                    if (col.fieldType >= ColDataType.Number && col.fieldType <= ColDataType.GPS) {
                        colDef.comparator = this.gridService.getNumComparator;
                        colDef.valueGetter = (params) => this.gridService.getNumericValue(col.id, params);
                        colDef.valueParser = (params) => parseFloat(params.newValue);
                    }

                    if (col.fieldType === ColDataType.Date) {
                        colDef.filterParams = { valueFormatter: this.gridService.getFilterValueFormatter };
                        colDef.comparator = this.gridService.getDateComparator;
                    }

                    if (col.type === ColumnVarType.ProgressInfo) {
                        colDef.headerClass = 'bg-header-color-3';
                        infoCols.push(colDef);
                    } else if (col.type === ColumnVarType.Progress) {
                        colDef.headerClass = 'bg-header-color-4';
                        progCols.push(colDef);
                    }
                });

                // sort dynamic cols by order and push to grid's columns
                infoCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);
                progCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);

                // make first col always visible in the group
                if (infoCols.length)
                    infoCols[0].columnGroupShow = null;

                if (progCols.length && !this.approvalMode)
                    progCols[0].columnGroupShow = null;

                hiddenCol = Object.assign({}, this.gridColumns.hiddenCol)
                hiddenCol.headerClass = 'bg-header-color-3';
                this.columnDefs.push({
                    groupId: `${ColumnVarType.ProgressInfo}`,
                    headerName: 'Info', headerClass: 'bg-header-group-color-3',
                    headerTooltip: 'Double click to expand or collapse this section.',
                    marryChildren: true,
                    openByDefault: !this.groupCollapsed[2].includes(this.selectedVals.profId),
                    children: infoCols.length > 1 ? [...infoCols, hiddenCol] : [...infoCols]
                });

                hiddenCol = Object.assign({}, this.gridColumns.hiddenCol)
                hiddenCol.headerClass = 'bg-header-color-4';
                this.columnDefs.push({
                    groupId: `${ColumnVarType.Progress}`,
                    headerName: 'Cumulative progress', headerClass: 'bg-header-group-color-4',
                    headerTooltip: 'Double click to expand or collapse this section.',
                    marryChildren: true,
                    openByDefault: !this.groupCollapsed[3].includes(this.selectedVals.profId),
                    children: progCols.length > 1 ? [this.gridColumns.progressAsOfCol, ...progCols, hiddenCol] : [this.gridColumns.progressAsOfCol, ...progCols]
                });
                // ---------------------------------------------------------------
            } else {      // target
                // ---------------------------------------------------------------
                this.getGroupsState(true);

                // location cols
                this.gridColumns.commonCols[2].columnGroupShow = null;
                this.gridColumns.hiddenCol.headerClass = 'bg-header-color-2';
                this.columnDefs.push({
                    groupId: `${ColumnVarType.TargetStatic}`, headerTooltip: 'Double click to expand or collapse this section.',
                    headerName: 'Location', headerClass: 'bg-header-group-color-2',
                    suppressMovable: true,
                    marryChildren: true,
                    openByDefault: !this.groupCollapsed[1].includes(this.selectedVals.profId),
                    children: [...this.gridColumns.commonCols, this.gridColumns.hiddenCol]
                });

                // add info and dynamic cols for target
                let infoCols = [];
                let targetCols = [];
                this.dynamicColumns.forEach(col => {
                    // update static cols props
                    if (col.type === ColumnVarType.TargetStatic) {
                        let staticCol = this.gridColumns.commonCols.find(sc => sc.field === col.name.toLowerCase());
                        if (staticCol) {
                            staticCol.colId = col.id;
                            staticCol.headerName = col.displayName || col.name;
                            staticCol.headerTooltip = col.description;
                            staticCol.headerComponentParams = {
                                enableEdit: false,
                                isRequired: col.isRequired,
                                colType: col.fieldType,
                                showInfoIcon: col.description?.length > 0
                            }
                        }
                        return;
                    }

                    // add dynamic column
                    let colDef: ColDef = {
                        colId: `${col.id}`,
                        field: 'colVals.dCol' + col.id,
                        headerName: col.displayName || col.name,
                        headerTooltip: col.description,
                        headerComponent: GridCustomHeader,
                        headerComponentParams: {
                            enableEdit: false,
                            isRequired: col.isRequired,
                            type: col.type,
                            colType: col.fieldType,
                            colOrder: col.order,
                            showInfoIcon: col.description?.length > 0,
                            pivot: true
                        },
                        cellEditorParams: {
                            ...this.gridService.getCellEditorParams(col),
                            onComment: (colDef: ColDef, node: IRowNode, ctxMenu: DOMRect, isNote?: boolean, readOnly?: boolean) => {
                                this.commentModalComponent.open(colDef, node, ctxMenu, isNote, readOnly);
                            }, onCellLink: (colId: string, node: IRowNode, gotoCell?: boolean) => {
                                let params: any = {
                                    projId: node.data.projId, profId: node.data.profId, orgId: node.data.orgId,
                                    month: node.data.month, year: node.data.year
                                };
                                if (!this.selectedVals.isTarget) {
                                    params = {...params, comment: { columnId: colId, activityId: node.data.id,
                                        activityProgressId: node.data.progressId, month: node.data.month, year: node.data.year } }
                                } else
                                    params = { ...params, comment: { targetId: node.data.id, columnId: colId } };
                                this.generateLink(params, gotoCell);
                            }
                        },
                        valueFormatter: this.gridService.getValueFormatter, // add formatting for col if set
                        cellRenderer: this.gridService.getCellRenderer(col.fieldType),
                        cellClassRules: { 'text-end': this.gridService.getCellTextAlign, 'text-danger': this.gridService.getCellClassRules },
                        lockPinned: true, enableRowGroup: this.gridService.getEnableGrouping(col.fieldType),
                        //aggFunc: this.gridService.getAggFunc(col.fieldType),
                        width: getColWidth(col.name + (col.isRequired ? '*' : ''), [false, col.description?.length > 0]),
                        columnGroupShow: 'open'
                    };

                    // attach cell click for attachment columns
                    if (col.fieldType === ColDataType.Attachment) {
                        colDef.onCellClicked = (event) => this.onCellClicked(event);
                        colDef.comparator = this.gridService.getNumComparator;
                    }

                    // attach num and date comparators
                    if (col.fieldType >= ColDataType.Number && col.fieldType <= ColDataType.GPS) {
                        colDef.comparator = this.gridService.getNumComparator;
                        colDef.valueGetter = (params) => this.gridService.getNumericValue(col.id, params);
                        colDef.valueParser = (params) => parseFloat(params.newValue);
                    }

                    if (col.fieldType === ColDataType.Date) {
                        colDef.filterParams = { valueFormatter: this.gridService.getFilterValueFormatter };
                        colDef.comparator = this.gridService.getDateComparator;
                    }

                    if (col.type === ColumnVarType.TargetInfo) {
                        colDef.headerClass = 'bg-header-color-3';
                        infoCols.push(colDef);
                    } else if (col.type === ColumnVarType.Target) {
                        colDef.headerClass = 'bg-header-color-4';
                        targetCols.push(colDef);
                    }
                });

                // sort dynamic cols by order and push to grid's columns
                infoCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);
                targetCols.sort((x, y) => (x.headerComponentParams.colOrder > y.headerComponentParams.colOrder) ? 1 : -1);

                // make first col always visible in the group
                if (targetCols.length)
                    targetCols[0].columnGroupShow = null;

                // if dynamic info cols are there and info group is collapsed
                if (infoCols.length && this.groupCollapsed[2])
                    this.gridColumns.hiddenCol.hide = false;

                let hiddenCol = Object.assign({}, this.gridColumns.hiddenCol)
                hiddenCol.headerClass = 'bg-header-color-3';
                this.columnDefs.push({
                    groupId: `${ColumnVarType.TargetInfo}`, headerTooltip: 'Double click to expand or collapse this section.',
                    headerName: 'Info', headerClass: 'bg-header-group-color-3',
                    marryChildren: true,
                    openByDefault: !this.groupCollapsed[2].includes(this.selectedVals.profId),
                    children: [...this.gridColumns.targetInfoCols, hiddenCol, ...infoCols]
                });

                hiddenCol = Object.assign({}, this.gridColumns.hiddenCol)
                hiddenCol.headerClass = 'bg-header-color-4';
                this.columnDefs.push({
                    groupId: `${ColumnVarType.Target}`,
                    headerName: 'Target', headerClass: 'bg-header-group-color-4',
                    headerTooltip: 'Double click to expand or collapse this section.',
                    marryChildren: true, openByDefault: !this.groupCollapsed[3].includes(this.selectedVals.profId),
                    children: targetCols.length > 1 ? [...targetCols, hiddenCol] : [...targetCols]
                });
            }
            
            this.gridReady();
            this.working = false;
        } catch (ex) {
            console.error(ex);
            this.messageService.error('Something went wrong.');
        } 
    }

    // init'ed and ready
    onGridReady(params: GridReadyEvent) {
        this.gridApi = params.api;
        this.gridColumnApi = params.columnApi;
    }

    private gridReady(): void {
        // bind to info group expand icon
        setTimeout(() => {
            // register click event on top left header cell
            const headerCell = document.querySelector('.ag-header-cell[col-id="rowNum"]');
            if (headerCell) {
                headerCell.addEventListener('click', (e) => {
                    this.gridApi.selectAllOnCurrentPage();
                });
            }

            // register approve all click event
            const btnApproveAll: HTMLButtonElement = document.querySelector('btn-approve-all-header button');
            if (btnApproveAll) {
                btnApproveAll.addEventListener('click', (e) => {
                    this.approveAll.emit(btnApproveAll.classList.contains('active'));
                });
            }

            // register click event info column group collapase button
            const colGroupRow = document.querySelector('.ag-header-container .ag-header-row-column-group');
            colGroupRow.addEventListener('click', (e: MouseEvent) => this.storeGroupState(e));
            colGroupRow.addEventListener('dblclick', (e: MouseEvent) => this.storeGroupState(e, true));

            const groupExpandIcons = document.querySelectorAll('.ag-header-group-cell .ag-header-icon.ag-header-expand-icon');
            if (groupExpandIcons) {
                groupExpandIcons.forEach(icon => {
                    icon.setAttribute('title', icon.classList.contains('ag-header-expand-icon-expanded')
                        ? 'Collapse columns group' : 'Expand columns group');
                });
            }

            // scroll grid even if cursor is outide the viewport (i.e. in header)
            const gridHeader = document.querySelector('.ag-header-viewport');
            const gridViewPort = document.querySelector('.ag-body-viewport');
            const gridViewPortCenter = document.querySelector('.ag-center-cols-viewport');

            gridHeader.addEventListener('wheel', (e: any) => {
                e.preventDefault();
                const deltaY = e.deltaY || e.wheelDeltaY;
                const deltaX = e.deltaX || e.wheelDeltaX;
                gridViewPort.scrollTop += deltaY;
                gridViewPortCenter.scrollLeft += deltaX;
            });

            this.setExcelExportParams();
        }, 500);
    }

    private storeGroupState(e: MouseEvent, dblClick?: boolean): void {
        let colId;
        const target = e.target as HTMLElement;
        let group = dblClick ? target.querySelector('.ag-header-group-text') as HTMLSpanElement :
            target.parentElement.parentElement.querySelector('.ag-header-group-text') as HTMLSpanElement;

        if (!group)
            return;

        if (group.innerText.startsWith('Status'))
            colId = 1;
        else if (group.innerText.startsWith('Location'))
            colId = 2;
        else if (group.innerText.startsWith('Info'))
            colId = 3;
        else colId = 4;

        const storeName = `${this.selectedVals.isTarget ? 't' : ''}group${colId}Collapsed`;
        let stored = JSON.parse(localStorage.getItem(storeName) || '[]');
        if (stored.includes(this.selectedVals.profId))
            stored = stored.filter(i => i !== this.selectedVals.profId);
        else
            stored.push(this.selectedVals.profId);
        localStorage.setItem(storeName, JSON.stringify(stored));
        this.groupCollapsed[colId - 1] = stored;
    }

    // *** Other grid's functions --------------------------------
    onCellClicked(event: CellClickedEvent): void {
        const colId = event.column.getColId();
        if (colId === 'rowNum') {                   // if the row number column was clicked
            const cols = event.columnApi.getColumns().map(c => c.getColId());
            let lastVisibleCol = cols[cols.length - 1];
            if (lastVisibleCol.startsWith('cols-hidden')) {
                for (let i = cols.length - 2; i >= 0; i--) {
                    if (!cols[i].startsWith('cols-hidden')) {
                        lastVisibleCol = cols[i];
                        break;
                    }
                }
            }
            event.api.clearRangeSelection();
            event.api.addCellRange({
                columnStart: cols[2],               // start from column after rowNum and approval status
                columnEnd: lastVisibleCol,
                rowStartIndex: event.rowIndex,
                rowEndIndex: event.rowIndex
            });
        } else if (event.data.id > 0 && event.data.id > 0 && event.data.file)
            this.attachmentModalComponent.ngOnInit(colId, event);
    }

    onCellFocused(event: CellFocusedEvent): void {
        this.gridApi.deselectAll();
    }

    @Output() filterApplied = new EventEmitter<string>();
    onFilterApplied(event: FilterChangedEvent): void {
        const col = event.columns[0].getColDef().headerName;
        this.filterApplied.emit(event.columns[0].isFilterActive() ? col : '-' + col);

        let rowData = [];
        this.gridApi.forEachNodeAfterFilter(node => rowData.push(node.data));
        
        // refresh approve all button
        this.refreshApproveAll(rowData);
    }

    @Output() sortApplied = new EventEmitter<number>();
    onSortChanged(event: SortChangedEvent): void {
        let colsOnSort = 0;
        this.gridColumnApi.getColumns()
            .forEach(c => { if (c.getSort()) colsOnSort++ });
        this.sortApplied.emit(colsOnSort);
    }

    onColumnRowGroupChanged(event: ColumnRowGroupChangedEvent) {
        const colId = event.column?.getColId();
        if(colId)
            event.columnApi.applyColumnState({ state: [{ colId: colId, hide: false }] });
    }

    onRowGroupChanged() {
        if (!this.gridColumnApi)
            return;

        const rowGroupColumns = this.gridColumnApi.getRowGroupColumns();

        if (rowGroupColumns.length > 0) {
            const firstRowGroupColumn = rowGroupColumns[0];

            this.gridOptions.autoGroupColumnDef.headerName =
                firstRowGroupColumn.getColDef().headerName;
        }

        this.gridApi.setAutoGroupColumnDef(this.gridOptions.autoGroupColumnDef);
        this.gridApi.refreshHeader();
    }
    // -----------------------------------------------------------

    // *** DATA
    refreshGridRows(data: IProgressDataView[] | ITargetDataView[]): void {
        this.gridApi.setDefaultColDef(this.defaultColDefs);
        
        // Filter out rows with N/A periods for progress data
        let filteredData = [...data];
        if (!this.selectedVals.isTarget && this.hideNAPeriods) {
            filteredData = this.filterOutNAPeriods(filteredData);
        }
        
        this.rowData = filteredData;
        
        // refresh approve all button
        this.refreshApproveAll();
    }

    /**
     * Filter out data rows that have N/A periods (for progress data only) and
     * activities without meaningful cumulative progress data in the filtered time series.
     * Uses a balanced approach to ensure interventions don't become completely empty.
     * @param data The data array to filter
     * @returns Filtered data array with balanced filtering approach
     */
    private filterOutNAPeriods(data: any[]): any[] {
        // Group data by intervention (profId) to ensure each intervention has at least one activity
        const dataByIntervention = data.reduce((groups, item) => {
            const profId = item.profId || 'unknown';
            if (!groups[profId]) {
                groups[profId] = [];
            }
            groups[profId].push(item);
            return groups;
        }, {});
        
        const filteredData = [];
        
        // Process each intervention separately
        Object.keys(dataByIntervention).forEach(profId => {
            const interventionData = dataByIntervention[profId];
            const validActivities = [];
            
            // For progress data, apply validation for time series filtering
            if (interventionData.length > 0 && 'asOf' in interventionData[0]) {
                
                // First pass: find activities that meet strict criteria
                for (const item of interventionData) {
                    // Check if this is an ongoing activity - only apply strict filtering to ongoing activities
                    const isOngoingActivity = item.status === 0; // ActivityStatus.Ongoing = 0
                    
                    // STEP 1: Basic validation - must have valid asOf and progressId
                    const hasValidAsOf = item.asOf && 
                                       item.asOf !== 'N/A' && 
                                       item.asOf.trim() !== '';
                    
                    const hasValidProgressId = item.progressId && item.progressId > 0;
                    
                    if (hasValidAsOf && hasValidProgressId) {
                        if (isOngoingActivity) {
                            // STEP 2: For ongoing activities, check if they have actual meaningful cumulative progress values
                            const hasProgressValues = item.colVals && 
                                                    Object.keys(item.colVals).some(key => {
                                                        const value = item.colVals[key];
                                                        
                                                        if (value === null || value === undefined || value === '') {
                                                            return false;
                                                        }
                                                        
                                                        const stringValue = value.toString().trim();
                                                        
                                                        // Filter out common empty/default values
                                                        if (stringValue === '' || 
                                                            stringValue === '0' || 
                                                            stringValue === '0.0' || 
                                                            stringValue === '0.00' ||
                                                            stringValue === 'N/A' ||
                                                            stringValue === '-' ||
                                                            stringValue === 'null' ||
                                                            stringValue === 'undefined') {
                                                            return false;
                                                        }
                                                        
                                                        // For numeric values, ensure they're greater than 0
                                                        const numValue = parseFloat(stringValue.replace(/,/g, ''));
                                                        if (!isNaN(numValue)) {
                                                            return numValue > 0;
                                                        }
                                                        
                                                        // For non-numeric values, they should be meaningful strings
                                                        return stringValue.length > 0;
                                                    });
                            
                            if (hasProgressValues) {
                                validActivities.push(item);
                            }
                        } else {
                            // For non-ongoing activities (Completed, Archived, Cancelled), apply more lenient filtering
                            // Just check that it's not completely empty
                            validActivities.push(item);
                        }
                    }
                }
                
                // If no activities passed strict validation, include the most recent activity
                // to ensure the intervention doesn't disappear completely
                if (validActivities.length === 0 && interventionData.length > 0) {
                    // Find the most recent activity (prefer those with valid asOf dates)
                    const activitiesWithDates = interventionData.filter(item => 
                        item.asOf && item.asOf !== 'N/A' && item.asOf.trim() !== ''
                    );
                    
                    let mostRecentActivity;
                    if (activitiesWithDates.length > 0) {
                        // Sort by asOf date
                        mostRecentActivity = activitiesWithDates.sort((a, b) => {
                            return b.asOf.localeCompare(a.asOf);
                        })[0];
                    } else {
                        // Fallback to first activity if no valid dates
                        mostRecentActivity = interventionData[0];
                    }
                    
                    validActivities.push(mostRecentActivity);
                }
                
                filteredData.push(...validActivities);
            } else {
                // For non-progress data (like target data), keep all items
                filteredData.push(...interventionData);
            }
        });
        
        return filteredData;
    }

    private refreshApproveAll(rowData?: any): void {
        if (this.approvalMode) {
            rowData = rowData || this.rowData;

            setTimeout(() => {
                const btnApproveAll: HTMLButtonElement = document.querySelector('btn-approve-all-header button');
                btnApproveAll.classList.remove('d-none');
                btnApproveAll.classList.remove('active');
                btnApproveAll.title = 'Approve all';

                if (!rowData.length || rowData.findIndex(r => r.dateSubmitted) === -1)
                    btnApproveAll.classList.add('d-none');
                else if (rowData.findIndex(r => !r.dateApproved) === -1) {
                    btnApproveAll.classList.add('active');
                    btnApproveAll.title = 'Toggle approve all';
                }
            }, 100);
        }
    }

    @Output() approve = new EventEmitter<CellClickedEvent>();
    @Output() approveAll = new EventEmitter<boolean>();
    private approveData(event: CellClickedEvent): void {
        this.approve.emit(event);
    }

    private setExcelExportParams(): void {
        let fName = 'AIMS3_' + this.selectedVals.prof;
        //fName += this.selectedVals.proj.replace(/[<>:"\/\\|?*]+/g, '');
        fName += this.selectedVals.isTarget ? '_Target.xlsx' : '_Progress.xlsx';
        let cols = this.gridColumnApi.getColumns().map(c => c.getColId());
        cols = cols.filter(c => !c.startsWith('cols-hidden'));

        // set excel export params
        this.gridOptions.defaultExcelExportParams = {
            author: 'AIMS 3.0', fileName: fName, allColumns: true,
            sheetName: this.selectedVals.prof.replace(/[<>:"\/\\|?*]+/g, ''),
            columnKeys: cols
        };
    }

    onComment(e: any): void {
        const node = this.gridApi.getRowNode(e.node.id);
        if (!node.data.comments)
            node.data.comments = [];

        if (e.updated) {
            const comment = node.data.comments.find(c => c.commentId === e.commentId);
            if(comment) comment.isResolved = !comment.isResolved;
        } else if (e.deleted)
            node.data.comments = node.data.comments.filter(c => c.commentId !== e.commentId);
        else {
            node.data.comments = [...node.data.comments, {
                id: e.isProgress ? node.data.progressId : node.data.id,
                commentId: e.commentId, colId: e.colId, isNote: e.isNote, isResolved: e.isResolved
            }];
        }
        this.gridApi.refreshCells({ rowNodes: [node], force: true, suppressFlash: true });
    }

    generateLink(e: any, gotoCell?: boolean): void {
        const comment = e.comment;

        // format: {OrgId}S{ProfId}a{ProjId}b{ActOrTarId}a{Month-Year}n{ColId}
        let link = `${e.orgId}S${e.profId}`;
        link += `a${e.projId}`;

        if (!comment.commentOn) {
            const colMap = this.commentModalComponent.staticColMap
                .find(c => c.colId === comment.columnId);
            if (colMap)
                comment.columnId = colMap.dColId;
        }

        if (comment.activityId > 0) {
            link += `b${comment.activityId}`;
            if (comment.activityProgressId > 0)
                link += `a${e.month}-${e.year}`;
            else link += `aMY`;
        } else link += `b${comment.targetId}a0`;
        
        link += `n${comment.columnId}`;
        link = (comment.commentOn ? '#comment=' : '#cell=') + link;

        if (gotoCell) {
            window.open(location.origin + '/data-entry/targets-progress' + link, '_blank');
        } else {
            navigator.clipboard.writeText(location.origin + '/data-entry/targets-progress' + link);
            this.messageService.info('Link copied to your clipboard.', 'Link Created');
        }
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}