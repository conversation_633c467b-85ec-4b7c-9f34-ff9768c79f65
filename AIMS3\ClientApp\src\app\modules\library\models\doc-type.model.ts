import { ColDataType, UploadLocation } from "../../../shared/enums";

export class DocType {
    constructor(
        public id: number,
        public typeName: string,
        public uploadLocation: UploadLocation,
        public isActRequired: boolean,
        public fields?: DocTypeField[]
    ) { }
}

export class DocTypeField {
    constructor(
        public id: number,
        public documentTypeId: number,
        public fieldName: string,
        public fieldType: ColDataType,
        public isRequired: boolean,
        public fieldTypeValues?: any,
        public fTypeInfo?: string[]
    ) { }
}