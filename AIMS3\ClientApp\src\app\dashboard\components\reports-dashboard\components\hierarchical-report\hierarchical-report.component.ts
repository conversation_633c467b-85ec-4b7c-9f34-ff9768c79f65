import { Component, OnInit, OnDestroy, Input, OnChanges, SimpleChanges, ChangeDetectionStrategy, TrackByFunction } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReportFilter, DynamicColumnData, ActivityInfo, ActivityProgress } from '../../models/reports.model';
import { ReportsService } from '../../services/reports.service';
import { Subject, of } from 'rxjs';
import { takeUntil, timeout, catchError, finalize } from 'rxjs/operators';
import { DynamicColumnsDisplayComponent } from '../dynamic-columns-display/dynamic-columns-display.component';
import { CategoryModalComponent } from '../category-modal/category-modal.component';

@Component({
  selector: 'app-hierarchical-report',
  templateUrl: './hierarchical-report-optimized.component.html',
  styleUrls: ['./hierarchical-report.component.scss'],
  standalone: true,
  imports: [CommonModule, DynamicColumnsDisplayComponent, CategoryModalComponent],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class HierarchicalReportComponent implements OnInit, OnDestroy, OnChanges {
  @Input() filters: ReportFilter;
  @Input() reportData: any[] = []; // New input to receive pre-loaded hierarchical data
  
  // Local reportData for the component
  hierarchicalData: any[] = [];
  loading: boolean = false;
  error: string | null = null;
  expandedGroups: { [key: string]: boolean } = {}; // Track expanded/collapsed state
  
  // Track expanded states
  expandedProjects: {[key: string]: boolean} = {};
  expandedInterventions: {[key: string]: boolean} = {};
  
  // Debug mode flag
  showDebugInfo: boolean = true; // Set to true to show debug info by default
  
  // Dynamic columns loading state
  dynamicColumnsLoading: boolean = false;
  
  // Category modal state
  categoryModalVisible: boolean = false;
  selectedCategoryId: string = '';
  categoryDynamicColumns: any[] = [];
  categoryActivities: any[] = [];
  
  // Performance optimization - cache function results
  private dynamicColumnsCache = new Map<string, boolean>();
  private columnNamesCache = new Map<string, string[]>();
  
  // Performance optimization - debounce expensive operations
  private debounceTimer: any;
  
  // TrackBy functions for ngFor performance
  trackByGroupId: TrackByFunction<any> = (index, item) => item.id;
  trackByProjectId: TrackByFunction<any> = (index, item) => item.id;
  trackByInterventionId: TrackByFunction<any> = (index, item) => item.id;
  trackByActivityId: TrackByFunction<any> = (index, item) => item.id;
  trackByColumnName: TrackByFunction<string> = (index, item) => item;
  
  // Memory optimization settings
  maxActivitiesPerIntervention = 50; // Limit activities shown per intervention
  maxDynamicColumnsPerActivity = 20; // Limit dynamic columns shown per activity
  enableVirtualScrolling = true; // Enable virtual scrolling for large lists
  
  // Lazy loading state
  loadedInterventions = new Set<string>();
  
  private destroy$ = new Subject<void>();

  constructor(private reportsService: ReportsService) { }

  ngOnInit(): void {
    console.log('🔍 HierarchicalReportComponent initialized');
    
    // Check if we already have report data via @Input
    if (this.reportData && this.reportData.length > 0) {
      console.log('🔍 Using provided hierarchical data:', this.reportData);
      this.hierarchicalData = this.reportData;
      this.loading = false;
      this.error = null;
      
      // Expand the first group by default
      if (this.hierarchicalData.length > 0) {
        this.expandedGroups[this.hierarchicalData[0].id] = true;
      }
    } else {
      // Load from API with dynamic columns enabled
      this.loadReport({ includeDynamicColumns: true });
    }
  }
  
  ngOnChanges(changes: SimpleChanges): void {
    // When report data changes from parent
    if (changes.reportData && changes.reportData.currentValue) {
      console.log('🔍 Hierarchical report data changed via @Input:', this.reportData);
      
      // Clear performance cache when new data is loaded
      this.clearPerformanceCache();
      
      this.hierarchicalData = this.reportData;
      this.loading = false;
      this.error = null;
      
      // Expand the first group by default
      if (this.hierarchicalData.length > 0) {
        this.expandedGroups[this.hierarchicalData[0].id] = true;
      }
    }
    
    // When filters change but we don't have report data via input
    if (changes.filters && !this.reportData?.length) {
      console.log('🔍 Filters changed, loading new data:', this.filters);
      this.loadReport({ includeDynamicColumns: true });
    }
  }
  
  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    
    // Clear debounce timer
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
    }
    
    // Clear caches
    this.clearPerformanceCache();
  }
  
  /**
   * Clear performance caches when data changes
   */
  private clearPerformanceCache(): void {
    this.dynamicColumnsCache.clear();
    this.columnNamesCache.clear();
    this.loadedInterventions.clear();
  }

  /**
   * Get limited activities for an intervention to prevent memory overload
   */
  getLimitedActivities(activities: any[]): any[] {
    if (!activities || activities.length <= this.maxActivitiesPerIntervention) {
      return activities;
    }
    return activities.slice(0, this.maxActivitiesPerIntervention);
  }

  /**
   * Get limited dynamic columns for an activity to prevent memory overload
   */
  getLimitedDynamicColumns(dynamicColumns: any[]): any[] {
    if (!dynamicColumns || dynamicColumns.length <= this.maxDynamicColumnsPerActivity) {
      return dynamicColumns;
    }
    return dynamicColumns.slice(0, this.maxDynamicColumnsPerActivity);
  }

  /**
   * Check if intervention activities should be loaded (lazy loading)
   */
  shouldLoadActivities(interventionId: string): boolean {
    return this.loadedInterventions.has(interventionId);
  }

  /**
   * Load activities for an intervention (lazy loading)
   */
  loadActivitiesForIntervention(interventionId: string): void {
    this.loadedInterventions.add(interventionId);
  }

  /**
   * Limit data size to prevent memory overload
   */
  private limitDataSize(data: any[]): any[] {
    if (!data || data.length === 0) return data;

    return data.map(group => ({
      ...group,
      projects: group.projects?.map(project => ({
        ...project,
        interventions: project.interventions?.map(intervention => ({
          ...intervention,
          activities: this.getLimitedActivities(intervention.activities || []).map(activity => ({
            ...activity,
            dynamicColumns: this.getLimitedDynamicColumns(activity.dynamicColumns || []),
            // Remove heavy objects that can be loaded on demand
            processedDynamicData: null,
            DynamicColumnsValues: null,
            DynamicColactprogvalues: null
          }))
        }))
      }))
    }));
  }

  /**
   * Force garbage collection and memory cleanup
   */
  private forceMemoryCleanup(): void {
    // Clear all caches
    this.clearPerformanceCache();
    
    // Clear expanded states for non-visible items
    const visibleGroups = this.hierarchicalData.map(g => g.id);
    Object.keys(this.expandedGroups).forEach(groupId => {
      if (!visibleGroups.includes(groupId)) {
        delete this.expandedGroups[groupId];
      }
    });

    // Clear project and intervention states for non-visible items
    Object.keys(this.expandedProjects).forEach(projectId => {
      const isVisible = this.hierarchicalData.some(g => 
        g.projects?.some(p => p.id === projectId)
      );
      if (!isVisible) {
        delete this.expandedProjects[projectId];
      }
    });

    // Trigger garbage collection hint (if available)
    if ((window as any).gc) {
      (window as any).gc();
    }
  }
  
  /**
   * Load the hierarchical report data
   * @param options Optional settings for loading the report
   */
  loadReport(options: { includeDynamicColumns?: boolean } = {}): void {
    this.loading = true;
    this.error = null;
    
    // Use the includeDynamicColumns option, defaulting to true for better UX
    const includeDynamicColumns = options.includeDynamicColumns !== false;
    
    console.log(`⏳ Loading hierarchical report with filters:`, this.filters);
    console.log(`⏳ Dynamic columns will be ${includeDynamicColumns ? 'included' : 'excluded'}`);
    
    this.reportsService.getHierarchicalReport(this.filters, { includeDynamicColumns })
      .pipe(
        takeUntil(this.destroy$),
        // Add timeout to avoid hanging UI
        timeout(60000), // 60 seconds timeout
        catchError(error => {
          console.error('❌ Error in loadReport pipe:', error);
          // Try fallback to mock data
          console.warn('⚠️ Falling back to mock data due to API error');
          return of(this.createMockData());
        })
      )
      .subscribe({
        next: (data) => {
          console.log('✅ Hierarchical report data loaded successfully');
          console.log(`✅ Received ${Array.isArray(data) ? data.length : 0} project groups`);
          
          // Clear performance cache when new data is loaded
          this.clearPerformanceCache();
          
          // Limit data size to prevent memory issues
          this.hierarchicalData = this.limitDataSize(data || []);
          this.loading = false;
          
          // Force memory cleanup after data load
          setTimeout(() => this.forceMemoryCleanup(), 1000);
          
          if (!data || (Array.isArray(data) && data.length === 0)) {
            console.warn('⚠️ Hierarchical report returned empty data, falling back to mock data');
            this.loadMockData();
            return;
          }
          
          // Process dynamic columns data for better display
          this.processDynamicColumnsForActivities();
          
          // Expand the first group by default if any exist
          if (this.hierarchicalData.length > 0) {
            this.expandedGroups[this.hierarchicalData[0].id] = true;
          }
          
          // Log sample activity structure for debugging
          if (this.hierarchicalData.length > 0) {
            const sampleActivity = this.findFirstActivity();
            if (sampleActivity) {
              console.log('🔍 SAMPLE ACTIVITY FROM DATABASE:', {
              id: sampleActivity.id,
              uniqueId: sampleActivity.uniqueId,
              hasDynamicColumns: !!sampleActivity.dynamicColumns,
              dynamicColumnsLength: sampleActivity.dynamicColumns ? sampleActivity.dynamicColumns.length : 0,
                dynamicColumnsPreview: sampleActivity.dynamicColumns ? sampleActivity.dynamicColumns.slice(0, 2) : null
            });
            }
          }
        },
        error: (err) => {
          console.error('❌ Error loading hierarchical report:', err);
          this.error = `Failed to load hierarchical report. Showing mock data instead.`;
          this.loading = false;
          
          // Load mock data as fallback
          console.warn('⚠️ Loading mock data as fallback');
          this.loadMockData();
        }
      });
  }
  
  /**
   * Check if dataset has activities that need dynamic columns
   */
  needsDynamicColumns(): boolean {
    if (!this.hierarchicalData || !Array.isArray(this.hierarchicalData) || this.hierarchicalData.length === 0) {
      return false;
    }
    
    // Look for any activities without dynamic columns
    for (const group of this.hierarchicalData) {
      if (!group.projects || !Array.isArray(group.projects)) continue;
      
      for (const project of group.projects) {
        if (!project.interventions || !Array.isArray(project.interventions)) continue;
        
        for (const intervention of project.interventions) {
          if (!intervention.activities || !Array.isArray(intervention.activities)) continue;
          
          // Check if any activity is missing dynamic columns
          const hasActivitiesWithoutDynamicColumns = intervention.activities.some(activity => 
            !activity.dynamicColumns || 
            !Array.isArray(activity.dynamicColumns) || 
            activity.dynamicColumns.length === 0
          );
          
          if (hasActivitiesWithoutDynamicColumns) {
            return true;
          }
        }
      }
    }
    
    return false;
  }
  
  /**
   * Process dynamic columns data for activities
   */
  private processDynamicColumnsForActivities(): void {
    if (!this.hierarchicalData) return;
    
    for (const group of this.hierarchicalData) {
      if (!group.projects) continue;
      
      for (const project of group.projects) {
        if (!project.interventions) continue;
        
        for (const intervention of project.interventions) {
          if (!intervention.activities) continue;
          
          for (const activity of intervention.activities) {
            if (activity.dynamicColumns && activity.dynamicColumns.length > 0) {
              // Process the dynamic columns data using the service
              activity.processedDynamicData = this.reportsService.processDynamicColumnData(activity.dynamicColumns);
            }
          }
        }
      }
    }
  }
  
  /**
   * Load dynamic column data on demand
   */
  loadDynamicColumns(): void {
    if (this.loading) return;
    
    console.log('🔄 Loading dynamic column data on demand');
    
    // Show loading state but keep existing data visible
    this.loading = true;
    
    // Call the service to enhance the data with dynamic columns
    this.reportsService.enhanceHierarchicalDataWithDynamicColumns(this.hierarchicalData)
      .pipe(
        takeUntil(this.destroy$),
        // Add timeout to avoid hanging UI
        timeout(60000), // 60 seconds timeout
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe({
        next: (enhancedData) => {
          console.log('✅ Dynamic column data loaded successfully');
          this.hierarchicalData = enhancedData;
        },
        error: (err) => {
          console.error('❌ Error loading dynamic column data:', err);
          // Keep existing data, just show error message
          this.error = 'Failed to load dynamic indicator data. Some indicators may not be displayed.';
        }
      });
  }
  
  /**
   * Load mock data as fallback when API fails
   */
  private loadMockData(): void {
    this.loading = true;
    
    // Clear performance cache when new data is loaded
    this.clearPerformanceCache();
    
    // Use the mock data directly
    this.hierarchicalData = this.createMockData();
    
    // Expand the first group by default
    if (this.hierarchicalData.length > 0) {
      this.expandedGroups[this.hierarchicalData[0].id] = true;
    }
    
    this.error = null;
    this.loading = false;
  }
  
  /**
   * Refresh the report with updated filters
   */
  refreshReport(filters: ReportFilter): void {
    this.filters = filters;
    this.loadReport();
  }
  
  /**
   * Toggle expansion state for a project group
   */
  toggleGroup(groupId: string): void {
    this.expandedGroups[groupId] = !this.expandedGroups[groupId];
  }
  
  /**
   * Toggle expansion state for a project
   */
  toggleProject(projectId: string): void {
    this.expandedProjects[projectId] = !this.expandedProjects[projectId];
  }
  
  /**
   * Toggle expansion state for an intervention
   */
  toggleIntervention(interventionId: string): void {
    this.expandedInterventions[interventionId] = !this.expandedInterventions[interventionId];
    
    // Load activities lazily when intervention is expanded
    if (this.expandedInterventions[interventionId]) {
      this.loadActivitiesForIntervention(interventionId);
    }
    
    // Clean up memory when intervention is collapsed
    if (!this.expandedInterventions[interventionId]) {
      // Trigger cleanup after a delay to allow for smooth animation
      setTimeout(() => this.forceMemoryCleanup(), 500);
    }
  }
  
  /**
   * Check if a group is expanded
   */
  isGroupExpanded(groupId: string): boolean {
    return this.expandedGroups[groupId] === true;
  }
  
  /**
   * Check if a project is expanded
   */
  isProjectExpanded(projectId: string): boolean {
    return this.expandedProjects[projectId] === true;
  }
  
  /**
   * Check if an intervention is expanded
   */
  isInterventionExpanded(interventionId: string): boolean {
    return this.expandedInterventions[interventionId] === true;
  }

  /**
   * Open category modal with dynamic indicators
   */
  openCategoryModal(categoryId: string, categoryName: string): void {
    console.log('🔍 HierarchicalReport: Opening category modal for:', { categoryId, categoryName });
    
    // Validate categoryId
    if (!categoryId || categoryId.trim() === '') {
      console.error('🔍 HierarchicalReport: Invalid categoryId provided:', categoryId);
      alert('Error: Category ID is missing. Cannot open category modal.');
      return;
    }
    
    // Collect all activities for this category
    this.categoryActivities = [];
    this.categoryDynamicColumns = [];
    
    let foundInterventions = 0;
    let foundActivities = 0;
    
    this.hierarchicalData.forEach(group => {
      if (group.projects) {
        group.projects.forEach(project => {
          if (project.interventions) {
            project.interventions.forEach(intervention => {
              if (intervention.categoryId === categoryId) {
                foundInterventions++;
                console.log('🔍 HierarchicalReport: Found matching intervention:', {
                  interventionId: intervention.id,
                  interventionName: intervention.name,
                  categoryId: intervention.categoryId,
                  activitiesCount: intervention.activities ? intervention.activities.length : 0
                });
                
                if (intervention.activities) {
                  intervention.activities.forEach(activity => {
                    foundActivities++;
                    this.categoryActivities.push(activity);
                    
                    // Collect dynamic columns from this activity
                    if (activity.dynamicColumns) {
                      console.log('🔍 HierarchicalReport: Activity has dynamic columns:', {
                        activityId: activity.id || activity.uniqueId,
                        dynamicColumnsCount: activity.dynamicColumns.length
                      });
                      this.categoryDynamicColumns.push(...activity.dynamicColumns);
                    }
                  });
                }
              }
            });
          }
        });
      }
    });
    
    console.log('🔍 HierarchicalReport: Category modal data collection complete:', {
      categoryId,
      foundInterventions,
      foundActivities,
      totalActivities: this.categoryActivities.length,
      totalDynamicColumns: this.categoryDynamicColumns.length,
      hierarchicalDataLength: this.hierarchicalData.length
    });
    
    // If no activities found, let's try to find any activities in the system for debugging
    if (this.categoryActivities.length === 0) {
      console.log('🔍 HierarchicalReport: No activities found for category. Debugging hierarchical data structure:');
      this.debugHierarchicalDataStructure();
    }
    
    // Set the category ID and show modal
    this.selectedCategoryId = categoryId;
    this.categoryModalVisible = true;
    
    console.log('🔍 HierarchicalReport: Setting modal visible with categoryId:', {
      selectedCategoryId: this.selectedCategoryId,
      categoryModalVisible: this.categoryModalVisible
    });
    
    // Force change detection
    setTimeout(() => {
      console.log('🔍 HierarchicalReport: Modal should be visible now with data:', {
        modalVisible: this.categoryModalVisible,
        selectedCategoryId: this.selectedCategoryId,
        activitiesCount: this.categoryActivities.length,
        dynamicColumnsCount: this.categoryDynamicColumns.length
      });
    }, 100);
  }

  /**
   * Debug hierarchical data structure to understand why no activities are found
   */
  private debugHierarchicalDataStructure(): void {
    console.log('=== DEBUGGING HIERARCHICAL DATA STRUCTURE ===');
    console.log('Total groups:', this.hierarchicalData.length);
    
    this.hierarchicalData.forEach((group, groupIndex) => {
      console.log(`Group ${groupIndex}:`, {
        id: group.id,
        name: group.name,
        projectsCount: group.projects ? group.projects.length : 0
      });
      
      if (group.projects) {
        group.projects.forEach((project, projectIndex) => {
          console.log(`  Project ${projectIndex}:`, {
            id: project.id,
            name: project.name,
            interventionsCount: project.interventions ? project.interventions.length : 0
          });
          
          if (project.interventions) {
            project.interventions.forEach((intervention, interventionIndex) => {
              console.log(`    Intervention ${interventionIndex}:`, {
                id: intervention.id,
                name: intervention.name,
                categoryId: intervention.categoryId,
                categoryName: intervention.categoryName,
                activitiesCount: intervention.activities ? intervention.activities.length : 0
              });
              
              if (intervention.activities && intervention.activities.length > 0) {
                console.log(`      Sample activity:`, {
                  id: intervention.activities[0].id || intervention.activities[0].uniqueId,
                  status: intervention.activities[0].status,
                  hasDynamicColumns: !!(intervention.activities[0].dynamicColumns && intervention.activities[0].dynamicColumns.length > 0)
                });
              }
            });
          }
        });
      }
    });
    console.log('=== END DEBUGGING ===');
  }

  /**
   * Close category modal
   */
  closeCategoryModal(): void {
    this.categoryModalVisible = false;
    this.selectedCategoryId = '';
    this.categoryDynamicColumns = [];
    this.categoryActivities = [];
  }

  /**
   * Find the first activity in the hierarchical data for debugging
   */
  private findFirstActivity(): any {
    if (!this.hierarchicalData || this.hierarchicalData.length === 0) return null;
    
    for (const group of this.hierarchicalData) {
      if (!group.projects) continue;
      
      for (const project of group.projects) {
        if (!project.interventions) continue;
        
        for (const intervention of project.interventions) {
          if (!intervention.activities || intervention.activities.length === 0) continue;
          
          return intervention.activities[0];
        }
      }
    }
    
    return null;
  }
  
  /**
   * Format a number with commas for thousands
   */
  formatNumber(value: number): string {
    if (value === undefined || value === null) return '0';
    return value.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
  }
  
  /**
   * Format a currency value
   */
  formatCurrency(value: number): string {
    if (value === undefined || value === null) return '$0';
    return '$' + this.formatNumber(value);
  }
  
  /**
   * Format a percentage value
   */
  formatPercentage(value: number): string {
    if (value === undefined || value === null) return '0%';
    return value + '%';
  }
  
  /**
   * Export report to Excel
   */
  exportToExcel(): void {
    console.log('Export to Excel clicked - this would be implemented with Excel export library');
    // This would be implemented with an Excel export library
  }
  
  /**
   * Export report to PDF
   */
  exportToPdf(): void {
    console.log('Export to PDF clicked - this would be implemented with PDF export library');
    // This would be implemented with a PDF export library
  }
  
  /**
   * Check if any activity in the array has dynamic columns
   */
  hasDynamicColumns(activities: any[]): boolean {
    if (!activities || !Array.isArray(activities)) {
      return false;
    }
    
    // Create a cache key based on activity IDs
    const cacheKey = activities.map(a => a.id || a.uniqueId).join(',');
    
    // Check cache first
    if (this.dynamicColumnsCache.has(cacheKey)) {
      return this.dynamicColumnsCache.get(cacheKey)!;
    }
    
    // Calculate result
    const result = activities.some(activity => 
      activity.dynamicColumns && 
      Array.isArray(activity.dynamicColumns) && 
      activity.dynamicColumns.length > 0
    );
    
    // Cache the result
    this.dynamicColumnsCache.set(cacheKey, result);
    
    return result;
  }
  
  /**
   * Get a list of all unique dynamic column names from all activities
   */
  getDynamicColumnNames(activities: any[]): string[] {
    if (!activities || !Array.isArray(activities)) return [];
    
    // Create a cache key based on activity IDs
    const cacheKey = activities.map(a => a.id || a.uniqueId).join(',');
    
    // Check cache first
    if (this.columnNamesCache.has(cacheKey)) {
      return this.columnNamesCache.get(cacheKey)!;
    }
    
    // Create a Set to store unique column names
    const columnNamesSet = new Set<string>();
    
    // Go through all activities and their dynamic columns
    activities.forEach(activity => {
      if (activity.dynamicColumns && Array.isArray(activity.dynamicColumns)) {
        activity.dynamicColumns.forEach((column: DynamicColumnData) => {
          if (column.columnName) {
            columnNamesSet.add(column.columnName);
          }
        });
      }
    });
    
    // Convert the Set to an array
    const result = Array.from(columnNamesSet);
    
    // Cache the result
    this.columnNamesCache.set(cacheKey, result);
    
    return result;
  }
  
  /**
   * Get the dynamic column value for a specific activity and column name
   */
  getActivityDynamicColumnValue(activity: any, columnName: string): string {
    if (!activity || !activity.dynamicColumns || !Array.isArray(activity.dynamicColumns)) {
      return '';
    }
    
    // Find the column with the matching name
    const column = activity.dynamicColumns.find(
      (col: DynamicColumnData) => col.columnName === columnName
    );
    
    if (!column) {
      return '';
    }
    
    // Format the value based on column type
    if (column.columnType === 'number') {
      // Format as number with units if available
      return column.unit 
        ? `${this.formatNumber(column.value as number)} ${column.unit}`
        : this.formatNumber(column.value as number);
    } else if (column.columnType === 'boolean') {
      // Convert boolean to Yes/No
      return (column.value === true || column.value === 'true') ? 'Yes' : 'No';
    } else if (column.columnType === 'currency') {
      // Format as currency
      return this.formatCurrency(column.value as number);
    } else {
      // Default string representation
      return column.value ? column.value.toString() : '';
    }
  }
  
  /**
   * Create mock hierarchical report data
   */
  private createMockData(): any[] {
    const mockData = [
      {
        id: 'economic',
        name: 'Economic Development',
        projects: [
          {
            id: 'p1',
            code: 'ECO-001',
            name: 'Market Access Program',
            status: 'active',
            startDate: '2023-01-15',
            endDate: '2024-12-31',
            budget: 3500000,
            cashDistributed: 2100000,
            progress: 60,
            interventions: [
              {
                id: 'int1',
                name: 'Small Business Support',
                categoryId: 'cat1',
                categoryName: 'Economic Empowerment',
                description: 'Supporting small businesses through training and grants',
                activities: [
                  {
                    id: 1,
                    uniqueId: 'ECO-001-SBS-001',
                    status: 'ongoing',
                    startDate: '2023-01-15',
                    endDate: '2023-09-30',
                    location: {
                      provinceId: 1,
                      provinceName: 'Kabul',
                      districtId: 101,
                      districtName: 'District 1'
                    },
                    progress: 75,
                    budget: 150000,
                    cashDistributed: 112500,
                    dynamicColumns: [
                      { 
                        id: 1, 
                        columnId: 101, 
                        columnName: 'Businesses Supported', 
                        columnType: 'number', 
                        value: 25, 
                        unit: 'businesses' 
                      },
                      { 
                        id: 2, 
                        columnId: 102, 
                        columnName: 'Women Entrepreneurs', 
                        columnType: 'number', 
                        value: 12, 
                        unit: 'persons' 
                      },
                      { 
                        id: 3, 
                        columnId: 103, 
                        columnName: 'Market Access Enabled', 
                        columnType: 'boolean', 
                        value: true 
                      }
                    ]
                  },
                  {
                    id: 2,
                    uniqueId: 'ECO-001-SBS-002',
                    status: 'completed',
                    startDate: '2023-02-01',
                    endDate: '2023-08-15',
                    location: {
                      provinceId: 2,
                      provinceName: 'Herat',
                      districtId: 201,
                      districtName: 'District 5'
                    },
                    progress: 100,
                    budget: 120000,
                    cashDistributed: 120000,
                    dynamicColumns: [
                      { 
                        id: 4, 
                        columnId: 101, 
                        columnName: 'Businesses Supported', 
                        columnType: 'number', 
                        value: 18, 
                        unit: 'businesses' 
                      },
                      { 
                        id: 5, 
                        columnId: 102, 
                        columnName: 'Women Entrepreneurs', 
                        columnType: 'number', 
                        value: 10, 
                        unit: 'persons' 
                      },
                      { 
                        id: 6, 
                        columnId: 103, 
                        columnName: 'Market Access Enabled', 
                        columnType: 'boolean', 
                        value: true 
                      },
                      { 
                        id: 7, 
                        columnId: 104, 
                        columnName: 'Additional Funding Secured', 
                        columnType: 'currency', 
                        value: 50000 
                      }
                    ]
                  }
                ],
                summary: {
                  totalActivities: 2,
                  totalBudget: 270000,
                  totalCashDistributed: 232500,
                  progress: 85,
                  beneficiaries: 850
                }
              }
            ],
            summary: {
              totalInterventions: 1,
              totalActivities: 2,
              totalBudget: 270000,
              totalCashDistributed: 232500,
              progress: 85,
              beneficiaries: 850
            }
          }
        ],
        summary: {
          totalProjects: 1,
          totalInterventions: 1,
          totalActivities: 2,
          totalBudget: 270000,
          totalCashDistributed: 232500,
          progress: 85,
          beneficiaries: 850
        }
      },
      {
        id: 'health',
        name: 'Health',
        projects: [
          {
            id: 'p2',
            code: 'HEA-001',
            name: 'Primary Healthcare Strengthening',
            status: 'active',
            startDate: '2023-03-01',
            endDate: '2025-02-28',
            budget: 4800000,
            cashDistributed: 2400000,
            progress: 50,
            interventions: [
              {
                id: 'int2',
                name: 'Healthcare Facilities Improvement',
                categoryId: 'cat2',
                categoryName: 'Health Infrastructure',
                description: 'Upgrading rural healthcare facilities',
                activities: [
                  {
                    id: 3,
                    uniqueId: 'HEA-001-HFI-001',
                    status: 'ongoing',
                    startDate: '2023-03-15',
                    endDate: '2024-06-30',
                    location: {
                      provinceId: 3,
                      provinceName: 'Kandahar',
                      districtId: 301,
                      districtName: 'District 2'
                    },
                    progress: 45,
                    budget: 350000,
                    cashDistributed: 157500,
                    dynamicColumns: [
                      { 
                        id: 8, 
                        columnId: 201, 
                        columnName: 'Facilities Rehabilitated', 
                        columnType: 'number', 
                        value: 3, 
                        unit: 'facilities' 
                      },
                      { 
                        id: 9, 
                        columnId: 202, 
                        columnName: 'Patients Served', 
                        columnType: 'number', 
                        value: 1250, 
                        unit: 'patients' 
                      },
                      { 
                        id: 10, 
                        columnId: 203, 
                        columnName: 'Medical Equipment Supplied', 
                        columnType: 'boolean', 
                        value: true 
                      }
                    ]
                  }
                ],
                summary: {
                  totalActivities: 1,
                  totalBudget: 350000,
                  totalCashDistributed: 157500,
                  progress: 45,
                  beneficiaries: 12000
                }
              },
              {
                id: 'int3',
                name: 'Maternal and Child Health',
                categoryId: 'cat2',
                categoryName: 'Health Infrastructure',
                description: 'Improving maternal and child health services',
                activities: [
                  {
                    id: 4,
                    uniqueId: 'HEA-001-MCH-001',
                    status: 'ongoing',
                    startDate: '2023-04-01',
                    endDate: '2024-03-31',
                    location: {
                      provinceId: 4,
                      provinceName: 'Balkh',
                      districtId: 401,
                      districtName: 'District 3'
                    },
                    progress: 55,
                    budget: 280000,
                    cashDistributed: 154000,
                    dynamicColumns: [
                      { 
                        id: 11, 
                        columnId: 301, 
                        columnName: 'Prenatal Visits', 
                        columnType: 'number', 
                        value: 450, 
                        unit: 'visits' 
                      },
                      { 
                        id: 12, 
                        columnId: 302, 
                        columnName: 'Vaccinations', 
                        columnType: 'number', 
                        value: 780, 
                        unit: 'children' 
                      },
                      { 
                        id: 13, 
                        columnId: 303, 
                        columnName: 'Training Completed', 
                        columnType: 'boolean', 
                        value: true
                      },
                      { 
                        id: 14, 
                        columnId: 304, 
                        columnName: 'Nutrition Supplements', 
                        columnType: 'currency', 
                        value: 32000
                      }
                    ]
                  }
                ],
                summary: {
                  totalActivities: 1,
                  totalBudget: 280000,
                  totalCashDistributed: 154000,
                  progress: 55,
                  beneficiaries: 8500
                }
              }
            ],
            summary: {
              totalInterventions: 2,
              totalActivities: 2,
              totalBudget: 630000,
              totalCashDistributed: 311500,
              progress: 50,
              beneficiaries: 20500
            }
          }
        ],
        summary: {
          totalProjects: 1,
          totalInterventions: 2,
          totalActivities: 2,
          totalBudget: 630000,
          totalCashDistributed: 311500,
          progress: 50,
          beneficiaries: 20500
        }
      }
    ];
    
    return mockData;
  }
  
  /**
   * Toggle debug information visibility
   */
  toggleDebugInfo(): void {
    this.showDebugInfo = !this.showDebugInfo;
    console.log(`Debug info is now ${this.showDebugInfo ? 'visible' : 'hidden'}`);
  }
  
  /**
   * Check if an object has properties (is not empty)
   */
  hasProperties(obj: any): boolean {
    if (!obj) return false;
    return Object.keys(obj).length > 0;
  }
  
  /**
   * Get properties of an object as key-value pairs
   */
  getObjectProperties(obj: any): {key: string, value: any}[] {
    if (!obj) return [];
    return Object.entries(obj).map(([key, value]) => ({ key, value }));
  }
  
  /**
   * Format a property name for display
   * Converts camelCase to Title Case with spaces
   */
  formatPropertyName(name: string): string {
    if (!name) return '';
    
    // Convert camelCase to space-separated words
    const words = name.replace(/([A-Z])/g, ' $1').trim();
    
    // Capitalize first letter of each word
    return words.charAt(0).toUpperCase() + words.slice(1);
  }
  
  /**
   * Get an appropriate unit for beneficiary types
   */
  getBeneficiaryUnit(key: string): string {
    const lowerKey = key.toLowerCase();
    
    if (lowerKey.includes('count') || 
        lowerKey.includes('num') || 
        lowerKey.includes('total') ||
        lowerKey.includes('men') || 
        lowerKey.includes('women') || 
        lowerKey.includes('children') || 
        lowerKey.includes('household')) {
      return 'persons';
    }
    
    return '';
  }
  
  /**
   * Get info properties that are not in the beneficiary details
   */
  getInfoProperties(activity: any): {key: string, value: any}[] {
    if (!activity || !activity.info) return [];
    
    return Object.entries(activity.info)
      .filter(([key]) => key !== 'beneficiaryDetails') // Exclude beneficiary details which are shown separately
      .map(([key, value]) => ({ key, value }));
  }
  
  /**
   * Get progress properties excluding indicators and standard properties that are shown separately
   */
  getProgressProperties(activity: any): {key: string, value: any}[] {
    if (!activity || !activity.cumulativeProgress) return [];
    
    // Exclude properties that are already shown elsewhere
    const excludedKeys = ['indicators', 'target', 'actual', 'percentComplete'];
    
    return Object.entries(activity.cumulativeProgress)
      .filter(([key]) => !excludedKeys.includes(key))
      .map(([key, value]) => ({ key, value }));
  }
} 