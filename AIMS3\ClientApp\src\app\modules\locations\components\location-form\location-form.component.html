<ng-template #modal>
    <div class="row">
        <div class="col-md-6">
            <div class="modal-content" [ngClass]="{'d-none': !editMode}">
                <div class="modal-header">
                    <h3>{{ modalConfig.modalTitle }}</h3>
                    <div class="btn btn-sm btn-icon btn-active-color-primary" title="Close Dialog" (click)="close()"
                         *ngIf="!modalConfig.hideCancelButton">
                        <span class="svg-icon svg-icon-1" [inlineSVG]="'./assets/media/icons/duotune/arrows/arr061.svg'"></span>
                    </div>
                </div>
                <div class="modal-body min-h-500px">
                    <form id="locationForm" class="form" [formGroup]="form">
                        <div class="row">
                            <div class="col-md-5">
                                <div class="form-group">
                                    <label class="required">Province</label>
                                    <select id="province" class="form-select" data-control="select2" data-placeholder="Select province" formControlName="provId">
                                        <option *ngFor="let prov of provinces" [value]="prov.id">{{ prov.name }}</option>
                                    </select>
                                    <!--<div class="fv-plugins-message-container" *ngIf="f.provId.dirty && !f.provId.value">
                                        <div class="fv-help-block">
                                            <span role="alert">Province is required.</span>
                                        </div>
                                    </div>-->
                                </div>
                            </div>
                            <div class="col-md-7">
                                <div class="form-group" [ngClass]="{ 'd-none': f.isNewDist.value }">
                                    <label class="required">District</label>
                                    <i class="la la-info-circle icon-md text-muted ms-2" ngbTooltip="Select a district. You may add a new one if required."></i>
                                    <span class="spinner-border text-secondary h-15px w-15px float-end" title="Getting data..." *ngIf="gettingDist"></span>
                                    <a role="button" class="btn btn-sm btn-link m-0 p-0 fs-8 float-end" ngbTooltip="Add new district" *ngIf="!gettingDist"
                                       (click)="toggleNewDist(true)">
                                        <span class="fas fa-plus"></span>&nbsp; ADD NEW
                                    </a>
                                    <select id="district" class="form-select" data-control="select2" data-placeholder="Select district">
                                        <option *ngFor="let dist of districts" [value]="dist.id">{{ dist.name }}</option>
                                    </select>
                                    <!--<div class="fv-plugins-message-container" *ngIf="f.distId.dirty && !f.distId.value">
                                        <div class="fv-help-block">
                                            <span role="alert">District is required.</span>
                                        </div>
                                    </div>-->
                                </div>
                                <div class="form-group" [ngClass]="{ 'd-none': !f.isNewDist.value }">
                                    <label class="required text-danger">New district</label>
                                    <a role="button" class="btn btn-sm btn-link m-0 p-0 fs-8 float-end" ngbTooltip="Back to select from a districts list."
                                       (click)="toggleNewDist(false)">
                                        <span class="fas fa-angle-left"></span>&nbsp; Select district
                                    </a>
                                    <input name="distName" class="form-control" type="text" placeholder="Enter new district" formControlName="distName"
                                           [ngClass]="{ 'is-invalid': form.controls['distName'].dirty && form.controls['distName'].invalid }" />
                                    <ng-container [ngTemplateOutlet]="formError"
                                                  [ngTemplateOutletContext]="{
                                    validation: 'required',
                                    message: 'New district is required.',
                                    control: form.controls['distName']
                                    }"></ng-container>
                                </div>
                            </div>
                        </div>
                        <!-- Name, GPS -->
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="required">Community name</label>
                                    <input name="locName" class="form-control" type="text" placeholder="Location name" formControlName="name"
                                           [ngClass]="{ 'is-invalid': form.controls['name'].dirty && form.controls['name'].invalid }" />
                                    <ng-container [ngTemplateOutlet]="formError"
                                                  [ngTemplateOutletContext]="{
                                    validation: 'required',
                                    message: 'Community name is required.',
                                    control: form.controls['name']
                                    }"></ng-container>
                                </div>
                            </div>
                            <div class="col-md-3 form-group">
                                <label class="required">GPS Latitude</label>
                                <i class="la la-info-circle icon-md text-muted ms-2"
                                   ngbTooltip="Please provide GPS cooridnates in Decimal Degrees (DD) format. Right click on the map to get GPS points."></i>
                                <input name="gpsLat" class="form-control" type="number" step="0.00000001" placeholder="Latitude" formControlName="gpsLat"
                                       [ngClass]="{ 'is-invalid': form.controls['gpsLat'].dirty && form.controls['gpsLat'].invalid }" />
                                <ng-container [ngTemplateOutlet]="formError"
                                              [ngTemplateOutletContext]="{
                                    validation: 'required',
                                    message: 'GPS Latitude is required.',
                                    control: form.controls['gpsLat']
                                    }"></ng-container>
                                <ng-container [ngTemplateOutlet]="formError"
                                              [ngTemplateOutletContext]="{
                                    validation: 'min',
                                    message: 'Min value accepted is: 29.00000001',
                                    control: form.controls['gpsLat']
                                    }"></ng-container>
                                <ng-container [ngTemplateOutlet]="formError"
                                              [ngTemplateOutletContext]="{
                                    validation: 'max',
                                    message: 'Max value accepted is: 38.99999999',
                                    control: form.controls['gpsLat']
                                    }"></ng-container>
                            </div>
                            <div class="col-md-3 form-group">
                                <label class="required">GPS Longitude</label>
                                <i class="la la-info-circle icon-md text-muted ms-2"
                                   ngbTooltip="Please provide GPS cooridnates in Decimal Degrees (DD) format. Right click on the map to get GPS points."></i>
                                <input name="gpsLon" class="form-control" type="number" step="0.000001" placeholder="Longitude" formControlName="gpsLon"
                                       [ngClass]="{ 'is-invalid': form.controls['gpsLon'].dirty && form.controls['gpsLon'].invalid }" />
                                <ng-container [ngTemplateOutlet]="formError"
                                              [ngTemplateOutletContext]="{
                                    validation: 'required',
                                    message: 'GPS Longitude is required.',
                                    control: form.controls['gpsLon']
                                    }"></ng-container>
                                <ng-container [ngTemplateOutlet]="formError"
                                              [ngTemplateOutletContext]="{
                                    validation: 'min',
                                    message: 'Min value accepted is: 60.0000001',
                                    control: form.controls['gpsLon']
                                    }"></ng-container>
                                <ng-container [ngTemplateOutlet]="formError"
                                              [ngTemplateOutletContext]="{
                                    validation: 'max',
                                    message: 'Max value accepted is: 74.9999999',
                                    control: form.controls['gpsLon']
                                    }"></ng-container>
                            </div>
                        </div>
                        <!-- Verification -->
                        <!--<div class="row" *ngIf="isAdmin">
                            <div class="form-group col-md-6">
                                <label class="required">Status</label>
                                <div class="form-check form-check-custom form-check-solid pt-2">
                                    <input id="status" type="checkbox" class="form-check-input" formControlName="isVerified" />
                                    <label for="status" class="form-check-label ms-3">Location verified</label>
                                </div>
                            </div>
                        </div>-->

                        <!-- Remarks -->
                        <!--<div class="row" *ngIf="!isAdmin && (!location.locationId || location.locationId > 0)">
                            <div class="col-md-12 form-group">
                                <label>Remarks</label>
                                <i class="la la-info-circle icon-md text-muted ms-2"
                                   ngbTooltip="Please provide any remarks to consider approving your request for this location."></i>
                                <textarea class="form-control" type="text" formControlName="remarks" rows="2"></textarea>
                            </div>
                        </div>-->
                        <!-- Table -->
                        <ng-container *ngIf="similarLocations.length; else searching">
                            <div class="separator separator-dashed mb-5"></div>
                            <div class="row blockui">
                                <div class="col-md-12">
                                    <aims-working *ngIf="working"></aims-working>
                                    <div class="notice bg-light-warning rounded border-warning border border-dashed p-3">
                                        <h6 class="fs-6 text-gray-800 pe-7">Locations with similar spellings and/or within close proximity have been identified.</h6>
                                        <p class="m-0">
                                            Please review and confirm if you still require this location in this province.
                                            Locations listed are within the radius of <span class="fw-semibold">{{ LOCATIONS_WITHIN_RADIUS | formatNum }}m</span> distance (direct).
                                        </p>
                                    </div>
                                    <table class="table table-sm table-striped align-middle mt-3">
                                        <thead>
                                            <tr class="border-bottom fw-semibold">
                                                <th width="5%"></th>
                                                <th width="35%">Location</th>
                                                <th width="20%">District</th>
                                                <th width="25%">
                                                    GPS
                                                    <i class="la la-info-circle icon-md text-muted ms-2" ngbTooltip="Marked on the map."></i>
                                                </th>
                                                <th width="15%">
                                                    Distance <i class="la la-info-circle icon-md text-muted ms-2"
                                                                ngbTooltip="Approximate direct distance from this location."></i>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr class="text-gray-800" *ngFor="let loc of similarLocations; let i = index"
                                                [ngClass]="{'table-danger': !loc.id}" [title]="!loc.id ? 'Not Approved' : ''">
                                                <td class="text-center align-center">{{ i + 1}}</td>
                                                <td>{{ loc.name }}</td>
                                                <td>{{ loc.distName }}</td>
                                                <td *ngIf="loc.isVerified">
                                                    <span ngbTooltip="GPS Verified">
                                                        <span class="badge badge-success fs-8 me-1">{{ loc.gpsLat }}</span>
                                                        <span class="badge badge-success fs-8">{{ loc.gpsLon }}</span>
                                                    </span>
                                                </td>
                                                <td *ngIf="!loc.isVerified">
                                                    <span class="badge badge-light fs-8 me-1">{{ loc.gpsLat }}</span>
                                                    <span class="badge badge-light fs-8">{{ loc.gpsLon }}</span>
                                                </td>
                                                <td class="fs-8" [ngClass]="{'text-danger': (loc.distance || 0) <= LOCATIONS_WITHIN_RADIUS}">
                                                    {{ loc.distance | formatNum:true }}m
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </ng-container>
                        <ng-template #searching>
                            <div class="text-center align-center mt-2">
                                <span class="spinner-border text-primary" *ngIf="working"></span>
                            </div>
                        </ng-template>
                    </form>
                </div>
                <!-- Dialog footer -->
                <div class="modal-footer d-flex">
                    <div class="d-flex flex-grow-1" *ngIf="similarLocations.length">
                        <div class="notice d-flex rounded border border-dashed p-2"
                             [ngClass]="{'bg-light-primary border-primary': isConfirmed && !modalConfig.disableDoneButton,
                                         'bg-light-danger border-danger': !isConfirmed || modalConfig.disableDoneButton}">
                            <div class="d-flex flex-stack flex-grow-1 flex-wrap flex-md-nowrap">
                                <div class="mb-3 mb-md-0">
                                    <div class="fs-6 text-gray-700 pe-7" *ngIf="!isAdmin">Confirm request</div>
                                    <div class="fs-6 text-gray-700 pe-7" *ngIf="isAdmin">Confirm {{ modalConfig.doneButtonLabel }}</div>
                                </div>
                                <div class="form-check form-switch form-check-custom form-check-solid">
                                    <input class="form-check-input" type="checkbox" value="" id="status" name="status"
                                           (change)="isReady=!isReady;isConfirmed=!isConfirmed">
                                    <label class="form-check-label fw-semibold text-gray-400 ms-3" for="status">Confirm</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-outline-secondary" (click)="close()"
                            *ngIf="!modalConfig.hideCancelButton" [disabled]="modalConfig.disableCancelButton">
                        {{ modalConfig.cancelButtonLabel || 'Cancel' }}
                    </button>
                    <button type="button" class="btn btn-primary" (click)="save()"
                            *ngIf="!modalConfig.hideDoneButton"
                            [disabled]="!isReady || modalConfig.disableDoneButton || working || modalConfig.working">
                        <span class="indicator-label" *ngIf="!modalConfig.working; else btnSpinner">
                            {{ modalConfig.doneButtonLabel || 'Submit' }}
                        </span>
                        <ng-template #btnSpinner>
                            <span class="indicator-progress" style="display: block">
                                Please wait...
                                <span class="spinner-border spinner-border-sm align-middle ms-2"></span>
                            </span>
                        </ng-template>
                    </button>
                </div>
                <ng-template #formError let-control="control" let-message="message" let-validation="validation">
                    <ng-container *ngIf="control.hasError(validation) && control.dirty">
                        <div class="fv-plugins-message-container">
                            <div class="fv-help-block">
                                <span role="alert">{{ message }}</span>
                            </div>
                        </div>
                    </ng-container>
                </ng-template>
            </div>

            <!-- View Mode -->
            <ng-container *ngIf="!editMode">
                <div class="modal-header">
                    <h3>
                        Location
                        <span class="text-primary fs-7 ms-2">{{ location.name }}, {{ location.distName || '-' }}, {{ location.provName }}</span>
                    </h3>
                    <div class="btn btn-sm btn-icon btn-active-color-primary" title="Close Dialog" (click)="close()">
                        <span class="svg-icon svg-icon-1" [inlineSVG]="'./assets/media/icons/duotune/arrows/arr061.svg'"></span>
                    </div>
                </div>
                <div class="modal-body min-h-500px">
                    <div class="row">
                        <div class="col-md-5">
                            <div class="form-group">
                                <label class="required">Province</label>
                                <input class="form-control" type="text" [value]="location.provName" disabled />
                            </div>
                        </div>
                        <div class="col-md-7">
                            <div class="form-group">
                                <label class="required">District</label>
                                <input class="form-control" type="text" [value]="location.distName || ''" disabled />
                            </div>
                        </div>
                    </div>
                    <!-- Name, GPS -->
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="required">Location name</label>
                                <input class="form-control" type="text" [value]="location.name" disabled />
                            </div>
                        </div>
                        <div class="col-md-3 form-group">
                            <label class="required">GPS Latitude</label>
                            <input class="form-control" type="number" [value]="location.gpsLat" disabled />

                        </div>
                        <div class="col-md-3 form-group">
                            <label class="required">GPS Longitude</label>
                            <input class="form-control" type="number" [value]="location.gpsLon" disabled />
                        </div>
                    </div>
                    <!-- Remarks -->
                    <!--<div class="row" *ngIf="!location.id">
                        <div class="col-md-12 form-group">
                            <label>Remarks</label>
                            <textarea class="form-control" type="text" rows="3" [value]="location.remarks" disabled></textarea>
                        </div>
                    </div>-->
                </div>
                <!-- Dialog footer -->
                <div class="modal-footer" *ngIf="!editMode">
                    <button type="button" class="btn btn-outline-secondary" (click)="close()">
                        Close
                    </button>
                </div>
            </ng-container>
        </div>

        <!-- Map -->
        <div class="col-md-6 p-0 m-0">
            <location-map class="bg-light-info rounded-end blockui" [locName]="location.name"
                          [loc]="[location.distName, location.provName]" [editMode]="editMode"
                          [point]="[location.gpsLat, location.gpsLon]" (onGpsUpdate)="updateGps($event)"
                          (initialized)="onMapComponentInit($event)">
            </location-map>
        </div>
    </div>
</ng-template>