.category-dashboard {
  .stats-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    .card-body {
      padding: 1.25rem;
    }
    
    h2 {
      font-weight: 600;
      font-size: 1.75rem;
    }
    
    .icon-bg {
      font-size: 1.5rem;
      opacity: 0.8;
      padding: 10px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    .progress-sm {
      height: 6px;
      border-radius: 3px;
    }
  }
  
  .card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    
    &.selected {
      border-color: #4e73df;
      box-shadow: 0 0 0 0.25rem rgba(78, 115, 223, 0.25);
    }
    
    &:hover:not(.selected) {
      transform: translateY(-5px);
      box-shadow: 0 4px 8px rgba(0,0,0,0.1);
      cursor: pointer;
    }
    
    .card-body {
      padding: 1.5rem;
    }
    
    .card-title {
      margin-bottom: 1.25rem;
      font-weight: 600;
    }
  }
  
  .output-selector {
    display: flex;
    flex-wrap: nowrap;
    gap: 15px;
    margin-top: 15px;
    overflow-x: auto;
    padding-bottom: 10px;
    
    /* Custom scrollbar styling */
    &::-webkit-scrollbar {
      height: 8px;
    }
    
    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 4px;
    }
    
    &::-webkit-scrollbar-thumb:hover {
      background: #a1a1a1;
    }
    
    .output-item {
      flex: 0 0 auto;
      width: 180px;
      height: 75px;
      border-radius: 8px;
      border: 1px solid #e0e0e0;
      padding: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
      
      &:hover {
        border-color: #673AB7;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      
      &.active {
        border-color: #673AB7;
        background-color: rgba(103, 58, 183, 0.05);
        box-shadow: 0 2px 8px rgba(103, 58, 183, 0.2);
        
        h6 {
          color: #673AB7;
        }
      }
      
      .output-item-inner {
        height: 100%;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        
        h6 {
          margin-bottom: 3px;
          font-weight: 600;
          font-size: 0.9rem;
          line-height: 1.2;
          max-height: 2.4em;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        
        p {
          margin-top: 0;
          margin-bottom: 0;
          font-size: 0.75rem;
        }
      }
    }
  }
  
  .detail-card {
    padding: 10px 15px;
    border-radius: 6px;
    background-color: #f9f9f9;
    display: flex;
    flex-direction: column;
    height: 100%;
    
    .detail-label {
      font-size: 0.85rem;
      color: #666;
      margin-bottom: 8px;
    }
    
    .detail-value {
      font-size: 1.25rem;
      font-weight: 600;
      color: #333;
    }
  }
  
  .table {
    th {
      font-weight: 600;
      border-top: none;
      font-size: 0.9rem;
      text-transform: uppercase;
      letter-spacing: 0.5px;
      padding: 0.75rem 1rem;
      background-color: rgba(0, 0, 0, 0.02);
    }
    
    td {
      vertical-align: middle;
      padding: 0.75rem 1rem;
      
      .progress {
        margin-bottom: 0.25rem;
      }
      
      .small {
        font-size: 0.8rem;
        color: #666;
      }
    }
  }
  
  .badge {
    font-size: 0.9rem;
    padding: 0.35em 0.65em;
  }
  
  .category-indicators {
    border-top: 1px solid #e3e6f0;
    padding-top: 1rem;
  }
  
  .progress {
    height: 1rem;
    border-radius: 0.5rem;
    
    .progress-bar {
      transition: width 0.6s ease;
    }
  }
  
  .font-weight-bold {
    font-weight: 600;
  }
  
  .text-primary {
    color: #4e73df;
  }
  
  .text-muted {
    color: #858796;
  }
  
  .chart-container {
    position: relative;
    height: 400px;
    width: 100%;
  }
  
  i {
    width: 24px;
    text-align: center;
    margin-right: 8px;
  }
}

// Responsive adjustments
@media (max-width: 767.98px) {
  .category-dashboard {
    .stats-card {
      h2 {
        font-size: 1.5rem;
      }
    }
    
    .output-selector {
      .output-item {
        flex: 1 1 150px;
        max-width: 100%;
      }
    }
    
    .detail-card {
      margin-bottom: 15px;
    }
  }
}

// Fix for the intervention list to ensure all items are visible
.list-group-flush {
  max-height: 400px;
  overflow-y: auto;
}

// Make sure the parent container allows full visibility
.card-body.p-0 {
  padding: 0;
  max-height: none !important;
  overflow: visible !important;
}

// Ensure each list item has proper sizing and is visible
.list-group-item {
  padding: 10px 15px;
  border-radius: 0;
  border-left: none;
  border-right: none;
}

// Force display of all items in intervention container
:host ::ng-deep [class*="selectedCategory"] a {
  display: block !important;
}

.category-tabs {
  margin-bottom: 1rem;
}

.category-cards {
  display: flex;
  flex-wrap: wrap;
  gap: 1.5rem;
  margin-top: 1rem;
}

// Make category cards look clickable
.card {
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  
  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  }
}

// Category card content styling
.card-header {
  background-color: #f8f9fa;
  border-bottom: 2px solid #e9ecef;
}

.card-body {
  padding: 1.5rem;
}

.card h5 {
  margin: 0;
  font-weight: 500;
}

.small.text-muted {
  font-size: 0.8rem;
  color: #6c757d;
}

.font-weight-bold {
  font-weight: 600;
  font-size: 1.1rem;
}

// For status indicators
.status-pill {
  padding: 0.25rem 0.75rem;
  border-radius: 50px;
  display: inline-block;
  font-size: 0.8rem;
  font-weight: 500;
  
  &.high {
    background-color: #d4edda;
    color: #155724;
  }
  
  &.medium {
    background-color: #fff3cd;
    color: #856404;
  }
  
  &.low {
    background-color: #f8d7da;
    color: #721c24;
  }
}

// Modal dialog styles
::ng-deep .full-width-dialog {
  max-width: 95vw !important;
  
  .mat-mdc-dialog-container {
    padding: 0;
    
    .mat-mdc-dialog-content {
      max-height: 80vh;
    }
  }
  
  .mat-mdc-tab-header {
    background-color: #f8f9fa;
    position: sticky;
    top: 0;
    z-index: 100;
  }
} 