﻿using AIMS3.Business.Models;
using AIMS3.Data.Models;
using System.Collections.Generic;
using System.Threading.Tasks;
using AIMS3.Business.Models.DashboardModels;

namespace AIMS3.Business.Services;

public interface IDashboardService
{
    Task<IEnumerable<GpsPointModel>> GetActivityDataPoints(DataFilterModel filters);
    Task<IEnumerable<ActivityStatusModel>> GetActivitiesForDistrict(int distId, DataFilterModel filters);

    Task<IEnumerable<PivotTableModel>> GetPivotTables(int? orgId = null);
    Task<IEnumerable<ResultsDataModel>> GetData(DataFilterModel filters);
    
    // Reports Dashboard API
    Task<object> GetReportsDashboardData(DataFilterModel filters);
    Task<object> GetReportsSummaryStats(DataFilterModel filters);
    Task<object> GetReportsTimeSeriesData(DataFilterModel filters);
    Task<CategoryStatisticsModel> GetCategoryStatistics(DataFilterModel filters);
}