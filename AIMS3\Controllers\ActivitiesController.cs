using AIMS3.Business.Models;
using AIMS3.Business.Services;
using AIMS3.Data;
using AIMS3.Misc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;

namespace AIMS3.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class ActivitiesController : ControllerBase
{
    private readonly IActivityService _actService;

    public ActivitiesController(IActivityService actService)
    {
        _actService = actService;
    }

    //[HttpGet("years")]
    ////[Authorize(Roles = "SystemAdmin,MnEOfficer,Partner")]
    //public async Task<IActionResult> GetYears()
    //{
    //    return new OkObjectResult(await _progService.GetYears());
    //}

    [HttpGet("{profId:int}/{projId:int}/{month:int}/{year:int}")]
    [HttpGet("{profId:int}/{projId:int}/{month:int}/{year:int}/{orgId:int}")]
    public async Task<IActionResult> GetOrgActivityProgress(int profId, int projId, int month, int year, int? orgId)
    {
        if(User.IsInRole("Admin") || User.IsInRole("Approver") || User.IsInRole("Viewer"))
            orgId = orgId > 0 ? orgId : User.GetUserOrgId();
        else
            orgId = User.GetUserOrgId();

        return new ObjectResult(
            await _actService.GetActivityProgress(profId, projId, (int)orgId, (byte)month, year)
        );
    }

    [HttpPost("{profId:int}/{projId:int}")]
    [HttpPost("{profId:int}/{projId:int}/{orgId:int}")]
    [Authorize(Policy = "RequireDataEntryRole")]
    public async Task<IActionResult> SaveActivities([FromBody] ActivityDataModel model, int profId, int projId, int? orgId)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        if (User.IsInRole("Admin") || User.IsInRole("Approver") || User.IsInRole("LocalApprover"))
            orgId = orgId > 0 ? orgId : User.GetUserOrgId();
        else
            orgId = User.GetUserOrgId();

        var result = await _actService.SaveActivityProgress(model, profId, projId, (int)orgId, User.GetUserName());
        if (result.Result != OperationResult.Succeeded)
        {
            ModelState.TryAddModelError("serverError", "Error on the server.");
            return new BadRequestObjectResult(ModelState);
        }

        return Ok(result);
    }

    [HttpPatch("submit/{month:int}/{year:int}")]
    [HttpPatch("submit/{month:int}/{year:int}/{voidStatus}")]
    [Authorize(Policy = "RequireDataEntryRole")]
    public async Task<IActionResult> ToggleSubmissionStatus([FromBody] int[] actIds, int month, int year, string voidStatus)
    {
        var result = await _actService.MarkActivitiesForSubmission(actIds, User.GetUserOrgId(), (byte)month,
                            year, User.GetUserName(), !string.IsNullOrEmpty(voidStatus));
        
        if (result.Result != OperationResult.Succeeded)
        {
            ModelState.TryAddModelError("serverError", "Something went wrong.");
            return new BadRequestObjectResult(ModelState);
        }

        return Ok(result.Data);
    }

    [HttpPatch("submit/{month:int}/{year:int}/{orgId:int}")]
    [HttpPatch("submit/{month:int}/{year:int}/{orgId:int}/{voidStatus}")]
    [Authorize(Policy = "RequireDataEntryRole")]
    public async Task<IActionResult> ToggleSubmissionStatus([FromBody] int[] actIds, int month, int year, int orgId, string voidStatus)
    {
        if (!User.IsInRole("Admin") && !User.IsInRole("Approver"))
            orgId = User.GetUserOrgId();

        var result = await _actService.MarkActivitiesForSubmission(actIds, orgId, (byte)month,
                            year, User.GetUserName(), !string.IsNullOrEmpty(voidStatus));

        if (result.Result != OperationResult.Succeeded)
        {
            ModelState.TryAddModelError("serverError", "Something went wrong.");
            return new BadRequestObjectResult(ModelState);
        }

        return Ok(result.Data);
    }

    #region Submit Data -----------------------------------------------------

    // Get submission log 
    [HttpGet("submission-log")]
    [Authorize(Policy = "RequireDataEntryRole")]
    public async Task<IActionResult> GetSubmissionLog()
    {
        if (User.IsInRole("DataEntry") || User.IsInRole("LocalApprover"))
        {
            return new ObjectResult(
            await _actService.GetSubmissionLog(User.GetUserOrgId()));
        }

        return new ObjectResult(await _actService.GetSubmissionLog());
    }

    [HttpPost("submit/check-data")]
    [Authorize(Policy = "RequireDataEntryRole")]
    public async Task<IActionResult> CheckMarkedData([FromBody] SubmitDataModel model)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        if (User.IsInRole("DataEntry") || User.IsInRole("LocalApprover"))
            model.OrgId = User.GetUserOrgId();

        return new ObjectResult(await _actService.CheckMarkedForSubmissionData(model));
    }

    [HttpPatch("submit/data")]
    [Authorize(Policy = "RequireDataEntryRole")]
    public async Task<IActionResult> SubmitData([FromBody] SubmitDataModel model)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        if (User.IsInRole("DataEntry") || User.IsInRole("LocalApprover"))
            model.OrgId = User.GetUserOrgId();

        var result = await _actService.SubmitData(model);
        if (result != OperationResult.Succeeded)
        {
            if (result == OperationResult.NoData)
                return new ObjectResult("NoData");
            else
                ModelState.TryAddModelError("serverError", "Error on the server.");

            return new BadRequestObjectResult(ModelState);
        }

        return Ok();
    }
    
    #endregion

    /*
    [HttpPut]
    [Authorize(Roles = "SystemAdmin,MnEOfficer,Partner")]
    public async Task<IActionResult> UpdateProgress([FromBody] InterventionProgressModel model)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var result = await _progService.UpdateProgress(model, User.GetUserName(), User.GetUserRole());
        if (result.OperationResult != OperationResult.Succeeded)
        {
            if (result.OperationResult == OperationResult.NotAllowed)
                ModelState.TryAddModelError("accessDenied", "Access denied.");
            else
                ModelState.TryAddModelError("serverError", "Error on the server.");

            return new BadRequestObjectResult(ModelState);
        }

        return Ok(result.Data);
    }


    

    [HttpPatch("approval/{progId:int}")]
    [Authorize(Roles = "SystemAdmin,MnEOfficer")]
    public async Task<IActionResult> ToggleApprovalStatus(int progId)
    {
        return new OkObjectResult(
            await _progService.ToggleApprovalStatus(progId, User.GetUserName())
        );
    }*/
}