using AIMS3.Business.Models;
using AIMS3.Business.Services;
using AIMS3.Data;
using AIMS3.Misc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System;
using System.Threading.Tasks;

namespace AIMS3.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
public class ViewDataController : ControllerBase
{
    private readonly IViewDataService _dataService;

    public ViewDataController(IViewDataService _viewDataService)
    {
        _dataService = _viewDataService;
    }

    [HttpPost]
    public async Task<IActionResult> GetData([FromBody] DataFilterModel filters)
    {
        try
        {
            if(!User.IsInRole("Admin") && !User.IsInRole("Approver") && !User.IsInRole("LocalApprover") && !User.IsInRole("Viewer"))
                filters.OrgIds = new int[] { User.GetUserOrgId() };

            return new ObjectResult(
                await _dataService.GetData(filters)
            );
        }
        catch (Exception ex)
        {
            // Log the error and return a proper error response
            return StatusCode(500, new { 
                message = "An error occurred while retrieving data.",
                error = ex.Message,
                details = ex.InnerException?.Message
            });
        }
    }

    [HttpPost("{profId:int}")]
    public async Task<IActionResult> GetData([FromBody] DataFilterModel filters, int profId)
    {
        try
        {
            if (!User.IsInRole("Admin") && !User.IsInRole("Approver") && !User.IsInRole("LocalApprover") && !User.IsInRole("Viewer"))
                filters.OrgIds = new int[] { User.GetUserOrgId() };

            if (filters.IsTarget)
            {
                return new ObjectResult(
                    await _dataService.GetTargetData(profId, filters)
                );
            }
            else
            {
                return new ObjectResult(
                    await _dataService.GetProgressData(profId, filters)
                );
            }
        }
        catch (Exception ex)
        {
            // Log the error and return a proper error response
            return StatusCode(500, new { 
                message = "An error occurred while retrieving intervention data.",
                error = ex.Message,
                details = ex.InnerException?.Message,
                profId = profId,
                isTarget = filters?.IsTarget
            });
        }
    }

    [HttpPatch("target/approve")]
    [HttpPatch("target/approve/{voidApproval}")]
    [Authorize(Roles = "Admin,Approver,LocalApprover")]
    public async Task<IActionResult> ApproveTargetData([FromBody] int[] ids, string voidApproval)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        var result = await _dataService.ApproveTargetData(ids, voidApproval, User.GetUserName());

        if (result != OperationResult.Succeeded)
            return BadRequest();

        return Ok();
    }

    [HttpPatch("approve")]
    [HttpPatch("approve/{voidApproval}")]
    [Authorize(Roles = "Admin,Approver,LocalApprover")]
    public async Task<IActionResult> ApproveProgressData([FromBody] int[] ids, string voidApproval)
    {
        if (!ModelState.IsValid)
            return BadRequest(ModelState);

        //string usrRol = string.Join(",", User.GetUserRoles());
        var result = await _dataService.ApproveProgressData(ids, voidApproval, User.GetUserName());

        if (result != OperationResult.Succeeded)
            return BadRequest();

        return Ok();
    }
}