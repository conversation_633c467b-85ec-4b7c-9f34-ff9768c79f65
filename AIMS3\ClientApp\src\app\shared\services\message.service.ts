import { Injectable } from '@angular/core';
import { IndividualConfig, ToastrService } from 'ngx-toastr';
import Swal from 'sweetalert2';

@Injectable()
export class MessageService {
    constructor(private toastService: ToastrService) {
       
    }

    error(errMsg: string, title: string = 'Error', config?: Partial<IndividualConfig>, clearPrevToast: boolean = true): void {
        if (clearPrevToast)
            this.toastService.clear();

        if (!config)
            config = { tapToDismiss: true, closeButton: true };
        this.toastService.error(errMsg, title, config);
    }

    success(msg: string, title: string = 'Success', config?: Partial<IndividualConfig>, clearPrevToast: boolean = true): void {
        if (clearPrevToast)
            this.toastService.clear();

        if (!config)
            config = { tapToDismiss: true, closeButton: true };
        this.toastService.success(msg, title, config);
    }

    info(msg: string, title: string = 'Info', config?: Partial<IndividualConfig>, clearPrevToast: boolean = true): void {
        if (clearPrevToast)
            this.toastService.clear();

        if (!config)
            config = { tapToDismiss: true, closeButton: true };
        this.toastService.info(msg, title, config);
    }

    warning(msg: string, title: string = 'Warning', config?: Partial<IndividualConfig>, clearPrevToast: boolean = true): void {
        if (clearPrevToast)
            this.toastService.clear();

        if (!config)
            config = { tapToDismiss: true, closeButton: true };
        this.toastService.warning(msg, title, config);
    }

    // Alerts and confirmation dialogues
    confirmMessage(title: string, msg: string,
        successCallback: () => void, btnDel: boolean = false,
        strConfirm: string = 'Yes', showCancelButton: boolean = true,
        strCancel: string = 'Cancel', cancelCallback?: () => void
    ): void {
        Swal.fire({
            title: title,
            html: msg,
            icon: 'question',
            confirmButtonText: strConfirm,
            confirmButtonColor: btnDel ? '#d33' : '',
            cancelButtonText: strCancel,
            showCancelButton: showCancelButton
        }).then((result: any) => {
            if (result.value) {
                successCallback();
            } else {
                if (cancelCallback)
                    cancelCallback();
            }
        });
    }

    confirmAction(title: string, msg: string,
        strConfirm: string = 'Discard', strCancel: string = 'Cancel',
        successCallback: () => void, cancelCallback?: () => boolean
    ): Promise<boolean> {
        return Swal.fire({
            title: title,
            html: msg,
            icon: 'warning',
            confirmButtonText: strConfirm,
            confirmButtonColor: '#d33',
            cancelButtonText: strCancel,
            showCancelButton: true
        }).then((result: any) => {
            if (result.value) {
                successCallback();
                return true;
            } else {
                if (cancelCallback)
                    return cancelCallback();
                else
                    return true;
            }
        });
    }

    confirmActionWithCondition(condition: boolean, title: string, msg: string,
        successCallback: () => Promise<boolean>, denyCallback?: () => void, cancelCallback?: () => void
    ): Promise<boolean> {
        if (!condition) {
            denyCallback();
            return;
        }

        return Swal.fire({
            title: title,
            html: msg,
            icon: 'warning',
            confirmButtonText: 'Save',
            confirmButtonColor: 'var(--qs-primary)',
            cancelButtonText: 'Cancel',
            focusCancel: true,
            showDenyButton: true,
            denyButtonText: "Don't save",
            denyButtonColor: 'var(--qs-danger)',
            showCancelButton: true
        }).then((result: any) => {
            if (result.value) {
                successCallback().then((succeeded: boolean) => {
                    if (succeeded) {
                        denyCallback();
                        return true;
                    }
                    return false;
                });
            } else if (result.value === false) {
                if (denyCallback)
                    denyCallback();
                return true;
            } else {
                if (cancelCallback)
                    cancelCallback();
                return false;
            }
        });
    }
}