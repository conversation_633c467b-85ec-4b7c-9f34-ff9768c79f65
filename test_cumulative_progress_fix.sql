-- Test script to verify cumulative progress values are visible in data view
-- Run this after applying the fix

-- 1. Check if the view returns progress values
SELECT 
    'Progress Values from View' as TestType,
    COUNT(*) as RecordCount
FROM vDynColsWithValues 
WHERE ProgressId IS NOT NULL;

-- 2. Compare data between data-entry and data-view approaches
-- This should show the same cumulative progress values in both systems
WITH DataEntryValues AS (
    SELECT 
        ap.ActivityId,
        ap.Id as ProgressId,
        dcpv.DynamicColumnId,
        dcpv.Value as DataEntryValue
    FROM DynColActProgValues dcpv
    INNER JOIN ActivitiesProgress ap ON dcpv.ActivityProgressId = ap.Id
    WHERE ap.ActivityId IN (SELECT TOP 5 Id FROM Activities WHERE Status = 1)
),
DataViewValues AS (
    SELECT 
        ActivityId,
        ProgressId,
        DynamicColumnId,
        Value as DataViewValue
    FROM vDynColsWithValues
    WHERE ProgressId IS NOT NULL
    AND ActivityId IN (SELECT TOP 5 Id FROM Activities WHERE Status = 1)
)
SELECT 
    COALESCE(de.ActivityId, dv.ActivityId) as ActivityId,
    COALESCE(de.ProgressId, dv.ProgressId) as ProgressId,
    COALESCE(de.DynamicColumnId, dv.DynamicColumnId) as DynamicColumnId,
    de.DataEntryValue,
    dv.DataViewValue,
    CASE 
        WHEN de.DataEntryValue = dv.DataViewValue THEN 'MATCH'
        WHEN de.DataEntryValue IS NULL AND dv.DataViewValue IS NOT NULL THEN 'MISSING_IN_DATA_ENTRY'
        WHEN de.DataEntryValue IS NOT NULL AND dv.DataViewValue IS NULL THEN 'MISSING_IN_DATA_VIEW'
        ELSE 'MISMATCH'
    END as ComparisonResult
FROM DataEntryValues de
FULL OUTER JOIN DataViewValues dv ON de.ActivityId = dv.ActivityId 
    AND de.ProgressId = dv.ProgressId 
    AND de.DynamicColumnId = dv.DynamicColumnId
ORDER BY ActivityId, ProgressId, DynamicColumnId;

-- 3. Check specific cumulative progress indicators
SELECT 
    dc.Name as ColumnName,
    dc.DisplayName,
    COUNT(CASE WHEN vdcv.ProgressId IS NOT NULL THEN 1 END) as ProgressValueCount,
    COUNT(CASE WHEN vdcv.ActivityId IS NOT NULL AND vdcv.ProgressId IS NULL THEN 1 END) as ActivityValueCount
FROM DynamicColumns dc
LEFT JOIN vDynColsWithValues vdcv ON dc.Id = vdcv.DynamicColumnId
WHERE dc.Type = 2  -- Progress columns
GROUP BY dc.Id, dc.Name, dc.DisplayName
HAVING COUNT(CASE WHEN vdcv.ProgressId IS NOT NULL THEN 1 END) > 0
ORDER BY dc.Name;
