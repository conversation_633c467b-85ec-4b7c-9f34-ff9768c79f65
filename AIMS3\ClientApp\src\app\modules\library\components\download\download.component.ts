import { Component, OnD<PERSON>roy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { MessageService } from '../../../../shared/services/message.service';
import { SharedService } from '../../../../shared/services/shared.service';
import { IDoc } from '../../models/document.model';
import { DocumentService } from '../../services/document.service';
import { SafeUrl } from '@angular/platform-browser';

@Component({
    selector: 'download-modal',
    templateUrl: './download.component.html',
    styleUrls: ['../documents/documents.component.scss', './download.component.scss']
})
export class DownloadComponent implements OnInit, OnDestroy {
    working: boolean = false; isGlobalUser: boolean = false;

    document: IDoc;
    fileBlobUrl: SafeUrl;
    subscriptions: Subscription[] = [];

    constructor(
        private route: ActivatedRoute,
        private docService: DocumentService,
        private sharedService: SharedService,
        private messageService: MessageService
    ){  }

    ngOnInit() {
        const docId = this.route.snapshot.params?.docId;

        const pageHeading = document.querySelector('.page-heading');
        if (pageHeading)
            pageHeading.textContent = 'Download';

        if (!docId)
            return;

        this.getFileDetails(+docId);
    }

    getFileDetails(fileId: number): void {
        this.working = true;
        
        this.subscriptions.push(
            this.docService.getDocument(fileId).subscribe({
                next: (doc) => {
                    this.document = doc;
                },
                error: (e) => {
                    console.log(e);
                    this.working = false;
                },
                complete: () => {
                    this.working = false;
                }
            }));
    }
    
    downloadFile(fileName: string): void {
        this.sharedService.downloadFile(fileName).subscribe({
            next: (fileBlob) => {
                this.messageService.info('The file is being downloaded.');

                let file = document.createElement("a");
                file.href = URL.createObjectURL(fileBlob);
                file.download = fileName;
                file.click(); // start download
            }, error: (err) => {
                this.messageService.error('Sorry, there was an error downloading the file.');
                console.log(err);
            }
        });
    }



    //* This method is used to preview the file in a modal.
    
    previewFile(fileName: string): void {
        this.sharedService.downloadFile(fileName).subscribe({
            next: (fileBlob) => {
                this.fileBlobUrl = URL.createObjectURL(fileBlob);
            }, error: (err) => {
                this.messageService.error('Sorry, There was an error previewing the file.');
                console.log(err);
            }
        });
    }
 
    //* This method is used to check if the file is an image or not.
    isImageFile(filename: string): boolean {
        if (!filename) return false;
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp'];
        return imageExtensions.some(ext => filename.toLowerCase().endsWith(ext));
      }

    showModal = false;

    openPreview(fileName: string) {
        this.previewFile(fileName);
        this.showModal = true;
        // off the scrolling when modal is open
        document.body.style.overflow = 'hidden';
        
    }

    closePreview() {
        this.showModal = false;
        // Restore body scrolling and Close Modal
        document.body.style.overflow = 'auto';
    }
    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}