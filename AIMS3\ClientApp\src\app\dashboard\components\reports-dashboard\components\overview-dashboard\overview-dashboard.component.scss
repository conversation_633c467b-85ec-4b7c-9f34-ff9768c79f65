.overview-dashboard {
  .stats-card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
    
    &:hover {
      transform: translateY(-5px);
    }
    
    .card-body {
      padding: 1.25rem;
    }
    
    h2 {
      font-weight: 600;
      font-size: 1.75rem;
    }
    
    .icon-bg {
      font-size: 1.5rem;
      opacity: 0.8;
      padding: 10px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    .progress-sm {
      height: 6px;
      border-radius: 3px;
    }
  }
  
  .stats-card-sm {
    border-radius: 10px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
    
    .card-body {
      padding: 1rem;
    }
    
    h4 {
      font-weight: 600;
      font-size: 1.35rem;
    }
    
    .icon-bg-sm {
      font-size: 1.25rem;
      opacity: 0.7;
      padding: 8px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.04);
      
      i {
        width: 20px;
        height: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }
  }
  
  .card {
    border: none;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
    
    .card-body {
      padding: 1.5rem;
    }
  }

  .region-filter {
    min-width: 150px;
    margin-left: 1rem;
    
    select {
      background-color: #f8f9fa;
      border: 1px solid #dee2e6;
      border-radius: 4px;
      padding: 0.25rem 0.5rem;
      font-size: 0.875rem;
      
      &:focus {
        outline: none;
        border-color: #80bdff;
        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
      }
    }
  }

  .chart-container {
    margin-top: 1rem;
    height: 350px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

// Responsive adjustments
@media (max-width: 767.98px) {
  .overview-dashboard {
    .stats-card {
      h2 {
        font-size: 1.5rem;
      }
    }
    
    .stats-card-sm {
      h4 {
        font-size: 1.2rem;
      }
    }
  }
} 