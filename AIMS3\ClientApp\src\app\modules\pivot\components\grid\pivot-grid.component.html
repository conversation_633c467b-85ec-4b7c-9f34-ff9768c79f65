<aims-working *ngIf="working"></aims-working>

<ag-grid-angular id="aims-pivot-grid" style="width: 100%; height: 100%" #aimsPivotGrid
                 [ngClass]="{ 'ag-theme-alpine': !isDarkMode, 'ag-theme-alpine-dark': isDarkMode }"
                 [gridOptions]="gridOptions"
                 [columnDefs]="columnDefs"
                 [defaultColDef]="defaultColDefs"
                 [components]="components"
                 [rowData]="rowData"
                 [animateRows]="true"
                 [tooltipShowDelay]="tooltipShowDelay"
                 (gridReady)="onGridReady($event)"
                 (cellFocused)="onCellFocused($event)">
</ag-grid-angular>