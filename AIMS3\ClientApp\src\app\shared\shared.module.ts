import { CommonModule, DatePipe } from '@angular/common';
import { NgModule } from '@angular/core';
import { NgbTooltipModule } from '@ng-bootstrap/ng-bootstrap';
import { ToastrModule } from 'ngx-toastr';
import { FilterDropdownList } from './components/filter-dropdown/filter-ddl.control';
import { LoadingComponent } from './components/loading/loading.component';
import { WorkingComponent } from './components/working/working.component';
import { AuthorizationPipe } from './pipes/auth.pipe';
import { FileExtColorPipe } from './pipes/file-ext-color.pipe';
import { FileExtPipe } from './pipes/file-ext.pipe';
import { FileSizePipe } from './pipes/format-file-size.pipe';
import { FormatNumPipe } from './pipes/format-num.pipe';
import { NewLineHtmlPipe } from './pipes/nl-html.pipe';
import { ErrorService } from './services/error.service';
import { MessageService } from './services/message.service';
import { SharedService } from './services/shared.service';


@NgModule({
    declarations: [
        WorkingComponent,
        LoadingComponent,
        FilterDropdownList,
        AuthorizationPipe,
        FormatNumPipe,
        FileExtPipe,
        FileSizePipe,
        FileExtColorPipe,
        NewLineHtmlPipe
    ],
    imports: [
        CommonModule,
        NgbTooltipModule,
        ToastrModule.forRoot()
    ],
    providers: [
        SharedService,
        MessageService,
        ErrorService,
        DatePipe
    ],
    exports: [
        CommonModule,
        AuthorizationPipe,
        WorkingComponent,
        LoadingComponent,
        FilterDropdownList,
        NgbTooltipModule,
        FormatNumPipe,
        FileExtPipe,
        FileSizePipe,
        FileExtColorPipe,
        NewLineHtmlPipe
    ]
})
export class SharedModule { }