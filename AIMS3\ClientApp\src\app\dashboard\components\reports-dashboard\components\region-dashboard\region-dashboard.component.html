<div class="region-dashboard">
  <!-- Coverage Stats Row -->
  <div class="row mb-4">
    <div class="col-md-4 mb-3">
      <div class="card stats-card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="card-title mb-0">Provinces Covered</h6>
            <i class="fas fa-map-marker-alt text-danger icon-bg"></i>
          </div>
          <h2 class="mb-2">{{ formatNumber(reportData?.geographicCoverage?.provinces) }}</h2>
          <div class="progress progress-sm">
            <div class="progress-bar bg-danger" role="progressbar" 
                 [style.width]="(reportData?.geographicCoverage?.provinces / 34 * 100) + '%'" 
                 [attr.aria-valuenow]="reportData?.geographicCoverage?.provinces" 
                 aria-valuemin="0" aria-valuemax="34"></div>
          </div>
          <small class="text-muted">{{ Math.round(reportData?.geographicCoverage?.provinces / 34 * 100) }}% of total provinces</small>
        </div>
      </div>
    </div>
    
    <div class="col-md-4 mb-3">
      <div class="card stats-card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="card-title mb-0">Districts Covered</h6>
            <i class="fas fa-map text-success icon-bg"></i>
          </div>
          <h2 class="mb-2">{{ formatNumber(reportData?.geographicCoverage?.districts) }}</h2>
          <div class="progress progress-sm">
            <div class="progress-bar bg-success" role="progressbar" 
                 [style.width]="(reportData?.geographicCoverage?.districts / 400 * 100) + '%'" 
                 [attr.aria-valuenow]="reportData?.geographicCoverage?.districts" 
                 aria-valuemin="0" aria-valuemax="400"></div>
          </div>
          <small class="text-muted">{{ Math.round(reportData?.geographicCoverage?.districts / 400 * 100) }}% of total districts</small>
        </div>
      </div>
    </div>
    
    <div class="col-md-4 mb-3">
      <div class="card stats-card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-center mb-2">
            <h6 class="card-title mb-0">Regional Coverage</h6>
            <i class="fas fa-globe-asia text-primary icon-bg"></i>
          </div>
          <h2 class="mb-2">5</h2>
          <div class="progress progress-sm">
            <div class="progress-bar bg-primary" role="progressbar" 
                 [style.width]="'100%'" 
                 aria-valuenow="5" 
                 aria-valuemin="0" aria-valuemax="5"></div>
          </div>
          <small class="text-muted">100% of total regions</small>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Interactive Map Row -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Activity Region Distribution</h5>
          <p class="text-muted">Interactive map showing activities across Afghanistan</p>
          
          <!-- Map Legend -->
          <div class="map-legend mb-3">
            <span class="legend-item">
              <span class="legend-marker" style="background-color: #999ed8;"></span>
              Central Highlands
            </span>
            <span class="legend-item">
              <span class="legend-marker" style="background-color: #a7bccf;"></span>
              North Eastern
            </span>
            <span class="legend-item">
              <span class="legend-marker" style="background-color: #82b4b1;"></span>
              Western
            </span>
            <span class="legend-item">
              <span class="legend-marker" style="background-color: #cfe0bc;"></span>
              Southern
            </span>
            <span class="legend-item">
              <span class="legend-marker" style="background-color: #dde2a0;"></span>
              Eastern
            </span>
            <span class="legend-item">
              <span class="legend-marker" style="background-color: #9cd6c0;"></span>
              Central
            </span>
            <span class="legend-item">
              <span class="legend-marker" style="background-color: #99d597;"></span>
              South Eastern
            </span>
            <span class="legend-item">
              <span class="legend-marker" style="background-color: #afb593;"></span>
              Northern
            </span>
            <small class="text-muted ms-2">* Circle size indicates number of activities</small>
          </div>
          
          <!-- The Map -->
          <div id="region-map" class="map-container">
            <!-- Loading State -->
            <div class="map-loading" *ngIf="mapLoading">
              <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
              </div>
              <p class="mt-2">Loading map data...</p>
            </div>
            
            <!-- Error State -->
            <div class="map-error" *ngIf="mapError">
              <i class="fas fa-exclamation-triangle text-danger"></i>
              <p class="mt-2">Failed to load map data. Please try refreshing the page.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <!-- Charts Row -->
  <div class="row">
    <div class="col-md-6 mb-3">
      <div class="card">
        <div class="card-body">
          <div *ngIf="regionChartOptions">
            <apx-chart
              [series]="regionChartOptions.series"
              [chart]="regionChartOptions.chart"
              [title]="regionChartOptions.title"
              [colors]="regionChartOptions.colors"
              [xaxis]="regionChartOptions.xaxis"
              [yaxis]="regionChartOptions.yaxis"
              [plotOptions]="regionChartOptions.plotOptions"
              [dataLabels]="regionChartOptions.dataLabels"
              [tooltip]="regionChartOptions.tooltip"
            ></apx-chart>
          </div>
        </div>
      </div>
    </div>
    
    <div class="col-md-6 mb-3">
      <div class="card">
        <div class="card-body">
          <div *ngIf="provinceChartOptions">
            <apx-chart
              [series]="provinceChartOptions.series"
              [chart]="provinceChartOptions.chart"
              [title]="provinceChartOptions.title"
              [colors]="provinceChartOptions.colors"
              [xaxis]="provinceChartOptions.xaxis"
              [yaxis]="provinceChartOptions.yaxis"
              [plotOptions]="provinceChartOptions.plotOptions"
              [dataLabels]="provinceChartOptions.dataLabels"
              [tooltip]="provinceChartOptions.tooltip"
            ></apx-chart>
          </div>
        </div>
      </div>
    </div>
  </div>
</div> 