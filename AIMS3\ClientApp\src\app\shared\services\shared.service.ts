import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { environment } from '../../../environments/environment';
import { Category } from '../../modules/admin/models/category.model';
import { IProjectIntervention, Project } from '../../modules/admin/models/project.model';
import { OrgInfo } from '../../modules/auth/models/org-info.model';
import { ProfileInfo } from '../../modules/data-entry/models/profile.model';
import { ICommunityInfo } from '../../modules/locations/models/community.model';
import { District, IProvince } from '../../modules/locations/models/location.model';

const API_URL = `${environment.apiUrl}`;

@Injectable({ providedIn: 'root' })
export class SharedService {
    constructor(private http: HttpClient) { }

    //refreshUserSession(): Observable<IUser> {
    //    return this.http.get<IUser>(`${API_URL}/auth/refresh-session`);
    //}

    checkSession(userFName: string, orgName: string): Observable<void> {
        return this.http.post<void>(`${API_URL}/auth/check-session`,
            { firstName: userFName, userName: orgName }); // userName to transfer orgName here
    }

    getOrgsList(_return: boolean = false): Observable<OrgInfo[]> {
        if (_return)
            return of([]);
        
        return this.http.get<OrgInfo[]>(`${API_URL}/organizations/list`);
    }

    getCategories(): Observable<Category[]> {
        return this.http.get<Category[]>(`${API_URL}/categories/list`);
    }

    //getYears(): Observable<number[]> {
    //    return this.http.get<number[]>(`${API_URL}/reports/years`);
    //}

    getProvinces(): Observable<IProvince[]> {
        return this.http.get<IProvince[]>('../../data/provinces.json');
    }

    getDistricts(provId?: number): Observable<District[]> {
        if(provId)
            return this.http.get<District[]>(`${API_URL}/districts/${provId}`);
        else
            return this.http.get<District[]>(`${API_URL}/districts`);
    }

    getCommunitiesByProvince(provIds: string): Observable<ICommunityInfo[]> {
        return this.http.get<ICommunityInfo[]>(`${API_URL}/communities/provinces/${provIds}`);
    }

    getProjects(): Observable<Project[]> {
        return this.http.get<Project[]>(`${API_URL}/projects`);
    }

    getProjectsList(): Observable<Project[]> {
        return this.http.get<Project[]>(`${API_URL}/projects/list`);
    }

    getProjectsFiltered(withIntvns?: boolean): Observable<IProjectIntervention[]> {
        if(withIntvns)
            return this.http.get<IProjectIntervention[]>(`${API_URL}/projects/filtered/with-interventions`);
        return this.http.get<IProjectIntervention[]>(`${API_URL}/projects/filtered`);
    }

    getProjectsWithGrouping(): Observable<Project[]> {
        return this.http.get<Project[]>(`${API_URL}/projects/with-grouping`);
    }

    getInterventionProfiles(): Observable<ProfileInfo[]> {
        return this.http.get<ProfileInfo[]>(`${API_URL}/interventions/list`);
    }

    //getAllVariables(): Observable<IndicatorInfoVariable[]> {
    //    return this.http.get<IndicatorInfoVariable[]>(`${API_URL}api/indicatorVariables/list`);
    //}

    getTestData(source: string): Observable<any[]> {
        return this.http.get<any[]>(`../../data/${source}.test-data.json`);
    }


    //getProfileVariables(profId: number, tVars?: boolean): Observable<IProfileVariable[]> {
    //    if (tVars === undefined || tVars === null)
    //        return this.http.get<IProfileVariable[]>(`${API_URL}api/interventions/${profId}/variables`);

    //    return this.http.get<IProfileVariable[]>(`${API_URL}api/interventions/${profId}/variables/${tVars}`);
    //}

    downloadFile(fileName: string): Observable<any> {
        const headers = new HttpHeaders({
            'Content-Type': 'application/octet-stream',
            'Accept': 'application/octet-stream'
        });

        return this.http.get(`${API_URL}/documents/file/${fileName}`,
            { headers: headers, responseType: 'blob' });
    }
}