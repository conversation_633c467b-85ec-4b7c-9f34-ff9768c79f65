import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { DataTablesModule } from 'angular-datatables';
import { InlineSVGModule } from 'ng-inline-svg-2';
import { ModalsModule } from '../../_theme/partials';
import { SharedModule } from '../../shared/shared.module';
import { AttachmentsFormComponent } from './components/attachments/attachments.component';
import { DocumentTypesComponent } from './components/doc-types/doc-types.component';
import { DocumentsComponent } from './components/documents/documents.component';
import { DownloadComponent } from './components/download/download.component';
import { UploadFormComponent } from './components/upload/upload-form.component';
import { LibraryRoutingModule } from './library-routing.module';
import { DocumentTypeService } from './services/doc-type.service';
import { DocumentService } from './services/document.service';

@NgModule({
    declarations: [
        DocumentsComponent,
        DownloadComponent,
        UploadFormComponent,
        DocumentTypesComponent,
        AttachmentsFormComponent
    ],
    imports: [
        FormsModule,
        ReactiveFormsModule,
        SharedModule,
        LibraryRoutingModule,
        InlineSVGModule,
        DataTablesModule,
        ModalsModule
    ],
    exports: [
        AttachmentsFormComponent
    ],
    providers: [
        DocumentService,
        DocumentTypeService
    ]
})
export class LibraryModule { }