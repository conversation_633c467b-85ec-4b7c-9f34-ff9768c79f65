import { Pipe, PipeTransform } from '@angular/core';
import { AuthService, UserType } from '../../modules/auth';

@Pipe({ name: 'isAuth' })
export class AuthorizationPipe implements PipeTransform {
    loggedInUser: UserType;
    rolesAbbrviations: { [key: string]: string } = {
        Admin: 'admin',
        Approver: 'ga',
        Viewer: 'gv',
        LocalApprover: 'la',
        DataEntry: 'lde',
        LocalViewer: 'lv'
    }

    constructor(private authService: AuthService) {
        this.loggedInUser = this.authService.currentUserValue;
    }

    transform(rolesAbbr: string[], orgId?: number, exact?: boolean): boolean {
        // if user is not logged in
        if (!this.loggedInUser)
            return false;

        // if no role is specified or is admin, user is authorized
        if (!exact && (!rolesAbbr || this.loggedInUser.roles.includes('Admin')))
            return true;

        // only allow if the user is in the role
        if (this.loggedInUser.roles.findIndex(r => rolesAbbr.includes(this.rolesAbbrviations[r])) > -1) {
            // check to see if allowed locally
            if (rolesAbbr.includes('la') || rolesAbbr.includes('lde')) {
                if (!orgId || orgId === this.loggedInUser.org.id)
                    return true;
                return false;
            }

            return true;
        }

        // otherwise, not authorized
        return false;
    }
}