import { Compo<PERSON>, <PERSON><PERSON><PERSON>ter, On<PERSON><PERSON>roy, OnInit, Output, ViewChild } from '@angular/core';
import { ColumnState } from 'ag-grid-community';
import { lastValueFrom, Subject, Subscription } from 'rxjs';
import { FilterDropdownList } from '../../../../shared/components/filter-dropdown/filter-ddl.control';
import { MessageService } from '../../../../shared/services/message.service';
import { AppUtilities } from '../../../../shared/utilities';
import { ModalComponent, ModalConfig } from '../../../../_theme/partials';
import { IProjectIntervention } from '../../../admin/models/project.model';
import { ProfileInfo } from '../../../data-entry/models/profile.model';
import { IDataFilter } from '../../../data/models/data.model';
import { ViewDataService } from '../../../data/services/view-data.service';
import { IPivot, Pivot } from '../../models/pivot.model';
import { PivotService } from '../../services/pivot.service';
import { PivotGridComponent } from '../grid/pivot-grid.component';

@Component({
    selector: 'pivot-modal',
    templateUrl: './pivot-modal.component.html',
    styleUrls: ['./pivot-modal.component.scss']
})
export class PivotModalComponent implements OnInit, OnDestroy {
    working: boolean = false;
    isAdmin: boolean = false;

    @ViewChild('modal') private modalComponent: ModalComponent;
    modalConfig: ModalConfig = {
        modalTitle: 'Pivot',
        cancelButtonLabel: 'Close',
        options: { size: 'xxl' },
        shouldDo: () => this.save(),
        shouldCancel: () => { this.ngOnDestroy(); return true; }
    };
    @Output() done = new EventEmitter();

    pivot: Pivot;
    orgs: any[] = [];
    projects: IProjectIntervention[] = [];
    profName: string;

    @ViewChild(PivotGridComponent, { static: true })
    private pivotGridComponent: PivotGridComponent;

    subscriptions: Subscription[] = [];
    constructor(
        private pivotService: PivotService,
        private dataService: ViewDataService,
        private messageService: MessageService
    ) {
        this.pivot = new Pivot(0, 'New pivot', 0, false, 1);
    }

    ngOnInit(pivot?: IPivot) {
        if (pivot) {
            if (this.isAdmin) {
                if (pivot.id > 0) {
                    this.profName = pivot.prof?.abbreviation;
                    this.modalConfig.doneButtonLabel = 'Save changes';
                } else {
                    this.profName = pivot.prof?.abbreviation;
                    this.modalConfig.modalTitle = '';
                    this.modalConfig.doneButtonLabel = 'Add';
                }
            } else {
                this.modalConfig.hideDoneButton = true;
                this.modalConfig.modalTitle = `Pivot: ${pivot.name} ` +
                    `<span class="text-gray-600 fs-7 fw-semibold ms-4">${pivot.prof?.abbreviation}</span>`;

                if (pivot.id) {
                    if (pivot.isTarget) {
                        this.modalConfig.modalTitle +=
                            `<span class="text-info fs-8 fw-semibold px-2">Targets</span>`;
                    } else {
                        this.modalConfig.modalTitle +=
                            `<span class="text-primary fs-8 fw-semibold px-2">Progress</span>`;
                    }
                }
            }

            this.pivot = new Pivot(pivot.id, pivot.name, pivot.profId, pivot.isTarget,
                pivot.order, pivot.pivotColumns, pivot.filtersApplied);

            setTimeout(() => {
                if (!this.pivot.id) {
                    AppUtilities().initSelect2('#pivotDataType');
                    $('#pivotDataType').on('change', (e) => {
                        const selVal = $(e.target).val() || 'Progress';
                        this.pivot.isTarget = selVal[0] === 'P' ? false : true;
                        this.initPivotGrid(pivot.prof);
                        this.pivotGridComponent.gridColumnApi.resetColumnState();
                    });
                }
            }, 100);

            this.initPivotGrid(pivot.prof);
            this.modalComponent?.open(true).then();
        }
    }

    private initPivotGrid(prof: ProfileInfo): void {
        if (this.pivot.isTarget) {
            this.pivotGridComponent.dynamicColumns = prof.variables
                .filter(v => [0, 1, 3].includes(v.type)); // ColumnVarType.Target, Info, Static
        } else {
            this.pivotGridComponent.dynamicColumns = prof.variables
                .filter(v => [2, 4, 5].includes(v.type)); // ColumnVarType.Progress, Info, Static
        }

        this.pivotGridComponent.selectedVals.isTarget = this.pivot.isTarget;
        this.pivotGridComponent.selectedVals.profId = prof.id;
        this.pivotGridComponent.selectedVals.prof = prof.abbreviation;

        if (this.pivot.id)
            this.pivotGridComponent.gridColumnApi.setPivotMode(true);
        this.pivotGridComponent.initGrid();          

        // get data, and set pivot columns and apply filters
        this.getData();
    }


    /** DATA for Pivot */
    dataFilters: IDataFilter = {
        isTarget: false,
        approvalMode: false,
        projIds: []
    };
    onFilterChange(ctrl: FilterDropdownList): void {
        this.dataFilters[ctrl.id] = ctrl['selVals'];
    }

    getData(): void {
        this.working = true;

        this.subscriptions.push(
            this.dataService.getInterventionData(this.pivot.profId, this.dataFilters).subscribe({
                next: (res) => {
                    this.pivotGridComponent.refreshGridRows([...res]);
                },
                error: (err) => {
                    this.working = false;
                    console.log(err);
                },
                complete: () => {
                    // set pivot columns state
                    if (this.pivot.pivotColumns) {
                        const colStates = JSON.parse(this.pivot.pivotColumns) as ColumnState[];
                        this.pivotGridComponent.gridColumnApi.applyColumnState({
                            state: colStates,
                            applyOrder: true
                        });
                    }

                    // apply filters
                    if (this.pivot.filtersApplied) {
                        this.pivotGridComponent.gridApi.setFilterModel(
                            JSON.parse(this.pivot.filtersApplied)
                        );
                    }

                    // change group column name
                    this.pivotGridComponent.onRowGroupChanged();
                    this.working = false;
                }
            }));
    }

    exists: boolean = false;
    async save(): Promise<boolean> {
        const result = new Subject<boolean>();
        if (this.exists)
            return;

        // validate
        if (!this.pivotGridComponent.gridColumnApi.isPivotMode()) {
            this.messageService.error('Please enable pivot mode.');
            return;
        }

        //const pivotColumns =
        //this.pivotGridComponent.gridColumnApi
        //    .getRowGroupColumns().map(c => c.getColId());

        //const valueColumns =
        //    this.pivotGridComponent.gridColumnApi
        //        .getValueColumns().map(c => c.getColId());

        //const labelColumns =
        //    this.pivotGridComponent.gridColumnApi
        //        .getPivotColumns().map(c => c.getColId());

        // get pivoted column states
        const colStates: ColumnState[] = this.pivotGridComponent.gridColumnApi.getColumnState();
        const pivotedColStates = colStates.filter(c => c.rowGroup || c.aggFunc || c.pivot);

        if (!pivotedColStates.length) {
            this.messageService.error("Please add at least one pivot column " +
                "to the 'Row Groups' or one value column to the 'Values' section.");
            return;
        }

        this.pivot.pivotColumns = JSON.stringify(pivotedColStates);
        this.pivot.filtersApplied = JSON.stringify(
            this.pivotGridComponent.gridApi.getFilterModel());

        if (this.pivot.filtersApplied === '{}')
            this.pivot.filtersApplied = null;

        //console.log(this.pivot)
        
        this.modalConfig.working = true;

        // add or update pivot config
        let operation = this.pivot.id > 0 ?
            this.pivotService.updatePivot(this.pivot) :
            this.pivotService.addPivot(this.pivot);

        this.subscriptions.push(operation.subscribe({
            error: (err) => {
                this.modalConfig.working = false;
                console.log(err);
                this.exists = err.error.srPivotExists;

                if (this.exists)
                    this.messageService.error('A pivot table with this name and type already exists for this intervention.', 'Exists');

                result.next(false);
                result.complete();
            },
            complete: () => {
                let successMsg = 'The pivot table has been added successfully.';
                if (this.pivot.id > 0)
                    successMsg = "The pivot table has been updated successfully.";

                this.messageService.success(successMsg);

                // notify to refresh pivots
                this.done.emit();

                this.modalConfig.working = false;
                result.next(true);
                result.complete();
            }
        }));

        return await lastValueFrom(result.asObservable());
    }

    ngOnDestroy() {
        this.subscriptions.forEach((sb) => sb.unsubscribe());
    }
}